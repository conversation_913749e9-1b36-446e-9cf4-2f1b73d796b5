package com.hvisions.purchase.srm.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.QueryDTO;
import com.hvisions.purchase.dto.purchase.srm.*;
import com.hvisions.purchase.purchase.dao.*;
import com.hvisions.purchase.purchase.entity.*;
import com.hvisions.purchase.purchase.service.TPoSrmLogService;
import com.hvisions.purchase.srm.constant.SrmConfig;
import com.hvisions.purchase.srm.dto.*;
import com.hvisions.purchase.srm.vo.SrmProductPlanVo;
import com.hvisions.purchase.utils.GenerateCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SrmService {

    @Resource
    private TPoSrmLogService srmLogService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private ProcurementPlanMapper procurementPlanMapper;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    private VendorMapper vendorMapper;

    @Resource
    private MaterialClient materialClient;

    @Resource
    private TMpMonthDemandOrderMapper monthDemandOrderMapper;

    @Resource
    private TMpMonthDemandDetailMapper mpMonthDemandDetailMapper;

    @Resource
    private DailyDeliveryMapper dailyDeliveryMapper;

    @Resource
    private DailyDeliveryDetailMapper dailyDeliveryDetailMapper;

    @Resource
    private DemandOrderMapper demandOrderMapper;

    @Resource
    private DeliveryItemMapper deliveryItemMapper;

    @Resource
    private TMpVendorManageMapper vendorManageMapper;

    @Resource
    private VehicleBlacklistMapper vehicleBlacklistMapper;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private RequestSrm requestSrm;

    @Resource
    private SrmConfig srmConfig;

    /**
     * 接收年度需求采购详情
     * @param yearDemandDetailDto
     * @return
     */
    public String receiveYearDemandDetail(YearDemandDetailDTO yearDemandDetailDto) {
        String jsonString = JSONObject.toJSONString(yearDemandDetailDto);
        log.info("接收年度需求采购详情, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "年度采购需求");
        List<OrderDetailDTO> orderDetailList = yearDemandDetailDto.getOrderDetailList();
        //采购订单信息校验
        checkAndSet(srmLog, yearDemandDetailDto);
        //采购订单子集信息校验
        checkAndSetDetail(srmLog, orderDetailList, yearDemandDetailDto);
        //采购订单信息逻辑判断
        if ("10".equals(yearDemandDetailDto.getOperationType())) {
            //新增
            TMpPurchaseOrder add = new TMpPurchaseOrder();
            add.setVendorId(yearDemandDetailDto.getVendorId());
            add.setRemark(yearDemandDetailDto.getRemark());
            add.setSapOrder(yearDemandDetailDto.getSapOrder());
            add.setContractNumber(yearDemandDetailDto.getContractNumber());
            add.setPlanningId(yearDemandDetailDto.getPlanningId());
            add.setOrderNo(generateCodeUtil.generateInspectionCode("PO", 3));
            purchaseOrderMapper.insert(add);
            yearDemandDetailDto.setOrderId(add.getId());
        } else if("20".equals(yearDemandDetailDto.getOperationType())) {
            //修改
            TMpPurchaseOrder update = new TMpPurchaseOrder();
            update.setId(yearDemandDetailDto.getOrderId());
            update.setVendorId(yearDemandDetailDto.getVendorId());
            update.setRemark(yearDemandDetailDto.getRemark());
            update.setSapOrder(yearDemandDetailDto.getSapOrder());
            update.setContractNumber(yearDemandDetailDto.getContractNumber());
            update.setPlanningId(yearDemandDetailDto.getPlanningId());
            purchaseOrderMapper.updateById(update);
        } else if("30".equals(yearDemandDetailDto.getOperationType())) {
            //删除
            purchaseOrderMapper.deleteById(yearDemandDetailDto.getOrderId());
        }
        //采购订单详情
        receiveOrderDetail(orderDetailList, yearDemandDetailDto.getOrderId());
        srmLogService.add(srmLog);
        return "成功";
    }

    /**
     * 校验物料信息
     * @param orderDetailList
     */
    private void checkAndSetDetail(TPoSrmLog srmLog, List<OrderDetailDTO> orderDetailList, YearDemandDetailDTO yearDemandDetailDto) {
        //是否有详情信息
        if (CollectionUtils.isEmpty(orderDetailList)){
            if ("20".equals(yearDemandDetailDto.getOperationType()) || "10".equals(yearDemandDetailDto.getOperationType())) {
                String errMeg = "新增修改操作 采购订单行号详情 orderDetailList 不能为空";
                srmLog.setErrMsg(errMeg);
                srmLog.setPostStatus("失败");
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            return;
        }
        //校验合同号是否异常
        List<String> contractNumber = orderDetailList.stream().map(OrderDetailDTO::getContractNumber).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contractNumber)) {
            String errMeg = "合同信息编号不能为空";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        } else if (contractNumber.size() > 1) {
            String errMeg = "采购订单行号详情 合同信息编号 不一致";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        yearDemandDetailDto.setContractNumber(contractNumber.get(0));
        //校验采购需求号是否异常
        List<String> planningNo = orderDetailList.stream().map(OrderDetailDTO::getPlanningNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planningNo)) {
            String errMeg = "采购计划编号不能为空";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        } else if (planningNo.size() > 1) {
            String errMeg = "采购订单行号详情 采购计划编号 不一致";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        yearDemandDetailDto.setContractNumber(orderDetailList.get(0).getContractNumber());
        TMpProcurementPlanning plan = procurementPlanMapper.selectOne(new LambdaUpdateWrapper<TMpProcurementPlanning>()
                .eq(TMpProcurementPlanning::getPlanCode, planningNo.get(0))
                .orderByDesc(TMpProcurementPlanning::getId).last("limit 1"));
        //采购需求是否存在
        if (plan == null) {
            String errMeg = "采购计划编号不存在:" + yearDemandDetailDto.getVendorCode();
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        yearDemandDetailDto.setPlanningId(plan.getId());
        //校验子集信息
        Map<String, Integer> materialMap = new HashMap<>();
        for (OrderDetailDTO orderDetailDTO : orderDetailList) {
            if ("30".equals(orderDetailDTO.getOperationType()) || "20".equals(orderDetailDTO.getOperationType())) {
                TMpPurchaseOrderDetail detail = purchaseOrderDetailMapper.selectOne(new LambdaUpdateWrapper<TMpPurchaseOrderDetail>()
                        .eq(TMpPurchaseOrderDetail::getOrderId, yearDemandDetailDto.getOrderId())
                        .eq(TMpPurchaseOrderDetail::getItemKey, orderDetailDTO.getItemKey())
                        .eq(TMpPurchaseOrderDetail::getDeleted, false)
                        .orderByDesc(TMpPurchaseOrderDetail::getId).last("limit 1"));
                //校验采购订单详情是否存在
                if (null == detail) {
                    String errMeg = "采购订单行号不存在:" + orderDetailDTO.getItemKey();
                    srmLog.setErrMsg(errMeg);
                    srmLog.setPostStatus("失败");
                    srmLogService.add(srmLog);
                    throw new BaseKnownException(10000, errMeg);
                }
                orderDetailDTO.setDetailId(detail.getId());
                if ("30".equals(orderDetailDTO.getOperationType())) {
                    //删除的话 需要校验是否存在
                    Integer count = deliveryItemMapper.selectCount(new LambdaUpdateWrapper<TMpDeliveryItem>()
                            .eq(TMpDeliveryItem::getOrderDetailId, orderDetailDTO.getDetailId())
                            .eq(TMpDeliveryItem::getDeleted, false));
                    if (count > 0) {
                        String errMeg = "采购订单行号不能删除，已经关联数据:" + orderDetailDTO.getItemKey();
                        srmLog.setErrMsg(errMeg);
                        srmLog.setPostStatus("失败");
                        srmLogService.add(srmLog);
                        throw new BaseKnownException(10000, errMeg);
                    }
                    continue;
                }
            }
            //校验物料信息
            Integer materialId = materialMap.get(orderDetailDTO.getMaterialCode());
            if (materialId == null) {
                QueryDTO queryDTO = new QueryDTO();
                queryDTO.setMaterialCode(orderDetailDTO.getMaterialCode());
                ResultVO<HvPage<MaterialDTO>> materialByNameOrCode = materialClient.getMaterialByNameOrCode(queryDTO);
                if (CollectionUtils.isEmpty(materialByNameOrCode.getData().getContent())) {
                    String errMeg = "物料信息不存在:" + orderDetailDTO.getMaterialCode();
                    srmLog.setErrMsg(errMeg);
                    srmLog.setPostStatus("失败");
                    srmLogService.add(srmLog);
                    throw new BaseKnownException(10000, errMeg);
                }
                materialId = materialByNameOrCode.getData().getContent().get(0).getId();
                materialMap.put(orderDetailDTO.getMaterialCode(), materialId);
            }
            //设置物料信息
            orderDetailDTO.setMaterialId(materialId);
        }
    }

    /**
     * 采购订单信息校验
     * @param srmLog
     * @param yearDemandDetailDto
     */
    private void checkAndSet(TPoSrmLog srmLog, YearDemandDetailDTO yearDemandDetailDto) {
        if (StringUtils.isBlank(yearDemandDetailDto.getSapOrder())) {
            String errMeg = "采购订单不不能为空";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        TMpPurchaseOrder query = purchaseOrderMapper.selectOne(new LambdaUpdateWrapper<TMpPurchaseOrder>()
                .eq(TMpPurchaseOrder::getSapOrder, yearDemandDetailDto.getSapOrder())
                .eq(TMpPurchaseOrder::getDeleted, false)
                .orderByDesc(TMpPurchaseOrder::getId).last("limit 1"));
        if ("10".equals(yearDemandDetailDto.getOperationType()) && query != null) {
            String errMeg = "新增采购订单失败,采供订单已存在:" + yearDemandDetailDto.getSapOrder();
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        } else if ("30".equals(yearDemandDetailDto.getOperationType()) || "20".equals(yearDemandDetailDto.getOperationType())) {
            //校验采购订单是否存在
            if (null == query) {
                String errMeg = "采购订单不存在:" + yearDemandDetailDto.getSapOrder();
                srmLog.setErrMsg(errMeg);
                srmLog.setPostStatus("失败");
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            yearDemandDetailDto.setOrderId(query.getId());
            if ("30".equals(yearDemandDetailDto.getOperationType())) {
                //如果删除采购订单，需要判断是否有要货需求
                Integer count = dailyDeliveryMapper.selectCount(new LambdaUpdateWrapper<TMpDailyDelivery>()
                        .eq(TMpDailyDelivery::getDeleted, false)
                        .eq(TMpDailyDelivery::getPurchaseId, yearDemandDetailDto.getOrderId()));
                if (count > 0) {
                    String errMeg = "已存在要货计划, 采购订单不允许删除:" + yearDemandDetailDto.getSapOrder();
                    srmLog.setErrMsg(errMeg);
                    srmLog.setPostStatus("失败");
                    srmLogService.add(srmLog);
                    throw new BaseKnownException(10000, errMeg);
                }
                return;
            }
        }
        //校验供应商信息
        TMpVendor vendor = vendorMapper.selectOne(new LambdaUpdateWrapper<TMpVendor>()
                .eq(TMpVendor::getCode, yearDemandDetailDto.getVendorCode())
                .eq(TMpVendor::getDeleted, false)
                .orderByDesc(TMpVendor::getId).last("limit 1"));
        if (vendor == null) {
            String errMeg = "供应商信息不存在:" + yearDemandDetailDto.getVendorCode();
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        yearDemandDetailDto.setVendorId(vendor.getId());
    }

    /**
     * 接收处理采购订单明细数据
     *
     * @param orderDetailList
     * @param purchaseOrderId
     */
    private void receiveOrderDetail(List<OrderDetailDTO> orderDetailList, Integer purchaseOrderId) {
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return;
        }
        for (OrderDetailDTO orderDetailDTO : orderDetailList) {
            if ("10".equals(orderDetailDTO.getOperationType())) {
                //新增
                TMpPurchaseOrderDetail detail = new TMpPurchaseOrderDetail();
                BeanUtils.copyProperties(orderDetailDTO, detail);
                detail.setOrderId(purchaseOrderId);
                detail.setIsNew("1");
                purchaseOrderDetailMapper.insert(detail);
            } else if ("20".equals(orderDetailDTO.getOperationType())) {
                //修改
                TMpPurchaseOrderDetail update = new TMpPurchaseOrderDetail();
                BeanUtils.copyProperties(orderDetailDTO, update);
                update.setId(orderDetailDTO.getDetailId());
                update.setOrderId(purchaseOrderId);
                purchaseOrderDetailMapper.updateById(update);
            } else if ("30".equals(orderDetailDTO.getOperationType())) {
                //删除
                purchaseOrderDetailMapper.deleteById(orderDetailDTO.getDetailId());
            }
        }
    }

    /**
     * 初始化srm日志信息
     * @param jsonString
     * @param interactionType
     * @param businessType
     */
    private TPoSrmLog initSrmLog(String jsonString, String interactionType, String businessType) {
        TPoSrmLog srmLog = new TPoSrmLog();
        srmLog.setInteractionType(interactionType);
        srmLog.setBusinessType(businessType);
        srmLog.setRequestParam(jsonString);
        srmLog.setCreateTime(new Date());
        srmLog.setPostStatus("成功");
        return srmLog;
    }

    /**
     * 接收月度供货计划
     * @param monthDemandPlanDTO
     * @return
     */
    public String receiveMonthDemandPlan(MonthDemandPlanDTO monthDemandPlanDTO) {
        String jsonString = JSONObject.toJSONString(monthDemandPlanDTO);
        log.info("接收月要货需求, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "月要货需求");
        checkAndSetMonthDemandPlan(srmLog, monthDemandPlanDTO);
        if ("10".equals(monthDemandPlanDTO.getOperationType())) {
            //新增
            TMpMonthDemandDetail demandDetail = new TMpMonthDemandDetail();
            BeanUtils.copyProperties(monthDemandPlanDTO, demandDetail);
            mpMonthDemandDetailMapper.insert(demandDetail);
        } else if ("20".equals(monthDemandPlanDTO.getOperationType())) {
            //修改
            TMpMonthDemandDetail update = new TMpMonthDemandDetail();
            update.setId(monthDemandPlanDTO.getMouthDemandDetailId());
            BeanUtils.copyProperties(monthDemandPlanDTO, update);
            mpMonthDemandDetailMapper.updateById(update);
        } else if ("30".equals(monthDemandPlanDTO.getOperationType())) {
            //删除
            mpMonthDemandDetailMapper.deleteById(monthDemandPlanDTO.getMouthDemandDetailId());
        }
        srmLogService.add(srmLog);
        return "成功";
    }

    /**
     * 校验设置参数--月需求
     * @param srmLog
     * @param monthDemandPlanDTO
     */
    private void checkAndSetMonthDemandPlan(TPoSrmLog srmLog, MonthDemandPlanDTO monthDemandPlanDTO) {
        //月要货需求校验
        if (StringUtils.isBlank(monthDemandPlanDTO.getMouthDemandNo())) {
            String errMeg = "月需求单编号(MES)不能为空";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        TMpMonthDemandOrder tMpMonthDemandOrder = monthDemandOrderMapper.selectOne(new LambdaUpdateWrapper<TMpMonthDemandOrder>()
                .eq(TMpMonthDemandOrder::getOrderNo, monthDemandPlanDTO.getMouthDemandNo())
                .eq(TMpMonthDemandOrder::getDeleted, false)
                .orderByDesc(TMpMonthDemandOrder::getId).last("limit 1"));
        if (null == tMpMonthDemandOrder) {
            String errMeg = "月需求单编号(MES)不存在:" + monthDemandPlanDTO.getMouthDemandNo();
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        monthDemandPlanDTO.setMouthDemandId(tMpMonthDemandOrder.getId());
        TMpMonthDemandDetail query = mpMonthDemandDetailMapper.selectOne(new LambdaUpdateWrapper<TMpMonthDemandDetail>()
                .eq(TMpMonthDemandDetail::getSupplyNo, monthDemandPlanDTO.getSupplyNo())
                .eq(TMpMonthDemandDetail::getDeleted, false)
                .orderByDesc(TMpMonthDemandDetail::getId).last("limit 1"));
        //供货分解单号校验
        if (StringUtils.isBlank(monthDemandPlanDTO.getSupplyNo())) {
            String errMeg = "供货分解单号数据不能为空";
            srmLog.setErrMsg(errMeg);
            srmLog.setPostStatus("失败");
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        if ("30".equals(monthDemandPlanDTO.getOperationType()) || "20".equals(monthDemandPlanDTO.getOperationType())) {
            if (query == null) {
                String errMeg = "供货分解单号数据不存在:" + monthDemandPlanDTO.getSupplyNo();
                srmLog.setErrMsg(errMeg);
                srmLog.setPostStatus("失败");
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            monthDemandPlanDTO.setMouthDemandDetailId(query.getId());
        } else if ("10".equals(monthDemandPlanDTO.getOperationType())) {
            if (query != null) {
                String errMeg = "供货分解单号已存在:" + monthDemandPlanDTO.getSupplyNo();
                srmLog.setErrMsg(errMeg);
                srmLog.setPostStatus("失败");
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
        }
    }

    /**
     *
     * @param deliveryDetailDTO
     * @return
     */
    public String receiveDeliveryDetail(DeliveryDetailDTO deliveryDetailDTO) {
        String jsonString = JSONObject.toJSONString(deliveryDetailDTO);
        log.info("接收月要货需求, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "日要货需求");
        checkAndSetDeliveryDetail(srmLog, deliveryDetailDTO);
        //日送货计划数据创建
        createDailyDelivery(deliveryDetailDTO);
        createDeliveryItem(deliveryDetailDTO);
        if ("10".equals(deliveryDetailDTO.getOperationType())) {
            //新增
            TMpDailyDeliveryDetail add = new TMpDailyDeliveryDetail();
            BeanUtils.copyProperties(deliveryDetailDTO, add);
            if (add.getMaterialName().contains("LG")) {
                add.setType("1");
            } else if (add.getMaterialName().contains("DK")) {
                add.setType("2");
            } else if (add.getMaterialName().contains("LQM")) {
                add.setType("3");
            }
            add.setState("0");
            add.setPostingState("0");
            dailyDeliveryDetailMapper.insert(add);
        } else if ("20".equals(deliveryDetailDTO.getOperationType())) {
            //修改
            TMpDailyDeliveryDetail update = new TMpDailyDeliveryDetail();
            BeanUtils.copyProperties(deliveryDetailDTO, update);
            update.setId(deliveryDetailDTO.getDailyDeliveryDetailId());
            dailyDeliveryDetailMapper.updateById(update);
        } else if ("30".equals(deliveryDetailDTO.getOperationType())) {
            //删除
            dailyDeliveryDetailMapper.deleteById(deliveryDetailDTO.getDailyDeliveryDetailId());
        }
        srmLogService.add(srmLog);
        return "成功";
    }

    /**
     * 创建日送货子项
     * @param deliveryDetailDTO
     */
    private void createDeliveryItem(DeliveryDetailDTO deliveryDetailDTO) {
        TMpDeliveryItem tMpDeliveryItem = deliveryItemMapper.selectOne(new LambdaUpdateWrapper<TMpDeliveryItem>()
                .eq(TMpDeliveryItem::getDeliveryId, deliveryDetailDTO.getDeliveryId())
                .eq(TMpDeliveryItem::getOrderDetailId, deliveryDetailDTO.getPurchaseDetailId())
                .eq(TMpDeliveryItem::getDeleted, false));
        if (null != tMpDeliveryItem) {
            log.info("日送货计划子项数据已经存在,不在创建:" + deliveryDetailDTO.getDeliveryPlanNo());
            deliveryDetailDTO.setDeliveryItemId(tMpDeliveryItem.getId());
            return;
        }
        TMpDeliveryItem item = new TMpDeliveryItem();
        item.setDeliveryId(deliveryDetailDTO.getDeliveryId());
        item.setOrderDetailId(deliveryDetailDTO.getPurchaseDetailId());
        deliveryItemMapper.insert(item);
        deliveryDetailDTO.setDeliveryItemId(item.getId());
    }

    /**
     * 创建日送货计划
     * @param deliveryDetailDTO
     */
    private void createDailyDelivery(DeliveryDetailDTO deliveryDetailDTO) {
        TMpDailyDelivery tMpDailyDelivery = dailyDeliveryMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDelivery>()
                .eq(TMpDailyDelivery::getPlanNumber, deliveryDetailDTO.getDeliveryPlanNo())
                .eq(TMpDailyDelivery::getDeleted, false));
        if (null != tMpDailyDelivery) {
            log.info("日送货计划数据已经存在,不在创建:" + deliveryDetailDTO.getDeliveryPlanNo());
            deliveryDetailDTO.setDeliveryId(tMpDailyDelivery.getId());
           return;
        }
        TMpDailyDelivery delivery = new TMpDailyDelivery();
        delivery.setPurchaseId(deliveryDetailDTO.getPurchaseId());
        delivery.setDemandId(deliveryDetailDTO.getDemandId());
        delivery.setBaseName(deliveryDetailDTO.getBaseName());
        TMpDemandOrder tMpDemandOrder = demandOrderMapper.selectById(delivery.getDemandId());
        delivery.setMaterialId(tMpDemandOrder.getMaterialId());
        delivery.setMaterialCode(tMpDemandOrder.getMaterialCode());
        delivery.setMaterialName(tMpDemandOrder.getMaterialName());
        delivery.setPlanNumber(deliveryDetailDTO.getDeliveryPlanNo());
        delivery.setFeedType(tMpDemandOrder.getFeedType());
        delivery.setState("0");
        delivery.setMonthDemandId(deliveryDetailDTO.getMonthDemandId());
        dailyDeliveryMapper.insert(delivery);
        deliveryDetailDTO.setDeliveryId(delivery.getId());
    }

    /**
     * 校验设置参数--日要货需求
     * @param srmLog
     * @param deliveryDetailDTO
     */
    private void checkAndSetDeliveryDetail(TPoSrmLog srmLog, DeliveryDetailDTO deliveryDetailDTO) {
        //非空校验
        if (StringUtils.isBlank(deliveryDetailDTO.getMouthDemandNo()) || StringUtils.isBlank(deliveryDetailDTO.getDeliveryNumber()) || StringUtils.isBlank(deliveryDetailDTO.getDayDemandNo())
                || StringUtils.isBlank(deliveryDetailDTO.getSapOrder()) || StringUtils.isBlank(deliveryDetailDTO.getItemKey())) {
            String errMeg = "送货单号(唯一)|日需求单编号(MES)|采购订单编号|采购订单行号 不能为空";
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        TMpMonthDemandOrder tMpMonthDemandOrder = monthDemandOrderMapper.selectOne(new LambdaUpdateWrapper<TMpMonthDemandOrder>()
                .eq(TMpMonthDemandOrder::getOrderNo, deliveryDetailDTO.getMouthDemandNo())
                .eq(TMpMonthDemandOrder::getDeleted, false)
                .orderByDesc(TMpMonthDemandOrder::getId).last("limit 1"));
        if (null == tMpMonthDemandOrder) {
            String errMeg = "月需求单编号(MES)不存在:" + deliveryDetailDTO.getDayDemandNo();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        deliveryDetailDTO.setMonthDemandId(tMpMonthDemandOrder.getId());
        TMpDemandOrder demandOrder = demandOrderMapper.selectOne(new LambdaUpdateWrapper<TMpDemandOrder>()
                .eq(TMpDemandOrder::getOrderNo, deliveryDetailDTO.getDayDemandNo())
                .eq(TMpDemandOrder::getDeleted, false)
                .orderByDesc(TMpDemandOrder::getId).last("limit 1"));
        if (null == demandOrder) {
            String errMeg = "日需求单编号(MES)不存在:" + deliveryDetailDTO.getDayDemandNo();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        deliveryDetailDTO.setDemandId(demandOrder.getId());
        deliveryDetailDTO.setBaseName(demandOrder.getBaseName());
        //校验采购订单和采购订单行号是否正常
        TMpPurchaseOrder query = purchaseOrderMapper.selectOne(new LambdaUpdateWrapper<TMpPurchaseOrder>()
                .eq(TMpPurchaseOrder::getSapOrder, deliveryDetailDTO.getSapOrder())
                .eq(TMpPurchaseOrder::getDeleted, false)
                .orderByDesc(TMpPurchaseOrder::getId).last("limit 1"));
        if (null == query) {
            String errMeg = "采购订单不存在:" + deliveryDetailDTO.getSapOrder();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        deliveryDetailDTO.setPurchaseId(query.getId());
        TMpPurchaseOrderDetail detail = purchaseOrderDetailMapper.selectOne(new LambdaUpdateWrapper<TMpPurchaseOrderDetail>()
                .eq(TMpPurchaseOrderDetail::getOrderId, query.getId())
                .eq(TMpPurchaseOrderDetail::getItemKey, deliveryDetailDTO.getItemKey())
                .eq(TMpPurchaseOrderDetail::getDeleted, false)
                .orderByDesc(TMpPurchaseOrderDetail::getId).last("limit 1"));
        if (null == detail) {
            String errMeg = "采购订单行号不存在:" + deliveryDetailDTO.getItemKey();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        deliveryDetailDTO.setPurchaseDetailId(detail.getId());
        if ("20".equals(deliveryDetailDTO.getOperationType()) || "30".equals(deliveryDetailDTO.getOperationType())) {
            TMpDailyDeliveryDetail tMpDailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDeliveryDetail>()
                    .eq(TMpDailyDeliveryDetail::getDeliveryNumber, deliveryDetailDTO.getDeliveryNumber())
                    .eq(TMpDailyDeliveryDetail::getDeleted, false)
                    .orderByDesc(TMpDailyDeliveryDetail::getId).last("limit 1"));
            if (null == tMpDailyDeliveryDetail) {
                String errMeg = "送货单号(唯一)不存在:" + deliveryDetailDTO.getItemKey();
                srmLog.setPostStatus("失败");
                srmLog.setErrMsg(errMeg);
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            deliveryDetailDTO.setDailyDeliveryDetailId(tMpDailyDeliveryDetail.getId());
        }
    }

    /**
     * 接收供应商处理结果
     * @param vendorProcessingResultDTO
     * @return
     */
    public String receiveVendorProcessingResult(VendorProcessingResultDTO vendorProcessingResultDTO) {
        String jsonString = JSONObject.toJSONString(vendorProcessingResultDTO);
        log.info("接收供应商处理结果, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "异常处理");
        srmLogService.add(srmLog);
        if ("1".equals(vendorProcessingResultDTO.getType())) {
            //供应商场内管理
            TMpVendorManage tMpVendorManage = vendorManageMapper.selectOne(new LambdaUpdateWrapper<TMpVendorManage>()
                    .eq(TMpVendorManage::getOrderNo, vendorProcessingResultDTO.getOrderNo()));
            if (null == tMpVendorManage) {
                String errMeg = "异常单号不存在:" + vendorProcessingResultDTO.getOrderNo();
                srmLog.setPostStatus("失败");
                srmLog.setErrMsg(errMeg);
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            TMpVendorManage update = new TMpVendorManage();
            update.setId(tMpVendorManage.getId());
            update.setState("2");
            update.setDisposePicUrls(vendorProcessingResultDTO.getProcessingFile());
            update.setDisposeUser(vendorProcessingResultDTO.getDisposeUser());
            update.setDisposeDescription(vendorProcessingResultDTO.getProcessingResult());
            vendorManageMapper.updateById(update);
        } else if ("2".equals(vendorProcessingResultDTO.getType())) {
            //检验不合格退货
            TMpVehicleBlacklist tMpVehicleBlacklist = vehicleBlacklistMapper.selectOne(new LambdaUpdateWrapper<TMpVehicleBlacklist>()
                    .eq(TMpVehicleBlacklist::getReceiptNumber, vendorProcessingResultDTO.getOrderNo())
                    .eq(TMpVehicleBlacklist::getDeleted, false)
                    .orderByDesc(TMpVehicleBlacklist::getId).last("limit 1"));
            if (null == tMpVehicleBlacklist) {
                String errMeg = "送货单号黑名单数据不存在:" + vendorProcessingResultDTO.getOrderNo();
                srmLog.setPostStatus("失败");
                srmLog.setErrMsg(errMeg);
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            TMpVehicleBlacklist update = new TMpVehicleBlacklist();
            update.setId(tMpVehicleBlacklist.getId());
            update.setSrmState("1");
            update.setState(vendorProcessingResultDTO.getRestrictionLifted());
            if (StringUtils.isNotBlank(vendorProcessingResultDTO.getRestrictionLifted()) && "1".equals(vendorProcessingResultDTO.getRestrictionLifted())) {
                update.setRelieveTime(new Date());
            }
            update.setProcessDetail(vendorProcessingResultDTO.getProcessingResult());
            update.setProcessingFile(vendorProcessingResultDTO.getProcessingFile());
            vehicleBlacklistMapper.updateById(update);
        }
        return "成功";
    }

    /**
     * 接收退杂车辆
     * @param impurityVehicleDTO
     * @return
     */
    public String receiveImpurityVehicle(ImpurityVehicleDTO impurityVehicleDTO) {
        String jsonString = JSONObject.toJSONString(impurityVehicleDTO);
        log.info("接收退杂车辆, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "退杂申请");
        ImpurityVehicleCheck(impurityVehicleDTO, srmLog);
        //创建杂质车数据
        TMpDailyDeliveryDetail detail = new TMpDailyDeliveryDetail();
        detail.setDeliveryNumber(generateCodeUtil.generateInspectionCode("SH", 3));
        detail.setDeliveryItemId(impurityVehicleDTO.getDeliveryItemId());
        detail.setState("0");
        detail.setLicensePlateNumber(impurityVehicleDTO.getLicensePlateNumber());
        detail.setMaterialCode("0000");
        //获取其他车的物料来设置物料名字
        TMpDailyDeliveryDetail tMpDailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDeliveryDetail>()
                .eq(TMpDailyDeliveryDetail::getDeliveryItemId, impurityVehicleDTO.getDeliveryItemId())
                .eq(TMpDailyDeliveryDetail::getDeleted, false)
                .orderByAsc(TMpDailyDeliveryDetail::getId).last("limit 1"));
        if (tMpDailyDeliveryDetail != null) {
            detail.setMaterialName(tMpDailyDeliveryDetail.getMaterialName() + "杂质");
        }
        dailyDeliveryDetailMapper.insert(detail);
        srmLogService.add(srmLog);
        return "成功";
    }

    /**
     * 接收退杂车辆数据校验
     * @param impurityVehicleDTO
     * @param srmLog
     */
    private void ImpurityVehicleCheck(ImpurityVehicleDTO impurityVehicleDTO, TPoSrmLog srmLog) {
        if (StringUtils.isBlank(impurityVehicleDTO.getDeliveryItem()) || StringUtils.isBlank(impurityVehicleDTO.getLicensePlateNumber())) {
            String errMeg = "日要货计划号(SRM)|车牌号 不能为空";
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        //校验 日要货计划号
        TMpDailyDelivery tMpDailyDelivery = dailyDeliveryMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDelivery>()
                .eq(TMpDailyDelivery::getPlanNumber, impurityVehicleDTO.getDeliveryItem())
                .eq(TMpDailyDelivery::getDeleted, false));
        if (null == tMpDailyDelivery) {
            String errMeg = "日送货计划号数据不存在:" + impurityVehicleDTO.getDeliveryItem();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        impurityVehicleDTO.setDeliveryId(tMpDailyDelivery.getId());
        TMpDeliveryItem tMpDeliveryItem = deliveryItemMapper.selectOne(new LambdaUpdateWrapper<TMpDeliveryItem>()
                .eq(TMpDeliveryItem::getDeliveryId, impurityVehicleDTO.getDeliveryId())
                .eq(TMpDeliveryItem::getDeleted, false)
                .orderByDesc(TMpDeliveryItem::getId).last("limit 1"));
        if (tMpDeliveryItem == null) {
            String errMeg = "日送货计划数据异常:" + impurityVehicleDTO.getDeliveryItem();
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        impurityVehicleDTO.setDeliveryItemId(tMpDeliveryItem.getId());
    }

    /**
     * 月需求修改接收审批结果
     * @param demandAuditResultDTO
     * @return
     */
    public String receiveDemandAuditResult(DemandAuditResultDTO demandAuditResultDTO) {
        String jsonString = JSONObject.toJSONString(demandAuditResultDTO);
        log.info("月需求修改接收审批结果, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "接收", "月要货需求");
        if (StringUtils.isBlank(demandAuditResultDTO.getAuditResult()) || StringUtils.isBlank(demandAuditResultDTO.getMouthDemandNo()) || demandAuditResultDTO.getAuditNumber() == null) {
            String errMeg = "月需求单编号(MES)|审核结果|审核重量 不能为空";
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(errMeg);
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, errMeg);
        }
        if ("1".equals(demandAuditResultDTO.getAuditResult())) {
            //审核通过
            TMpMonthDemandOrder tMpMonthDemandOrder = monthDemandOrderMapper.selectOne(new LambdaUpdateWrapper<TMpMonthDemandOrder>()
                    .eq(TMpMonthDemandOrder::getOrderNo, demandAuditResultDTO.getMouthDemandNo())
                    .eq(TMpMonthDemandOrder::getDeleted, false));
            if (tMpMonthDemandOrder == null) {
                String errMeg = "月需求单编号(MES)数据不存在:" + demandAuditResultDTO.getMouthDemandNo();
                srmLog.setPostStatus("失败");
                srmLog.setErrMsg(errMeg);
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            if (tMpMonthDemandOrder.getAuditNumber() == null || tMpMonthDemandOrder.getAuditNumber().compareTo(demandAuditResultDTO.getAuditNumber()) != 0) {
                String errMeg = "审核数量不一致，数据已废弃";
                srmLog.setPostStatus("失败");
                srmLog.setErrMsg(errMeg);
                srmLogService.add(srmLog);
                throw new BaseKnownException(10000, errMeg);
            }
            //更改需求数据
            TMpMonthDemandOrder update = new TMpMonthDemandOrder();
            update.setId(tMpMonthDemandOrder.getId());
            update.setDemandQuantity(tMpMonthDemandOrder.getAuditNumber());
            monthDemandOrderMapper.updateById(update);
        }
        srmLogService.add(srmLog);
        return "成功";
    }

    /**
     * 发送退杂申请
     * @param srmImpurityAuditDto
     */
    public void sendImpurityAudit(SrmImpurityAuditDto srmImpurityAuditDto) {
        String jsonString = JSONObject.toJSONString(srmImpurityAuditDto);
        log.info("发送退杂申请, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "退杂申请");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmImpurityAuditDto, srmConfig.getSendOrder(), "向srm发送退杂申请");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送退杂数据
     * @param srmImpurityDataDto
     */
    public void sendImpurityData(SrmImpurityDataDto srmImpurityDataDto) {
        String jsonString = JSONObject.toJSONString(srmImpurityDataDto);
        log.info("发送退杂数据, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "退杂申请");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmImpurityDataDto, srmConfig.getSendOrder(), "向srm发送退杂数据");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送异常信息
     * @param srmExceptionTicketDto
     */
    public void sendExceptionTicket(SrmExceptionTicketDto srmExceptionTicketDto) {
        String jsonString = JSONObject.toJSONString(srmExceptionTicketDto);
        log.info("发送异常信息, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "异常处理");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmExceptionTicketDto, srmConfig.getSendOrder(), "向srm发送异常处理");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送年度物料需求
     * @param srmProcurementPlanDto
     */
    public void sendProcurementPlan(SrmProcurementPlanDto srmProcurementPlanDto) {
        String jsonString = JSONObject.toJSONString(srmProcurementPlanDto);
        log.info("发送年度物料需求, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "年度采购需求");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmProcurementPlanDto, srmConfig.getSendOrder(), "向srm发送年度物料需求");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送月物料需求
     * @param srmMonthDemandDto
     */
    public void sendMonthDemand(SrmMonthDemandDto srmMonthDemandDto) {
        String jsonString = JSONObject.toJSONString(srmMonthDemandDto);
        log.info("发送月物料需求, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "月要货需求");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmMonthDemandDto, srmConfig.getSendOrder(), "向srm发送月物料需求");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送日要货需求
     * @param srmDayDemandDto
     */
    public void sendDayDemand(SrmDayDemandDto srmDayDemandDto) {
        String jsonString = JSONObject.toJSONString(srmDayDemandDto);
        log.info("发送日要货需求, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "日要货需求");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmDayDemandDto, srmConfig.getSendOrder(), "向srm发送日要货需求");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 发送车辆数据
     * @param srmVehicleDataDto
     */
    public void sendVehicleData(SrmVehicleDataDto srmVehicleDataDto) {
        String jsonString = JSONObject.toJSONString(srmVehicleDataDto);
        log.info("发送车辆数据, 参数:" + jsonString);
        TPoSrmLog srmLog = initSrmLog(jsonString, "发送", "车辆数据同步");
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmVehicleDataDto, srmConfig.getSendOrder(), "向srm发送车辆数据");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            srmLog.setPostStatus("失败");
            srmLog.setErrMsg(responseDto.getMessage());
            srmLogService.add(srmLog);
            throw new BaseKnownException(10000, "srm接口调用失败，原因：" + responseDto.getMessage());
        }
        srmLogService.add(srmLog);
    }

    /**
     * 查询SRM自有生产计划
     * @param srmProductPlanDto
     * @return
     */
    public SrmProductPlanVo getProductPlan(SrmProductPlanDto srmProductPlanDto) {
        log.info("查询SRM自由生产计划, 参数:" + JSONObject.toJSONString(srmProductPlanDto));
        SrmBaseResponseDto responseDto = requestSrm.dockingSrm(srmProductPlanDto, srmConfig.getSendOrder(), "向srm发送车辆数据");
        //对返回结果进行判断
        if(!"200".equals(responseDto.getCode())){
            throw new BaseKnownException(10000, "查询SRM自有生产计划，原因：" + responseDto.getMessage());
        }
        return JSONObject.parseObject(JSONObject.toJSONString(responseDto.getData()), SrmProductPlanVo.class);
    }
}
