package com.hvisions.purchase.sap.service;

import com.hvisions.purchase.dto.purchase.daily.delivery.detail.SyncInspectionAndAvsDTO;
import com.hvisions.purchase.dto.sap.AvsBaseResponseDto;
import com.hvisions.purchase.sap.dto.SapBaseResponseDto;
import com.hvisions.purchase.sap.dto.cost.loss.CostLossItemDto;
import com.hvisions.purchase.sap.dto.inventory.InventoryAllocationDto;
import com.hvisions.purchase.sap.dto.purchase.OrderPostingItemDto;
import com.hvisions.purchase.sap.dto.purchase.OrderWriteOffHeaderDto;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: Jcao
 * @time: 2022/5/7 10:11
 */
public interface SapService {

    /*
     * @Description: MES采购订单同步SAP接口
     *
     * <AUTHOR>
     * @param id: 采购订单id
     */
    SapBaseResponseDto syncPurchaseOrder(Integer id, Integer type);

    /*
     * @description: 收货sap过账
     * <AUTHOR>
     * @date 2022/5/7 13:41
     * @param headerDto
     * @param itemDto
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    SapBaseResponseDto posting(List<OrderPostingItemDto> list, Date postDate, Date orderDate, String initialWarehouseName, String initialWarehouseCode);

    /**
     * @param list
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     * @Description MES成本中心耗损同步SAP接口
     * <AUTHOR>
     * @Date 2022-8-11 14:22
     **/
    SapBaseResponseDto syncCostLoss(List<CostLossItemDto> list, Date lossDate);


    /*
     * @description: SAP冲销
     * <AUTHOR>
     * @date 2022/5/7 14:32
     * @param headerDto
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    SapBaseResponseDto writeOff(OrderWriteOffHeaderDto headerDto);

    /***
     * @Description 正式环境冲销
     *
     * <AUTHOR>
     * @Date 2022-11-23 16:53
     * @param matDoc
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     **/
    SapBaseResponseDto formalWriteOff(String matDoc);


    /*
     * @Description: sap采购单删除
     *
     * <AUTHOR>
     * @param id：采购单id
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    SapBaseResponseDto deleteSapPurchaseOrder(Integer id);

    /*
     * @Description: MES库存调拨同步SAP接口
     *
     * <AUTHOR>
     * @param inventoryDto: 库存信息
     */
    SapBaseResponseDto inventoryAllocation(InventoryAllocationDto inventoryDto);

    /*
     * @Description: MES库存查询同步SAP接口
     *
     * <AUTHOR>
     * @param materialCodes: 物料编码集合
     */
    SapBaseResponseDto inventoryQuery(List<String> materialCodes);

    /**
     * @Description sap库存查询（根据物料编码和库存地点）
     *
     * <AUTHOR>
     * @Date 2024-5-27 19:21
     * @param materialCodes 物料编码集合
     * @param storageNo 库存地点
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     **/
    SapBaseResponseDto getInventoryByStorageNo(List<String> materialCodes, String storageNo);

    /*
     * @Description: MES库存差异调账同步SAP接口
     *
     * <AUTHOR>
     * @param checkDto: 盘点库存信息
     */
    SapBaseResponseDto inventoryCheck(Integer id);

    /**
     * 生产过磅管理，下发过磅
     *
     * @param id: 生产过磅管理id
     */
    Boolean sendOut(Integer id);

    /**
     * 采供送货车辆，下发过磅
     *
     * @param item: 采供送货车辆，下发过磅
     */
    AvsBaseResponseDto purchaseSendOut(SyncInspectionAndAvsDTO item);

    /***
     * @Description 高粱稻壳发， 根据发放时间判断是否过账给sap
     *
     * <AUTHOR>
     * @Date 2022-12-15 15:56
     * @param date 发放时间
     * @return boolean true-过账给sap false-不过账给sap
     **/
    boolean judgeSapPostTime(Date date);

    /***
     * @Description 判断是否是年度扎帐, 返回年度扎帐日期
     *
     * <AUTHOR>
     * @Date 2022-12-15 17:22
     * @param
     * @return Date
     **/
    Date isYearPost();

    /*
     * @Description 发送过账失败通知
     *
     * <AUTHOR>
     * @Date 2023-11-14 11:13
     * @param operate 具体过账操作
     * @return void
     **/
    void sendPostFailNotice(String operate, String orderNo, String reason);
}
