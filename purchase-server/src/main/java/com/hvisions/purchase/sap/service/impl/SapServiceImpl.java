package com.hvisions.purchase.sap.service.impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.hvisions.auth.client.MessageClient;
import com.hvisions.auth.client.RoleClient;
import com.hvisions.brewage.client.ConfigurationClient;
import com.hvisions.brewage.dto.mkwine.dto.TPoConfigurationDTO;
import com.hvisions.brewage.dto.mkwine.vo.PageVO;
import com.hvisions.brewage.feign.purchaseproduction.PurchaseProductionClient;
import com.hvisions.brewage.purchaseproduction.entity.TMpdInventory;
import com.hvisions.brewage.purchaseproduction.entity.TMpdProductionWeight;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.QueryDTO;
import com.hvisions.powder.client.SapClient;
import com.hvisions.powder.dto.qudou.SapPostVO;
import com.hvisions.purchase.PurchaseApplication;
import com.hvisions.purchase.advice.UserAuditorAware;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.SyncInspectionAndAvsDTO;
import com.hvisions.purchase.dto.purchase.order.PurchaseOrderInfoDTO;
import com.hvisions.purchase.dto.purchase.order.detail.PurchaseOrderDetailDTO;
import com.hvisions.purchase.dto.sap.AvsBaseResponseDto;
import com.hvisions.purchase.dto.storage.inventory.InventoryDetailDTO;
import com.hvisions.purchase.production.consts.ProductionWeightState;
import com.hvisions.purchase.purchase.consts.PurchaseOrderState;
import com.hvisions.purchase.purchase.dao.DailyDeliveryDetailMapper;
import com.hvisions.purchase.purchase.dao.PurchaseOrderDetailMapper;
import com.hvisions.purchase.purchase.dao.PurchaseOrderMapper;
import com.hvisions.purchase.purchase.entity.TMpPurchaseOrder;
import com.hvisions.purchase.purchase.entity.TMpPurchaseOrderDetail;
import com.hvisions.purchase.purchase.service.InventoryLocationService;
import com.hvisions.purchase.sap.RequestAvs;
import com.hvisions.purchase.sap.RequestSap;
import com.hvisions.purchase.purchase.consts.SapConst;
import com.hvisions.purchase.sap.dto.AvsBaseDataDto;
import com.hvisions.purchase.sap.dto.SapBaseRequestDto;
import com.hvisions.purchase.sap.dto.SapBaseResponseDto;
import com.hvisions.purchase.sap.dto.cost.loss.CostLossHeaderDto;
import com.hvisions.purchase.sap.dto.cost.loss.CostLossItemDto;
import com.hvisions.purchase.sap.dto.inventory.*;
import com.hvisions.purchase.sap.dto.purchase.*;
import com.hvisions.purchase.sap.service.SapService;
import com.hvisions.purchase.utils.DateUtil;
import com.hvisions.purchase.utils.GenerateCodeUtil;
import com.hvisions.purchase.utils.IntervalTaskUtil;
import com.hvisions.purchase.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @Description: SAP接口对接
 * @author: Jcao
 * @time: 2022/5/7 10:11
 */
@Slf4j
@Service
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PurchaseApplication.class)
public class SapServiceImpl implements SapService {


    //    @Resource
//    SapPostMapper sapPostMapper;
//
//    @Resource
//    WorkshopTmpTankCategoryMapper workshopTmpTankCategoryMapper;
//
//    @Resource
//    TmpTankMapper tmpTankMapper;
//
    @Resource
    private ConfigurationClient configurationClient;
//
//    @Resource
//    private WorkshopPitOrderMapper workshopPitOrderMapper;
//
//    @Resource
//    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;
//
//    @Resource
//    private OrderTypeMapper orderTypeMapper;
//
//    @Resource
//    private RowMapper rowMapper;
//
//    @Resource
//    private WorkshopPitOrderSapService workshopPitOrderSapService;
//    @Resource
//    TPoWorkshopPitOrderWorkinghoursItemsService itemsService;
//
//    @Resource
//    TPoWorkshopPitOrderWorkinghoursMapper tPoWorkshopPitOrderWorkinghoursMapper;
//
//    @Resource
//    private TPoWorkshopHandinTaskMapper tPoWorkshopHandinTaskMapper;
//
//    @Resource
//    private TPoWorkshopReceiveWineOrderMapper tPoWorkshopReceiveWineOrderMapper;
//
//    @Resource
//    private WorkshopPitMapper workshopPitMapper;
//
//    @Resource
//    private VinasseSourceMapper vinasseSourceMapper;
//
//    @Resource
//    private WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;

//    @Resource
//    private SendMessageService sendMessageService;

    @Resource
    private RequestSap requestSap;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private SapClient sapClient;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private RequestAvs requestAvs;

    @Resource
    private PurchaseProductionClient purchaseProductionClient;

    @Resource
    MaterialClient materialClient;

    @Resource
    IntervalTaskUtil intervalTaskUtil;

    @Resource
    DailyDeliveryDetailMapper dailyDeliveryDetailMapper;

    @Value("${StandardTime.name.machine}")
    String Machine;
    @Value("${StandardTime.name.power}")
    String Power;
    @Value("${StandardTime.name.other}")
    String Other;
    @Value("${StandardTime.name.auxiliary}")
    String Auxiliary;
    @Value("${material.type.level-wine}")
    String LevelWine;
    @Value("${StandardTime.name.work-hours}")
    String WorkHours;

    /*
     * @Description: MES采购订单同步SAP接口
     *
     * <AUTHOR>
     * @param id: 采购订单id
     * @param type: 0、新增；1、修改；
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto syncPurchaseOrder(Integer id, Integer type) {
        // 获取采购订单信息
        PurchaseOrderInfoDTO info = purchaseOrderMapper.getPurchaseOrderInfo(id);
        // 根据采购单id获取详情信息
        List<PurchaseOrderDetailDTO> details = purchaseOrderMapper.getPurchaseOrderDetail(id);
        // 头部数据
        OrderSyncHeaderDto header = new OrderSyncHeaderDto();
        header.setHeaderKey(type.intValue() == 0 ? info.getOrderNo() : info.getSapOrder());
        header.setDocType("NB");
        header.setVendor(info.getVendorCode());
        header.setCompCode(info.getCompanyCode());
        header.setCreatDate(DateUtil.dateFormat(info.getCreateTime()));
        header.setCreatedBy(StringUtil.isNotEmpty(info.getUserName()) ? info.getUserName() : "管理员");
        header.setPurchOrg(info.getOrganizationCode());
        header.setPurGroup(info.getGroupCode());
        // 子项数据
        List<OrderSyncItemDto> list = new ArrayList<>();
        Integer index = 10;
        for (PurchaseOrderDetailDTO detail : details) {
            String itemKey = (index >= 100 ? "00" : "000") + index;
            OrderSyncItemDto item = new OrderSyncItemDto();
            item.setItemKey(itemKey);
            item.setPoItem(itemKey);
            item.setMaterial(detail.getMaterialCode());
            item.setPlant(detail.getFactoryCode());
            item.setStgeLoc(detail.getLocationCode());
            item.setQuantity(detail.getQuantitySupplied().stripTrailingZeros().toPlainString());
            item.setPoUnit(detail.getUom());
            item.setPrice(detail.getUnitPrice());
            item.setPer(detail.getUnitPriceBase());
            item.setBbprm(detail.getUom());
            item.setDdate(DateUtil.currentDate());
            list.add(item);
            index += 10;

            // 新增修改采购单详情行号
            TMpPurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailMapper.selectById(detail.getId());
            purchaseOrderDetail.setItemKey(itemKey);
            purchaseOrderDetailMapper.updateById(purchaseOrderDetail);
        }
        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(header, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        // 根据传入的类型判断新增还是修改
        String code = type.intValue() == 0 ? SapConst.PURCHASE_ORDER_CREATE_NO : SapConst.PURCHASE_ORDER_UPDATE_NO;
        SapBaseResponseDto response = requestSap.dockingSap(inputs, code, SapConst.SAP_URI);

        //采购单新增修改插入sap记录
        SapPostVO sapPostVO = new SapPostVO();
        sapPostVO.setSapCode(code);
        sapPostVO.setOperatorType("采购单新增修改同步");
        sapPostVO.setOperatingTime(LocalDateTime.now());
        sapPostVO.setCertificateDate(LocalDate.now());
        sapPostVO.setValueJson(JSONObject.toJSONString(input));


        if ("S".equals(response.getEsMessage().getMsgty()) || response.getEsMessage().getMsgtx().contains("已处理，请勿重复传输")) {
            // 记录SAP返回的key
            TMpPurchaseOrder purchase = new TMpPurchaseOrder();
            String sapOrder = response.getEvOutput().getOutput().get(0).getEbeln();
            purchase.setId(id);
            // 新增记录sap单号
            if (type.intValue() == 0) purchase.setSapOrder(sapOrder);
            purchase.setSapResult(PurchaseOrderState.SAP_SUCCESS);
            purchaseOrderMapper.updateById(purchase);
            sapPostVO.setCertificateNumber(sapOrder);
            sapPostVO.setCertificateYear(response.getEvOutput().getOutput().get(0).getMjahr());
            sapPostVO.setState("1");

        } else {
            // 异常：同步失败
            TMpPurchaseOrder purchase = new TMpPurchaseOrder();
            purchase.setId(id);
            purchase.setSapResult(PurchaseOrderState.SAP_FAIL);
            purchaseOrderMapper.updateById(purchase);
            sapPostVO.setState("2");
            throw new BaseKnownException(10000, "SAP采购单同步失败，原因：" + response.getEsMessage().getMsgtx());
        }
        sapClient.insertPurchaseSapPostRecord(sapPostVO);

        return response;
    }

    /*
     * @Description: sap采购单删除
     *
     * <AUTHOR>
     * @param id：
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto deleteSapPurchaseOrder(Integer id) {
        // 获取采购订单信息
        PurchaseOrderInfoDTO info = purchaseOrderMapper.getPurchaseOrderInfo(id);
        // 头部数据
        OrderSyncHeaderDto header = new OrderSyncHeaderDto();
        header.setHeaderKey(info.getSapOrder()); // 只传头部就是删除
        // 子项数据
        List<OrderSyncItemDto> list = new ArrayList<>();
        list.add(new OrderSyncItemDto());
        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(header, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.PURCHASE_ORDER_UPDATE_NO, SapConst.SAP_URI);

        //采购单新增修改插入sao记录
        SapPostVO sapPostVO = new SapPostVO();
        sapPostVO.setSapCode(SapConst.PURCHASE_ORDER_UPDATE_NO);
        sapPostVO.setOperatorType("采购单删除同步");
        sapPostVO.setOperatingTime(LocalDateTime.now());
        sapPostVO.setCertificateDate(LocalDate.now());
        sapPostVO.setValueJson(JSONObject.toJSONString(input));
        sapPostVO.setState("1");
        if (!"S".equals(response.getEsMessage().getMsgty())) {
            if (response.getEsMessage().getMsgtx().contains("已处理，请勿重复传输")) {
                return response;
            }
            sapPostVO.setState("2");
            sapClient.insertPurchaseSapPostRecord(sapPostVO);
            throw new BaseKnownException(10000, "SAP采购单删除失败，原因：" + response.getEsMessage().getMsgtx());
        }
        sapClient.insertPurchaseSapPostRecord(sapPostVO);
        return response;
    }

    /*
     * @Description: MES库存调拨同步SAP接口
     *
     * <AUTHOR>
     * @param :
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto inventoryAllocation(InventoryAllocationDto inventoryDto) {
        InventoryAllocationHeaderDto headerDto = new InventoryAllocationHeaderDto();
        headerDto.setHeaderKey(inventoryDto.getOrderNo());
        String pstngDate = Optional.ofNullable(inventoryDto)
                .map(InventoryAllocationDto::getCertificateDate)
                .map(it -> DateUtil.formatLocaldate(it, DateUtil.LD_PATTERN))
                .orElse(DateUtil.dateFormat(new Date(), DatePattern.NORM_DATE_PATTERN));
        headerDto.setPstngDate(pstngDate);
        headerDto.setDocDate(pstngDate);
        headerDto.setHeaderTxt("");

        List<InventoryAllocationItemDto> list = new ArrayList<>();
        InventoryAllocationItemDto itemDto = DtoMapper.convert(inventoryDto, InventoryAllocationItemDto.class);
        itemDto.setItemKey("00010");
        itemDto.setPlant("1100");
        itemDto.setMoveType("311");
        itemDto.setEntryUom("kg");
        list.add(itemDto);
        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);


        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());

            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);

            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);

            String now = hour + ":" + minute + ":" + second;
            String format = "HH:mm:ss";
            Date nowTime = new SimpleDateFormat(format).parse(now);
            Date startTime = new SimpleDateFormat(format).parse("00:00:00");
            Date endTime = new SimpleDateFormat(format).parse("09:00:00");
            boolean flag = DateUtil.isEffectiveDate(nowTime, startTime, endTime);
            if (month == 1 && day == 1 && flag) { // 判断是否是1月1日 0点 - 9点之间，如果是设置为1月9点30调用
                int second1 = DateUtil.differSecond(new Date(), new SimpleDateFormat(format).parse("09:30:00"));

                intervalTaskUtil.postDelay(() -> {
                    requestSap.dockingSap(inputs, SapConst.INVENTORY_ALLOCATION_NO, SapConst.SAP_URI);
                }, second1 * 1000);
            } else {
                return requestSap.dockingSap(inputs, SapConst.INVENTORY_ALLOCATION_NO, SapConst.SAP_URI);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;

    }

    /*
     * @description: 收货sap过账
     * <AUTHOR>
     * @date 2022/5/7 13:41
     * @param headerDto
     * @param itemDto
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto posting(List<OrderPostingItemDto> list, Date postDate, Date orderDate, String initialWarehouseName, String initialWarehouseCode) {
        OrderPostingHeaderDto headerDto = new OrderPostingHeaderDto();
        headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_NO));
        headerDto.setPstingDate(cn.hutool.core.date.DateUtil.format(postDate, DatePattern.PURE_DATETIME_PATTERN));
        headerDto.setDocDate(cn.hutool.core.date.DateUtil.format(postDate, DatePattern.PURE_DATETIME_PATTERN));
        headerDto.setHeaderTxt("");

        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        SapBaseResponseDto sapBaseResponseDto = requestSap.dockingSap(inputs, SapConst.PURCHASE_RECEIVING_NO, SapConst.SAP_URI);

        // sap操作记录
        SapPostVO sapPostVO = new SapPostVO();

        QueryDTO queryDTO = new QueryDTO();
        queryDTO.setMaterialCode(list.get(0).getMaterial());
        ResultVO<HvPage<MaterialDTO>> material = materialClient.getMaterialByNameOrCode(queryDTO);
        if (StringUtil.isNotEmpty(material.getData())) {
            HvPage<MaterialDTO> data = material.getData();
            List<MaterialDTO> content = data.getContent();
            sapPostVO.setMaterialName(content.get(0).getMaterialName());
        }
        sapPostVO.setMaterialCode(list.get(0).getMaterial());
        sapPostVO.setUnit(list.get(0).getEntryUom());
        sapPostVO.setWeight(list.get(0).getEntryQut());
        sapPostVO.setMovementTypeId(Integer.parseInt(list.get(0).getMoveType()));
        sapPostVO.setType(0);
        //如果没有传默认黄舣
        sapPostVO.setInitialWarehouseCode(StringUtils.isBlank(initialWarehouseCode)?"1106":initialWarehouseCode);
        sapPostVO.setInitialWarehouseName(StringUtils.isBlank(initialWarehouseName)?"黄舣粮库":initialWarehouseName);
        sapPostVO.setOperatorType(SapConst.SAP_RECEIVE);
        sapPostVO.setSapCode(SapConst.PURCHASE_RECEIVING_NO);
        sapPostVO.setCertificateDate(postDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        sapPostVO.setOperatingTime(LocalDateTime.now());
        sapPostVO.setValueJson(JSONObject.toJSONString(input));
        sapPostVO.setOrderDate(orderDate);
//         写入sap过账记录
        if ("S".equals(sapBaseResponseDto.getEsMessage().getMsgty())) {
            sapPostVO.setState("1");
            sapPostVO.setCertificateNumber(sapBaseResponseDto.getEvOutput().getOutput().get(0).getMblnr());
            sapPostVO.setCertificateYear(sapBaseResponseDto.getEvOutput().getOutput().get(0).getMjahr());
        } else {
            sapPostVO.setState("2");
            sapPostVO.setFailReason(sapBaseResponseDto.getEsMessage().getMsgtx());
        }
        userAuditorAware.getCurrentUserAudit().ifPresent(u -> {
            sapPostVO.setOperatorId(u.getId());
            sapPostVO.setOperator(u.getUserName());
        });
        sapClient.insertPurchaseSapPostRecord(sapPostVO);
        return sapBaseResponseDto;
    }

    /**
     * @param list
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     * @Description MES成本中心耗损同步SAP接口
     * <AUTHOR>
     * @Date 2022-8-11 14:23
     **/
    @Override
    public SapBaseResponseDto syncCostLoss(List<CostLossItemDto> list, Date lossDate) {
        CostLossHeaderDto headerDto = new CostLossHeaderDto();
        headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.COST_LOSS_NO));
        headerDto.setPstingDate(cn.hutool.core.date.DateUtil.format(lossDate, DatePattern.NORM_DATE_PATTERN));
        headerDto.setDocDate(cn.hutool.core.date.DateUtil.format(lossDate, DatePattern.NORM_DATE_PATTERN));
        headerDto.setHeaderTxt("MES");

        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        SapBaseResponseDto sapBaseResponseDto = requestSap.dockingSap(inputs, SapConst.COST_LOSS_NO, SapConst.SAP_URI);

        SapPostVO sapPostVO = new SapPostVO();

        sapPostVO.setMaterialCode(list.get(0).getMaterial());
        sapPostVO.setUnit(list.get(0).getEntryUom());
        sapPostVO.setWeight(list.get(0).getEntryQut());
//        sapPostVO.setMovementTypeId(Integer.parseInt(list.get(0).getMoveType()));
        sapPostVO.setType(1);
        sapPostVO.setOperatorType(SapConst.SAP_LOST);
        sapPostVO.setSapCode(SapConst.COST_LOSS_NO);
        sapPostVO.setCertificateDate(lossDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        sapPostVO.setValueJson(JSONObject.toJSONString(input));


        // 写入sap过账记录
        if (sapBaseResponseDto.getEsMessage().getMsgty().equals("S")) {
            sapPostVO.setState("1");
            sapPostVO.setCertificateNumber(sapBaseResponseDto.getEvOutput().getOutput().get(0).getMblnr());
            sapPostVO.setCertificateYear(sapBaseResponseDto.getEvOutput().getOutput().get(0).getMjahr());
        } else {
            sapPostVO.setState("2");
            sapPostVO.setFailReason(sapBaseResponseDto.getEsMessage().getMsgtx());
        }
        sapClient.insertPurchaseSapPostRecord(sapPostVO);

        return sapBaseResponseDto;
    }

    /*
     * @description: SAP冲销
     * <AUTHOR>
     * @date 2022/5/7 14:32
     * @param headerDto
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto writeOff(OrderWriteOffHeaderDto headerDto) {
        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, null);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.PURCHASE_RECEIVING_REVOKE_NO, SapConst.SAP_URI);
        return response;
    }

    @Override
    public SapBaseResponseDto formalWriteOff(String matDoc) {

        // sap esb 正式 url
        String SAP_URI = "http://esbsc.lzljidc.com:9001/WP_LZLJ_SOA/APP_MES_SERVICES/Proxy_Services/LZLJ_399_MES_PubInterface_PS";

        OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
        headerDto.setHeaderKey(generateCodeUtil.generatePlanCode("NJ015"));
        headerDto.setMblnr(matDoc);

        headerDto.setPstingDate(DateUtil.dateFormat(new Date(), "yyyy-MM-dd"));
        headerDto.setMjahr(DateUtil.format(new Date(),"yyyy"));
        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, null);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);

        SapBaseResponseDto response = requestSap.dockingSap(inputs, SapConst.PURCHASE_RECEIVING_REVOKE_NO, SAP_URI);

        return response;

    }

    /*
     * @Description: MES库存查询同步SAP接口
     *
     * <AUTHOR>
     * @param materialCodes: 物料编码集合
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto inventoryQuery(List<String> materialCodes) {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        materialCodes.forEach(item -> {
            InventoryQueryHeaderDto headerDto = new InventoryQueryHeaderDto();
            headerDto.setMaterial(item);
            headerDto.setPlant("1100");
            headerDto.setStgeLoc("1106");

            SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, null);
            inputs.add(input);
        });
        return requestSap.dockingSap(inputs, SapConst.INVENTORY_QUERY_NO, SapConst.SAP_URI);
    }

    /**
     * @Description sap库存查询（根据物料编码和库存地点）
     *
     * <AUTHOR>
     * @Date 2024-5-27 19:23
     * @param materialCodes
     * @param storageNo 1106-高粱稻壳、1396-小麦、1397-曲坯曲块曲粉
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     **/
    @Override
    public SapBaseResponseDto getInventoryByStorageNo(List<String> materialCodes, String storageNo) {
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        materialCodes.forEach(item -> {
            InventoryQueryHeaderDto headerDto = new InventoryQueryHeaderDto();
            headerDto.setMaterial(item);
            headerDto.setPlant("1100");
            headerDto.setStgeLoc(storageNo); // 1106-高粱稻壳、1396-小麦、1397-曲坯曲块曲粉

            SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, null);
            inputs.add(input);
        });
        return requestSap.dockingSap(inputs, SapConst.INVENTORY_QUERY_NO, SapConst.SAP_URI);
    }

    /*
     * @Description: MES库存差异调账同步SAP接口
     *
     * <AUTHOR>
     * @param checkDto: 盘点库存信息
     * @return com.hvisions.purchase.sap.dto.SapBaseResponseDto
     */
    @Override
    public SapBaseResponseDto inventoryCheck(Integer id) {
        // 获取盘点任务数据
        TMpdInventory inventory = purchaseProductionClient.getTMpdInventoryById(id).getData();
        // 获取盘点任务明细数据
        List<com.hvisions.brewage.purchaseproduction.dto.InventoryDetailDTO> detailDTOList = purchaseProductionClient.getInventoryDetail(id).getData();
        List<InventoryDetailDTO> inventoryDetail = DtoMapper.convertList(detailDTOList,InventoryDetailDTO.class);
        // 实盘总数
        BigDecimal total = new BigDecimal(0);
        inventoryDetail.forEach(item -> {
            total.add(item.getInventoryQuantity());
        });

        InventoryCheckHeaderDto headerDto = new InventoryCheckHeaderDto();
        headerDto.setHeaderKey(inventory.getOrderNo());
        headerDto.setDocDate(DateUtil.dateFormat(new Date(), DatePattern.PURE_DATE_PATTERN));
        headerDto.setPstngDate(DateUtil.dateFormat(new Date(), DatePattern.PURE_DATE_PATTERN));
        headerDto.setHeaderTxt("");

        List<InventoryCheckItemDto> list = new ArrayList<>();
        InventoryCheckItemDto itemDto = new InventoryCheckItemDto();
        itemDto.setMaterial(inventoryDetail.get(0).getMaterialCode());
        itemDto.setItemKey("00010");
        itemDto.setPlant("1100");
        itemDto.setStgeLoc("1106");
        itemDto.setMoveType("701");
        itemDto.setEntryQnt(total.toString());
        itemDto.setEntryUom("kg");
        list.add(itemDto);

        SapBaseRequestDto.Request.List.IvInput.Input input = RequestSap.getInput(headerDto, list);
        List<SapBaseRequestDto.Request.List.IvInput.Input> inputs = new ArrayList<>();
        inputs.add(input);
        return requestSap.dockingSap(inputs, SapConst.INVENTORY_MOVEMENT_NO, SapConst.SAP_URI);
    }

    /**
     * @param id: 生产过磅管理id
     * @return com.hvisions.purchase.dto.sap.AvsBaseResponseDto
     * @Description: 生产过磅管理，下发过磅
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean sendOut(Integer id) {
        // 根据id获取，获取过磅任务信息
        TMpdProductionWeight item = purchaseProductionClient.getTMpdProductionWeightById(id).getData();
        AvsBaseDataDto avsBaseDataDto = new AvsBaseDataDto();
        avsBaseDataDto.setDeliveryNumber(item.getWeightOrder());
        avsBaseDataDto.setVehicleNo(item.getLicensePlateNumber());
        avsBaseDataDto.setCargoName(item.getMaterialName());
        avsBaseDataDto.setBusinessType(1);
        avsBaseDataDto.setType(1);
        AvsBaseResponseDto avsBaseResponseDto = requestAvs.dockingAvs(avsBaseDataDto, SapConst.SAP_URI);
        // 下发成功
        if ("200".equals(avsBaseResponseDto.getCode())) {
            // 当过磅任务状态为新增时，修改过磅状态为已下发
            if (ProductionWeightState.NEWLY_INCREASED.equals(item.getState())) {
                item.setState(ProductionWeightState.HAS_BEEN_ISSUED);
                purchaseProductionClient.updateTMpdProductionWeightById(item).getData();
                return true;
            }
        } else {
            throw new BaseKnownException(10000, "下发失败，过磅单号：" + item.getWeightOrder()
                    + "，原因：" + avsBaseResponseDto.getMessage());
        }
        return true;
    }

    /**
     * @param item
     * @return
     * @Description: 采供送货车辆，下发采购地磅
     */
    @Override
    public AvsBaseResponseDto purchaseSendOut(SyncInspectionAndAvsDTO item) {
        AvsBaseDataDto avsBaseDataDto = new AvsBaseDataDto();
        avsBaseDataDto.setDeliveryNumber(item.getDeliveryNumber());
        avsBaseDataDto.setVehicleNo(item.getLicensePlateNumber());
        avsBaseDataDto.setCargoName(item.getMaterialName());
        avsBaseDataDto.setBusinessType(1);
        avsBaseDataDto.setType(Integer.parseInt(item.getState()));
        avsBaseDataDto.setRemarks(item.getRemarks()); // todo 下发地磅时需要检验单
        avsBaseDataDto.setSrcName(item.getVendorName());
        avsBaseDataDto.setDstName("泸州老窖酿酒有限责任公司");
        AvsBaseResponseDto avsBaseResponseDto = requestAvs.dockingAvs(avsBaseDataDto, SapConst.SAP_URI);
        log.info(JSONObject.toJSONString(avsBaseResponseDto));
        // 下发成功
        if (!"200".equals(avsBaseResponseDto.getCode())) {
            if (!item.getDeliveryNumber().equals("LZLJDZCL")) {
                dailyDeliveryDetailMapper.updateSyncStatus(item.getDeliveryNumber(), "2");//同步失败
            }
            throw new BaseKnownException(10000, "下发失败，过磅单号：" + item.getDeliveryNumber()
                    + "，原因：" + avsBaseResponseDto.getMessage());
        } else {
            dailyDeliveryDetailMapper.updateSyncStatus(item.getDeliveryNumber(), "1");//同步成功
        }
        return avsBaseResponseDto;
    }

    @Override
    public boolean judgeSapPostTime(Date postingDate) {

        /**
         * 发放sap同步逻辑
         * 如果是 12月
         *      根据接口获取年度扎帐时间，如果是在该时间后的，不过账sap，一月1号一起过账给sap
         * 不是12月
         *      当月24号到月底，不过账给sap，下月1号过账给sap
         *
         */
        TPoConfigurationDTO tPoConfiguration = new TPoConfigurationDTO();
        tPoConfiguration.setCode(String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));
        Date yearTime = null; // 年度扎帐日期

        ResultVO<PageVO<TPoConfigurationDTO>> resultVO = configurationClient.getConfPage(tPoConfiguration);
        if (resultVO.getCode() != 200) {
            throw new BaseKnownException(10000, "获取年度扎帐日期失败");
        }

        PageVO<TPoConfigurationDTO> vo = resultVO.getData();

        if (StringUtil.isNotEmpty(vo) && vo.getData().size() > 0) {
            yearTime = vo.getData().get(0).getDate();
        }

        // 2、获取当前过账日期月份
        Calendar calendar = Calendar.getInstance();

        int nowMonth = calendar.get(Calendar.MONTH) + 1; // 操作月
        calendar.setTime(postingDate);
        int postMonth = calendar.get(Calendar.MONTH) + 1; // 当前过账时间月份
        int postDay = calendar.get(Calendar.DAY_OF_MONTH); // 当前过账时间日期

        // 3、如果是12月，且年度扎帐日期不为空，过账日期在年度扎帐日期后，不过账给sap
        if (postMonth == 12 && StringUtil.isNotEmpty(yearTime) && postingDate.after(yearTime)) {
            log.info("如果是12月，且年度扎帐日期不为空，过账日期在年度扎帐日期后，不过账");
            return false;
        } else {
            // 过账月为当前月，过账日期 >= 24,不过张给sap
            if (nowMonth == postMonth && postDay >= 24) {
                log.info("过账月为当前月，过账日期 >= 24,不过账");
                return false;
            }
            // 过账月份大于 当前月并且月份加+1，不过账给sap
            if (postMonth - nowMonth == 1) {
                log.info("过账月份大于 当前月并且月份加+1，不过账给sap");
                return false;
            }
            // 过账月份大于 当前月，报错
            if (nowMonth != postMonth) {
                log.info("过账日期大于当前月，抛出异常");
                throw new BaseKnownException(10000, "过账日期错误，请重新选择");
            }
        }
        return true;
    }

    @Override
    public Date isYearPost() {
        // 判定是否是年度过账
        TPoConfigurationDTO tPoConfiguration = new TPoConfigurationDTO();
        tPoConfiguration.setCode(String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));
        Date yearTime = null; // 年度扎帐日期
        ResultVO<PageVO<TPoConfigurationDTO>> resultVO = configurationClient.getConfPage(tPoConfiguration);
        if (resultVO.getCode() != 200) {
            throw new BaseKnownException(10000, "获取年度扎帐日期失败");
        }
        PageVO<TPoConfigurationDTO> vo = resultVO.getData();

        if (StringUtil.isNotEmpty(vo) && vo.getData().size() > 0) {
            yearTime = vo.getData().get(0).getDate();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int month = calendar.get(Calendar.MONTH) + 1;

        if (month == 12 && StringUtil.isNotEmpty(yearTime)) {
            return yearTime;
        }
        return null;
    }

    /**
     * sap 使用redis锁逻辑处理
     */
    public void toSapLock() {
        String isBatchSap = generateCodeUtil.getIsBatchSap();

        if ("1".equals(isBatchSap)) {
            throw new BaseKnownException(10000, "有其他用户正在同步SAP");
        } else {
            generateCodeUtil.setIsBatchSap("1");
        }
    }

    /**
     * 解锁sap
     */
    public void unLockSap() {
        generateCodeUtil.setIsBatchSap("0");
    }

    /***
     * @Description 发送过账失败通知
     *
     * <AUTHOR>
     * @Date 2023-11-14 11:14
     * @param operate sap过账操作
     * @param orderNo 过账单据
     * @param reason 失败原因
     * @return void
     **/
    @Override
    public void sendPostFailNotice(String operate, String orderNo, String reason) {
        // 获取当前登录人
        Integer userId = userAuditorAware.getCurrentAuditor()
                .orElseThrow(() -> new BaseKnownException(414001, "请登录之后再操作"));
        String message = orderNo + "SAP过账失败，请重新过账，过账失败原因是：" + reason;
        String title = operate + "过账失败";
//        sendMessageService.sendMessageByUserId(title, message, userId);

    }
}
