package com.hvisions.purchase.purchase.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.*;
import com.hvisions.purchase.dto.purchase.demand.order.ImpurityAuditDTO;
import com.hvisions.purchase.dto.purchase.receiving.*;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordDTO;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordListDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:收货管理
 * @date 2022/4/6 10:18
 */
public interface ReceivingService {

    /*
     * @description: 分页查询送货车辆列表
     * <AUTHOR>
     * @date 2022/4/11 13:37
     * @param deliveryVehiclePageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.receiving.DeliveryVehiclePageDTO>
     */
    Page<DeliveryVehiclePageDTO> getDeliveryVehiclePageList(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO);

    Page<DeliveryVehiclePageDTO> getDzVehiclePageList(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO);

    Page<DzVehicleReportDTO> getDzVehicleReport(DeliveryVehiclePageQueryDTO queryDTO);

    ResultVO<ExcelExportDto> exportDzVehicleReport(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException;

    /***
     * @Description 获取丢糟总重
     *
     * <AUTHOR>
     * @Date 2022-10-17 15:38
     * @param deliveryVehiclePageQueryDTO
     * @return java.lang.String
     **/
    String getDzNetWeightSum(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO);

    /**
     * @Description 修改丢糟收货单位
     *
     * <AUTHOR>
     * @Date 2024-3-15 15:33
     * @param dzReceiptPlaceUpdateDTO
     * @return java.lang.Integer
     **/
    Integer updateDzReceiptPlace(DzReceiptPlaceUpdateDTO dzReceiptPlaceUpdateDTO);


    ResultVO<ExcelExportDto> exportVehicle(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException;


    ResultVO<ExcelExportDto> exportDzVehicle(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException;


    /*
     * @description: 送货异常关联
     * <AUTHOR>
     * @date 2022/4/11 14:34
     * @param receivingExceptionDTO
     * @return java.lang.Integer
     */
    Integer receiveException(ReceivingExceptionDTO receivingExceptionDTO);

    /*
     * @description: 送货单过账
     * <AUTHOR>
     * @date 2022/4/12 14:27
     * @param postingDTO
     * @return java.lang.Integer
     */
    Integer posting(PostingDTO postingDTO);

    /*
     * @description: 收货冲销
     * <AUTHOR>
     * @date 2022/4/12 14:33
     * @param ids: 送货车辆集合
     * @return java.lang.Integer
     */
    Integer writeOff(List<String> matDocs);

    /*
     * @description: 同步过磅数据，接收地磅数据
     * <AUTHOR>
     * @date 2022/5/9 9:52
     * @param weighDataDTO
     * @return java.lang.Integer
     */
    Integer syncWeighData(WeighDataDTO weighDataDTO);

    /**
     * @Description 高粱稻壳结束卸货
     *
     * <AUTHOR>
     * @Date 2024-5-11 10:32
     * @param id
     * @return java.lang.Integer
     **/
    Integer unloading(Integer id);

    /***
     * @Description 高粱稻壳小麦开始卸货
     *
     * <AUTHOR>
     * @Date 2022-11-22 17:18
     * @param unloadRecordDTO
     * @return java.lang.Integer
     **/
    Integer startUnloading(UnloadRecordDTO unloadRecordDTO);

    /**
     * @Description 结束卸货，车辆出门时调用
     *
     * <AUTHOR>
     * @Date 2024-6-18 15:09
     * @param vehicleId
     * @return java.lang.Integer
     **/
    Integer endUnloading(Integer vehicleId);

    /***
     * @Description 获取最新卸货记录
     *
     * <AUTHOR>
     * @Date 2022-11-29 17:49
     * @param
     * @return java.util.List<com.hvisions.purchase.dto.purchase.unload.UnloadRecordListDTO>
     **/
    List<UnloadRecordListDTO> getUnloadRecordList();

    /***
     * @Description 出门车修改
     *
     * <AUTHOR>
     * @Date 2022-9-1 10:28
     * @param outVehicleUpdateDTO
     * @return java.lang.Integer
     **/
    Integer updateOutVehicle(OutVehicleUpdateDTO outVehicleUpdateDTO);

    /*
     * @Description:
     *
     * <AUTHOR>
     * @param id: 送货车辆id
     * @return java.lang.Integer
     */
    Integer updatePrintState(Integer id);

    List<VehicleBatchDTO> getVehicleByBatch(String batch);

    /*
     * @Description: 根据送货单号获取送货车辆信息
     * <AUTHOR>
     */
    DailyDeliveryDetailInfoDTO getDeliveryCarInfo(String deliveryNumber);

    /**
     * @Description 根据送货单号修改车辆状态
     *
     * <AUTHOR>
     * @Date 2023-12-14 16:18
     * @param vehicleStateUpdateDTO
     * @return java.lang.Integer
     **/
    Integer updateVehicleState(VehicleStateUpdateDTO vehicleStateUpdateDTO);

    /**
     * @Description 修改车辆质检状态
     *
     * <AUTHOR>
     * @Date 2023-12-15 10:14
     * @param vehicleInspectStateUpdateDTO
     * @return java.lang.Integer
     **/
    Integer updateVehicleInspectState(VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO);

    /**
     * @Description 计算扣重
     *
     * <AUTHOR>
     * @Date 2024-6-18 17:04
     * @param deliveryNumber 送货单号
     * @param materialCode 物料编码
     * @param netWeight 离场净重
     * @return java.math.BigDecimal
     **/
    BigDecimal calculateVehicleBuckleWeight(String deliveryNumber, String materialCode, BigDecimal netWeight);

    /**
     * 查询丢糟周报表
     * @param dto
     * @return
     */
    DzWeeklyReportDTO getDzWeeklyReport(DzWeeklyReportQueryDTO dto);

    /**
     * 查询丢糟周报表
     * @param dto
     * @return
     */
    DzQuarterReportDTO getDzQuarterReport(DzQuarterReportQueryDTO dto);

    /**
     * 查询丢糟日表
     * @param dto
     * @return
     */
    List<DzSunReportDTO> getDzSunReport(DzSunReportQueryDTO dto);

    /**
     * 退杂申请
     * @param impurityAuditDTO
     * @return
     */
    Boolean impurityAudit(ImpurityAuditDTO impurityAuditDTO);

    /**
     * 更改车辆信息-数据补录
     * @param vehicleDataUpdateDTO
     * @return
     */
    Boolean updateVehicleData(VehicleDataUpdateDTO vehicleDataUpdateDTO);
}
