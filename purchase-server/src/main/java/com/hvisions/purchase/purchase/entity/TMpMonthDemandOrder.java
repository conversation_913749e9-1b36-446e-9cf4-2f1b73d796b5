package com.hvisions.purchase.purchase.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 月要货需求
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_month_demand_order")
@ApiModel(value="TMpMonthDemandOrder对象", description="月要货需求")
public class TMpMonthDemandOrder extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "要货需求单号")
    private String orderNo;

    @ApiModelProperty(value = "类型：1-月要货需求、2-季度要货需求")
    private String type;

    @ApiModelProperty(value = "物料代码")
    private String materialCode;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "要货数量kg")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal harvestQuantity;

    @ApiModelProperty(value = "要货需求状态;0-待下发、0-待执行、1-执行中、2-已完成")
    private String state;

    @ApiModelProperty(value = "审核数量 -- 如果修改重量超过20%需要发起审核")
    private BigDecimal auditNumber;

    @ApiModelProperty(value = "入库地点id")
    private Integer locationId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改备注")
    private String updateRemark;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "供货入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date warehousingTime;

//    @ApiModelProperty(value = "开始时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTime;
//
//    @ApiModelProperty(value = "结束时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date endTime;

    @ApiModelProperty(value = "要货周期")
    private String yearMonthStr;

    @ApiModelProperty(value = "基地 HY：黄舣基地，LH：罗汉基地、CQ：城区、ZQ：制曲中心")
    private String baseName;
}
