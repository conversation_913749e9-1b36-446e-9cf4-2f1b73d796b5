package com.hvisions.purchase.purchase.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.*;
import com.hvisions.purchase.dto.purchase.demand.order.ImpurityAuditDTO;
import com.hvisions.purchase.dto.purchase.receiving.*;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordDTO;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordListDTO;
import com.hvisions.purchase.purchase.service.ReceivingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:收货管理
 * @date 2022/4/11 10:18
 */
@RestController
@RequestMapping(value = "/receiving")
@Api(tags = "收货管理")
public class ReceivingController {
    @Resource
    private ReceivingService receivingService;

    /*
     * @description: 分页查询送货车辆列表
     * <AUTHOR>
     * @date 2022/4/11 13:37
     * @param deliveryVehiclePageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.receiving.DeliveryVehiclePageDTO>
     */
    @ApiOperation(value = "分页查询送货车辆列表")
    @RequestMapping(value = "/vehicle/list/page/get", method = RequestMethod.POST)
    public Page<DeliveryVehiclePageDTO> getDeliveryVehiclePageList(@RequestBody DeliveryVehiclePageQueryDTO deliveryPageQueryDTO) {
        return receivingService.getDeliveryVehiclePageList(deliveryPageQueryDTO);
    }

    @ApiOperation(value = "分页查询丢糟车辆列表")
    @RequestMapping(value = "/dz/list/page/get", method = RequestMethod.POST)
    public Page<DeliveryVehiclePageDTO> getDzVehiclePageList(@RequestBody DeliveryVehiclePageQueryDTO deliveryPageQueryDTO) {
        return receivingService.getDzVehiclePageList(deliveryPageQueryDTO);
    }

    @ApiOperation(value = "获取丢糟车辆汇总报表")
    @RequestMapping(value = "/getDzVehicleReport", method = RequestMethod.POST)
    public Page<DzVehicleReportDTO> getDzVehicleReport(@RequestBody DeliveryVehiclePageQueryDTO deliveryPageQueryDTO) {
        return receivingService.getDzVehicleReport(deliveryPageQueryDTO);
    }

    @ApiResultIgnore
    @ApiOperation(value = "导出丢糟车辆汇总报表")
    @RequestMapping(value = "/exportDzVehicleReport", method = RequestMethod.POST)
    public ResultVO<ExcelExportDto> exportDzVehicleReport(@RequestBody DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        return receivingService.exportDzVehicleReport(queryDTO);
    }

    @ApiOperation(value = "获取丢糟总重")
    @RequestMapping(value = "/dz/netWeight/sum/get", method = RequestMethod.POST)
    public String getDzNetWeightSum(@RequestBody DeliveryVehiclePageQueryDTO deliveryPageQueryDTO) {
        return receivingService.getDzNetWeightSum(deliveryPageQueryDTO);
    }

    @ApiOperation(value = "修改丢糟收货单位")
    @RequestMapping(value = "/dz/receiptPlace/update", method = RequestMethod.POST)
    public Integer updateDzReceiptPlace(@RequestBody DzReceiptPlaceUpdateDTO dzReceiptPlaceUpdateDTO) {
        return receivingService.updateDzReceiptPlace(dzReceiptPlaceUpdateDTO);
    }

    @ApiResultIgnore
    @ApiOperation(value = "送货车辆导出")
    @RequestMapping(value = "/vehicle/export", method = RequestMethod.POST)
    public ResultVO<ExcelExportDto> exportVehicle(@RequestBody DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        return receivingService.exportVehicle(queryDTO);
    }

    @ApiResultIgnore
    @ApiOperation(value = "丢糟车辆导出")
    @RequestMapping(value = "/vehicle/dz/export", method = RequestMethod.POST)
    public ResultVO<ExcelExportDto> exportDzVehicle(@RequestBody DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        return receivingService.exportDzVehicle(queryDTO);
    }

    /*
     * @description: 送货异常关联
     * <AUTHOR>
     * @date 2022/4/11 14:34
     * @param receivingExceptionDTO
     * @return java.lang.Integer
     */
    @ApiOperation(value = "送货异常关联")
    @RequestMapping(value = "/exception", method = RequestMethod.POST)
    public Integer receiveException(@RequestBody ReceivingExceptionDTO receivingExceptionDTO) {
        return receivingService.receiveException(receivingExceptionDTO);
    }


    /*
     * @description: 送货单过账
     * <AUTHOR>
     * @date 2022/4/12 14:27
     * @param postingDTO
     * @return java.lang.Integer
     */
    @ApiOperation(value = "送货单过账")
    @RequestMapping(value = "/posting", method = RequestMethod.POST)
    public Integer posting(@RequestBody PostingDTO postingDTO) {
        return receivingService.posting(postingDTO);
    }

    /*
     * @description: 收货冲销
     * <AUTHOR>
     * @date 2022/4/12 14:33
     * @param ids: 送货车辆集合
     * @return java.lang.Integer
     */
    @ApiOperation(value = "收货冲销")
    @RequestMapping(value = "/write/off", method = RequestMethod.POST)
    public Integer writeOff(@RequestBody List<String> matDocs) {
        return receivingService.writeOff(matDocs);
    }

    @ApiOperation(value = "接收高粱稻壳地磅数据")
    @RequestMapping(value = "/sync/weigh/data", method = RequestMethod.POST)
    public Integer syncWeighData(@RequestBody WeighDataDTO weighDataDTO) {
        return receivingService.syncWeighData(weighDataDTO);
    }

    @ApiOperation(value = "高粱稻壳结束卸货")
    @RequestMapping(value = "/unloading/{id}", method = RequestMethod.GET)
    public Integer unloading(@PathVariable Integer id) {
        return receivingService.unloading(id);
    }

    @ApiOperation(value = "高粱稻壳小麦开始卸货")
    @RequestMapping(value = "/unloading/start", method = RequestMethod.POST)
    public Integer startUnloading(@RequestBody @Valid UnloadRecordDTO unloadRecordDTO) {
        return receivingService.startUnloading(unloadRecordDTO);
    }

    @ApiOperation(value = "获取最新卸货记录")
    @RequestMapping(value = "/unload/record/list/get", method = RequestMethod.GET)
    public List<UnloadRecordListDTO> getUnloadRecordList() {
        return receivingService.getUnloadRecordList();
    }

    @ApiOperation(value = "出门车修改")
    @RequestMapping(value = "/out/vehicle/update", method = RequestMethod.POST)
    public Integer updateOutVehicle(@RequestBody OutVehicleUpdateDTO outVehicleUpdateDTO) {
        return receivingService.updateOutVehicle(outVehicleUpdateDTO);
    }

    @ApiOperation(value = "打印状态修改")
    @RequestMapping(value = "/print/state/update/{id}", method = RequestMethod.GET)
    public Integer updatePrintState(@PathVariable Integer id) {
        return receivingService.updatePrintState(id);
    }

    @ApiOperation(value = "根据批次获取送货车辆信息")
    @RequestMapping(value = "/getVehicleByBatch/{batch}", method = RequestMethod.GET)
    public List<VehicleBatchDTO> getVehicleByBatch(@PathVariable String batch) {
        return receivingService.getVehicleByBatch(batch);
    }

    @ApiOperation(value = "根据送货单号获取送货车辆信息")
    @RequestMapping(value = "/getDeliveryCarInfo/{deliveryNumber}", method = RequestMethod.GET)
    public DailyDeliveryDetailInfoDTO getDeliveryCarInfo(@PathVariable String deliveryNumber) {
        return receivingService.getDeliveryCarInfo(deliveryNumber);
    }

    @ApiOperation(value = "根据送货单号修改车辆状态")
    @RequestMapping(value = "/updateVehicleState", method = RequestMethod.POST)
    public Integer updateVehicleState(@RequestBody VehicleStateUpdateDTO vehicleStateUpdateDTO) {
        return receivingService.updateVehicleState(vehicleStateUpdateDTO);
    }

    @ApiOperation(value = "根据送货单号修改车辆状态")
    @RequestMapping(value = "/updateVehicleInspectState", method = RequestMethod.POST)
    public Integer updateVehicleInspectState(@RequestBody VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO) {
        return receivingService.updateVehicleInspectState(vehicleInspectStateUpdateDTO);
    }

    @ApiOperation(value = "根据送货单号修改车辆状态")
    @RequestMapping(value = "/calculateVehicleBuckleWeight/{deliveryNumber}/{materialCode}/{netWeight}", method = RequestMethod.POST)
    public BigDecimal calculateVehicleBuckleWeight(@PathVariable String deliveryNumber, @PathVariable String materialCode, @PathVariable BigDecimal netWeight) {
        return receivingService.calculateVehicleBuckleWeight(deliveryNumber, materialCode, netWeight);
    }


    @ApiOperation(value = "查询丢糟周报表")
    @RequestMapping(value = "/dz/weekly/report", method = RequestMethod.POST)
    public DzWeeklyReportDTO getDzWeeklyReport(@RequestBody DzWeeklyReportQueryDTO dto) {
        return receivingService.getDzWeeklyReport(dto);
    }


    @ApiOperation(value = "查询丢糟季度表")
    @RequestMapping(value = "/dz/quarter/report", method = RequestMethod.POST)
    public DzQuarterReportDTO getDzQuarterReport(@RequestBody DzQuarterReportQueryDTO dto) {
        return receivingService.getDzQuarterReport(dto);
    }

    @ApiOperation(value = "查询丢糟日表")
    @RequestMapping(value = "/dz/sun/report", method = RequestMethod.POST)
    public List<DzSunReportDTO> getDzSunReport(@RequestBody DzSunReportQueryDTO dto) {
        return receivingService.getDzSunReport(dto);
    }

    @ApiOperation(value = "退杂申请")
    @PostMapping("/impurityAudit")
    public Boolean impurityAudit(@RequestBody ImpurityAuditDTO impurityAuditDTO) {
        return receivingService.impurityAudit(impurityAuditDTO);
    }

    @ApiOperation(value = "更改车辆信息-数据补录")
    @PostMapping("/updateVehicleData")
    public Boolean updateVehicleData(@RequestBody VehicleDataUpdateDTO vehicleDataUpdateDTO) {
        return receivingService.updateVehicleData(vehicleDataUpdateDTO);
    }
}
