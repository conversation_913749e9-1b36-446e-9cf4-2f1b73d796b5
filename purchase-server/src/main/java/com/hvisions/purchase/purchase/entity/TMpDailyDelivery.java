package com.hvisions.purchase.purchase.entity;

import com.hvisions.purchase.entity.SysBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 日送货计划
 * @date 2022/4/6 10:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_daily_delivery")
public class TMpDailyDelivery extends SysBase {

    /**
     * 采购单id
     */
    private Integer purchaseId;
    /**
     * 要货需求id
     */
    private Integer demandId;
    /**
     * 预计送货数量
     */
    private BigDecimal deliverQuantity;
    /**
     * 送货计划号
     */
    private String planNumber;
    /**
     * 物料id
     */
    private Integer materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 日计划状态 0-新建、1-进行中、2-完成
     */
    private String state;
    /**
     * 备注
     */
    private String remark;

    /**
     * 投料类型：1-生产用料、2-试验用料、3-科研用料
     */
    private String feedType;

    // 产地
    private String productionPlace;

    @ApiModelProperty(value = "月需求id")
    private Integer monthDemandId;

    @ApiModelProperty(value = "基地 HY：黄舣基地，LH：罗汉基地、ZQ：制曲中心、XS：小市中心、GJ：国窖、ZJX：皂角巷")
    private String baseName;
}
