package com.hvisions.purchase.purchase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.feign.purchaseproduction.PurchaseProductionClient;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.dto.purchase.order.detail.ArrivalWeightQueryDTO;
import com.hvisions.purchase.dto.purchase.receiving.WeighDataDTO;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordListDTO;
import com.hvisions.purchase.dto.purchase.wheat.WheatMaterialQuantityDTO;
import com.hvisions.purchase.dto.purchase.wheat.vehicle.WarehouseDeliveryCarDTO;
import com.hvisions.purchase.dto.purchase.wheat.vehicle.WheatWeighDetailDTO;
import com.hvisions.purchase.dto.purchase.wheat.vehicle.WheatWeighQueryDTO;
import com.hvisions.purchase.purchase.consts.VehicleState;
import com.hvisions.purchase.purchase.dao.*;
import com.hvisions.purchase.purchase.entity.*;
import com.hvisions.purchase.purchase.service.PcReceivingService;
import com.hvisions.purchase.purchase.service.ReceivingService;
import com.hvisions.purchase.purchase.service.WheatService;
import com.hvisions.purchase.utils.DateUtil;
import com.hvisions.purchase.utils.StringUtil;
import com.hvisions.quality.client.MessageClient;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:小麦管理
 * @date 2022/4/6 10:18
 */
@Service
@Slf4j
public class WheatServiceImpl implements WheatService {

    @Resource
    DailyDeliveryDetailMapper dailyDeliveryDetailMapper;

    @Resource
    WheatMapper wheatMapper;

    @Resource
    PcDailyDeliveryDetailMapper pcDailyDeliveryDetailMapper;

    @Resource
    MessageClient messageClient;

    @Resource
    DailyDeliveryMapper dailyDeliveryMapper;

    @Resource
    DeliveryItemMapper deliveryItemMapper;

    @Resource
    PcReceivingService pcReceivingService;

    @Resource
    PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    ReceivingService receivingService;

    @Resource
    private PurchaseProductionClient purchaseProductionClient;

    @Resource
    DemandOrderMapper demandOrderMapper;

    /**
     * @Description 获取麦号
     *
     * <AUTHOR>
     * @Date 2024-4-26 9:28
     * @param
     * @return java.lang.Integer
     **/
    @Override
    public String generateWheatNumber() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int month = calendar.get(Calendar.MONTH) + 1;
        if (new Date().after(DateUtil.getAppointDay(new Date(), 24))) {
            month++;
        }
        return String.valueOf(month);
    }

    /**
     * @Description 入厂重量接收时修改车辆信息
     *
     * <AUTHOR>
     * @Date 2024-5-10 18:46
     * @param dailyDeliveryDetail
     * @return java.lang.Integer
     **/
    public Integer inUpdateVehicle(WeighDataDTO weighDataDTO, TMpDailyDeliveryDetail dailyDeliveryDetail) {
        dailyDeliveryDetail.setGrossWeight(weighDataDTO.getGrossWeight());
        dailyDeliveryDetail.setAdmissionTime(new Date());
        dailyDeliveryDetail.setWheatNumber(this.generateWheatNumber());

//        if (StringUtil.isNotEmpty(dailyDeliveryDetail.getMaterialId())) {
//            log.info("小麦车辆入厂，开始报检==========");
//            VehicleLaunchInspectionDTO vehicleLaunchInspectionDTO = new VehicleLaunchInspectionDTO();
//            vehicleLaunchInspectionDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
//            vehicleLaunchInspectionDTO.setMaterialId(dailyDeliveryDetail.getMaterialId());
//            vehicleLaunchInspectionDTO.setLicensePlateNumber(dailyDeliveryDetail.getLicensePlateNumber());
//
//            DailyDeliveryDetailInfoDTO dailyDeliveryDetailInfoDTO = deliveryItemMapper.getDemandByItemId(dailyDeliveryDetail.getDeliveryItemId());
//            vehicleLaunchInspectionDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
//            vehicleLaunchInspectionDTO.setDepartmentId(dailyDeliveryDetail.getDepartmentId());
//            vehicleLaunchInspectionDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
//            String inspectionOrder = vehicleInspectService.vehicleLaunchInspection(vehicleLaunchInspectionDTO);
//            log.info("小麦车辆入厂，报检结束=========={}", inspectionOrder);
//        }

        // 检验结果合格 可卸货，否则入厂
        if (VehicleState.InspectState.QUALIFIED.equals(dailyDeliveryDetail.getInspectState())) {
            dailyDeliveryDetail.setState(VehicleState.CAN_UNLOADING);
        } else {
            dailyDeliveryDetail.setState(VehicleState.ENTERED);
        }
        return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
    }

    /**
     * @Description 小麦车 出厂重量接收时修改车辆信息
     *
     * <AUTHOR>
     * @Date 2024-5-10 18:51
     * @param
     * @return java.lang.Integer
     **/
    public Integer outUpdateVehicle(WeighDataDTO weighDataDTO, TMpDailyDeliveryDetail dailyDeliveryDetail) {
        log.info("出厂操作");
        if (VehicleState.GO_OUT.equals(dailyDeliveryDetail.getState())) {
            throw new BaseKnownException(10000, "车辆已经出厂过磅，无需重新过磅！");
        }
        dailyDeliveryDetail.setGrossWeight(weighDataDTO.getGrossWeight());
        dailyDeliveryDetail.setAppearanceWeight(weighDataDTO.getAppearanceWeight());
        dailyDeliveryDetail.setNetWeight(weighDataDTO.getNetWeight());
        dailyDeliveryDetail.setLeaveNetWeight(weighDataDTO.getNetWeight());
        dailyDeliveryDetail.setAppearanceTime(new Date());
        dailyDeliveryDetail.setUpdateTime(new Date());
        dailyDeliveryDetail.setState(VehicleState.GO_OUT);

        // 获取要货需要单子项
        LambdaQueryWrapper<TMpDeliveryItem> itemWrapper = Wrappers.<TMpDeliveryItem>lambdaQuery()
                .eq(TMpDeliveryItem::getId, dailyDeliveryDetail.getDeliveryItemId())
                .eq(TMpDeliveryItem::getDeleted, 0);
        TMpDeliveryItem item = deliveryItemMapper.selectOne(itemWrapper);
        if ("0000".equals(dailyDeliveryDetail.getMaterialCode())) {
            log.info("小麦杂质车2次过磅");
            // 小麦杂质车二次过磅
            /*
             * 去最新的未过账的送货车进行扣杂
             */

            // 获取扣杂车辆
//            TMpDailyDeliveryDetail impurityDailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                    .eq(TMpDailyDeliveryDetail::getDeliveryItemId, dailyDeliveryDetail.getDeliveryItemId())
//                    .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                    .ge(TMpDailyDeliveryDetail::getNetWeight, weighDataDTO.getNetWeight())
//                    .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                    .eq(TMpDailyDeliveryDetail::getDeleted, 0)
//                    .orderByDesc(TMpDailyDeliveryDetail::getAppearanceTime)
//                    .last("limit 1"));
//
//            if (StringUtil.isEmpty(impurityDailyDeliveryDetail)) {
//                throw new BaseKnownException(10000, "无扣杂车辆，请查看后过磅！");
//            } else {
//                impurityDailyDeliveryDetail.setImpurityWeight(weighDataDTO.getNetWeight());
//                impurityDailyDeliveryDetail.setNetWeight(impurityDailyDeliveryDetail.getNetWeight().subtract(weighDataDTO.getNetWeight()));
//                dailyDeliveryDetailMapper.updateById(impurityDailyDeliveryDetail);
//            }
            if (StringUtil.isNotEmpty(item.getInboundWeight())) {
                // 入库重量减去杂质重量
                item.setInboundWeight(item.getInboundWeight().subtract(weighDataDTO.getNetWeight()));
                deliveryItemMapper.updateById(item);
            }
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);

        } else { // 非杂质车过磅
            log.info("非杂质车过磅");

            BigDecimal weight = weighDataDTO.getNetWeight();
            // 获取扣重
            BigDecimal buckleWeight = receivingService.calculateVehicleBuckleWeight(dailyDeliveryDetail.getDeliveryNumber(), dailyDeliveryDetail.getMaterialCode(), weighDataDTO.getNetWeight());
            // 存在扣重
            if (buckleWeight.compareTo(new BigDecimal(0)) > 0) {
                DecimalFormat df = new DecimalFormat("0.00");
                TMpDailyDelivery dailyDelivery = dailyDeliveryMapper.selectById(item.getDeliveryId());
                //  存在扣重，给YFLCG 发送通知
                SendMessageDTO sendMessageDTO = new SendMessageDTO();
                sendMessageDTO.setTitle("扣重处理通知");
                sendMessageDTO.setContent(dailyDelivery.getPlanNumber() + "送货计划号，" + weighDataDTO.getLicensePlateNumber() + "车辆有扣重，" + "扣重比例" + df.format(buckleWeight) + "，请处理");
                sendMessageDTO.setRoleCode("YFLCG");
                messageClient.sendMessage(sendMessageDTO);
                log.info("扣重处理通知发送完成");
                dailyDeliveryDetail.setBuckleIsHandle(VehicleState.BuckleHandleState.WAIT_HANDLE);// 扣重待处理
            } else {
                dailyDeliveryDetail.setBuckleIsHandle(VehicleState.BuckleHandleState.ALREADY_HANDLE);// 扣重已经处理
            }


            log.info("----------------更新送货子项已到货重量和入库重量 开始-------------------");
            // 2、更新已到货数量，不除去扣重
            item.setArrivalWeight(StringUtil.isEmpty(item.getArrivalWeight()) ? weight : item.getArrivalWeight().add(weight));

            // 3、更新入库重量，，并且入库
            BigDecimal inboundWeight = new BigDecimal(0);
            if (item.getInboundWeight() != null) {
                // 入库重量不为空，进行重量累加
                inboundWeight = item.getInboundWeight().add(weight.subtract(buckleWeight));
            } else {
                // 入库重量未空，则直接记录重量
                inboundWeight = inboundWeight.add(weight.subtract(buckleWeight));
            }
            item.setInboundWeight(inboundWeight);

            // 4、修改送货计划子项
            deliveryItemMapper.updateById(item);
            log.info("----------------更新送货子项已到货重量和入库重量 结束-------------------");

            log.info("-------------校验采购单已到货数量是否达到需求，如果达到则修改状态为已完成  开始------------");
            // 获取采购详情数据
            TMpPurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailMapper.selectById(item.getOrderDetailId());
            TMpPurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderDetail.getOrderId());
            // 根据采购id获取所有已到货数量总数
            ArrivalWeightQueryDTO queryDTO = new ArrivalWeightQueryDTO();
            queryDTO.setOrderId(purchaseOrderDetail.getOrderId());
            BigDecimal total = purchaseOrderDetailMapper.getArrivalWeight(queryDTO);
            // 根据采购id获取所有采购数量总数
            BigDecimal quantitySupplied = purchaseOrderDetailMapper.getQuantitySupplied(purchaseOrderDetail.getOrderId());
            // 计算获取5%的数量
            BigDecimal divide = quantitySupplied.multiply(new BigDecimal(5)).divide(quantitySupplied);
            // 获取-5%的数量
            BigDecimal min = quantitySupplied.subtract(divide);
            // 判断已到货数量是否超过采购数量-5%的数量，修改状态已完成
//            if (total.compareTo(min) > -1) {
//                purchaseOrder.setState(PurchaseOrderState.FINISH);
//                purchaseOrderMapper.updateById(purchaseOrder);
//            }
            log.info("-------------校验采购单已到货数量是否达到需求，如果达到则修改状态为已完成  结束------------");

            log.info("-------------卸货记录改成卸货完成------------");
            receivingService.endUnloading(dailyDeliveryDetail.getId());

            buckleWeight = buckleWeight.setScale(0, BigDecimal.ROUND_HALF_UP);
            // 6、更新车辆送货信息
            dailyDeliveryDetail.setBuckleWeight(buckleWeight);
            dailyDeliveryDetail.setNetWeight(weight.subtract(buckleWeight));
            if (!VehicleState.InspectState.SPECIAL_RETURN.equals(dailyDeliveryDetail.getInspectState())) {
                //如果不是特殊退货，就直接收货
                dailyDeliveryDetail.setState(VehicleState.RECEIVED);
            } else {
                log.info("特殊退货，不再收货，将状态改回特殊退货");
                dailyDeliveryDetail.setState(VehicleState.SPECIAL_RETURN);
            }
            dailyDeliveryDetail.setUnloadState(VehicleState.UnloadState.UNLOAD_COMPLETE);// 卸货状态改成卸货完成

            ResultVO resultVO = purchaseProductionClient.updateTMpdMachiningOrder(dailyDeliveryDetail.getId(), dailyDeliveryDetail.getNetWeight());
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }
            log.info("送货车辆2次过磅完成");
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
        }
    }


    /**
     * @Description 接收制曲地磅小麦送货数据
     *
     * <AUTHOR>
     * @Date 2024-6-5 17:00
     * @param weighDataDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer receiveWheatWeighData(WeighDataDTO weighDataDTO) {
        log.info("接收制曲地磅小麦相关数据===={}", weighDataDTO);
        int res = 0;
        try {
            /*
             * 品创小麦送货车过磅（或者品创小麦杂质车）
             */
            LambdaQueryWrapper<TMpPcDailyDeliveryDetail> pcWrapper = Wrappers.<TMpPcDailyDeliveryDetail>lambdaQuery()
                    .eq(TMpPcDailyDeliveryDetail::getDeliveryNumber, weighDataDTO.getDeliveryNumber())
                    .eq(TMpPcDailyDeliveryDetail::getLicensePlateNumber, weighDataDTO.getLicensePlateNumber())
                    .eq(TMpPcDailyDeliveryDetail::getDeleted, 0);
            TMpPcDailyDeliveryDetail pcDailyDeliveryDetail = pcDailyDeliveryDetailMapper.selectOne(pcWrapper);
            if (StringUtil.isNotEmpty(pcDailyDeliveryDetail)) {
                return pcReceivingService.syncPcWeighData(weighDataDTO, pcDailyDeliveryDetail);
            }

            /*
             * 小麦送货车辆过磅（小麦杂质车过磅）
             */
            LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                    .eq(TMpDailyDeliveryDetail::getDeliveryNumber, weighDataDTO.getDeliveryNumber())
                    .eq(TMpDailyDeliveryDetail::getLicensePlateNumber, weighDataDTO.getLicensePlateNumber())
                    .eq(TMpDailyDeliveryDetail::getDeleted, 0);
            TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(wrapper);

            if (StringUtil.isNotEmpty(dailyDeliveryDetail)) {
                if ("1".equals(weighDataDTO.getType())) { // 入厂
                    return this.inUpdateVehicle(weighDataDTO, dailyDeliveryDetail);
                } else { // 出厂
                    return this.outUpdateVehicle(weighDataDTO, dailyDeliveryDetail);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseKnownException(10000, "地磅数据同步调用失败，原因：" + e.getMessage());
        }
        return res;
    }

    /**
     * @Description 获取指定时间的小麦过磅数量
     *
     * <AUTHOR>
     * @Date 2024-6-20 9:56
     * @param queryDTO
     * @return java.math.BigDecimal
     **/
    @Override
    public WheatWeighDetailDTO getWheatWeighDetail(WheatWeighQueryDTO queryDTO) {
        return dailyDeliveryDetailMapper.getWheatWeighDetail(queryDTO);
    }

    /**
     * @Description 根据入仓任务id获取送货车列表
     *
     * <AUTHOR>
     * @Date 2024-6-20 10:46
     * @param warehouseTaskId
     * @return java.util.List<com.hvisions.purchase.dto.purchase.wheat.vehicle.WarehouseDeliveryCarDTO>
     **/
    @Override
    public List<WarehouseDeliveryCarDTO> getCarListByWarehouseTaskId(Integer warehouseTaskId) {
        return wheatMapper.getCarListByWarehouseTaskId(warehouseTaskId);
    }

    /**
     * @Description 根据送货单id获取车辆详情
     *
     * <AUTHOR>
     * @Date 2024-6-24 16:49
     * @param carId
     * @return com.hvisions.purchase.dto.purchase.wheat.vehicle.WarehouseDeliveryCarDTO
     **/
    @Override
    public WarehouseDeliveryCarDTO getCarDetailById(Integer carId) {
        return wheatMapper.getCarDetailById(carId);
    }

    @Override
    public List<UnloadRecordListDTO> getUnloadRecordList() {
        return wheatMapper.getUnloadRecordList();
    }

    /**
     * @Description 获取指定时间范围内的小麦过账数量(列表1是有机小麦 、 2是非有机小麦)
     *
     * <AUTHOR>
     * @Date 2024-7-18 17:22
     * @param dates
     * @return java.util.List<java.math.BigDecimal>
     **/
    @Override
    public List<WheatMaterialQuantityDTO> getWheatPostingQuantityByTime(List<LocalDate> dates) {
        return wheatMapper.getWheatPostingQuantityByTime(dates.get(0), dates.get(1));
    }
}
