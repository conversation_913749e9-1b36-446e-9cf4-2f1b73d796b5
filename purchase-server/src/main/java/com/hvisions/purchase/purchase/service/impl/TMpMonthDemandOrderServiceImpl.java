package com.hvisions.purchase.purchase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.purchase.dto.purchase.demand.order.MonthDemandOrderDTO;
import com.hvisions.purchase.dto.purchase.demand.order.MonthDemandQueryDTO;
import com.hvisions.purchase.purchase.dao.TMpMonthDemandDetailMapper;
import com.hvisions.purchase.purchase.dao.TMpMonthDemandOrderMapper;
import com.hvisions.purchase.purchase.entity.TMpMonthDemandOrder;
import com.hvisions.purchase.purchase.service.TMpMonthDemandOrderService;
import com.hvisions.purchase.utils.DateUtil;
import com.hvisions.purchase.utils.GenerateCodeUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 月要货需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class TMpMonthDemandOrderServiceImpl extends ServiceImpl<TMpMonthDemandOrderMapper, TMpMonthDemandOrder> implements TMpMonthDemandOrderService {

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private TMpMonthDemandDetailMapper demandDetailMapper;

    @Resource
    private TMpMonthDemandOrderMapper monthDemandOrderMapper;

    @Override
    public  Page<MonthDemandOrderDTO> findListByPage(MonthDemandQueryDTO req){
        LambdaQueryWrapper<TMpMonthDemandOrder> wrapper = new LambdaQueryWrapper<TMpMonthDemandOrder>()
                .like(Strings.isNotBlank(req.getOrderNo()), TMpMonthDemandOrder::getOrderNo, req.getOrderNo())
                .eq(Strings.isNotBlank(req.getState()), TMpMonthDemandOrder::getState, req.getState())
                .eq(req.getMaterialId() != null, TMpMonthDemandOrder::getMaterialId, req.getMaterialId())
                .eq(Strings.isNotBlank(req.getYearMonthStr()), TMpMonthDemandOrder::getYearMonthStr, req.getYearMonthStr())
                .eq(Strings.isNotBlank(req.getBaseName()), TMpMonthDemandOrder::getBaseName, req.getBaseName())
                .ge(req.getStartTime()!=null, TMpMonthDemandOrder::getCreateTime, req.getStartTime())
                .le(req.getEndTime()!=null, TMpMonthDemandOrder::getCreateTime, req.getEndTime())
                .orderByDesc(TMpMonthDemandOrder::getCreateTime);
        Page<MonthDemandOrderDTO> pageList = PageHelperUtil.getPage(monthDemandOrderMapper::selectPage, req, wrapper, MonthDemandOrderDTO.class);
        for (MonthDemandOrderDTO monthDemandOrderDTO : pageList) {
            req.setMaterialId(monthDemandOrderDTO.getMaterialId());
            req.setStartTime(monthDemandOrderDTO.getStartTime());
            req.setEndTime(monthDemandOrderDTO.getEndTime());
            req.setId(monthDemandOrderDTO.getId());
            //子集汇总数据
            //monthDemandOrderDTO.setPurchaseOrderMonthDTOList(purchaseOrderMapper.selectPurchaseOrderByMonthDemand(req));
            monthDemandOrderDTO.setDemandDetailDTOList(demandDetailMapper.selectByMonthDemand(req));
        }
        return pageList;
    }

    @Override
    public int add(TMpMonthDemandOrder tMpMonthDemandOrder){
        //只允许新增要货开始时间20天之前的数据
        if (tMpMonthDemandOrder.getWarehousingTime() == null || DateUtil.subtractTwoDates(new Date(), tMpMonthDemandOrder.getWarehousingTime()) < 20) {
            throw new BaseKnownException(10011, "只允许新增要货开始时间20天之前的数据");
        }
        tMpMonthDemandOrder.setOrderNo(generateCodeUtil.generateInspectionCode("MXQ", 3));
        tMpMonthDemandOrder.setState("0");
        return baseMapper.insert(tMpMonthDemandOrder);
    }

    @Override
    public int delete(Long id){
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateData(TMpMonthDemandOrder tMpMonthDemandOrder){
        TMpMonthDemandOrder query = baseMapper.selectById(tMpMonthDemandOrder.getId());
        if ("1".equals(query.getState()) || "2".equals(query.getState())) {
            throw new BaseKnownException(10011, "只允许修改待下发或者待执行的数据");
        }
        //要货开始时间20天之前允许修改，20天之内不允许修改
        if (tMpMonthDemandOrder.getWarehousingTime() == null || DateUtil.subtractTwoDates(tMpMonthDemandOrder.getWarehousingTime(), new Date()) < 20) {
            throw new BaseKnownException(10011, "只允许修改要货开始时间20天之前的数据");
        }
        return baseMapper.updateById(tMpMonthDemandOrder);
    }

    @Override
    public TMpMonthDemandOrder findById(Long id){
        return  baseMapper.selectById(id);
    }

    @Override
    @Transactional
    public int addMonthDemandOrderList(List<TMpMonthDemandOrder> tMpMonthDemandOrders) {
        for (TMpMonthDemandOrder tMpMonthDemandOrder : tMpMonthDemandOrders) {
            add(tMpMonthDemandOrder);
        }
        return tMpMonthDemandOrders.size();
    }

    @Override
    public int issuedMonthDemandOrder(Long id) {
        TMpMonthDemandOrder query = baseMapper.selectById(id);
        if (!"0".equals(query.getState())) {
            throw new BaseKnownException(10011, "当前状态不允许下发");
        }
        query.setState("1");
        return baseMapper.updateById(query);
    }
}
