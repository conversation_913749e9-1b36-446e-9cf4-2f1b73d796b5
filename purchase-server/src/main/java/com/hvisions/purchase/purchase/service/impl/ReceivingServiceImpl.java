package com.hvisions.purchase.purchase.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.feign.purchaseproduction.PurchaseProductionClient;
import com.hvisions.brewage.purchaseproduction.dto.MachiningOrderDTO;
import com.hvisions.brewage.purchaseproduction.dto.StorageDataInsertDTO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.powder.client.SapClient;
import com.hvisions.powder.dto.product.pretreatment.warehouseTask.WarehouseTaskBatchUpdateDTO;
import com.hvisions.powder.dto.product.pretreatment.warehouseTask.WarehouseTaskIdQueryDTO;
import com.hvisions.product.WarehouseTaskClient;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.*;
import com.hvisions.purchase.dto.purchase.demand.order.ImpurityAuditDTO;
import com.hvisions.purchase.dto.purchase.order.detail.ArrivalWeightQueryDTO;
import com.hvisions.purchase.dto.purchase.receiving.*;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordDTO;
import com.hvisions.purchase.dto.purchase.unload.UnloadRecordListDTO;
import com.hvisions.purchase.purchase.consts.MaterialConst;
import com.hvisions.purchase.purchase.consts.SapConst;
import com.hvisions.purchase.purchase.consts.VehicleState;
import com.hvisions.purchase.purchase.dao.*;
import com.hvisions.purchase.purchase.entity.*;
import com.hvisions.purchase.purchase.service.DailyDeliveryService;
import com.hvisions.purchase.purchase.service.ReceivingService;
import com.hvisions.purchase.purchase.service.VehicleBlacklistService;
import com.hvisions.purchase.sap.dto.SapBaseResponseDto;
import com.hvisions.purchase.sap.dto.purchase.OrderPostingItemDto;
import com.hvisions.purchase.sap.dto.purchase.OrderWriteOffHeaderDto;
import com.hvisions.purchase.sap.service.SapService;
import com.hvisions.purchase.srm.dto.SrmImpurityAuditDto;
import com.hvisions.purchase.srm.service.SrmService;
import com.hvisions.purchase.utils.GenerateCodeUtil;
import com.hvisions.purchase.utils.StringUtil;
import com.hvisions.quality.client.IncomingInspectionClient;
import com.hvisions.quality.client.MessageClient;
import com.hvisions.quality.dto.quality.inspection.incoming.data.IncomingBuckleWeightDTO;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:收货管理
 * @date 2022/4/6 10:18
 */
@Service
@Slf4j
public class ReceivingServiceImpl implements ReceivingService {

    @Resource
    MessageClient messageClient;

    @Resource
    IncomingInspectionClient incomingInspectionClient;

    @Resource
    DailyDeliveryMapper dailyDeliveryMapper;

    @Resource
    DeliveryItemMapper deliveryItemMapper;

    @Resource
    DailyDeliveryDetailMapper dailyDeliveryDetailMapper;

    @Resource
    PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    ProcurementPlanMapper procurementPlanMapper;

    @Resource
    InventoryLocationMapper inventoryLocationMapper;

    @Resource
    SapService sapService;

    @Resource
    SapClient sapClient;

    @Resource
    private PurchaseProductionClient purchaseProductionClient;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private DailyDeliveryService dailyDeliveryService;

    @Resource
    private UnloadRecordMapper unloadRecordMapper;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Resource
    private VehicleBlacklistService vehicleBlacklistService;

    @Resource
    private WarehouseTaskClient warehouseTaskClient;

    @Resource
    DemandOrderMapper demandOrderMapper;

    @Resource
    TMpAcceptFirmMapper acceptFirmMapper;

    @Resource
    SrmService srmService;


    /*
     * @description: 分页查询送货车辆列表
     * <AUTHOR>
     * @date 2022/4/11 13:37
     * @param deliveryVehiclePageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.receiving.DeliveryVehiclePageDTO>
     */
    @Override
    public Page<DeliveryVehiclePageDTO> getDeliveryVehiclePageList(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO) {
        if (deliveryVehiclePageQueryDTO.getStates() != null && deliveryVehiclePageQueryDTO.getStates().length > 0) {
            List<String> stateList = Arrays.asList(deliveryVehiclePageQueryDTO.getStates());
            if (stateList.contains("2") && stateList.size() == 1) {
                //卸货车辆，查询根据质检排序
                deliveryVehiclePageQueryDTO.setOrderByInspect("1");
            }
        }
        return PageHelperUtil.getPage(dailyDeliveryDetailMapper::getDeliveryVehiclePageList, deliveryVehiclePageQueryDTO, DeliveryVehiclePageDTO.class);
    }

    @Override
    public Page<DeliveryVehiclePageDTO> getDzVehiclePageList(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO) {
        return PageHelperUtil.getPage(dailyDeliveryDetailMapper::getDzVehiclePageList, deliveryVehiclePageQueryDTO, DeliveryVehiclePageDTO.class);
    }

    @Override
    public Page<DzVehicleReportDTO> getDzVehicleReport(DeliveryVehiclePageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(dailyDeliveryDetailMapper::getDzVehicleReport, queryDTO, DzVehicleReportDTO.class);

    }

    @Override
    public ResultVO<ExcelExportDto> exportDzVehicleReport(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        List<DzVehicleReportDTO> list = dailyDeliveryDetailMapper.getDzVehicleReport(queryDTO);
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(list, "丢糟车汇总报表", DzVehicleReportDTO.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("丢糟车汇总报表.xls");
        return ResultVO.success(excelExportDto);
    }

    @Override
    public String getDzNetWeightSum(DeliveryVehiclePageQueryDTO deliveryVehiclePageQueryDTO) {
        return dailyDeliveryDetailMapper.getDzNetWeightSum(deliveryVehiclePageQueryDTO);
    }

    @Override
    public ResultVO<ExcelExportDto> exportVehicle(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        List<DeliveryVehiclePageDTO> list = dailyDeliveryDetailMapper.getDeliveryVehiclePageList(queryDTO);
        List<VehicleExportDTO> vehicleReportExportDTOS = DtoMapper.convertList(list, VehicleExportDTO.class);
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(vehicleReportExportDTOS, "送货车辆列表", VehicleExportDTO.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("送货车辆列表.xls");
        return ResultVO.success(excelExportDto);
    }

    /**
     * @Description 修改丢糟收货单位
     *
     * <AUTHOR>
     * @Date 2024-3-15 15:33
     * @param dzReceiptPlaceUpdateDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer updateDzReceiptPlace(DzReceiptPlaceUpdateDTO dzReceiptPlaceUpdateDTO) {
        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(dzReceiptPlaceUpdateDTO.getId());
        dailyDeliveryDetail.setReceiptPlace(dzReceiptPlaceUpdateDTO.getReceiptPlace());
        return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
    }

    @Override
    public ResultVO<ExcelExportDto> exportDzVehicle(DeliveryVehiclePageQueryDTO queryDTO) throws IOException, IllegalAccessException {
        List<DeliveryVehiclePageDTO> list = dailyDeliveryDetailMapper.getDzVehiclePageList(queryDTO);
        List<VehicleDzExportDTO> vehicleReportExportDTOS = DtoMapper.convertList(list, VehicleDzExportDTO.class);
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(vehicleReportExportDTOS, "丢糟车辆列表", VehicleDzExportDTO.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("丢糟车辆列表.xls");
        return ResultVO.success(excelExportDto);
    }


    /*
     * @description: 送货异常关联
     * <AUTHOR>
     * @date 2022/4/11 14:37
     * @param receivingExceptionDTO
     * @return java.lang.Integer
     */
    @Override
    public Integer receiveException(ReceivingExceptionDTO receivingExceptionDTO) {
        TMpDailyDeliveryDetail dailyDeliveryDetail = new TMpDailyDeliveryDetail();
        dailyDeliveryDetail.setId(receivingExceptionDTO.getId());
        dailyDeliveryDetail.setExceptionId(receivingExceptionDTO.getExceptionId());
        dailyDeliveryDetail.setExceptionDescription(receivingExceptionDTO.getExceptionDescription());
        return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
    }


    /*
     * @description: 送货单过账
     * <AUTHOR>
     * @date 2022/4/12 14:27
     * @param postingDTO
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer posting(PostingDTO postingDTO) {
        Integer res = 0;
        // SAP过账所需车辆信息
        List<OrderPostingItemDto> list = new ArrayList<>();

        // sap过账数量汇总
        TMpDailyDeliveryDetail ddd = dailyDeliveryDetailMapper.selectById(postingDTO.getIds().get(0));
        TMpDeliveryItem allDeliveryItem = deliveryItemMapper.selectById(ddd.getDeliveryItemId());
        TMpPurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailMapper.selectById(allDeliveryItem.getOrderDetailId());

        Date demandDate = dailyDeliveryDetailMapper.getDemandDateByVehicleId(ddd.getId());

        // 循环插入过磅记录
        for (Integer id : postingDTO.getIds()) {
            TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(id);
            if (StringUtil.isEmpty(dailyDeliveryDetail)) {
                throw new BaseKnownException(10000, "送货车辆不存在");
            }
            // 插入SAP需要过账的车辆信息
            OrderPostingItemDto itemDto = new OrderPostingItemDto();
//            itemDto.setItemKey("00010");
            itemDto.setItemKey(purchaseOrderDetail.getItemKey());
            itemDto.setPlant("1100");
            itemDto.setEntryUom("kg");
            itemDto.setPoItem(purchaseOrderDetail.getItemKey());
            itemDto.setBatch(ddd.getBatch());
            itemDto.setMaterial(ddd.getMaterialCode());
            itemDto.setXblnr(ddd.getDeliveryNumber());
            TMpPurchaseOrder purchaseOrder = dailyDeliveryDetailMapper.getPurchaseOrderByDeliveryDetailId(id);
            // 获取库存地点
//            TMpInventoryLocation inventoryLocation = inventoryLocationMapper.selectById(procurementPlanMapper.selectById(purchaseOrder.getPlanningId()).getLocationId());
            itemDto.setStgeLoc(postingDTO.getInitialWarehouseCode());
            itemDto.setPoNumber(purchaseOrder.getSapOrder());
            //过账重量
            BigDecimal postWeight = dailyDeliveryDetail.getNetWeight();
            if (StringUtils.isBlank(dailyDeliveryDetail.getMaterialCode())) {
                throw new BaseKnownException(10000, "SAP过账失败，物料编码不能为空，送货单号:" + dailyDeliveryDetail.getDeliveryNumber());
            } else if ("0000".equals(dailyDeliveryDetail.getMaterialCode())) {
                //判断是否是杂质
                itemDto.setMoveType("122");
                //杂质过账结果为负数
                postWeight = postWeight.multiply(new BigDecimal("-1"));
                itemDto.setEntryQut(postWeight);
            } else {
                itemDto.setMoveType("101");
                itemDto.setEntryQut(postWeight);
            }
            list.add(itemDto);

            // sap过账对接
            SapBaseResponseDto response = sapService.posting(list, postingDTO.getPostDate(), demandDate, postingDTO.getInitialWarehouseName(), postingDTO.getInitialWarehouseCode());
            if ("S".equals(response.getEsMessage().getMsgty()) || response.getEsMessage().getMsgtx().contains("已处理，请勿重复传输")) {
                // 存储物料凭证
                TMpDailyDeliveryDetail detail = new TMpDailyDeliveryDetail();
                detail.setId(id);
                detail.setMatDoc(response.getEvOutput().getOutput().get(0).getMblnr()); // 物料凭证编号
                detail.setDocYear(response.getEvOutput().getOutput().get(0).getMjahr()); // 物料凭证年度
                detail.setPostingPeople(postingDTO.getPostingPeople());
                detail.setPostingTime(postingDTO.getPostDate());
                detail.setPostingState(VehicleState.PostingState.ALREADY_POSTING);
                dailyDeliveryDetailMapper.updateById(detail);
            } else {
                // 异常
                throw new BaseKnownException(10000, "SAP过账失败，原因：" + response.getEsMessage().getMsgtx());
            }
            // 修改子项过账数量
            TMpDeliveryItem deliveryItem = deliveryItemMapper.selectById(dailyDeliveryDetail.getDeliveryItemId());
            if (deliveryItem.getPostingQuantity() == null) {
                deliveryItem.setPostingQuantity(postWeight);
            } else {
                deliveryItem.setPostingQuantity(deliveryItem.getPostingQuantity().add(postWeight));
            }
            res += deliveryItemMapper.updateById(deliveryItem);
            // 判断改采购单下的送货单是否全部过账，如果是，则修改采购单过账状态
            TMpPurchaseOrder query = dailyDeliveryDetailMapper.getPurchaseOrderByDeliveryDetailId(id);
            if (StringUtil.isNotEmpty(query)) {
                Integer awaitPostingNum = dailyDeliveryDetailMapper.getAwaitPostingNum(query.getId());
                if (awaitPostingNum == 0) {
                    query.setSapState(VehicleState.PostingState.ALREADY_POSTING);
                    res += purchaseOrderMapper.updateById(query);
                }
            }
        }
        return res;
    }

    /*
     * @description: 收货冲销
     * <AUTHOR>
     * @date 2022/4/12 14:33
     * @param ids: 送货车辆集合
     * @return java.lang.Integer
     */
    @Override
//    @Transactional
    public Integer writeOff(List<String> matDocs) {
        int res = 0;
        if (matDocs.size() > 1) {
            throw new BaseKnownException(1000, "请使用一个凭证号进行冲销");
        }
        List<TMpDailyDeliveryDetail> details = new ArrayList<>();

        for (String matDoc : matDocs) {
            // 获取已经过账的且过账凭证号为选择的
            List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                    .eq(TMpDailyDeliveryDetail::getMatDoc, matDoc)
                    .eq(TMpDailyDeliveryDetail::getDeleted, 0)
                    .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.ALREADY_POSTING)
            );
            OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
            headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
            headerDto.setPstingDate(DateUtil.format(dailyDeliveryDetails.get(0).getPostingTime(), DatePattern.PURE_DATETIME_PATTERN));
            headerDto.setMjahr(dailyDeliveryDetails.get(0).getDocYear());
            headerDto.setMblnr(matDoc);
            SapBaseResponseDto sapBaseResponseDto = sapService.writeOff(headerDto);

            // 新增sap操作记录
//            SapPostVO sapPostVO = new SapPostVO();
//            sapPostVO.setMaterialCode(dailyDeliveryDetails.get(0).getMaterialCode());
//            sapPostVO.setMaterialName(dailyDeliveryDetails.get(0).getMaterialName());
//            sapPostVO.setUnit("kg");
//            sapPostVO.setWeight(dailyDeliveryDetails.get(0).getNetWeight().negate());
//            sapPostVO.setType(0);
//            sapPostVO.setMovementTypeId(101);
//            sapPostVO.setOperatorType("原辅料收货冲销");
//            sapPostVO.setCertificateDate(LocalDate.now());
//            sapPostVO.setValueJson(JSONObject.toJSONString(headerDto));
//            sapPostVO.setSapCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO);
//            if ("S".equals(sapBaseResponseDto.getEsMessage().getMsgty())) {
//                sapPostVO.setState("1");
                details.addAll(dailyDeliveryDetails);
//            } else {
//                sapPostVO.setState("2");
//                sapPostVO.setFailReason(sapBaseResponseDto.getEsMessage().getMsgtx());
//            }
            if (!"S".equals(sapBaseResponseDto.getEsMessage().getMsgty())) {
                throw new BaseKnownException(10000, "sap冲销失败" + sapBaseResponseDto.getEsMessage().getMsgtx());
            }

            sapClient.reversalSapPostStatus(matDoc);

            //冲销成功处理业务数据
            for (TMpDailyDeliveryDetail dailyDeliveryDetail : dailyDeliveryDetails) {
                // 送货计划子项回退
                TMpDeliveryItem deliveryItem = deliveryItemMapper.selectById(dailyDeliveryDetail.getDeliveryItemId());
                // 过账数量减少
                deliveryItem.setPostingQuantity(deliveryItem.getPostingQuantity().subtract(dailyDeliveryDetail.getNetWeight()));
                res += deliveryItemMapper.updateById(deliveryItem);

                // 送货单回退
                dailyDeliveryDetail.setPostingState(VehicleState.PostingState.WAIT_POSTING);
                dailyDeliveryDetail.setPostingTime(null);
                dailyDeliveryDetail.setPostingPeople("");
                dailyDeliveryDetail.setMatDoc("");
                dailyDeliveryDetail.setDocYear("");
                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);

                // 采购单过账状态回退
                TMpPurchaseOrder purchaseOrder = dailyDeliveryDetailMapper.getPurchaseOrderByDeliveryDetailId(dailyDeliveryDetail.getId());
                purchaseOrder.setSapState(VehicleState.PostingState.WAIT_POSTING);
                res += purchaseOrderMapper.updateById(purchaseOrder);
            }
        }
        return res;
    }

    /**
     * @Description 高粱稻壳车入厂重量接收时修改车辆信息
     *
     * <AUTHOR>
     * @Date 2024-5-10 18:46
     * @param dailyDeliveryDetail
     * @return java.lang.Integer
     **/
    public Integer inUpdateVehicle(WeighDataDTO weighDataDTO, TMpDailyDeliveryDetail dailyDeliveryDetail) {
        dailyDeliveryDetail.setGrossWeight(weighDataDTO.getGrossWeight());
        dailyDeliveryDetail.setAdmissionTime(new Date());
//        if (StringUtil.isNotEmpty(dailyDeliveryDetail.getMaterialId())) {
//            log.info("高粱稻壳车辆入厂，开始报检==========");
//            VehicleLaunchInspectionDTO vehicleLaunchInspectionDTO = new VehicleLaunchInspectionDTO();
//            vehicleLaunchInspectionDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
//            vehicleLaunchInspectionDTO.setMaterialId(dailyDeliveryDetail.getMaterialId());
//            vehicleLaunchInspectionDTO.setLicensePlateNumber(dailyDeliveryDetail.getLicensePlateNumber());
//
        DailyDeliveryDetailInfoDTO dailyDeliveryDetailInfoDTO = deliveryItemMapper.getDemandByItemId(dailyDeliveryDetail.getDeliveryItemId());
//            vehicleLaunchInspectionDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
//            vehicleLaunchInspectionDTO.setDepartmentId(dailyDeliveryDetail.getDepartmentId());
//            vehicleLaunchInspectionDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
//            String inspectionOrder = vehicleInspectService.vehicleLaunchInspection(vehicleLaunchInspectionDTO);
//            log.info("高粱稻壳车辆入厂，报检结束=========={}", inspectionOrder);
//        }
        // 检验结果合格 可卸货，否则入厂
        if (VehicleState.InspectState.QUALIFIED.equals(dailyDeliveryDetail.getInspectState())) {
            dailyDeliveryDetail.setState(VehicleState.CAN_UNLOADING);
        } else {
            dailyDeliveryDetail.setState(VehicleState.ENTERED);
        }
        return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
    }

    /**
     * @Description 出厂重量接收时修改车辆信息
     *
     * <AUTHOR>
     * @Date 2024-5-10 18:51
     * @param
     * @return java.lang.Integer
     **/
    public Integer outUpdateVehicle(WeighDataDTO weighDataDTO, TMpDailyDeliveryDetail dailyDeliveryDetail) {
        log.info("高粱稻壳出厂操作");
        if (VehicleState.GO_OUT.equals(dailyDeliveryDetail.getState())) {
            throw new BaseKnownException(10000, "车辆已经出厂过磅，无需重新过磅！");
        }
        log.info("车辆过磅数据:" + JSONObject.toJSONString(weighDataDTO));
        dailyDeliveryDetail.setGrossWeight(weighDataDTO.getGrossWeight());
        dailyDeliveryDetail.setAppearanceWeight(weighDataDTO.getAppearanceWeight());
        dailyDeliveryDetail.setNetWeight(weighDataDTO.getNetWeight());
        dailyDeliveryDetail.setLeaveNetWeight(weighDataDTO.getNetWeight());
        dailyDeliveryDetail.setAppearanceTime(new Date());
        dailyDeliveryDetail.setUpdateTime(new Date());
        dailyDeliveryDetail.setState(VehicleState.GO_OUT);

        // 获取要货需要单子项
        LambdaQueryWrapper<TMpDeliveryItem> itemWrapper = Wrappers.<TMpDeliveryItem>lambdaQuery()
                .eq(TMpDeliveryItem::getId, dailyDeliveryDetail.getDeliveryItemId())
                .eq(TMpDeliveryItem::getDeleted, 0);
        TMpDeliveryItem item = deliveryItemMapper.selectOne(itemWrapper);
        if ("0000".equals(dailyDeliveryDetail.getMaterialCode())) {
            log.info("杂质2次过磅");
            // 杂质车二次过磅
            /**
             * 杂质车辆过磅，修改未过账车辆的净重
             * 未过账净重 -  杂质重量/未过账车辆数量
             * 到货数量 = 到货数量 - 杂质重量
             */
//            LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                    .eq(TMpDailyDeliveryDetail::getDeliveryItemId, dailyDeliveryDetail.getDeliveryItemId())
//                    .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                    .eq(TMpDailyDeliveryDetail::getInspectState, VehicleState.InspectState.QUALIFIED)
//                    .isNotNull(TMpDailyDeliveryDetail::getNetWeight)
//                    .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                    .eq(TMpDailyDeliveryDetail::getBuckleWeight, BigDecimal.ZERO) // 扣重车不参与杂质扣重
//                    .eq(TMpDailyDeliveryDetail::getDeleted, 0);
//            List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
//
//            if (dailyDeliveryDetails.size() == 0) {
//                throw new BaseKnownException(10000, "无扣杂车辆，请查看后过磅！");
//            }
//
//            // 均摊杂质逻辑
//            BigDecimal surplus = weighDataDTO.getNetWeight(); // 剩余扣重
//            if (dailyDeliveryDetails != null && dailyDeliveryDetails.size() > 0) {
//                do {
//
//                    BigDecimal[] bigDecimals = surplus.divideAndRemainder(new BigDecimal(dailyDeliveryDetails.size()));
//                    BigDecimal quotient = bigDecimals[0]; // 商
//                    BigDecimal remainder = bigDecimals[1]; // 余数
//                    BigDecimal first = quotient.add(remainder); // 第一车将余数包含
//                    surplus = BigDecimal.ZERO; // 剩余扣重
//                    List<Integer> indexes = new ArrayList<>(); // 不够扣的项
//                    for (int i = 0; i < dailyDeliveryDetails.size(); i++) { // 每没一车循环扣重
//                        if (i == 0) {
//
//                            BigDecimal finalImpurity;
//                            if (dailyDeliveryDetails.get(i).getNetWeight().subtract(first).compareTo(BigDecimal.ZERO) == -1) {
//                                // 最终净重不够扣
//                                finalImpurity = dailyDeliveryDetails.get(i).getNetWeight();
//                                surplus = surplus.add(dailyDeliveryDetails.get(i).getNetWeight().subtract(first).abs());
//                                indexes.add(dailyDeliveryDetails.get(i).getId());
//                            } else {
//                                finalImpurity = first;
//                            }
//
//                            dailyDeliveryDetails.get(i).setNetWeight(dailyDeliveryDetails.get(i).getNetWeight().subtract(finalImpurity));
//                            dailyDeliveryDetails.get(i).setImpurityWeight(StringUtil.isEmpty(dailyDeliveryDetails.get(i).getImpurityWeight()) ? finalImpurity : dailyDeliveryDetails.get(i).getImpurityWeight().add(finalImpurity));
//                            dailyDeliveryDetailMapper.updateById(dailyDeliveryDetails.get(i));
//                        } else {
//                            BigDecimal finalImpurity;
//                            if (dailyDeliveryDetails.get(i).getNetWeight().subtract(quotient).compareTo(BigDecimal.ZERO) == -1) {
//                                // 最终净重不够扣
//                                finalImpurity = dailyDeliveryDetails.get(i).getNetWeight();
//                                surplus = surplus.add(dailyDeliveryDetails.get(i).getNetWeight().subtract(quotient).abs());
//                                indexes.add(dailyDeliveryDetails.get(i).getId());
//                            } else {
//                                finalImpurity = quotient;
//                            }
//                            dailyDeliveryDetails.get(i).setNetWeight(dailyDeliveryDetails.get(i).getNetWeight().subtract(finalImpurity));
//                            dailyDeliveryDetails.get(i).setImpurityWeight(StringUtil.isEmpty(dailyDeliveryDetails.get(i).getImpurityWeight()) ? finalImpurity : dailyDeliveryDetails.get(i).getImpurityWeight().add(finalImpurity));
//                            dailyDeliveryDetailMapper.updateById(dailyDeliveryDetails.get(i));
//                        }
//                    }
//
//                    // 存在剩余扣重
//                    if (surplus.compareTo(BigDecimal.ZERO) == 1) {
//
//                        // 移除不够扣重的
//                        dailyDeliveryDetails = dailyDeliveryDetails.stream().filter(o -> !indexes.contains(o.getId())).collect(Collectors.toList());
//
//                        if (dailyDeliveryDetails.size() == 0) {
//                            // break;
//                            // throw new BaseKnownException(10000,"扣杂不够扣");
//                        }
//
//                    }
//                } while (surplus.compareTo(BigDecimal.ZERO) == 1);
//            }

            if (StringUtil.isNotEmpty(item.getInboundWeight())) {
                // 入库重量减去杂质重量
                item.setInboundWeight(item.getInboundWeight().subtract(weighDataDTO.getNetWeight()));
                deliveryItemMapper.updateById(item);
            }
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);

        } else { // 非杂质车过磅
            log.info("非杂质车2次过磅");

            BigDecimal weight = weighDataDTO.getNetWeight();
            // 获取扣重
            BigDecimal buckleWeight = this.calculateVehicleBuckleWeight(dailyDeliveryDetail.getDeliveryNumber(), dailyDeliveryDetail.getMaterialCode(), weighDataDTO.getNetWeight());
            TMpDailyDelivery dailyDelivery = dailyDeliveryMapper.selectById(item.getDeliveryId());
            // 存在扣重
            if (buckleWeight.compareTo(new BigDecimal(0)) > 0) {
                DecimalFormat df = new DecimalFormat("0.00");
                //  存在扣重，给YFLCG 发送通知
                SendMessageDTO sendMessageDTO = new SendMessageDTO();
                sendMessageDTO.setTitle("扣重处理通知");
                sendMessageDTO.setContent(dailyDelivery.getPlanNumber() + "送货计划号，" + weighDataDTO.getLicensePlateNumber() + "车辆有扣重，" + "扣重比例" + df.format(buckleWeight) + "，请处理");
                sendMessageDTO.setRoleCode("YFLCG");
                messageClient.sendMessage(sendMessageDTO);
                log.info("扣重处理通知发送完成");
                dailyDeliveryDetail.setBuckleIsHandle(VehicleState.BuckleHandleState.WAIT_HANDLE);// 扣重待处理
            } else {
                dailyDeliveryDetail.setBuckleIsHandle(VehicleState.BuckleHandleState.ALREADY_HANDLE);// 扣重已经处理
            }

            log.info("----------------更新送货子项已到货重量和入库重量 开始-------------------");
            log.info("监控到货重量开始,日送货计划号=========={}", dailyDelivery.getPlanNumber());
            log.info("日送货计划之前的到货重量=={}", item.getArrivalWeight());

            // 2、更新已到货数量，不除去扣重
            if (item.getArrivalWeight() == null) {
                item.setArrivalWeight(weight);
            } else {
                item.setArrivalWeight(item.getArrivalWeight().add(weight));
            }

            log.info("送货车净重" + dailyDeliveryDetail.getDeliveryNumber() + "==" + weight);
            log.info("日送货计划最新到货重量=={}", item.getArrivalWeight());
            log.info("监控到货重量结束==========");

            // 3、更新入库重量，，并且入库
            BigDecimal inboundWeight = new BigDecimal(0);
            if (item.getInboundWeight() != null) {
                // 入库重量不为空，进行重量累加
                inboundWeight = item.getInboundWeight().add(weight.subtract(buckleWeight));
            } else {
                // 入库重量未空，则直接记录重量
                inboundWeight = inboundWeight.add(weight.subtract(buckleWeight));
            }
            item.setInboundWeight(inboundWeight);

            // 4、修改送货计划子项
            deliveryItemMapper.updateById(item);
            log.info("----------------更新送货子项已到货重量和入库重量 结束-------------------");

            log.info("-------------校验采购单已到货数量是否达到需求，如果达到则修改状态为已完成  开始------------");
            // 获取采购详情数据
            TMpPurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailMapper.selectById(item.getOrderDetailId());
            TMpPurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderDetail.getOrderId());
            // 根据采购id获取所有已到货数量总数
            ArrivalWeightQueryDTO queryDTO = new ArrivalWeightQueryDTO();
            queryDTO.setOrderId(purchaseOrderDetail.getOrderId());
            BigDecimal total = purchaseOrderDetailMapper.getArrivalWeight(queryDTO);
            // 根据采购id获取所有采购数量总数
            BigDecimal quantitySupplied = purchaseOrderDetailMapper.getQuantitySupplied(purchaseOrderDetail.getOrderId());
            // 计算获取5%的数量
            BigDecimal divide = quantitySupplied.multiply(new BigDecimal(5)).divide(quantitySupplied);
            // 获取-5%的数量
            BigDecimal min = quantitySupplied.subtract(divide);
            // 判断已到货数量是否超过采购数量-5%的数量，修改状态已完成
//            if (total.compareTo(min) > -1) {
//                purchaseOrder.setState(PurchaseOrderState.FINISH);
//                purchaseOrderMapper.updateById(purchaseOrder);
//            }
            log.info("-------------校验采购单已到货数量是否达到需求，如果达到则修改状态为已完成  结束------------");

            // 结束卸货
            this.endUnloading(dailyDeliveryDetail.getId());

            buckleWeight = buckleWeight.setScale(0, BigDecimal.ROUND_HALF_UP);
            // 6、更新车辆送货信息
            dailyDeliveryDetail.setBuckleWeight(buckleWeight);
            dailyDeliveryDetail.setNetWeight(weight.subtract(buckleWeight));
            if (!VehicleState.InspectState.SPECIAL_RETURN.equals(dailyDeliveryDetail.getInspectState())) {
                //如果不是特殊退货，就直接收货
                dailyDeliveryDetail.setState(VehicleState.RECEIVED);
            } else {
                log.info("特殊退货，不再收货，将状态改回特殊退货");
                dailyDeliveryDetail.setState(VehicleState.SPECIAL_RETURN);
            }
            dailyDeliveryDetail.setUnloadState(VehicleState.UnloadState.UNLOAD_COMPLETE);// 卸货状态改成卸货完成
            if (weighDataDTO.getAppearanceWeight() != null) {
                dailyDeliveryDetail.setAppearanceWeight(weighDataDTO.getAppearanceWeight());
            }

            if (dailyDeliveryDetail.getWarehouseId() != null) {
                log.info("-------------确定收货后，根据卸货得到的仓库，进行 入库  开始------------");
                StorageDataInsertDTO storageDataInsertDTO = new StorageDataInsertDTO();
                storageDataInsertDTO.setStorageId(dailyDeliveryDetail.getWarehouseId());
                storageDataInsertDTO.setMaterialId(purchaseOrderDetail.getMaterialId());
                storageDataInsertDTO.setMaterialCode(purchaseOrderDetail.getMaterialCode());
                storageDataInsertDTO.setMaterialName(purchaseOrderDetail.getMaterialName());
                storageDataInsertDTO.setUnit(purchaseOrderDetail.getUom());
                storageDataInsertDTO.setVendorId(purchaseOrder.getVendorId());
                storageDataInsertDTO.setInQuantity(dailyDeliveryDetail.getNetWeight());
                // 入库返回入库批次
                ResultVO<String> resultVO = purchaseProductionClient.addWareHouseData(storageDataInsertDTO);
                if(resultVO.getCode()!=200){
                    throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
                }
                String batch =resultVO.getData();
                log.info("-------------确定收货后，根据卸货得到的仓库，进行 入库  结束------------");
            }

            ResultVO resultVO = purchaseProductionClient.updateTMpdMachiningOrder(dailyDeliveryDetail.getId(), dailyDeliveryDetail.getNetWeight());
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }

            log.info("送货车辆2次过磅完成");
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
        }
    }


    /*
     * @description: 接受过磅数据
     * <AUTHOR>
     * @date 2022/6/24 11:22
     * @param weighDataDTO
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer syncWeighData(WeighDataDTO weighDataDTO) {


        LogDto logDto = new LogDto();
        logDto.setControllerName("地磅调用mes");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular("同步地磅");
        logDto.setLogInvocation("sys");
        logDto.setLocation(weighDataDTO.getDeliveryNumber());
        logDto.setMethodName("地磅");
        logDto.setLogParameter(JSONObject.toJSONString(weighDataDTO));
        int res = 0;
        try {
            log.info("同步过磅数据开始,请求参数:" + JSONObject.toJSONString(weighDataDTO));

            /**
             * 丢糟车辆过磅：
             */
            if ("LZLJDZCL".equals(weighDataDTO.getDeliveryNumber())) {
                if (weighDataDTO.getType().equals("1")) { // 入厂
                    log.info("丢糟车入厂");
                    TMpDailyDeliveryDetail tMpDailyDeliveryDetail = new TMpDailyDeliveryDetail();
                    tMpDailyDeliveryDetail.setDeliveryNumber("LZLJDZCL");
                    tMpDailyDeliveryDetail.setLicensePlateNumber(weighDataDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                    tMpDailyDeliveryDetail.setMaterialName("丢糟");
                    tMpDailyDeliveryDetail.setAdmissionTime(new Date());
                    tMpDailyDeliveryDetail.setCreateTime(new Date());
                    tMpDailyDeliveryDetail.setState(VehicleState.ENTERED);
                    if (StringUtil.isNotEmpty(weighDataDTO.getReceiptPlace())) {
                        tMpDailyDeliveryDetail.setReceiptPlace(weighDataDTO.getReceiptPlace());
                    }

                    logDto.setLogType(1);
                    logCaptureClient.logRecord(logDto);
                    return dailyDeliveryDetailMapper.insert(tMpDailyDeliveryDetail);
                } else if ("2".equals(weighDataDTO.getType())) { // 出厂
                    log.info("丢糟车出厂");
                    LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                            .eq(TMpDailyDeliveryDetail::getDeliveryNumber, weighDataDTO.getDeliveryNumber())
                            .eq(TMpDailyDeliveryDetail::getLicensePlateNumber, weighDataDTO.getLicensePlateNumber())
                            .eq(TMpDailyDeliveryDetail::getState, VehicleState.ENTERED)
                            .eq(TMpDailyDeliveryDetail::getDeleted, 0)
                            .orderByDesc(TMpDailyDeliveryDetail::getCreateTime);

                    // 获取已入厂的丢糟车辆
                    List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
                    if (dailyDeliveryDetails.size() > 0) {
                        TMpDailyDeliveryDetail deliveryDetail = dailyDeliveryDetails.get(0);
                        if (deliveryDetail != null) {
                            deliveryDetail.setGrossWeight(weighDataDTO.getGrossWeight());
                            deliveryDetail.setAppearanceWeight(weighDataDTO.getAppearanceWeight());
                            deliveryDetail.setNetWeight(weighDataDTO.getNetWeight());
                            deliveryDetail.setLeaveNetWeight(weighDataDTO.getNetWeight());
                            deliveryDetail.setAppearanceTime(new Date());
                            deliveryDetail.setState(VehicleState.GO_OUT);
                            if (StringUtil.isNotEmpty(weighDataDTO.getReceiptPlace())) {
                                deliveryDetail.setReceiptPlace(weighDataDTO.getReceiptPlace());
                            }
                            logDto.setLogType(1);
                            logCaptureClient.logRecord(logDto);
                            return dailyDeliveryDetailMapper.updateById(deliveryDetail);
                        }
                    }
                }
            }

            /**
             * 高粱稻壳，小麦送货车辆过磅（杂质车过磅）
             */
            LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                    .eq(TMpDailyDeliveryDetail::getDeliveryNumber, weighDataDTO.getDeliveryNumber())
                    .eq(TMpDailyDeliveryDetail::getLicensePlateNumber, weighDataDTO.getLicensePlateNumber())
                    .eq(TMpDailyDeliveryDetail::getDeleted, 0);
            TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(wrapper);

            if (StringUtil.isNotEmpty(dailyDeliveryDetail)) {
                if ("1".equals(weighDataDTO.getType())) { // 入厂
                    log.info("入厂操作");
                    logDto.setLogType(1);
                    logCaptureClient.logRecord(logDto);
                    if (dailyDeliveryDetail.getGrossWeight() != null && dailyDeliveryDetail.getGrossWeight().compareTo(BigDecimal.ZERO) != 0) {
                        log.info("入场重量(毛重)数据已经存在，本次操作放弃");
                        return 0;
                    }
                    return this.inUpdateVehicle(weighDataDTO, dailyDeliveryDetail);
                } else { // 出厂
                    if (dailyDeliveryDetail.getAppearanceTime() != null) {
                        log.info(dailyDeliveryDetail.getDeliveryNumber() + "车辆已经出场！");
                        return 0;
                    }
                    log.info("出厂操作");
                    logDto.setLogType(1);
                    logCaptureClient.logRecord(logDto);
                    if (dailyDeliveryDetail.getLeaveNetWeight() != null && dailyDeliveryDetail.getLeaveNetWeight().compareTo(BigDecimal.ZERO) != 0) {
                        log.info("离场净重数据已经存在，本次操作放弃");
                        return 0;
                    }
                    return this.outUpdateVehicle(weighDataDTO, dailyDeliveryDetail);
                }
            } else {
                log.info("根据地磅数据查询，未获取到车辆数据");
            }

            /**
             * 原辅料生产过磅称重
             */
            ResultVO<Integer> resultVO = purchaseProductionClient.rawMaterialWeighing(DtoMapper.convert(weighDataDTO, com.hvisions.brewage.purchase.dto.WeighDataDTO.class));
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }
            return resultVO.getData();

        } catch (Exception e) {
            logDto.setLogExceptionMessage(e.getMessage());
            logDto.setLogType(2);
            logCaptureClient.logRecord(logDto);
            //e.printStackTrace();
            log.error("地磅数据出错:" + e.getMessage());
//            throw new BaseKnownException(10000, "地磅数据同步调用失败，原因：" + e.getMessage());
        }
        log.info("接受过磅数据执行完成");
        return res;
    }

    /**
     * @Description 高粱稻壳结束卸货
     *
     * <AUTHOR>
     * @Date 2024-5-11 10:31
     * @param id
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer unloading(Integer id) {
        TMpDailyDeliveryDetail detail = dailyDeliveryDetailMapper.selectById(id);
        if (detail.getState().equals(VehicleState.ALREADY_UNLOADING)) {
            // 如果车辆已经卸货
            throw new BaseKnownException(10000, "车辆已经卸货完成");
        }
        LambdaQueryWrapper<TMpUnloadRecord> wrapper = Wrappers.<TMpUnloadRecord>lambdaQuery()
                .eq(TMpUnloadRecord::getVehicleId, id)
                .eq(TMpUnloadRecord::getState, VehicleState.UnloadState.UNLOADING)
                .eq(TMpUnloadRecord::getDeleted, "0");
        List<TMpUnloadRecord> unloadRecords = unloadRecordMapper.selectList(wrapper);


        if (unloadRecords.size() > 0) {
            TMpUnloadRecord unloadRecord = unloadRecords.get(0);
            unloadRecord.setUpdateTime(new Date());
            unloadRecord.setState(VehicleState.UnloadState.UNLOAD_COMPLETE);
            unloadRecordMapper.updateById(unloadRecord);
        }

        detail.setUnloadState(VehicleState.UnloadState.UNLOAD_COMPLETE);
        if (Integer.parseInt(detail.getState()) < 4) {
            // 车辆状态属于出门前，修改为已卸货
            detail.setState(VehicleState.ALREADY_UNLOADING);
        }
        return dailyDeliveryDetailMapper.updateById(detail);

    }

    /**
     * @Description 高粱稻壳小麦开始卸货，绑定入库筒仓
     *
     * <AUTHOR>
     * @Date 2024-5-9 14:57
     * @param unloadRecordDTO
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer startUnloading(UnloadRecordDTO unloadRecordDTO) {

        /*
         * 高粱稻壳小麦开始卸货
         *      1、判定是否已经卸货，卸货了直接提示已卸货，（根据卸货有无筒仓判定）
         *      2、设置卸货筒仓（小麦绑定入仓任务）
         *      3、生成卸货记录
         *      4、生成入库记录
         */

        log.info("开始卸货筒仓id{}", unloadRecordDTO.getWarehouseId());
        int res = 0;
        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(unloadRecordDTO.getVehicleId());
        if (StringUtil.isNotEmpty(dailyDeliveryDetail.getWarehouseId())) {
            throw new BaseKnownException(10000, "车辆已经卸货，请勿重复卸货");
        }

        // 3、生成卸货记录
        TMpUnloadRecord unloadRecord = new TMpUnloadRecord();
        unloadRecord.setVehicleId(dailyDeliveryDetail.getId());
        unloadRecord.setUnloadPosition(unloadRecordDTO.getUnloadPosition());
        // 车辆收货了，卸货完成，卸货中
        unloadRecord.setState(VehicleState.RECEIVED.equals(dailyDeliveryDetail.getState()) ? VehicleState.UnloadState.UNLOAD_COMPLETE : VehicleState.UnloadState.UNLOADING);
        unloadRecord.setCreateTime(new Date());
        res += unloadRecordMapper.insert(unloadRecord);

        dailyDeliveryDetail.setUnloadState(VehicleState.RECEIVED.equals(dailyDeliveryDetail.getState()) ? VehicleState.UnloadState.UNLOAD_COMPLETE : VehicleState.UnloadState.UNLOADING);
        dailyDeliveryDetail.setWarehouseId(unloadRecordDTO.getWarehouseId());

        //自动绑定小麦卸货筒仓绑定
        if ("3".equals(dailyDeliveryDetail.getType())) {
            Date demandDate = dailyDeliveryDetailMapper.getDemandDateByVehicleId(dailyDeliveryDetail.getId());
            String vendorCode = dailyDeliveryDetailMapper.getVendorCodeByVehicleId(dailyDeliveryDetail.getId());
            WarehouseTaskIdQueryDTO queryDTO = new WarehouseTaskIdQueryDTO();
            queryDTO.setWarehouseId(unloadRecordDTO.getWarehouseId());
            queryDTO.setDemandDate(demandDate);
            ResultVO<Integer> resultVO = warehouseTaskClient.getTaskIdByWarehouseIdAndDate(queryDTO);
//            ResultVO<WarehouseTaskPageDTO> resultVO = warehouseTaskClient.getTaskByWarehouseId(unloadRecordDTO.getWarehouseId());
            log.info("获取入仓任务{}", JSONObject.toJSONString(resultVO));
            if (resultVO.getCode() == 200 && StringUtil.isNotEmpty(resultVO.getData())) {
                // 送货车绑定入仓任务
                dailyDeliveryDetail.setWarehouseTaskId(resultVO.getData());
                WarehouseTaskBatchUpdateDTO warehouseTaskBatchUpdateDTO = new WarehouseTaskBatchUpdateDTO();
                warehouseTaskBatchUpdateDTO.setId(resultVO.getData());
                warehouseTaskBatchUpdateDTO.setVendor(vendorCode);
                warehouseTaskClient.updateWarehouseTaskBatch(warehouseTaskBatchUpdateDTO);
            } else {
                log.info("小麦筒仓绑定失败，不存在入仓任务");
                throw new BaseKnownException(1000, "不存在该筒仓的入厂任务！");
            }
        }

        // 生成卸货记录
        log.info("-------------开始卸货后、生产预处理加工工单  开始------------");
        TMpDeliveryItem item = deliveryItemMapper.selectById(dailyDeliveryDetail.getDeliveryItemId());
        TMpDailyDelivery dailyDelivery = dailyDeliveryMapper.selectById(item.getDeliveryId());
        TMpDemandOrder demandOrder = demandOrderMapper.selectById(dailyDelivery.getDemandId());
        MachiningOrderDTO machiningOrderDTO = new MachiningOrderDTO();
        machiningOrderDTO.setOrderDate(demandOrder.getDemandDate());
        machiningOrderDTO.setSiloId(dailyDeliveryDetail.getWarehouseId());
        machiningOrderDTO.setMaterialId(dailyDelivery.getMaterialId());
        machiningOrderDTO.setMaterialName(dailyDelivery.getMaterialName());
        machiningOrderDTO.setMaterialCode(dailyDelivery.getMaterialCode());
        machiningOrderDTO.setUnit("kg");
        machiningOrderDTO.setMachiningWeight(dailyDeliveryDetail.getEstimatedNumber());
        machiningOrderDTO.setMachiningBuckleWeight(dailyDeliveryDetail.getBuckleWeight());
        machiningOrderDTO.setActualWeight(dailyDeliveryDetail.getNetWeight());
        machiningOrderDTO.setBatch(dailyDeliveryDetail.getBatch());
        machiningOrderDTO.setType("3".equals(dailyDeliveryDetail.getType()) ? "2" : "1");
        machiningOrderDTO.setVehicleId(dailyDeliveryDetail.getId());
        ResultVO<Integer> resultVO = purchaseProductionClient.addMachiningOrder(machiningOrderDTO);
        if(resultVO.getCode()!=200){
            throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
        }
        log.info("-------------开始卸货后、生产预处理加工工单  结束------------");

        res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);

        return res;
    }

    /**
     * @Description 结束卸货，车辆出门时调用
     *
     * <AUTHOR>
     * @Date 2024-6-18 14:46
     * @param vehicleId
     * @return com.hvisions.purchase.purchase.entity.TMpDailyDeliveryDetail
     **/
    @Override
    @Transactional
    public Integer endUnloading(Integer vehicleId) {
        // 结束卸货：将卸货记录改成卸货完成
        LambdaQueryWrapper<TMpUnloadRecord> wrapper = Wrappers.<TMpUnloadRecord>lambdaQuery()
                .eq(TMpUnloadRecord::getVehicleId, vehicleId)
                .eq(TMpUnloadRecord::getState, VehicleState.UnloadState.UNLOADING)
                .eq(TMpUnloadRecord::getDeleted, "0");
        List<TMpUnloadRecord> unloadRecords = unloadRecordMapper.selectList(wrapper);
        if (unloadRecords.size() > 0) {
            TMpUnloadRecord unloadRecord = unloadRecords.get(0);
            unloadRecord.setUpdateTime(new Date());
            unloadRecord.setState(VehicleState.UnloadState.UNLOAD_COMPLETE);
            return unloadRecordMapper.updateById(unloadRecord);
        }
        return 0;
    }

    @Override
    public List<UnloadRecordListDTO> getUnloadRecordList() {
        return unloadRecordMapper.getUnloadRecordList();
    }

    /***
     * @Description 修改出门状态的送货车（毛皮净，送货车/杂质车切换）
     *
     * <AUTHOR>
     * @Date 2022-11-22 14:13
     * @param outVehicleUpdateDTO
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer updateOutVehicle(OutVehicleUpdateDTO outVehicleUpdateDTO) {

        /**
         * 修改出门状态的送货车（毛皮净，送货车/杂质车切换）
         * 1、根据物料名称判断是否切换车的类型
         *      不切换车的类型：
         *          送货车：
         *              修改（毛皮净），修改子项的已到货重量，最终净重不能小于0（离场净重-扣杂）
         *
         *          杂质车：
         *              修改（毛皮净），最终净重修改，
         *              增加，需要均摊扣重，减少，需要将出门的送货车的扣杂减少
         *
         *      切换车的类型：
         *          物料信息修改
         *          送货车变杂质车：
         *              到货数量减少，
         *              如果存在扣杂，将这扣杂+这车净重去均摊扣杂
         *
         *          杂质车变送货车：
         *              其他车的扣杂得加回来，
         *              到货数量累加
         *              发起报检
         */

        int res = 0;

        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(outVehicleUpdateDTO.getId());
        TMpDeliveryItem deliveryItem = deliveryItemMapper.selectById(dailyDeliveryDetail.getDeliveryItemId());

        if (VehicleState.PostingState.ALREADY_POSTING.equals(dailyDeliveryDetail.getPostingState())) {
            throw new BaseKnownException(10000, "车辆已过账，不能修改！");
        }

        dailyDeliveryDetail.setGrossWeight(outVehicleUpdateDTO.getGrossWeight());
        dailyDeliveryDetail.setAppearanceWeight(outVehicleUpdateDTO.getAppearanceWeight());


        // >0 是增加了， <0 是减少了
        BigDecimal leaveDiff = outVehicleUpdateDTO.getLeaveNetWeight().subtract(dailyDeliveryDetail.getLeaveNetWeight());

        //送货车
        if (dailyDeliveryDetail.getMaterialName().equals(outVehicleUpdateDTO.getMaterialName())) {
            // 不切换车的类型
            dailyDeliveryDetail.setLeaveNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());

            if ("0000".equals(dailyDeliveryDetail.getMaterialCode())) {
                // 杂质车
//                if (leaveDiff.compareTo(BigDecimal.ZERO) == 1) {
//                    dailyDeliveryService.averageWeight(leaveDiff, deliveryItem.getId(), 0);
//                } else {
//                    BigDecimal leaveDiffAbs = leaveDiff.abs();
//
//                    LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                            .eq(TMpDailyDeliveryDetail::getDeliveryItemId, deliveryItem.getId())
//                            .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                            .eq(TMpDailyDeliveryDetail::getInspectState, VehicleState.InspectState.QUALIFIED)
//                            .isNotNull(TMpDailyDeliveryDetail::getNetWeight)
//                            .isNotNull(TMpDailyDeliveryDetail::getImpurityWeight)
//                            .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                            .eq(TMpDailyDeliveryDetail::getDeleted, 0);
//                    List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
//                    if (dailyDeliveryDetails.size() > 0) {
//                        for (TMpDailyDeliveryDetail dailyDeliveryDetail1 : dailyDeliveryDetails) {
//                            if (dailyDeliveryDetail1.getImpurityWeight().subtract(leaveDiffAbs).compareTo(BigDecimal.ZERO) > -1) {
//                                dailyDeliveryDetail1.setNetWeight(dailyDeliveryDetail1.getNetWeight().add(leaveDiffAbs));
//                                dailyDeliveryDetail1.setImpurityWeight(dailyDeliveryDetail1.getImpurityWeight().subtract(leaveDiffAbs));
//                                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail1);
//                                leaveDiffAbs = BigDecimal.ZERO;
//                                break;
//                            }
//                            leaveDiffAbs = leaveDiffAbs.subtract(dailyDeliveryDetail1.getImpurityWeight());
//
//                            dailyDeliveryDetail1.setNetWeight(dailyDeliveryDetail1.getNetWeight().add(dailyDeliveryDetail1.getImpurityWeight()));
//                            dailyDeliveryDetail1.setImpurityWeight(BigDecimal.ZERO);
//                            res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail1);
//                        }
//                        if (leaveDiffAbs.compareTo(BigDecimal.ZERO) == 1) {
//                            throw new BaseKnownException(10000, "扣杂无法冲销");
//                        }
//                    }
//
//                }
                dailyDeliveryDetail.setNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());
                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);


            } else {
                // 送货车
                deliveryItem.setArrivalWeight(deliveryItem.getArrivalWeight().add(leaveDiff));
                if (dailyDeliveryDetail.getNetWeight().add(leaveDiff).compareTo(BigDecimal.ZERO) > -1) {
                    dailyDeliveryDetail.setNetWeight(dailyDeliveryDetail.getNetWeight().add(leaveDiff));
                } else {
                    throw new BaseKnownException(10000, "扣杂不足冲销");
                }
                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
                res += deliveryItemMapper.updateById(deliveryItem);

            }


        } else {
            // 切换车的类型

            if ("0000".equals(outVehicleUpdateDTO.getMaterialCode())) {
                // 送货车变成杂质车

                deliveryItem.setArrivalWeight(deliveryItem.getArrivalWeight().subtract(dailyDeliveryDetail.getLeaveNetWeight()));
                res += deliveryItemMapper.updateById(deliveryItem);

//                BigDecimal weight = StringUtil.isEmpty(dailyDeliveryDetail.getImpurityWeight()) ? outVehicleUpdateDTO.getLeaveNetWeight() : outVehicleUpdateDTO.getLeaveNetWeight().add(dailyDeliveryDetail.getImpurityWeight());

//                dailyDeliveryService.averageWeight(weight, deliveryItem.getId(), dailyDeliveryDetail.getId());

//                dailyDeliveryDetail.setImpurityWeight(BigDecimal.ZERO);
                dailyDeliveryDetail.setLeaveNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());
                dailyDeliveryDetail.setNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());
                dailyDeliveryDetail.setMaterialId(null);
                dailyDeliveryDetail.setMaterialCode(outVehicleUpdateDTO.getMaterialCode());
                dailyDeliveryDetail.setMaterialName(outVehicleUpdateDTO.getMaterialName());
                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);


            } else {
                // 杂质车变成送货车

                log.info("扣杂冲销开始");
//                LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                        .eq(TMpDailyDeliveryDetail::getDeliveryItemId, dailyDeliveryDetail.getDeliveryItemId())
////                        .eq(TMpDailyDeliveryDetail::getState, VehicleState.GO_OUT)
//                        .isNotNull(TMpDailyDeliveryDetail::getImpurityWeight)
//                        .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                        .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                        .eq(TMpDailyDeliveryDetail::getDeleted, 0);
//                List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
//                BigDecimal weight = dailyDeliveryDetail.getNetWeight();
//
//                // 冲销扣杂
//                for (TMpDailyDeliveryDetail detail : dailyDeliveryDetails) {
//                    //剩余量
//                    BigDecimal subtract = weight.subtract(detail.getImpurityWeight());
//
//                    if (subtract.compareTo(BigDecimal.ZERO) == 1) {
//                        //这里本来扣杂的加回来操作
//                        detail.setNetWeight(detail.getNetWeight().add(detail.getImpurityWeight()));
//                        detail.setImpurityWeight(BigDecimal.ZERO);
//                        weight = subtract;
//
//                    } else {
//                        //一下就扣完了
//                        detail.setNetWeight(detail.getNetWeight().add(weight));
//                        detail.setImpurityWeight(detail.getImpurityWeight().subtract(weight));
//                        weight = BigDecimal.ZERO;
//                    }
//                    res += dailyDeliveryDetailMapper.updateById(detail);
//                    if (subtract.compareTo(BigDecimal.ZERO) < 1) {
//                        break;
//                    }
//                }
//                //扣去的杂加不回来了
//                if (weight.compareTo(BigDecimal.ZERO) == 1) {
//                    throw new BaseKnownException(10000, "扣杂不够冲销");
//                }
                log.info("扣杂冲销结束");

                dailyDeliveryDetail.setLeaveNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());
                dailyDeliveryDetail.setNetWeight(outVehicleUpdateDTO.getLeaveNetWeight());
                dailyDeliveryDetail.setMaterialId(outVehicleUpdateDTO.getMaterialId());
                dailyDeliveryDetail.setMaterialCode(outVehicleUpdateDTO.getMaterialCode());
                dailyDeliveryDetail.setMaterialName(outVehicleUpdateDTO.getMaterialName());
                res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);

                deliveryItem.setArrivalWeight(deliveryItem.getArrivalWeight().add(outVehicleUpdateDTO.getLeaveNetWeight()));
                res += deliveryItemMapper.updateById(deliveryItem);

            }
        }

        return res;
    }

    /*
     * @Description: 打印状态修改
     *
     * <AUTHOR>
     * @param id: 送货车辆id
     * @return java.lang.Integer
     */
    @Override
    public Integer updatePrintState(Integer id) {
        TMpDailyDeliveryDetail detail = new TMpDailyDeliveryDetail();
        detail.setId(id);
        detail.setPrint("1");
        return dailyDeliveryDetailMapper.updateById(detail);
    }

    @Override
    public List<VehicleBatchDTO> getVehicleByBatch(String batch) {
        return dailyDeliveryDetailMapper.getVehicleByBatch(batch);
    }

    @Override
    public DailyDeliveryDetailInfoDTO getDeliveryCarInfo(String deliveryNumber) {
        return dailyDeliveryDetailMapper.getDeliveryCarInfo(deliveryNumber);
    }

    /**
     * @Description 修改车辆状态
     *
     * <AUTHOR>
     * @Date 2023-12-15 10:15
     * @param vehicleStateUpdateDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer updateVehicleState(VehicleStateUpdateDTO vehicleStateUpdateDTO) {
        int res = 0;
        LambdaQueryWrapper<TMpDailyDeliveryDetail> queryWrapper = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                .eq(TMpDailyDeliveryDetail::getDeliveryNumber, vehicleStateUpdateDTO.getDeliveryNumber())
                .eq(TMpDailyDeliveryDetail::getDeleted, "0");
        TMpDailyDeliveryDetail detail = dailyDeliveryDetailMapper.selectOne(queryWrapper);
        if (StringUtil.isNotEmpty(detail)) {
            if (VehicleState.CAN_UNLOADING.equals(vehicleStateUpdateDTO.getVehicleState())) {
                // 车辆变成可卸货2
                if (detail.getState().equals(VehicleState.ENTERED) || detail.getState().equals(VehicleState.REINSPECT)) {
                    // 如果车辆入厂或者 车辆复检中
                    detail.setState(VehicleState.CAN_UNLOADING);
                }
                detail.setInspectState(VehicleState.InspectState.QUALIFIED);
            } else if (VehicleState.WAIT_HANDLE.equals(vehicleStateUpdateDTO.getVehicleState())) {
                // 车辆变成待处理5
                // 质检不合格后，不合格审批
                detail.setState(VehicleState.WAIT_HANDLE);
                detail.setInspectState(VehicleState.InspectState.UNQUALIFIED);
            } else if (VehicleState.REJECTION.equals(vehicleStateUpdateDTO.getVehicleState())) {
                // 车辆变成拒收6
                // 不合格处理拒收操作
                detail.setState(VehicleState.REJECTION);

                // 添加到黑名单
                res += vehicleBlacklistService.addVehicleBlacklist(detail, vehicleStateUpdateDTO.getInspectionOrder(), false);
            } else if (VehicleState.REINSPECT.equals(vehicleStateUpdateDTO.getVehicleState())) {
                // 车辆变成复检7
                // 不合格处理复检操作
                if (VehicleState.GO_OUT.equals(detail.getState())) {
                    // 车辆已经出门，不能复检
                    throw new BaseKnownException(10000, "送货车辆已经出厂，不能复检");
                }
                detail.setState(VehicleState.REINSPECT);
                detail.setInspectState(VehicleState.InspectState.WAIT_INSPECT);

                ResultVO<String> resultVO = incomingInspectionClient.reInspection(vehicleStateUpdateDTO.getInspectionId());
                if (resultVO.getCode() != 200 || StringUtil.isEmpty(resultVO.getData())) {
                    throw new BaseKnownException(10000, "复检失败");
                }

            }
            detail.setUpdateTime(new Date());
            res += dailyDeliveryDetailMapper.updateById(detail);
        } else {
            throw new BaseKnownException(10000, "不存在送货单号，请排查原有");
        }
        return res;
    }

    /**
     * @Description 修改车辆检验状态
     *
     * <AUTHOR>
     * @Date 2023-12-15 10:15
     * @param vehicleInspectStateUpdateDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer updateVehicleInspectState(VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO) {
        int res = 0;
        LambdaQueryWrapper<TMpDailyDeliveryDetail> queryWrapper = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                .eq(TMpDailyDeliveryDetail::getDeliveryNumber, vehicleInspectStateUpdateDTO.getDeliveryNumber())
                .eq(TMpDailyDeliveryDetail::getDeleted, "0")
                .last("limit 1");
        TMpDailyDeliveryDetail detail = dailyDeliveryDetailMapper.selectOne(queryWrapper);
        if (StringUtil.isNotEmpty(detail)) {

            if (VehicleState.InspectState.INSPECTING.equals(vehicleInspectStateUpdateDTO.getVehicleInspectState())) {
                // 变成质检中1
                if (VehicleState.InspectState.WAIT_INSPECT.equals(detail.getInspectState())) {
                    // 只有待检验才能变成质检中
                    detail.setInspectState(VehicleState.InspectState.INSPECTING);
                }
            }
            res += dailyDeliveryDetailMapper.updateById(detail);
        } else {
            throw new BaseKnownException(10000, "不存在送货单号，请排查原有");
        }
        return res;
    }


    /**
     * @Description 计算扣重
     *
     * <AUTHOR>
     * @Date 2024-6-18 17:03
     * @param deliveryNumber 送货单号
     * @param materialCode 物料编码
     * @param netWeight 离场净重
     * @return java.math.BigDecimal
     **/
    @Override
    public BigDecimal calculateVehicleBuckleWeight(String deliveryNumber, String materialCode, BigDecimal netWeight) {

        log.info("扣重开始=========");
        BigDecimal buckleWeight = new BigDecimal(0);
        BigDecimal weight = netWeight;

        // 有机物料不参与扣重
        if (MaterialConst.LQM1.equals(materialCode)
                || MaterialConst.LGL1.equals(materialCode)
                || MaterialConst.LGL2.equals(materialCode)
        ) {
            return buckleWeight;
        }

        // 获取扣重指标
        ResultVO<IncomingBuckleWeightDTO> resultVO = incomingInspectionClient.getIncomingBuckleWeightData(deliveryNumber);
        if (resultVO.getCode() != 200) {
            return buckleWeight;
        }
        IncomingBuckleWeightDTO incomingBuckleWeightData = resultVO.getData();

        // 扣重指标为空，或者容重不合格、或者杂质指标不合格，不扣重、或者水分大于上限、或者不完善粒大于上限
        if (StringUtil.isEmpty(incomingBuckleWeightData)
                || StringUtil.isNotEmpty(incomingBuckleWeightData.getWeightValue())
                || StringUtil.isNotEmpty(incomingBuckleWeightData.getImpurityValue())
                || StringUtil.isEmpty(incomingBuckleWeightData.getWaterValue())
                || StringUtil.isEmpty(incomingBuckleWeightData.getGrainValue())
        ) {
            return buckleWeight;
        }

        // 1、根据指标扣重逻辑
        log.info("扣重开始，计算扣重开始=========");
        BigDecimal waterValue = StringUtil.isEmpty(incomingBuckleWeightData.getWaterValue()) ? new BigDecimal(0) : new BigDecimal(incomingBuckleWeightData.getWaterValue());
        BigDecimal grainValue = StringUtil.isEmpty(incomingBuckleWeightData.getGrainValue()) ? new BigDecimal(0) : new BigDecimal(incomingBuckleWeightData.getGrainValue());

        BigDecimal water = BigDecimal.valueOf(-1); // 水分折扣
        BigDecimal grain = BigDecimal.valueOf(-1); // 不完善粒折扣
        // 普通小麦扣重计算
        if (MaterialConst.LQM2.equals(materialCode)) {
            water = waterValue.subtract(BigDecimal.valueOf(12.5)).divide(BigDecimal.valueOf(100));
            grain = grainValue.subtract(BigDecimal.valueOf(6)).divide(BigDecimal.valueOf(100));
        } else if (MaterialConst.LGL3.equals(materialCode) || MaterialConst.LGL4.equals(materialCode)) {
            // 高粱扣重计算
            // 水分折扣 (水分指标值 - 14)/100 /( 0.1 * 0.15)
            water = waterValue.subtract(new BigDecimal(14)).divide(new BigDecimal(100)).divide(new BigDecimal(Double.toString(0.1))).multiply(new BigDecimal(Double.toString(0.15)));
            // 不完善粒 - 3 / 0.1 * 0.05
            grain = grainValue.subtract(new BigDecimal(3)).divide(new BigDecimal(100)).divide(new BigDecimal(Double.toString(0.1))).multiply(new BigDecimal(Double.toString(0.05)));

        }

        // 计算扣重：折扣和*过磅净重
        BigDecimal zero = new BigDecimal(0);
        if (water.compareTo(zero) > -1 && grain.compareTo(zero) > -1) { // 水分和不完善粒>=0
            buckleWeight = water.add(grain).multiply(weight);
        }
        if (water.compareTo(zero) < 0 && grain.compareTo(zero) > -1) { // 水分《0 不完善粒>=0
            buckleWeight = grain.multiply(weight);
        }
        if (grain.compareTo(zero) < 0 && water.compareTo(zero) > -1) {// 水分 >=0 不完善粒<0
            buckleWeight = water.multiply(weight);
        }
        log.info("扣重结束，扣重重量为========={}", buckleWeight);
        return buckleWeight;
    }

    /**
     * 查询丢糟周报表
     * @param dto
     * @return
     */
    @Override
    public DzWeeklyReportDTO getDzWeeklyReport(DzWeeklyReportQueryDTO dto) {
        DzWeeklyReportDTO reportDTO = new DzWeeklyReportDTO();

        List<DzWeeklyReportDetailDTO> detailDTOList=dailyDeliveryDetailMapper.getDzWeeklyReport(dto);
        if(!CollectionUtils.isEmpty(detailDTOList)){
            BigDecimal weighTotalNum = detailDTOList.stream().filter(item -> null != item.getNetWeight()).map(DzWeeklyReportDetailDTO::getNetWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            reportDTO.setWeighTotalNum(weighTotalNum.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
            reportDTO.setDtoList(detailDTOList);
            //设置接收单位信息
            for (DzWeeklyReportDetailDTO dzWeeklyReportDetailDTO : detailDTOList) {
                TMpAcceptFirm tMpAcceptFirm = acceptFirmMapper.selectOne(new LambdaUpdateWrapper<TMpAcceptFirm>().eq(TMpAcceptFirm::getFirm, dzWeeklyReportDetailDTO.getReceiptPlace()).orderByDesc(TMpAcceptFirm::getId).last("limit 1"));
                if (tMpAcceptFirm != null) {
                    BeanUtils.copyProperties(tMpAcceptFirm, dzWeeklyReportDetailDTO);
                }
                dzWeeklyReportDetailDTO.setNetWeight(dzWeeklyReportDetailDTO.getNetWeight().divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
            }
        }
        return reportDTO;
    }

    /**
     * 查询丢糟周报表
     * @param dto
     * @return
     */
    @Override
    public DzQuarterReportDTO getDzQuarterReport(DzQuarterReportQueryDTO dto) {
        DzQuarterReportDTO reportDTO = new DzQuarterReportDTO();

        //分月台账
        List<DzMonthlyLedgerDTO> ledgerDTOList=dailyDeliveryDetailMapper.getDzQuarterReport(dto);
        //单位转换
        for (DzMonthlyLedgerDTO dzMonthlyLedgerDTO : ledgerDTOList) {
            dzMonthlyLedgerDTO.setNetWeight(dzMonthlyLedgerDTO.getNetWeight().divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
        }
        reportDTO.setMonthlyLedgerDTOList(ledgerDTOList);

        //分单位台账
        List<DzUnitLedgerDTO> unitLedgerDTOList=dailyDeliveryDetailMapper.getDzQuarterUnitLedgerReport(dto);
        //单位转换
        for (DzUnitLedgerDTO dzUnitLedgerDTO : unitLedgerDTOList) {
            dzUnitLedgerDTO.setNetWeight(dzUnitLedgerDTO.getNetWeight().divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
        }
        reportDTO.setUnitLedgerDTOList(unitLedgerDTOList);
        return reportDTO;
    }

    /**
     * 查询丢糟日表
     * @param dto
     * @return
     */
    @Override
    public List<DzSunReportDTO> getDzSunReport(DzSunReportQueryDTO dto) {
        List<DzSunReportDTO> sunReportDTOList = new ArrayList<>();

        List<DzSunReportDetailDTO> ledgerDTOList=dailyDeliveryDetailMapper.getDzSunReport(dto);
        if(!CollectionUtils.isEmpty(ledgerDTOList)){
            Map<String, List<DzSunReportDetailDTO>> collect = ledgerDTOList.stream().collect(Collectors.groupingBy(DzSunReportDetailDTO::getReceiptPlace));
            collect.forEach((k,v)->{
                DzSunReportDTO reportDTO =new DzSunReportDTO();
                reportDTO.setReceiptPlace(k);
                reportDTO.setDetailDTOList(v);
                sunReportDTOList.add(reportDTO);
            });
        }
        return sunReportDTOList;
    }

    /**
     * 退杂申请
     * @param impurityAuditDTO
     * @return
     */
    @Override
    public Boolean impurityAudit(ImpurityAuditDTO impurityAuditDTO) {
        TMpDeliveryItem tMpDeliveryItem = deliveryItemMapper.selectById(impurityAuditDTO.getDeliveryItemId());
        if (tMpDeliveryItem == null) {
            throw new BaseKnownException(10011, "申请数据异常");
        }
        if (tMpDeliveryItem.getInboundWeight() == null || tMpDeliveryItem.getInboundWeight().compareTo(BigDecimal.ZERO) == 0) {
            throw new BaseKnownException(10011, "当前收货重量小于零，不支持扣杂。");
        } else if (tMpDeliveryItem.getInboundWeight().compareTo(tMpDeliveryItem.getPostingQuantity()) <= 0) {
            throw new BaseKnownException(10011, "当前收货重量已经过账，不支持扣杂。");
        }
        //创建杂质车数据
        TMpDailyDeliveryDetail detail = new TMpDailyDeliveryDetail();
        detail.setDeliveryNumber(generateCodeUtil.generateInspectionCode("SH", 3));
        detail.setDeliveryItemId(tMpDeliveryItem.getId());
        detail.setState("0");
        detail.setImpurityType(impurityAuditDTO.getImpurityType());
        detail.setMaterialCode("0000");
        //获取其他车的物料来设置物料名字
        TMpDailyDeliveryDetail tMpDailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDeliveryDetail>()
                .eq(TMpDailyDeliveryDetail::getDeliveryItemId, tMpDeliveryItem.getId())
                .eq(TMpDailyDeliveryDetail::getDeleted, false)
                .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
                .orderByAsc(TMpDailyDeliveryDetail::getId).last("limit 1"));
        if (tMpDailyDeliveryDetail != null) {
            detail.setMaterialName(tMpDailyDeliveryDetail.getMaterialName() + "杂质");
        }
        TMpDailyDelivery tMpDailyDelivery = dailyDeliveryMapper.selectById(tMpDeliveryItem.getDeliveryId());
        //发送退杂申请到srm
        SrmImpurityAuditDto srmImpurityAuditDto = new SrmImpurityAuditDto();
        srmImpurityAuditDto.setDailyOrderPlanNum(tMpDailyDelivery.getPlanNumber());
        srmImpurityAuditDto.setReturnImpurityApply(detail.getDeliveryNumber());
        srmImpurityAuditDto.setReturnImpurityDateTime(DateUtil.formatDate(new Date()));
        srmImpurityAuditDto.setReturnImpurityType(impurityAuditDTO.getImpurityType());
        srmImpurityAuditDto.setStatus("pending");
        srmService.sendImpurityAudit(srmImpurityAuditDto);
        return dailyDeliveryDetailMapper.insert(detail) > 0;
    }

    /**
     * 更改车辆信息-数据补录
     * @param vehicleDataUpdateDTO
     * @return
     */
    @Override
    public Boolean updateVehicleData(VehicleDataUpdateDTO vehicleDataUpdateDTO) {
        TMpDailyDeliveryDetail update = new TMpDailyDeliveryDetail();
        update.setNetWeight(vehicleDataUpdateDTO.getLeaveNetWeight());
        update.setUpdateTime(new Date());
        BeanUtils.copyProperties(vehicleDataUpdateDTO, update);
        return dailyDeliveryDetailMapper.updateById(update) > 0;
    }

}
