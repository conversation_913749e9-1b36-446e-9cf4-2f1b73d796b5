package com.hvisions.purchase.purchase.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageQueryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryRemarkDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailCreateDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.SpecialReturnsDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.InboundTaskDeliveryListDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.InboundTaskPageQueryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.UpdateInspectionResultDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemDetailDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemDetailQueryDTO;
import com.hvisions.purchase.dto.purchase.receiving.BuckleHandleDTO;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:日送货计划
 * @date 2022/4/6 10:18
 */
public interface DailyDeliveryService {


    /*
     * @description: 分页查询日送货计划
     * <AUTHOR>
     * @date 2022/4/6 15:12
     * @param dailyDeliveryPageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageDTO>
     */
    Page<DailyDeliveryPageDTO> getDailyDeliveryPageList(DailyDeliveryPageQueryDTO dailyDeliveryPageQueryDTO);

    /*
     * @description: 批量新增日送货计划
     * <AUTHOR>
     * @date 2022/4/6 15:04
     * @param dailyDeliveryDTOList
     * @return java.lang.Integer
     */
    Integer batchInsertDailyDelivery(List<DailyDeliveryDTO> dailyDeliveryDTOList);

    /*
     * @description: 批量新增送货车辆
     * <AUTHOR>
     * @date 2022/4/11 11:01
     * @param detailCreateDTOS
     * @param itemId
     * @return java.lang.Integer
     */
    Integer batchInsertDailyDeliveryDetail(List<DailyDeliveryDetailCreateDTO> detailCreateDTOS, Integer itemId, Integer departmentId, String materialType);

    Integer batchInsertImpurityDailyDeliveryDetail(List<DailyDeliveryDetailCreateDTO> detailCreateDTOS, Integer itemId, String materialType);

    Integer addRemark(DailyDeliveryRemarkDTO remarkDTO);

    /*
     * @description: 送货车辆导入模板下载
     * <AUTHOR>
     * @date 2022/4/11 11:07
     * @param
     * @return com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
     */
    ResultVO<ExcelExportDto> getDailyDeliveryDetailImportTemplate() throws IOException, IllegalAccessException;

    /*
     * @Description: 特殊退货
     *
     * <AUTHOR>
     * @param id: 车辆id
     * @return java.lang.Integer
     */
    Integer specialReturns(SpecialReturnsDTO specialReturnsDTO);

    /*
     * @description: 处理扣重
     * <AUTHOR>
     * @date 2022/6/24 13:34
     * @param buckleHandleDTO
     * @return java.lang.Integer
     */
    Integer handleBuckle(BuckleHandleDTO buckleHandleDTO);


    /*
     * @description: 车辆调整行号
     * <AUTHOR>
     * @date 2022/7/6 15:22
     * @param vehicleId
     * @param itemId
     * @return java.lang.Integer
     */
    Integer changeLine(Integer vehicleId, Integer itemId);

//    /***
//     * @Description 均摊扣扣杂
//     *
//     * <AUTHOR>
//     * @Date 2022-10-26 14:10
//     * @param weight 扣杂总重
//     * @param destItemId 目标子项
//     * @param detailId  调整的车
//     * @return void
//     **/
//    void averageWeight(BigDecimal weight, Integer destItemId, Integer detailId);

    List<DeliveryItemDetailDTO> getDeliveryItemList(DeliveryItemDetailQueryDTO queryDTO);

    void purchaseSendOutByDetailId(String id,String materialType);

    List<InboundTaskDeliveryListDTO> getDailyDeliveryDetailByInboundId(InboundTaskPageQueryDTO inboundTaskPageQueryDTO);

    /**
     * 更新车辆的质检结论
     *
     * @param updateInspectionResultDTO
     * @return
     */
    Boolean updateInspectionResult(UpdateInspectionResultDTO updateInspectionResultDTO);
}
