package com.hvisions.purchase.purchase.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.common.utils.RedissonLockUtil;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO;
import com.hvisions.powder.client.AvsClient;
import com.hvisions.powder.dto.avs.AvsBaseResponseDto;
import com.hvisions.powder.dto.avs.VehicleSendOutDTO;
import com.hvisions.purchase.dto.SysBaseDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageQueryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryRemarkDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.*;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.InboundTaskDeliveryListDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.InboundTaskPageQueryDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.inbound.UpdateInspectionResultDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemDetailDTO;
import com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemDetailQueryDTO;
import com.hvisions.purchase.dto.purchase.receiving.BuckleHandleDTO;
import com.hvisions.purchase.dto.purchase.vendor.VendorPageDTO;
import com.hvisions.purchase.production.consts.MaterialType;
import com.hvisions.purchase.purchase.consts.DailyDeliveryState;
import com.hvisions.purchase.purchase.consts.DemandOrderState;
import com.hvisions.purchase.purchase.consts.VehicleState;
import com.hvisions.purchase.purchase.dao.*;
import com.hvisions.purchase.purchase.entity.*;
import com.hvisions.purchase.purchase.service.DailyDeliveryService;
import com.hvisions.purchase.purchase.service.VehicleInspectService;
import com.hvisions.purchase.purchase.service.WheatService;
import com.hvisions.purchase.sap.service.SapService;
import com.hvisions.purchase.utils.DateUtil;
import com.hvisions.purchase.utils.GenerateCodeUtil;
import com.hvisions.purchase.utils.StringUtil;
import com.hvisions.quality.client.InspectionTaskClient;
import com.hvisions.quality.client.SamplingClient;
import com.hvisions.quality.dto.quality.inspection.task.InspectionTaskInfoDTO;
import com.hvisions.quality.dto.quality.sampling.SamplingDetailDTO;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:日送货计划
 * @date 2022/4/6 10:18
 */
@Slf4j
@Service
@EnableScheduling
public class DailyDeliveryServiceImpl implements DailyDeliveryService {


    @Resource
    private InspectionTaskClient inspectionTaskClient;

    @Resource
    private SamplingClient samplingClient;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    DailyDeliveryMapper dailyDeliveryMapper;

    @Resource
    DailyDeliveryDetailMapper dailyDeliveryDetailMapper;

    @Resource
    VehicleBlacklistMapper vehicleBlacklistMapper;

    @Resource
    DemandOrderMapper demandOrderMapper;

    @Resource
    PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Resource
    DeliveryItemMapper deliveryItemMapper;

    @Resource
    MaterialClient materialClient;

    @Resource
    private SapService sapService;

    @Resource
    private UnloadRecordMapper unloadRecordMapper;

    @Resource
    private MaterialMapper materialMapper;

    @Resource
    private AvsClient avsClient;

    @Resource
    private WheatService wheatService;

    @Resource
    private VehicleInspectService vehicleInspectService;

    public DailyDeliveryServiceImpl() {
    }


    /*
     * @description: 分页查询日送货计划
     * <AUTHOR>
     * @date 2022/4/6 15:12
     * @param dailyDeliveryPageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageDTO>
     */
    @Override
    public Page<DailyDeliveryPageDTO> getDailyDeliveryPageList(DailyDeliveryPageQueryDTO dailyDeliveryPageQueryDTO) {
        return PageHelperUtil.getPage(dailyDeliveryMapper::getDailyDeliveryPageList, dailyDeliveryPageQueryDTO, DailyDeliveryPageDTO.class);
    }

    /*
     * @description: 批量新增日送货计划
     * <AUTHOR>
     * @date 2022/4/6 15:04
     * @param dailyDeliveryDTOList
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer batchInsertDailyDelivery(List<DailyDeliveryDTO> dailyDeliveryDTOList) {
        int res = 0;
        if (StringUtil.isNotEmpty(dailyDeliveryDTOList) && dailyDeliveryDTOList.size() > 0) {
            // 根据需求单id获取所有日送货计划，删除对应的日送货计划
            LambdaQueryWrapper<TMpDailyDelivery> dailyDeliveryWrapper = Wrappers.<TMpDailyDelivery>lambdaQuery()
                    .eq(TMpDailyDelivery::getDemandId, dailyDeliveryDTOList.get(0).getDemandId())
                    .eq(TMpDailyDelivery::getDeleted, 0);
            List<TMpDailyDelivery> dailyDeliverys = dailyDeliveryMapper.selectList(dailyDeliveryWrapper);

            // 循环校验删除取消关联的日送货计划
            dailyDeliverys.forEach(item -> {
                List<DailyDeliveryDTO> collect = dailyDeliveryDTOList.stream()
                        .filter(items -> item.getPurchaseId().intValue() == items.getPurchaseId().intValue())
                        .collect(Collectors.toList());
                if (collect.size() == 0) {
                    // 检验日送货计划是否在执行中，不然无法删除
                    if (DemandOrderState.EXECUTING.equals(item.getState())) {
                        TMpPurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(item.getPurchaseId());
                        throw new BaseKnownException(10000, "采购单【" + purchaseOrder.getOrderNo() + "】已存在车辆，无法取消关联");
                    }
                    List<TMpDeliveryItem> deliveryItems = deliveryItemMapper.selectList(Wrappers.<TMpDeliveryItem>lambdaQuery()
                            .eq(TMpDeliveryItem::getDeliveryId, item.getId())
                            .eq(TMpDeliveryItem::getDeleted, "0"));
                    if (StringUtil.isNotEmpty(deliveryItems) && deliveryItems.size() > 0) {
                        deliveryItems.forEach(deliveryItem -> {
                            List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
                                    .eq(TMpDailyDeliveryDetail::getDeliveryItemId, deliveryItem.getId())
                                    .eq(TMpDailyDeliveryDetail::getDeleted, "0"));
                            if (StringUtil.isNotEmpty(dailyDeliveryDetails) && dailyDeliveryDetails.size() > 0) {
                                throw new BaseKnownException(10000, "日送货计划【" + item.getPlanNumber() + "】已存在车辆，无法取消关联");
                            }
                        });
                    }
                    dailyDeliveryMapper.deleteById(item.getId());
                }
            });

            // 要或需求单状态变成执行中
            TMpDemandOrder demandOrder = new TMpDemandOrder();
            demandOrder.setId(dailyDeliveryDTOList.get(0).getDemandId());
            demandOrder.setState(DemandOrderState.EXECUTING);
            res += demandOrderMapper.updateById(demandOrder);
            for (DailyDeliveryDTO dailyDeliveryDTO : dailyDeliveryDTOList) {
                // 通过采购单id，获取对应的日送货计划
                List<TMpDailyDelivery> collect = dailyDeliverys.stream()
                        .filter(item -> item.getPurchaseId().intValue() == dailyDeliveryDTO.getPurchaseId().intValue())
                        .collect(Collectors.toList());
                // 新增日送货计划
                TMpDailyDelivery dailyDelivery = DtoMapper.convert(dailyDeliveryDTO, TMpDailyDelivery.class);
                dailyDelivery.setFeedType(demandOrder.getFeedType());
                if (collect.size() > 0) {
                    dailyDelivery.setId(collect.get(0).getId());
                    dailyDelivery.setUpdateTime(new Date());
                    res += dailyDeliveryMapper.updateById(dailyDelivery);
                } else {
                    dailyDelivery.setPlanNumber(generateCodeUtil.generateInspectionCode("RJH", 3));
                    dailyDelivery.setState(DailyDeliveryState.NEWLY_BUILD);
                    dailyDelivery.setCreateTime(new Date());
                    dailyDelivery.setUpdateTime(new Date());
                    res += dailyDeliveryMapper.insert(dailyDelivery);
                }

                // 获取采购单详情，新增日送货计划详情
                LambdaQueryWrapper<TMpPurchaseOrderDetail> wrapper = Wrappers.<TMpPurchaseOrderDetail>lambdaQuery()
                        .eq(TMpPurchaseOrderDetail::getOrderId, dailyDeliveryDTO.getPurchaseId())
                        .eq(TMpPurchaseOrderDetail::getDeleted, 0)
                        .eq(TMpPurchaseOrderDetail::getIsNew, "1");
                List<TMpPurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMapper.selectList(wrapper);

                if (StringUtil.isNotEmpty(purchaseOrderDetails) && purchaseOrderDetails.size() > 0 && collect.size() == 0) {

                    // 新增送货计划子项
                    for (TMpPurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetails) {
                        TMpDeliveryItem deliveryItem = new TMpDeliveryItem();
                        deliveryItem.setDeliveryId(dailyDelivery.getId());
                        deliveryItem.setOrderDetailId(purchaseOrderDetail.getId());
                        res += deliveryItemMapper.insert(deliveryItem);
                    }
                }
            }
        }
        return res;
    }

    /**
     * @param detailCreateDTOS
     * @param itemId
     * @return java.lang.Integer
     * @Description 批量新增修改杂质车辆
     * <AUTHOR>
     * @Date 2022-8-22 10:08
     **/
    @Override
    @Transactional
    public Integer batchInsertImpurityDailyDeliveryDetail(List<DailyDeliveryDetailCreateDTO> detailCreateDTOS, Integer itemId, String materialType) {

        int res = 0;
        if (itemId > 0) {

            if (detailCreateDTOS.size() > 0) {

                List<SyncInspectionAndAvsDTO> weightList = new ArrayList<>();

                for (DailyDeliveryDetailCreateDTO detailCreateDTO : detailCreateDTOS) {

                    if ("0".equals(detailCreateDTO.getOperateState())) {
                        // 新增杂质车辆
//                        Integer receiveVehicleNum = deliveryItemMapper.getReceiveVehicleNum(itemId);
//                        if (receiveVehicleNum == 0){
//                            throw new BaseKnownException(10000, "不存在已经收货车辆，无法创建杂质送货车！");
//                        }
                        // 1、校验车辆黑名单
//                        LambdaQueryWrapper<TMpVehicleBlacklist> vehicleBlacklistLambdaQueryWrapper = Wrappers.<TMpVehicleBlacklist>lambdaQuery()
//                                .eq(TMpVehicleBlacklist::getLicensePlateNumber, detailCreateDTO.getLicensePlateNumber())
//                                .eq(TMpVehicleBlacklist::getDeleted, 0)
//                                .eq(TMpVehicleBlacklist::getState, "0");
//                        List<TMpVehicleBlacklist> vehicleBlackList = vehicleBlacklistMapper.selectList(vehicleBlacklistLambdaQueryWrapper);
//                        if (StringUtil.isNotEmpty(vehicleBlackList) && vehicleBlackList.size() > 0) {
//                            throw new BaseKnownException(10000, "车牌为" + detailCreateDTO.getLicensePlateNumber() + "是黑名单车辆，不能创建！");
//                        }

                        // 2、新增杂质车辆
                        TMpDailyDeliveryDetail dailyDeliveryDetail = DtoMapper.convert(detailCreateDTO, TMpDailyDeliveryDetail.class);
                        dailyDeliveryDetail.setDeliveryItemId(itemId);
                        dailyDeliveryDetail.setDeliveryNumber(generateCodeUtil.generateInspectionCode("SH", 3));
                        dailyDeliveryDetail.setBatch(generateCodeUtil.generateInspectionCode("PC", 3));
                        dailyDeliveryDetail.setCreateTime(new Date());
                        dailyDeliveryDetail.setPostingState(VehicleState.PostingState.WAIT_POSTING);
                        dailyDeliveryDetail.setState(VehicleState.WAIT_ENTERED);
                        dailyDeliveryDetail.setInspectState(VehicleState.InspectState.WAIT_INSPECT);
                        if (MaterialType.SORGHUM.equals(materialType)) {
                            dailyDeliveryDetail.setType("1"); // 高粱杂质车
                        } else if (MaterialType.BRAN.equals(materialType)) {
                            dailyDeliveryDetail.setType("2"); // 稻壳杂质车
                        } else if (MaterialType.WHEAT.equals(materialType)) {
                            dailyDeliveryDetail.setType("3"); // 小麦杂质车
                        }
                        int i = dailyDeliveryDetailMapper.insert(dailyDeliveryDetail);
                        res = res + i;

                        // 3、新增杂质车辆后下发地磅
                        if (i > 0) {
                            SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                            syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                            syncInspectionAndAvsDTO.setMaterialCode(detailCreateDTO.getMaterialCode());
                            syncInspectionAndAvsDTO.setMaterialName(detailCreateDTO.getMaterialName());
                            syncInspectionAndAvsDTO.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber());
                            VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                            syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                            syncInspectionAndAvsDTO.setState("1");
                            weightList.add(syncInspectionAndAvsDTO);
                        }
                    } else if ("1".equals(detailCreateDTO.getOperateState())) {
                        // 1、校验车辆黑名单
//                        LambdaQueryWrapper<TMpVehicleBlacklist> vehicleBlacklistLambdaQueryWrapper = Wrappers.<TMpVehicleBlacklist>lambdaQuery()
//                                .eq(TMpVehicleBlacklist::getLicensePlateNumber, detailCreateDTO.getLicensePlateNumber())
//                                .eq(TMpVehicleBlacklist::getDeleted, 0)
//                                .eq(TMpVehicleBlacklist::getState, "0");
//                        List<TMpVehicleBlacklist> vehicleBlacklist = vehicleBlacklistMapper.selectList(vehicleBlacklistLambdaQueryWrapper);
//                        if (StringUtil.isNotEmpty(vehicleBlacklist) && vehicleBlacklist.size() > 0) {
//                            throw new BaseKnownException(10000, "车牌为" + detailCreateDTO.getLicensePlateNumber() + "是黑名单车辆，不能创建！");
//                        }
                        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(detailCreateDTO.getId());
                        if (!VehicleState.WAIT_ENTERED.equals(dailyDeliveryDetail.getState())) {
                            throw new BaseKnownException(10000, "车辆已经入厂，不能修改");
                        }
                        dailyDeliveryDetail.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                        res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
                        SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                        syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                        syncInspectionAndAvsDTO.setMaterialCode(detailCreateDTO.getMaterialCode());
                        syncInspectionAndAvsDTO.setMaterialName(detailCreateDTO.getMaterialName());
                        syncInspectionAndAvsDTO.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber());
                        syncInspectionAndAvsDTO.setState("1");
                        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                        syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                        weightList.add(syncInspectionAndAvsDTO);
                    } else if ("2".equals(detailCreateDTO.getOperateState())) {
                        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(detailCreateDTO.getId());
                        if (!VehicleState.WAIT_ENTERED.equals(dailyDeliveryDetail.getState())) {
                            throw new BaseKnownException(10000, "车辆已入厂，不能删除");
                        }
                        SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                        syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                        syncInspectionAndAvsDTO.setLicensePlateNumber(dailyDeliveryDetail.getLicensePlateNumber());
                        syncInspectionAndAvsDTO.setMaterialName(dailyDeliveryDetail.getMaterialName());
                        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                        syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                        syncInspectionAndAvsDTO.setState("2");
                        weightList.add(syncInspectionAndAvsDTO);
                        res += dailyDeliveryDetailMapper.deleteById(dailyDeliveryDetail.getId());
                    }
                }

                // 批量下发地磅
                if (weightList.size() > 0) {
                    for (SyncInspectionAndAvsDTO syncInspectionAndAvsDTO : weightList) {
                        if (MaterialType.WHEAT.equals(materialType)) {
                            VehicleSendOutDTO vehicleSendOutDTO = DtoMapper.convert(syncInspectionAndAvsDTO, VehicleSendOutDTO.class);
                            //对车牌号进行去空格处理
                            vehicleSendOutDTO.setLicensePlateNumber(vehicleSendOutDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                            vehicleSendOutDTO.setBusinessType("3");
                            vehicleSendOutDTO.setTimeType("2");
                            // todo 取消注释下发地磅
                            ResultVO<AvsBaseResponseDto> resultVO = avsClient.vehicleSendOut(vehicleSendOutDTO);
                            if (200 == resultVO.getCode()) {
                                if ("200".equals(resultVO.getData().getCode())) {
                                    dailyDeliveryDetailMapper.updateSyncStatus(vehicleSendOutDTO.getDeliveryNumber(), "1");//同步成功
                                }
                            }
                            // 不管是否下发成功，可以重新发送的
                        } else {
                            sapService.purchaseSendOut(syncInspectionAndAvsDTO);
                        }
                    }
                    log.info("批量下发地磅完成{}");
                }
            }
        }
        return res;

    }

    /*
     * @description: 批量新增送货车辆
     * <AUTHOR>
     * @date 2022/4/11 11:01
     * @param detailCreateDTOS
     * @param itemId
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer batchInsertDailyDeliveryDetail(List<DailyDeliveryDetailCreateDTO> detailCreateDTOS, Integer itemId, Integer departmentId, String materialType) {

        log.info("批量新增送货车辆,itemId:{} departmentId:{} materialType:{} 车辆信息:{}", itemId, departmentId, materialType, JSONObject.toJSONString(detailCreateDTOS));

        if (CollectionUtils.isEmpty(detailCreateDTOS)) {
            throw new BaseKnownException(10000, "车辆数据不能为空");
        }

        for (DailyDeliveryDetailCreateDTO detailCreateDTO : detailCreateDTOS) {
            if (StringUtils.isEmpty(detailCreateDTO.getLicensePlateNumber())) {
                if (CollectionUtils.isNotEmpty(detailCreateDTOS)) throw new BaseKnownException(10000, "车牌号不能为空，请检查数据项");
            }
        }

        int res = 0;
        DailyDeliveryDetailInfoDTO dailyDeliveryDetailInfoDTO = deliveryItemMapper.getDemandByItemId(itemId);
        LocalDate demandDate = dailyDeliveryDetailInfoDTO.getDemandDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        String wheatNumber = wheatService.generateWheatNumber();
        //判断今天是否在在要货期之前（如果在之前则只创建，由定时任务在晚上9点同步）
        Boolean isBefore = LocalDate.now().isBefore(demandDate);
        //判断当前时间是否为晚上9点之后
        if (isBefore && LocalTime.now().isAfter(LocalTime.of(21, 0))) {
            //判断要货期是否是明天
            if (LocalDate.now().plusDays(1).isEqual(demandDate)) {
                isBefore = false;
            }
        }
        /**
         * 1、循环变量送货车辆dto集合，
         * 2、根据操作类型 operateState 来区分每一个车进行什么操作： 0-新增，1-修改，2-删除，其他为不变
         * 3、日送货计划由修改变成 执行中
         */
        if (StringUtil.isNotEmpty(detailCreateDTOS) && detailCreateDTOS.size() > 0) {

            List<SyncInspectionAndAvsDTO> weightList = new ArrayList<>();
            TMpDeliveryItem deliveryItem = deliveryItemMapper.selectById(itemId);
            TMpDailyDelivery dailyDelivery = dailyDeliveryMapper.selectById(deliveryItem.getDeliveryId());
            for (DailyDeliveryDetailCreateDTO detailCreateDTO : detailCreateDTOS) {
                if ("0".equals(detailCreateDTO.getOperateState())) {
                    // 新增送货车辆
                    log.info("新增送货车辆{}", detailCreateDTO.getLicensePlateNumber());

                    // 1、校验车辆黑名单
//                    LambdaQueryWrapper<TMpVehicleBlacklist> vehicleBlacklistLambdaQueryWrapper = Wrappers.<TMpVehicleBlacklist>lambdaQuery()
//                            .eq(TMpVehicleBlacklist::getLicensePlateNumber, detailCreateDTO.getLicensePlateNumber())
//                            .eq(TMpVehicleBlacklist::getDeleted, 0)
//                            .eq(TMpVehicleBlacklist::getState, "0");
//                    List<TMpVehicleBlacklist> vehicleBlacklist = vehicleBlacklistMapper.selectList(vehicleBlacklistLambdaQueryWrapper);
//                    if (StringUtil.isNotEmpty(vehicleBlacklist) && vehicleBlacklist.size() > 0) {
//                        throw new BaseKnownException(10000, "车牌为" + detailCreateDTO.getLicensePlateNumber() + "是黑名单车辆，不能创建！");
//                    }
                    if (!detailCreateDTO.getMaterialCode().equals(dailyDelivery.getMaterialCode())) {
                        throw new BaseKnownException(10000, "车牌为" + detailCreateDTO.getLicensePlateNumber() + "送货物料不匹配");
                    }
                    MaterialQueryDTO queryDTO = new MaterialQueryDTO();
                    queryDTO.setMaterialCode(detailCreateDTO.getMaterialCode());
                    List<MaterialDTO> content = materialClient.getMaterialByNameOrCode(queryDTO).getData().getContent();
                    MaterialDTO materialDTO = new MaterialDTO();
                    if(!content.isEmpty()){
                        materialDTO = content.get(0);
                        detailCreateDTO.setMaterialId(materialDTO.getId());
                        detailCreateDTO.setMaterialName(materialDTO.getMaterialName());
                    }

                    // 2、新增送货车辆
                    TMpDailyDeliveryDetail dailyDeliveryDetail = DtoMapper.convert(detailCreateDTO, TMpDailyDeliveryDetail.class);
                    //处理车牌号带空格的情况
                    dailyDeliveryDetail.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                    dailyDeliveryDetail.setDeliveryItemId(itemId);
                    dailyDeliveryDetail.setDeliveryNumber(generateCodeUtil.generateInspectionCode("SH", 3));
                    if (MaterialType.SORGHUM.equals(materialDTO.getMaterialTypeCode())) {
                        dailyDeliveryDetail.setBatch(generateCodeUtil.generateInspectionCode("HG", 3));
                        dailyDeliveryDetail.setType("1");
                    } else if (MaterialType.BRAN.equals(materialDTO.getMaterialTypeCode())) {
                        dailyDeliveryDetail.setBatch(generateCodeUtil.generateInspectionCode("HD", 3));
                        dailyDeliveryDetail.setType("2");

                    } else if (MaterialType.WHEAT.equals(materialDTO.getMaterialTypeCode())) {
                        // 小麦送货批次 车牌_20240205_麦号
                        dailyDeliveryDetail.setBatch(dailyDeliveryDetail.getLicensePlateNumber() + "_" + DateUtil.format(new Date(), "yyyyMMdd") + "_" + wheatNumber);
                        dailyDeliveryDetail.setType("3");

                    } else {
                        dailyDeliveryDetail.setBatch(generateCodeUtil.generateInspectionCode("PC", 3));
                    }
                    dailyDeliveryDetail.setCreateTime(new Date());
                    dailyDeliveryDetail.setPostingState(VehicleState.PostingState.WAIT_POSTING);
                    dailyDeliveryDetail.setState(VehicleState.WAIT_ENTERED);
                    dailyDeliveryDetail.setInspectState(VehicleState.InspectState.WAIT_INSPECT);
                    dailyDeliveryDetail.setBuckleIsHandle(VehicleState.BuckleHandleState.ALREADY_HANDLE);
                    dailyDeliveryDetail.setSyncState("0");
                    dailyDeliveryDetail.setDepartmentId(departmentId);
                    int i = dailyDeliveryDetailMapper.insert(dailyDeliveryDetail);
                    log.info("新增送货车辆单号和车牌{}", dailyDeliveryDetail.getDeliveryNumber() + "——" + dailyDeliveryDetail.getLicensePlateNumber());
                    res = res + i;

                    // 3、新增送货车辆后下发地磅 (创建日期应不小于要货日期)
                    if (i > 0 && !isBefore) {
                        // 高粱稻壳，发起报检
                        String inspectionOrder = "";
                        if (!MaterialType.WHEAT.equals(materialDTO.getMaterialTypeCode())) {
                            VehicleLaunchInspectionDTO vehicleLaunchInspectionDTO = new VehicleLaunchInspectionDTO();
                            vehicleLaunchInspectionDTO.setMaterialId(dailyDeliveryDetail.getMaterialId());
                            vehicleLaunchInspectionDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                            vehicleLaunchInspectionDTO.setLicensePlateNumber(dailyDeliveryDetail.getLicensePlateNumber());
                            vehicleLaunchInspectionDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
                            vehicleLaunchInspectionDTO.setDepartmentId(28);// 原辅料管理部门
                            vehicleLaunchInspectionDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
                            inspectionOrder = vehicleInspectService.vehicleLaunchInspection(vehicleLaunchInspectionDTO);
                            log.info("新增送货车辆报检结束" + inspectionOrder);
                            if ("".equals(inspectionOrder)) {
                                throw new BaseKnownException(10000, "报检失败！");
                            }
                        }

                        SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                        syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                        syncInspectionAndAvsDTO.setMaterialId(detailCreateDTO.getMaterialId());
                        syncInspectionAndAvsDTO.setMaterialCode(detailCreateDTO.getMaterialCode());
                        syncInspectionAndAvsDTO.setMaterialName(detailCreateDTO.getMaterialName());
                        syncInspectionAndAvsDTO.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber());
                        syncInspectionAndAvsDTO.setState("1");
                        syncInspectionAndAvsDTO.setDepartmentId(departmentId);
                        syncInspectionAndAvsDTO.setCreatorId(detailCreateDTO.getCreatorId());
                        syncInspectionAndAvsDTO.setUpdaterId(detailCreateDTO.getUpdaterId());
                        syncInspectionAndAvsDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
                        syncInspectionAndAvsDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
                        syncInspectionAndAvsDTO.setRemarks(inspectionOrder);
                        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                        syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                        weightList.add(syncInspectionAndAvsDTO);
                    }
                } else if ("1".equals(detailCreateDTO.getOperateState())) {
                    /**
                     * 修改已有送货车辆列表
                     *  1、校验车辆黑名单
                     *  2、修改送货车辆信息
                     *  3、更新同步到地磅
                     *  4、修改质检取样地点
                     */
                    // 1、校验车辆黑名单
//                    LambdaQueryWrapper<TMpVehicleBlacklist> vehicleBlacklistLambdaQueryWrapper = Wrappers.<TMpVehicleBlacklist>lambdaQuery()
//                            .eq(TMpVehicleBlacklist::getLicensePlateNumber, detailCreateDTO.getLicensePlateNumber())
//                            .eq(TMpVehicleBlacklist::getDeleted, 0)
//                            .eq(TMpVehicleBlacklist::getState, "0");
//                    List<TMpVehicleBlacklist> vehicleBlacklist = vehicleBlacklistMapper.selectList(vehicleBlacklistLambdaQueryWrapper);
//                    if (StringUtil.isNotEmpty(vehicleBlacklist) && vehicleBlacklist.size() > 0) {
//                        throw new BaseKnownException(10000, "车牌为" + detailCreateDTO.getLicensePlateNumber() + "是黑名单车辆，不能创建！");
//                    }

                    TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(detailCreateDTO.getId());
                    if (!VehicleState.WAIT_ENTERED.equals(dailyDeliveryDetail.getState())) {
                        throw new BaseKnownException(10000, "车辆已经入厂，不能修改");
                    }

                    dailyDeliveryDetail.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                    dailyDeliveryDetail.setEstimatedNumber(detailCreateDTO.getEstimatedNumber());
                    // 2、修改送货车辆
                    dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
                    log.info("修改送货车辆成功");

                    String inspectionOrder = "";
                    ResultVO<InspectionTaskInfoDTO> resultVo = inspectionTaskClient.getInspectionTaskDetailByAssociate(dailyDeliveryDetail.getDeliveryNumber());
                    if (resultVo.getCode() == 200 && resultVo.getData() != null) {
                        InspectionTaskInfoDTO inspectionTaskInfoDTO = resultVo.getData();
                        if (StringUtil.isNotEmpty(inspectionTaskInfoDTO.getInspectionOrder())) {
                            inspectionOrder = inspectionTaskInfoDTO.getInspectionOrder();
                        }
                        // 4、更新取样任务的取样地点
                        SamplingDetailDTO samplingDetailDTO = new SamplingDetailDTO();
                        samplingDetailDTO.setId(inspectionTaskInfoDTO.getSamplingId());
                        samplingDetailDTO.setSamplingLocation(detailCreateDTO.getLicensePlateNumber());
                        samplingClient.updateSampling(samplingDetailDTO);
                        log.info("送货车修改更新取样地点");
                    }

                    // 3、如果同步了则更新下发地磅
                    if ("1".equals(dailyDeliveryDetail.getSyncState())) {
                        SyncInspectionAndAvsDTO newSyncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                        newSyncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                        newSyncInspectionAndAvsDTO.setMaterialId(detailCreateDTO.getMaterialId());
                        newSyncInspectionAndAvsDTO.setMaterialCode(detailCreateDTO.getMaterialCode());
                        newSyncInspectionAndAvsDTO.setMaterialName(detailCreateDTO.getMaterialName());
                        newSyncInspectionAndAvsDTO.setLicensePlateNumber(detailCreateDTO.getLicensePlateNumber());
                        newSyncInspectionAndAvsDTO.setState("1");
                        newSyncInspectionAndAvsDTO.setRemarks(inspectionOrder);
                        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                        newSyncInspectionAndAvsDTO.setVendorName(vendor.getName());
                        weightList.add(newSyncInspectionAndAvsDTO);
                    }


                } else if ("2".equals(detailCreateDTO.getOperateState())) {
                    // 删除已有送货车辆列表
                    TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(detailCreateDTO.getId());
                    log.info("删除送货车辆{}", dailyDeliveryDetail.getDeliveryNumber() + "——", dailyDeliveryDetail.getLicensePlateNumber());

                    if (!dailyDeliveryDetail.getState().equals(VehicleState.WAIT_ENTERED)) {
                        throw new BaseKnownException(10000, "送货车辆已入厂，不能删除");
                    }

                    // 删除检验任务
                    inspectionTaskClient.deleteInspection(dailyDeliveryDetail.getDeliveryNumber());

                    log.info("删除检验任务{}");

                    // 如果同步了则删除下发得过磅任务
                    if ("1".equals(dailyDeliveryDetail.getSyncState())) {
                        SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                        syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetail.getDeliveryNumber());
                        syncInspectionAndAvsDTO.setLicensePlateNumber(dailyDeliveryDetail.getLicensePlateNumber());
                        syncInspectionAndAvsDTO.setMaterialName(dailyDeliveryDetail.getMaterialName());
                        syncInspectionAndAvsDTO.setState("2");
                        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetail.getId());
                        syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                        weightList.add(syncInspectionAndAvsDTO);
                    }
                    dailyDeliveryDetailMapper.deleteById(detailCreateDTO.getId());
                    log.info("删除送货车完成{}");

                } else {
                    //  保持原有数据不变
                    TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(detailCreateDTO.getId());
                    dailyDeliveryDetail.setEstimatedNumber(detailCreateDTO.getEstimatedNumber());
                    dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
                }
            }

            // 车辆首次新增成功，日送货计划修改未执行中
            if (DailyDeliveryState.NEWLY_BUILD.equals(dailyDelivery.getState())) {
                dailyDelivery.setState(DailyDeliveryState.EXECUTING);
                res += dailyDeliveryMapper.updateById(dailyDelivery);
                log.info("日送货计划改成执行中{}", dailyDelivery.getPlanNumber());

            }
            // 批量下发地磅
            if (weightList.size() > 0) {
                log.info("开始批量下发地磅");
                for (SyncInspectionAndAvsDTO syncInspectionAndAvsDTO : weightList) {
                    if (MaterialType.WHEAT.equals(materialType)) {
                        VehicleSendOutDTO vehicleSendOutDTO = DtoMapper.convert(syncInspectionAndAvsDTO, VehicleSendOutDTO.class);
                        vehicleSendOutDTO.setBusinessType("1");
                        vehicleSendOutDTO.setTimeType("2");
                        //设置车牌号去空格
                        vehicleSendOutDTO.setLicensePlateNumber(vehicleSendOutDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                        // todo
                        log.info("开始下发给制曲地磅");
                        ResultVO<AvsBaseResponseDto> resultVO = avsClient.vehicleSendOut(vehicleSendOutDTO);
                        log.info("制曲地磅返回数据{}", resultVO);
                        if (resultVO.getCode() == 200) {
                            if ("200".equals(resultVO.getData().getCode())) {
                                dailyDeliveryDetailMapper.updateSyncStatus(vehicleSendOutDTO.getDeliveryNumber(), "1");//同步成功
                            }
                        }
                    } else {
                        //内部包含了如果同步成功或失败的处理操作
                        sapService.purchaseSendOut(syncInspectionAndAvsDTO);
                    }
                }
                log.info("批量下发地磅完成{}");

            }

        }
        return res;
    }

    /**
     * @Description 送货车辆手动同步给地磅和生成检验任务
     *
     * <AUTHOR>
     * @Date 2024-6-28 11:46
     * @param id
     * @param materialType
     * @return void
     **/
    @Override
    public void purchaseSendOutByDetailId(String id, String materialType) {
        TMpDailyDeliveryDetail detail = dailyDeliveryDetailMapper.selectById(id);
        DailyDeliveryDetailInfoDTO dailyDeliveryDetailInfoDTO = deliveryItemMapper.getDemandByItemId(detail.getDeliveryItemId());

        if (!Objects.equals(detail.getSyncState(), "2")) {
            throw new BaseKnownException(99999, "当前车辆状态不为同步失败，不可手动同步");
        }
        SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
        syncInspectionAndAvsDTO.setDeliveryNumber(detail.getDeliveryNumber());
        syncInspectionAndAvsDTO.setMaterialId(detail.getMaterialId());
        syncInspectionAndAvsDTO.setMaterialCode(detail.getMaterialCode());
        syncInspectionAndAvsDTO.setMaterialName(detail.getMaterialName());
        syncInspectionAndAvsDTO.setLicensePlateNumber(detail.getLicensePlateNumber());
        syncInspectionAndAvsDTO.setState("1");
        syncInspectionAndAvsDTO.setDepartmentId(detail.getDepartmentId());
        syncInspectionAndAvsDTO.setCreatorId(detail.getCreatorId());
        syncInspectionAndAvsDTO.setUpdaterId(detail.getUpdaterId());
        syncInspectionAndAvsDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
        syncInspectionAndAvsDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
        VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(detail.getId());
        syncInspectionAndAvsDTO.setVendorName(vendor.getName());
        if (MaterialType.WHEAT.equals(materialType)) {
            VehicleSendOutDTO vehicleSendOutDTO = DtoMapper.convert(syncInspectionAndAvsDTO, VehicleSendOutDTO.class);
            vehicleSendOutDTO.setBusinessType("1");
            vehicleSendOutDTO.setTimeType("2");
            //对车牌号进行去空格处理
            vehicleSendOutDTO.setLicensePlateNumber(vehicleSendOutDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
            ResultVO<AvsBaseResponseDto> resultVO = avsClient.vehicleSendOut(vehicleSendOutDTO);
            if (resultVO.getCode() == 200) {
                if ("200".equals(resultVO.getData().getCode())) {
                    dailyDeliveryDetailMapper.updateSyncStatus(vehicleSendOutDTO.getDeliveryNumber(), "1");//同步成功
                }
            }
        } else {
            // 高粱稻壳，发起报检
            VehicleLaunchInspectionDTO vehicleLaunchInspectionDTO = new VehicleLaunchInspectionDTO();
            vehicleLaunchInspectionDTO.setMaterialId(detail.getMaterialId());
            vehicleLaunchInspectionDTO.setDeliveryNumber(detail.getDeliveryNumber());
            vehicleLaunchInspectionDTO.setLicensePlateNumber(detail.getLicensePlateNumber());
            vehicleLaunchInspectionDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
            vehicleLaunchInspectionDTO.setDepartmentId(28); // 原辅料管理部门
            vehicleLaunchInspectionDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
            String inspectionOrder = vehicleInspectService.vehicleLaunchInspection(vehicleLaunchInspectionDTO);
            syncInspectionAndAvsDTO.setRemarks(inspectionOrder);
            log.info("新增送货车辆报检结束" + inspectionOrder);
            if ("".equals(inspectionOrder)) {
                throw new BaseKnownException(10000, "报检失败！");
            }

            sapService.purchaseSendOut(syncInspectionAndAvsDTO);
        }
    }

    /**
     * @Description 定时下发地磅，和生成报检任务
     *
     * <AUTHOR>
     * @Date 2024-6-13 18:05
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 5 21 * * ?")
//    @SchedulerLock(name = "scheduleGlDkPurchaseSendOut")
    public void scheduleGlDkPurchaseSendOut() {
        try {
            log.info("定时下发高粱稻壳地磅");
            if (RedissonLockUtil.tryLock("sendDataToAvg", 1, 200, TimeUnit.SECONDS)) {
                log.info("加锁竞争成功，执行下发地磅逻辑");
                sendDataToAvg();
                log.info("下发地磅执行结束，休眠10秒");
                Thread.sleep(10000);
            } else {
                log.info("加锁竞争失败，本服务不下发地磅");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            RedissonLockUtil.unlock("sendDataToAvg");
        }
        log.info("自动任务批量下发地磅完成");
    }

    /**
     * 发送数据到地磅
     */
    private void sendDataToAvg() {
        List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectTodayScheduleList();
        for (TMpDailyDeliveryDetail detail : dailyDeliveryDetails) {
            DailyDeliveryDetailInfoDTO dailyDeliveryDetailInfoDTO = deliveryItemMapper.getDemandByItemId(detail.getDeliveryItemId());
            SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
            syncInspectionAndAvsDTO.setDeliveryNumber(detail.getDeliveryNumber());
            syncInspectionAndAvsDTO.setMaterialId(detail.getMaterialId());
            syncInspectionAndAvsDTO.setMaterialCode(detail.getMaterialCode());
            syncInspectionAndAvsDTO.setMaterialName(detail.getMaterialName());
            syncInspectionAndAvsDTO.setLicensePlateNumber(detail.getLicensePlateNumber());
            syncInspectionAndAvsDTO.setState("1");
            syncInspectionAndAvsDTO.setDepartmentId(detail.getDepartmentId());
            syncInspectionAndAvsDTO.setCreatorId(detail.getCreatorId());
            syncInspectionAndAvsDTO.setUpdaterId(detail.getUpdaterId());
            VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(detail.getId());
            syncInspectionAndAvsDTO.setVendorName(vendor.getName());
            syncInspectionAndAvsDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
            syncInspectionAndAvsDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
            try {
                String materialType = materialMapper.getMaterialTypeByCode(syncInspectionAndAvsDTO.getMaterialCode());
                if (MaterialType.WHEAT.equals(materialType)) {
                    VehicleSendOutDTO vehicleSendOutDTO = DtoMapper.convert(syncInspectionAndAvsDTO, VehicleSendOutDTO.class);
                    vehicleSendOutDTO.setBusinessType("1");
                    vehicleSendOutDTO.setTimeType("2");
                    // todo
                    //对车牌号进行去空格处理
                    vehicleSendOutDTO.setLicensePlateNumber(vehicleSendOutDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                    ResultVO<AvsBaseResponseDto> resultVO = avsClient.vehicleSendOut(vehicleSendOutDTO);
                    if (resultVO.getCode() == 200) {
                        if ("200".equals(resultVO.getData().getCode())) {
                            dailyDeliveryDetailMapper.updateSyncStatus(vehicleSendOutDTO.getDeliveryNumber(), "1");//同步成功
                        }
                    }
                } else {
                    // 高粱稻壳，发起报检
                    VehicleLaunchInspectionDTO vehicleLaunchInspectionDTO = new VehicleLaunchInspectionDTO();
                    vehicleLaunchInspectionDTO.setMaterialId(detail.getMaterialId());
                    vehicleLaunchInspectionDTO.setDeliveryNumber(detail.getDeliveryNumber());
                    vehicleLaunchInspectionDTO.setLicensePlateNumber(detail.getLicensePlateNumber());
                    vehicleLaunchInspectionDTO.setLocationName(dailyDeliveryDetailInfoDTO.getLocationName());
                    vehicleLaunchInspectionDTO.setDepartmentId(28); // 原辅料管理部门
                    vehicleLaunchInspectionDTO.setDemandDate(dailyDeliveryDetailInfoDTO.getDemandDate());
                    String inspectionOrder = vehicleInspectService.vehicleLaunchInspection(vehicleLaunchInspectionDTO);
                    log.info("新增送货车辆报检结束" + inspectionOrder);
                    syncInspectionAndAvsDTO.setRemarks(inspectionOrder);
                    if ("".equals(inspectionOrder)) {
                        throw new BaseKnownException(10000, "报检失败！");
                    }
                    sapService.purchaseSendOut(syncInspectionAndAvsDTO);
                }
            } catch (BaseKnownException e) {
                log.error("同步失败报错:" + e.getMessage());
            }

        }
    }

    /**
     * 给制曲模块入仓任务查询送货车详情使用
     *
     * @param inboundTaskPageQueryDTO 入仓任务id
     * @return
     */
    @Override
    public List<InboundTaskDeliveryListDTO> getDailyDeliveryDetailByInboundId(InboundTaskPageQueryDTO inboundTaskPageQueryDTO) {
        log.info("查询送货车辆详情:" + JSONObject.toJSONString(inboundTaskPageQueryDTO));
        List<TMpDailyDeliveryDetail> tMpDailyDeliveryDetails = Optional.ofNullable(dailyDeliveryDetailMapper.selectList(Wrappers.<TMpDailyDeliveryDetail>lambdaQuery().eq(TMpDailyDeliveryDetail::getWarehouseTaskId, inboundTaskPageQueryDTO.getWarehouseTaskId()))).orElse(Collections.emptyList());
        List<InboundTaskDeliveryListDTO> inboundTaskDeliveryListDTOS = DtoMapper.convertList(tMpDailyDeliveryDetails, InboundTaskDeliveryListDTO.class);
        if (CollectionUtils.isNotEmpty(inboundTaskDeliveryListDTOS)) {
            List<Integer> ids = inboundTaskDeliveryListDTOS.stream().map(SysBaseDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());

//            查询车的卸货记录
            List<TMpUnloadRecord> tMpUnloadRecords = Optional.ofNullable(unloadRecordMapper.selectList(Wrappers.<TMpUnloadRecord>lambdaQuery().in(TMpUnloadRecord::getVehicleId, ids).eq(TMpUnloadRecord::getDeleted, "0"))).orElse(Collections.emptyList());
            inboundTaskDeliveryListDTOS.forEach(item -> {
                boolean res = tMpUnloadRecords.stream().anyMatch(it -> item.getId().equals(it.getVehicleId()));
                if (res) {
                    TMpUnloadRecord tMpUnloadRecord = tMpUnloadRecords.stream().filter(it -> item.getId().equals(it.getVehicleId())).max(Comparator.comparing(TMpUnloadRecord::getCreateTime)).get();
                    if (Objects.isNull(tMpUnloadRecord) || Objects.isNull(tMpUnloadRecord.getCreateTime())) {
                        item.setUnloadRecordTime(null);
                        return;
                    }
                    item.setUnloadRecordTime(tMpUnloadRecord.getCreateTime());
                }
            });
        }
        return inboundTaskDeliveryListDTOS;
    }

    /**
     * 更新质检结论
     *
     * @param updateInspectionResultDTO
     * @return
     */
    @Override
    public Boolean updateInspectionResult(UpdateInspectionResultDTO updateInspectionResultDTO) {
        log.info("开始更新质检结论:" + JSONObject.toJSON(updateInspectionResultDTO));
        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectOne(new LambdaUpdateWrapper<TMpDailyDeliveryDetail>().eq(TMpDailyDeliveryDetail::getDeliveryNumber, updateInspectionResultDTO.getDeliveryNumber()).orderByDesc(TMpDailyDeliveryDetail::getCreateTime).last("limit 1"));
        //找到车辆最新的检验结果，将质检结论改成检验结果，同时寻找车辆的净重，如果有净重且合格车辆状态改成已收货，质检结论改为合格；如果不合格车辆状态改成拒收，质检状态改为不合格；
        //如果车辆没有净重，则去寻找毛重，如果有毛重且合格改为待卸货，质检结论改为合格；有毛重不合格车辆状态则改成拒收，质检状态改为不合格；
        if (dailyDeliveryDetail != null) {
            if (null != dailyDeliveryDetail.getNetWeight()) {
                if ("合格".equals(updateInspectionResultDTO.getInspectionResult())) {
                    dailyDeliveryDetail.setInspectState("2");
                    dailyDeliveryDetail.setState("9");
                    log.info("更改状态为 合格收货");
                } else if ("不合格".equals(updateInspectionResultDTO.getInspectionResult())) {
                    dailyDeliveryDetail.setInspectState("3");
                    //拒收
                    dailyDeliveryDetail.setState("6");
                    log.info("更改状态为 不合格拒收");
                }
            } else if (null != dailyDeliveryDetail.getAppearanceWeight()) {
                if ("合格".equals(updateInspectionResultDTO.getInspectionResult())) {
                    dailyDeliveryDetail.setInspectState("2");
                    dailyDeliveryDetail.setState("3");
                    log.info("更改状态为 合格卸货");
                } else if ("不合格".equals(updateInspectionResultDTO.getInspectionResult())) {
                    dailyDeliveryDetail.setInspectState("3");
                    //拒收
                    dailyDeliveryDetail.setState("6");
                    log.info("更改状态为 不合格拒收");
                }
            }
            dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
        }
        return true;
    }

    /*
     * @Description: 未完成备注
     *
     * <AUTHOR>
     * @param remarkDTO:
     * @return java.lang.Integer
     */
    @Override
    public Integer addRemark(DailyDeliveryRemarkDTO remarkDTO) {
        return dailyDeliveryMapper.updateById(DtoMapper.convert(remarkDTO, TMpDailyDelivery.class));
    }

    /*
     * @description: 送货车辆导入模板下载
     * <AUTHOR>
     * @date 2022/4/11 11:07
     * @param
     * @return com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
     */
    @Override
    public ResultVO<ExcelExportDto> getDailyDeliveryDetailImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> workPlanImportTemplate = ExcelUtil.generateImportFile(DailyDeliveryDetailCreateTemplateDTO.class, "送货车辆导入模板");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(workPlanImportTemplate.getBody());
        excelExportDto.setFileName("送货车辆导入模板.xls");
        return ResultVO.success(excelExportDto);
    }

    /*
     * @Description: 特殊退货
     *
     * <AUTHOR>
     * @param id: 车辆id
     * @return java.lang.Integer
     */
    @Override
    public Integer specialReturns(SpecialReturnsDTO specialReturnsDTO) {
        // 不合格退货需要修改质检单和不合格退货单信息
        ResultVO<Integer> resultVO = inspectionTaskClient.specialReturnsUpdateTask(Integer.parseInt(specialReturnsDTO.getInspectionId()));
        if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
            throw new BaseKnownException(10000, "检验任务修改失败，无法特殊退货！");
        }

        // 修改车辆状态为特殊退货
        TMpDailyDeliveryDetail detail = new TMpDailyDeliveryDetail();
        detail.setId(specialReturnsDTO.getId());
        detail.setInspectState(VehicleState.InspectState.SPECIAL_RETURN);
        detail.setState(VehicleState.SPECIAL_RETURN);
        detail.setReturnRemark(specialReturnsDTO.getReturnRemark());
        return dailyDeliveryDetailMapper.updateById(detail);
    }

    /**
     * 定时任务：每天0点执行
     * 1、修改要货日期在今天之前的日送货计划改成【已完成】状态
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "dailyDeliveryFinishLock")
    public void dailyDeliveryFinish() {
        log.info("日送货计划改成【已完成】状态,开始执行");
        // 根据要货日期获取采购计划id
        List<Integer> ids = dailyDeliveryMapper.getDailyDeliveryIds();
        ids.forEach(item -> {
            TMpDailyDelivery dailyDelivery = new TMpDailyDelivery();
            dailyDelivery.setId(item);
            dailyDelivery.setState(DailyDeliveryState.COMPLETE);
            dailyDeliveryMapper.updateById(dailyDelivery);
        });
        log.info("日送货计划改成【已完成】状态,执行结束");
    }

    /***
     * @Description 0点删除昨天未入厂的送货车辆
     *
     * <AUTHOR>
     * @Date 2022-9-30 14:18
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "scheduleDeleteDkVehicleLock")
    @Transactional
    public void scheduleDeleteDkVehicle() {
        /**
         * 1、删除送货车辆
         * 2、删除地磅任务
         * 3、删除检验任务
         */
        List<DailyDeliveryDetailDTO> notInVehicleList = dailyDeliveryMapper.getNotInVehicleList();
        log.info("0点删除昨天未入厂的送货车=={}", JSONObject.toJSON(notInVehicleList));
        if (notInVehicleList.size() > 0) {

            List<SyncInspectionAndAvsDTO> list = new ArrayList<>();
            for (DailyDeliveryDetailDTO dailyDeliveryDetailDTO : notInVehicleList) {
                // 删除为入厂的送货车，需要删除送货车关联的质检信息
                try {
                    ResultVO<Integer> resultVO = inspectionTaskClient.deleteInspection(dailyDeliveryDetailDTO.getDeliveryNumber());
                    log.info("删除检验任务条数=={}", JSONObject.toJSON(resultVO));
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                // 删除下发的过磅任务
                SyncInspectionAndAvsDTO syncInspectionAndAvsDTO = new SyncInspectionAndAvsDTO();
                syncInspectionAndAvsDTO.setDeliveryNumber(dailyDeliveryDetailDTO.getDeliveryNumber());
                syncInspectionAndAvsDTO.setLicensePlateNumber(dailyDeliveryDetailDTO.getLicensePlateNumber());
                syncInspectionAndAvsDTO.setMaterialName(dailyDeliveryDetailDTO.getMaterialName());
                syncInspectionAndAvsDTO.setState("2");
                VendorPageDTO vendor = dailyDeliveryDetailMapper.getVendorByDetailId(dailyDeliveryDetailDTO.getId());
                syncInspectionAndAvsDTO.setVendorName(vendor.getName());
                list.add(syncInspectionAndAvsDTO);

                // 删除送货车
                dailyDeliveryDetailMapper.deleteById(dailyDeliveryDetailDTO.getId());
            }

            // 批量删除送货车
            if (list.size() > 0) {
                for (SyncInspectionAndAvsDTO syncInspectionAndAvsDTO : list) {
                    String materialType = materialMapper.getMaterialTypeByCode(syncInspectionAndAvsDTO.getMaterialCode());
                    if (MaterialType.WHEAT.equals(materialType)) {
                        VehicleSendOutDTO vehicleSendOutDTO = DtoMapper.convert(syncInspectionAndAvsDTO, VehicleSendOutDTO.class);
                        vehicleSendOutDTO.setBusinessType("1");
                        vehicleSendOutDTO.setTimeType("2");
                        // 下发制曲地磅
                        //对车牌号进行去空格处理
                        vehicleSendOutDTO.setLicensePlateNumber(vehicleSendOutDTO.getLicensePlateNumber().replaceAll("\\s+", ""));
                        ResultVO<AvsBaseResponseDto> resultVO = avsClient.vehicleSendOut(vehicleSendOutDTO);
                    } else {
                        // 下发采购地磅
                        sapService.purchaseSendOut(syncInspectionAndAvsDTO);
                    }
                }
            }
        }
        log.info("删除未入场的送货车车辆,执行结束");
    }

    /*
     * @description: 处理扣重
     * <AUTHOR>
     * @date 2022/6/24 13:34
     * @param buckleHandleDTO
     * @return java.lang.Integer
     */
    @Override
    public Integer handleBuckle(BuckleHandleDTO buckleHandleDTO) {
        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(buckleHandleDTO.getId());
        dailyDeliveryDetail.setBuckleIsHandle(buckleHandleDTO.getBuckleIsHandle());
        return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
    }

    @Override
    public List<DeliveryItemDetailDTO> getDeliveryItemList(DeliveryItemDetailQueryDTO queryDTO) {
        return deliveryItemMapper.getDeliveryItemList(queryDTO);
    }


    /***
     * @description: 车辆调整行号
     * <AUTHOR>
     * @date 2022/7/6 15:22
     * @param vehicleId 调整车id
     * @param itemId    目标送货子项id
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer changeLine(Integer vehicleId, Integer itemId) {

        /**
         * 1、如果车辆没有出门，可以随便调整
         * 2、如果车辆已经收货，不能调整
         * 3、如果是送货车辆:
         *      出操作：
         *          将自己的扣杂均摊给其他送货车，自己净重需要加上杂质，子项的已到货数量扣除该车离场净重
         *          不够均摊时不能调整
         *      入操作：
         *          入的子项目累加到货数量
         *
         * 4、如果是杂质车辆:
         *      出操作：
         *          其他车辆的扣杂减少该车的重量（一车一车减，直到扣杂为0），净重增加
         *          扣杂不够，不能调整
         *      入操作：
         *          正常杂质车出门均摊扣杂
         */
        int res = 0;
        TMpDeliveryItem destDeliveryItem = deliveryItemMapper.selectById(itemId);
        TMpDailyDeliveryDetail dailyDeliveryDetail = dailyDeliveryDetailMapper.selectById(vehicleId);
        TMpDeliveryItem initDeliveryItem = deliveryItemMapper.selectById(dailyDeliveryDetail.getDeliveryItemId());
        if (Integer.parseInt(dailyDeliveryDetail.getState()) < 4) { //
            dailyDeliveryDetail.setDeliveryItemId(itemId);
            dailyDeliveryDetail.setUpdateTime(new Date());
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
        }
        if (VehicleState.PostingState.ALREADY_POSTING.equals(dailyDeliveryDetail.getPostingState())) {
            throw new BaseKnownException(10000, "车辆过账，不能调整！");
        }

//        if (VehicleState.GO_OUT.equals(dailyDeliveryDetail.getState())) {
        if ("0000".equals(dailyDeliveryDetail.getMaterialCode())) { // 杂质车调整

            //如果没有入库车辆直接提示，不能调整杂质
            if (destDeliveryItem.getArrivalWeight() == null || destDeliveryItem.getArrivalWeight().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseKnownException(10000, "没有实收重量，不允许调整杂质车辆");
            }

//            LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                    .eq(TMpDailyDeliveryDetail::getDeliveryItemId, dailyDeliveryDetail.getDeliveryItemId())
////                        .eq(TMpDailyDeliveryDetail::getState, VehicleState.GO_OUT)
//                    .isNotNull(TMpDailyDeliveryDetail::getImpurityWeight)
//                    .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                    .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                    .eq(TMpDailyDeliveryDetail::getDeleted, 0);
//            List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
//            BigDecimal weight = dailyDeliveryDetail.getNetWeight();
//
//            // 冲销扣杂
//            for (TMpDailyDeliveryDetail detail : dailyDeliveryDetails) {
//
//                BigDecimal subtract = weight.subtract(detail.getImpurityWeight());
//
//                if (subtract.compareTo(BigDecimal.ZERO) == 1) {
//                    detail.setNetWeight(detail.getNetWeight().add(detail.getImpurityWeight()));
//                    detail.setImpurityWeight(BigDecimal.ZERO);
//                    weight = subtract;
//
//                } else {
//                    detail.setNetWeight(detail.getNetWeight().add(weight));
//                    detail.setImpurityWeight(detail.getImpurityWeight().subtract(weight));
//                    weight = BigDecimal.ZERO;
//                }
//                res += dailyDeliveryDetailMapper.updateById(detail);
//                if (subtract.compareTo(BigDecimal.ZERO) < 1) {
//                    break;
//                }
//            }
//            if (CollectionUtils.isNotEmpty(dailyDeliveryDetails) && weight.compareTo(BigDecimal.ZERO) == 1) {
//                throw new BaseKnownException(10000, "扣杂不够冲销");
//            }
//            this.averageWeight(dailyDeliveryDetail.getNetWeight(), itemId, dailyDeliveryDetail.getId());
            dailyDeliveryDetail.setDeliveryItemId(itemId);
            dailyDeliveryDetail.setUpdateTime(new Date());
            return dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);


        } else { // 送货车调整

            initDeliveryItem.setArrivalWeight(initDeliveryItem.getArrivalWeight().subtract(dailyDeliveryDetail.getLeaveNetWeight()));

//            if (StringUtil.isNotEmpty(dailyDeliveryDetail.getImpurityWeight())) {
//                // 存在扣杂
//                this.averageWeight(dailyDeliveryDetail.getImpurityWeight(), dailyDeliveryDetail.getDeliveryItemId(), dailyDeliveryDetail.getId());
//                dailyDeliveryDetail.setNetWeight(dailyDeliveryDetail.getNetWeight().add(dailyDeliveryDetail.getImpurityWeight()));
//                dailyDeliveryDetail.setImpurityWeight(BigDecimal.ZERO);
//            }
            dailyDeliveryDetail.setDeliveryItemId(itemId);
            destDeliveryItem.setArrivalWeight(destDeliveryItem.getArrivalWeight() == null
                    ? dailyDeliveryDetail.getLeaveNetWeight()
                    : destDeliveryItem.getArrivalWeight().add(dailyDeliveryDetail.getLeaveNetWeight()));
            //如果物料不一致，则修改车辆的物料信息
            TMpDailyDelivery delivery = dailyDeliveryMapper.selectById(destDeliveryItem.getDeliveryId());
            if (!delivery.getMaterialCode().equals(dailyDeliveryDetail.getMaterialCode())) {
                dailyDeliveryDetail.setMaterialName(delivery.getMaterialName());
                dailyDeliveryDetail.setMaterialCode(delivery.getMaterialCode());
                dailyDeliveryDetail.setMaterialId(delivery.getMaterialId());
            }
            res += dailyDeliveryDetailMapper.updateById(dailyDeliveryDetail);
            res += deliveryItemMapper.updateById(destDeliveryItem);
            res += deliveryItemMapper.updateById(initDeliveryItem);
            return res;
        }


//        return res;


    }

//    /***
//     * @Description 均摊扣扣杂
//     *
//     * <AUTHOR>
//     * @Date 2022-10-26 14:10
//     * @param weight 扣杂总重
//     * @param destItemId 目标子项
//     * @param detailId  调整的车
//     * @return void
//     **/
//    @Override
//    public void averageWeight(BigDecimal weight, Integer destItemId, Integer detailId) {
//
//        LambdaQueryWrapper<TMpDailyDeliveryDetail> wrapper1 = Wrappers.<TMpDailyDeliveryDetail>lambdaQuery()
//                .ne(TMpDailyDeliveryDetail::getId, detailId)
//                .eq(TMpDailyDeliveryDetail::getDeliveryItemId, destItemId)
//                .eq(TMpDailyDeliveryDetail::getPostingState, VehicleState.PostingState.WAIT_POSTING)
//                .eq(TMpDailyDeliveryDetail::getInspectState, VehicleState.InspectState.QUALIFIED)
//                .isNotNull(TMpDailyDeliveryDetail::getNetWeight)
//                .ne(TMpDailyDeliveryDetail::getMaterialCode, "0000")
//                .eq(TMpDailyDeliveryDetail::getDeleted, 0);
//        List<TMpDailyDeliveryDetail> dailyDeliveryDetails = dailyDeliveryDetailMapper.selectList(wrapper1);
//
//        BigDecimal surplus = weight; // 剩余扣重
//        if (dailyDeliveryDetails != null && dailyDeliveryDetails.size() > 0) {
//            do {
//                BigDecimal[] bigDecimals = surplus.divideAndRemainder(new BigDecimal(dailyDeliveryDetails.size()));
//                BigDecimal quotient = bigDecimals[0]; // 商
//                BigDecimal remainder = bigDecimals[1]; // 余数
//                BigDecimal first = quotient.add(remainder); // 第一车将余数包含
//                surplus = BigDecimal.ZERO; // 剩余扣重
//                List<Integer> indexes = new ArrayList<>(); // 不够扣的项
//                for (int i = 0; i < dailyDeliveryDetails.size(); i++) { // 每没一车循环扣重
//                    if (i == 0) {
//
//                        BigDecimal finalImpurity;
//                        if (dailyDeliveryDetails.get(i).getNetWeight().subtract(first).compareTo(BigDecimal.ZERO) == -1) {
//                            // 最终净重不够扣
//                            finalImpurity = dailyDeliveryDetails.get(i).getNetWeight();
//                            surplus = surplus.add(dailyDeliveryDetails.get(i).getNetWeight().subtract(first).abs());
//                            indexes.add(dailyDeliveryDetails.get(i).getId());
//                        } else {
//                            finalImpurity = first;
//                        }
//
//                        dailyDeliveryDetails.get(i).setNetWeight(dailyDeliveryDetails.get(i).getNetWeight().subtract(finalImpurity));
//                        dailyDeliveryDetails.get(i).setImpurityWeight(StringUtil.isEmpty(dailyDeliveryDetails.get(i).getImpurityWeight()) ? finalImpurity : dailyDeliveryDetails.get(i).getImpurityWeight().add(finalImpurity));
//                        dailyDeliveryDetailMapper.updateById(dailyDeliveryDetails.get(i));
//                    } else {
//                        BigDecimal finalImpurity;
//                        if (dailyDeliveryDetails.get(i).getNetWeight().subtract(quotient).compareTo(BigDecimal.ZERO) == -1) {
//                            // 最终净重不够扣
//                            finalImpurity = dailyDeliveryDetails.get(i).getNetWeight();
//                            surplus = surplus.add(dailyDeliveryDetails.get(i).getNetWeight().subtract(quotient).abs());
//                            indexes.add(dailyDeliveryDetails.get(i).getId());
//                        } else {
//                            finalImpurity = quotient;
//                        }
//                        dailyDeliveryDetails.get(i).setNetWeight(dailyDeliveryDetails.get(i).getNetWeight().subtract(finalImpurity));
//                        dailyDeliveryDetails.get(i).setImpurityWeight(StringUtil.isEmpty(dailyDeliveryDetails.get(i).getImpurityWeight()) ? finalImpurity : dailyDeliveryDetails.get(i).getImpurityWeight().add(finalImpurity));
//                        dailyDeliveryDetailMapper.updateById(dailyDeliveryDetails.get(i));
//                    }
//                }
//
//                // 存在剩余扣重
//                if (surplus.compareTo(BigDecimal.ZERO) == 1) {
//                    // 移除不够扣重的
//                    dailyDeliveryDetails = dailyDeliveryDetails.stream().filter(o -> !indexes.contains(o.getId())).collect(Collectors.toList());
//
//                    if (dailyDeliveryDetails.size() == 0) {
//                        throw new BaseKnownException(10000, "净重不够扣杂");
//                    }
//                }
//            } while (surplus.compareTo(BigDecimal.ZERO) == 1);
//        }
//    }
}
