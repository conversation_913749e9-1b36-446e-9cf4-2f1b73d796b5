<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.ProcurementPlanMapper">

    <!-- 分页查询采购计划列表 -->
    <select id="getProcurementPlanPageList"
            resultType="com.hvisions.purchase.dto.purchase.plan.ProcurementPlanPageDTO">
        SELECT
            p.id,
            p.material_code,
            p.material_name,
            p.plan_code,
            p.begin_time,
            IFNULL(di.arrival_qty, 0) arrival_qty,
            ((IFNULL(di.arrival_qty, 0) / IFNULL(p.procurement_number, 0)) * 100) progress,
            p.use_time_start,
            p.use_time_end,
            l.name AS location_name,
            l.id AS location_id,
            p.delivery_storage_time,
            p.end_time,
            p.procurement_number,
            p.state,
            p.audit_name,
            p.audit_time,
            p.audit_result,
            u.user_name AS creator_name,
            p.create_time,
            t.symbol AS uom,
            p.remark,
            p.adjust_people,
            p.adjust_time,
            p.adjust_quantity,
            p.feed_type,
            p.adjust_remark,
            p.submit_user,
            p.submit_time,
            (
            SELECT IFNULL(sum(pod.quantity_supplied),0)
            FROM  t_mp_purchase_order po
            LEFT JOIN t_mp_purchase_order_detail pod ON pod.order_id = po.id AND pod.deleted = 0 AND is_new = 1
            WHERE po.deleted = 0 AND po.planning_id = p.id
            ) relationOrderQuantity
        FROM
            t_mp_procurement_planning p
            LEFT JOIN authority.sys_user u ON p.creator_id = u.id
            LEFT JOIN t_mp_inventory_location l ON l.id = p.location_id
            LEFT JOIN materials.hv_bm_material m ON p.material_id = m.id
            LEFT JOIN materials.hv_bm_unit t ON m.uom = t.id
            LEFT JOIN (
                SELECT
                n1.planning_id, SUM( n3.arrival_weight ) arrival_qty
                FROM t_mp_purchase_order n1
                LEFT JOIN t_mp_purchase_order_detail n2 ON n1.id = n2.order_id AND n2.is_new = 1
                LEFT JOIN t_mp_delivery_item n3 ON n2.id = n3.order_detail_id
                GROUP BY n1.planning_id
            ) di ON di.planning_id = p.id
        WHERE p.deleted = 0
        <if test="planCode != null and planCode != ''">
            AND p.plan_code LIKE concat('%',#{planCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND p.material_name LIKE concat('%',#{materialName},'%')
        </if>
        <if test="states != null and states.size() > 0">
            AND p.`state` IN
            <foreach collection="states" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null and endTime != null">
            AND p.create_time BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="auditId != null">
            AND p.audit_id = #{auditId}
        </if>
        <if test="baseName != null and baseName != ''">
            AND p.base_name = #{baseName}
        </if>
        ORDER BY p.begin_time desc, p.state asc
    </select>

    <select id="getProcurementPlanDetail"
            resultType="com.hvisions.purchase.dto.purchase.plan.ProcurementPlanPageDTO">
        SELECT
            p.id,
            p.material_code,
            p.material_name,
            p.plan_code,
            p.begin_time,
            IFNULL(di.arrival_qty, 0) arrival_qty,
            ((IFNULL(di.arrival_qty, 0) / IFNULL(p.procurement_number, 0)) * 100) progress,
            p.use_time_start,
            p.use_time_end,
            l.name AS location_name,
            l.id AS location_id,
            p.delivery_storage_time,
            p.end_time,
            p.procurement_number,
            p.state,
            p.audit_name,
            p.audit_time,
            u.user_name AS creator_name,
            p.create_time,
            t.symbol AS uom,
            p.remark,
            p.adjust_people,
            p.adjust_time,
            p.adjust_quantity,
            p.feed_type,
            p.adjust_remark
        FROM
            t_mp_procurement_planning p
            LEFT JOIN authority.sys_user u ON p.creator_id = u.id
            LEFT JOIN t_mp_inventory_location l ON l.id = p.location_id
            LEFT JOIN materials.hv_bm_material m ON p.material_id = m.id
            LEFT JOIN materials.hv_bm_unit t ON m.uom = t.id
            LEFT JOIN (
                SELECT
                n1.planning_id, SUM( n3.arrival_weight ) arrival_qty
                FROM t_mp_purchase_order n1
                LEFT JOIN t_mp_purchase_order_detail n2 ON n1.id = n2.order_id AND n2.is_new = 1
                LEFT JOIN t_mp_delivery_item n3 ON n2.id = n3.order_detail_id
                GROUP BY n1.planning_id
            ) di ON di.planning_id = p.id
        WHERE p.deleted = 0 AND p.id = #{id}

    </select>

    <!-- 获取所有执行中采购订单的在途数量 -->
    <select id="getTransitQuantity" resultType="java.math.BigDecimal">
        SELECT (SUM(p.procurement_number) - IFNULL(SUM(di.arrival_qty), 0)) transit_quantity
        FROM t_mp_procurement_planning p
        LEFT JOIN (
            SELECT n1.planning_id, SUM( n3.arrival_weight ) arrival_qty
            FROM t_mp_purchase_order n1
            LEFT JOIN t_mp_purchase_order_detail n2 ON n1.id = n2.order_id AND n2.is_new = 1
            LEFT JOIN t_mp_delivery_item n3 ON n2.id = n3.order_detail_id
            GROUP BY n1.planning_id
        ) di ON di.planning_id = p.id
        WHERE p.state = 3 AND p.material_id = #{materialId}
        GROUP BY p.material_id
    </select>

    <!-- 获取物料类为高粱、稻壳的物料id -->
    <select id="getMaterialIds" resultType="java.lang.Integer">
        SELECT m.id
        FROM materials.hv_bm_material m
        WHERE m.material_type IN (
            SELECT t.id FROM materials.hv_bm_material_type t
            WHERE t.material_type_code = "GL" OR t.material_type_code = "DK" OR t.material_type_code = "XM"
        )
    </select>
    <select id="selectDeliverySummaryByPlan"
            resultType="com.hvisions.purchase.dto.purchase.plan.ProcurementProgressDTO">
        SELECT
            n1.planning_id, SUM( ddd.leave_net_weight ) arrivalQty, SUM (ddd.net_weight) netWeightQty
        FROM t_mp_purchase_order n1
                 LEFT JOIN t_mp_purchase_order_detail n2 ON n1.id = n2.order_id AND n2.is_new = 1
                 LEFT JOIN t_mp_delivery_item n3 ON n2.id = n3.order_detail_id
                 LEFT JOIN t_mp_daily_delivery_detail ddd on ddd.delivery_item_id = n3.id
        where n1.deleted = 0 and n1.planning_id = #{id}
        AND DATE_FORMAT(ddd.admission_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        GROUP BY n1.planning_id
    </select>

</mapper>
