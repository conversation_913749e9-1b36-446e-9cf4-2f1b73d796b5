<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.DailyDeliveryMapper">

    <!-- 分页查询日送货计划列表 -->
    <select id="getDailyDeliveryPageList" resultMap="dailyDeliveryMap">
        SELECT a.*
        FROM
        (
        SELECT po.wheat_certificate_batch, dd.base_name,
        dd.id delivery_id, dd.plan_number,deo.feed_type,dd.material_id,dd.material_code,dd.material_name,mt.material_type_name,dd.production_place,dd.deliver_quantity,dd.state, dd.remark AS not_finish_remark,
        deo.id demand_id, deo.order_no,po.order_no purchase_no,po.sap_order,deo.demand_date,deo.demand_quantity,deo.remark,po.vendor_id,v.`name` vendor_name,v.`code` vendor_code,di.arrival_weight,
        mt.material_type_code material_type_code
        FROM t_mp_daily_delivery dd
        LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
        LEFT JOIN materials.hv_bm_material m ON m.material_code = dd.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
        LEFT JOIN (
            SELECT di.delivery_id,SUM(di.arrival_weight) arrival_weight
            FROM t_mp_delivery_item di
            WHERE di.deleted = 0
            GROUP BY di.delivery_id
        ) di ON di.delivery_id = dd.id
        WHERE dd.deleted = 0
        ) a
        WHERE 1 = 1
        <if test="baseNameList != null and baseNameList.size() > 0">
            AND a.base_name IN
            <foreach collection="baseNameList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="planNumber != null and planNumber != ''">
            AND a.`plan_number` LIKE concat('%',#{planNumber},'%')
        </if>
        <if test="purchaseNo != null and purchaseNo != ''">
            AND a.`purchase_no` LIKE concat('%',#{purchaseNo},'%')
        </if>
        <if test="sapOrder != null and sapOrder != ''">
            AND a.`sap_order` LIKE concat('%',#{sapOrder},'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND a.order_no LIKE concat('%',#{orderNo},'%')
        </if>
        <if test="state != null and state != ''">
            AND a.`state`= #{state}
        </if>
        <if test="vendorId != null">
            AND a.`vendor_id` = #{vendorId}
        </if>
        <if test="materialId != null">
            AND a.`material_id` = #{materialId}
        </if>
        <if test="materialTypeCodeList != null and materialTypeCodeList.size() > 0">
            AND a.material_type_code IN
            <foreach collection="materialTypeCodeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(a.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="afterDay != null and afterDay != ''">
            AND DATE_FORMAT(a.demand_date, '%Y-%m-%d') >= DATE_FORMAT(NOW(), '%Y-%m-%d')
        </if>
        ORDER BY a.demand_date DESC
    </select>

    <resultMap id="dailyDeliveryMap" type="com.hvisions.purchase.dto.purchase.daily.delivery.DailyDeliveryPageDTO">
        <result property="id" column="delivery_id"></result>
        <result property="demandId" column="demand_id"></result>
        <result property="planNumber" column="plan_number"></result>
        <result property="orderNo" column="order_no"></result>
        <result property="purchaseNo" column="purchase_no"></result>
        <result property="sapOrder" column="sap_order"></result>
        <result property="materialId" column="material_id"></result>
        <result property="materialCode" column="material_code"></result>
        <result property="materialName" column="material_name"></result>
        <result property="materialTypeName" column="material_type_name"></result>
        <result property="demandDate" column="demand_date"></result>
        <result property="demandQuantity" column="demand_quantity"></result>
        <result property="deliverQuantity" column="deliver_quantity"></result>
        <result property="productionPlace" column="production_place"></result>
        <result property="vendorId" column="vendor_id"></result>
        <result property="vendorCode" column="vendor_code"></result>
        <result property="vendorName" column="vendor_name"></result>
        <result property="arrivalWeight" column="arrival_weight"></result>
        <result property="state" column="state"></result>
        <result property="remark" column="remark"></result>
        <result property="feedType" column="feed_type"></result>
        <result property="notFinishRemark" column="not_finish_remark"></result>
        <collection property="deliveryItemPageDTOList" column="delivery_id"
                    ofType="com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemPageDTO"
                    javaType="java.util.ArrayList"
                    select="selectItem"
        >
        </collection>
    </resultMap>

    <select id="selectItem" resultMap="itemMap">
            SELECT b.*,c.*
            FROM (
                SELECT
                di.id item_id,di.delivery_id,di.inbound_weight,di.arrival_weight, di.posting_quantity,
                di.impurity_state, di.impurity_type, di.is_supplement, di.supplement_time,
                pod.remark item_remark, pod.quantity_supplied,pod.unit_price,pod.unit_price_base,f.factory_name,l.`name` location_name
                ,SUM(ddd.impurity_weight) impurity_weight_total,
                ddi.inspect_result, ddi.submit_user, ddi.inspect_fail_reason, ddi.description, ddi.pic_urls
                FROM t_mp_delivery_item di
                LEFT JOIN t_mp_purchase_order_detail pod ON pod.id = di.order_detail_id AND pod.deleted = 0 AND pod.is_new = 1
                LEFT JOIN t_mp_purchase_order po ON po.id = pod.order_id AND po.deleted = 0
                LEFT JOIN t_mp_code_maintenance f ON f.id = po.maintenance_id AND f.deleted = 0
                LEFT JOIN t_mp_procurement_planning pp ON pp.id = po.planning_id AND pp.deleted = 0
                LEFT JOIN t_mp_inventory_location l ON l.id = pp.location_id AND l.deleted = 0
                LEFT JOIN t_mp_daily_delivery_detail ddd ON ddd.delivery_item_id = di.id AND ddd.deleted = 0
                LEFT JOIN t_mp_daily_delivery_inspect ddi ON ddi.delivery_detail_id = ddd.id AND ddi.deleted = 0
                WHERE di.deleted = 0
                GROUP BY di.id
                ) b
            LEFT JOIN
                (
                SELECT
                ddd.id delivery_detail_id,ddd.delivery_item_id,ddd.delivery_number,ddd.license_plate_number,ddd.estimated_number detail_deliver_quantity,ddd.admission_time,it.quality_result,ddd.state detail_state,ddd.inspect_state,
                ddd.material_code detail_material_code,ddd.material_name detail_material_name,
                ddd.gross_weight,ddd.buckle_weight,ddd.net_weight,ddd.appearance_time,ddd.appearance_weight,ddd.leave_net_weight,ddd.impurity_weight,
                ddd.batch,ddd.posting_state,ddd.buckle_is_handle,ddd.mat_doc,ddd.warehouse_id,mt.material_type_code,ddd.sync_state
                FROM t_mp_daily_delivery_detail ddd
                LEFT JOIN materials.hv_bm_material m ON m.material_code = ddd.material_code
	            LEFT JOIN materials.hv_bm_material_type mt ON mt.id = m.material_type
                LEFT JOIN (
                    SELECT it.inspection_result quality_result,it.associated_document
                    FROM t_qa_inspection_task it
                    WHERE it.id IN (SELECT MAX(i.id) id FROM t_qa_inspection_task i GROUP BY i.associated_document)
                ) it ON it.associated_document = ddd.delivery_number
                WHERE ddd.deleted = 0
                ) c ON b.item_id = c.delivery_item_id
            WHERE b.delivery_id = #{delivery_id}
    </select>

    <resultMap id="itemMap" type="com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemPageDTO">
        <result property="id" column="item_id"></result>
        <result property="quantitySupplied" column="quantity_supplied"></result>
        <result property="itemRemark" column="item_remark"></result>
        <result property="inboundWeight" column="inbound_weight"></result>
        <result property="arrivalWeight" column="arrival_weight"></result>
        <result property="unitPrice" column="unit_price"></result>
        <result property="unitPriceBase" column="unit_price_base"></result>
        <result property="factoryName" column="factory_name"></result>
        <result property="locationName" column="location_name"></result>
        <result property="postingQuantity" column="posting_quantity"></result>
        <result property="impurityWeightTotal" column="impurity_weight_total"></result>
        <result property="impurityState" column="impurity_state"></result>
        <result property="impurityType" column="impurity_type"></result>
        <result property="isSupplement" column="is_supplement"></result>
        <result property="supplementTime" column="supplement_time"></result>
        <collection property="deliveryDetailPageDTOList" ofType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailPageDTO">
            <result property="id" column="delivery_detail_id"></result>
            <result property="deliveryNumber" column="delivery_number"></result>
            <result property="licensePlateNumber" column="license_plate_number"></result>
            <result property="estimatedNumber" column="detail_deliver_quantity"></result>
            <result property="materialCode" column="detail_material_code"></result>
            <result property="materialName" column="detail_material_name"></result>
            <result property="qualityResult" column="quality_result"></result>
            <result property="state" column="detail_state"></result>
            <result property="syncState" column="sync_state"></result>
            <result property="inspectState" column="inspect_state"></result>
            <result property="admissionTime" column="admission_time"></result>
            <result property="grossWeight" column="gross_weight"></result>
            <result property="appearanceTime" column="appearance_time"></result>
            <result property="appearanceWeight" column="appearance_weight"></result>
            <result property="leaveNetWeight" column="leave_net_weight"></result>
            <result property="buckleWeight" column="buckle_weight"></result>
            <result property="impurityWeight" column="impurity_weight"></result>
            <result property="netWeight" column="net_weight"></result>
            <result property="batch" column="batch"></result>
            <result property="postingState" column="posting_state"></result>
            <result property="buckleIsHandle" column="buckle_is_handle"></result>
            <result property="matDoc" column="mat_doc"></result>
            <result property="warehouseId" column="warehouse_id"></result>
            <result property="materialTypeCode" column="material_type_code"></result>
            <result property="inspectResult" column="inspect_result"></result>
            <result property="submitUser" column="submit_user"></result>
            <result property="inspectFailReason" column="inspect_fail_reason"></result>
            <result property="description" column="description"></result>
            <result property="picUrls" column="pic_urls"></result>
        </collection>
    </resultMap>

    <!-- 根据要货日期获取采购计划id -->
    <select id="getDailyDeliveryIds" resultType="java.lang.Integer">
        SELECT d.id
        FROM
             t_mp_daily_delivery d
            LEFT JOIN t_mp_demand_order o ON d.demand_id = o.id
        WHERE
            DATE_FORMAT(o.demand_date, '%Y-%m-%d') <![CDATA[<]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
            AND d.state != 2
    </select>

    <select id="getNotInVehicleList" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailDTO">
        SELECT ddd.*
        FROM t_mp_daily_delivery_detail ddd
        LEFT JOIN t_mp_delivery_item di ON ddd.delivery_item_id = di.id AND di.deleted = 0
        LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
        LEFT JOIN t_mp_demand_order o ON dd.demand_id = o.id AND o.deleted = 0
        WHERE ddd.deleted = 0 AND ddd.state = 0 AND DATE_FORMAT(o.demand_date, '%Y-%m-%d') <![CDATA[<]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
    </select>

</mapper>
