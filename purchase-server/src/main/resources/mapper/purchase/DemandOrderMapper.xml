<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.purchase.purchase.dao.DemandOrderMapper">

    <!-- 分页查询要货需求单列表 -->
    <select id="getDemandOrderPageList"
            resultType="com.hvisions.purchase.dto.purchase.demand.order.DemandOrderPageDTO">
        SELECT d.*,GROUP_CONCAT(po.order_no) purchase_order,u.user_name update_name
        FROM `t_mp_demand_order` d
        LEFT JOIN t_mp_daily_delivery dd ON dd.demand_id = d.id AND dd.deleted = 0
        LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
        LEFT JOIN `authority`.sys_user AS u ON u.id = d.updater_id
        LEFT JOIN materials.hv_bm_material m ON m.material_code = d.material_code
        LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
        WHERE d.deleted = 0
        <if test="baseName != null and baseName != ''">
            AND d.base_name = #{baseName}
        </if>
        <if test="baseNameList != null and baseNameList.size() > 0">
            AND d.base_name IN
            <foreach collection="baseNameList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND d.`order_no` LIKE concat('%',#{orderNo},'%')
        </if>
        <if test="purchaseOrder != null and purchaseOrder != ''">
            AND po.order_no LIKE concat('%',#{purchaseOrder},'%')
        </if>
        <if test="materialId != null">
            AND d.`material_id` = #{materialId}
        </if>
        <if test="materialName != null and materialName != ''">
            AND d.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="materialTypeCodeList != null and materialTypeCodeList.size() > 0">
            AND mt.material_type_code IN
            <foreach collection="materialTypeCodeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="states != null and states.size() > 0">
            AND d.`state` IN
            <foreach collection="states" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(d.demand_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        <if test="feedType != null">
            AND d.`feed_type` = #{feedType}
        </if>
        GROUP BY d.id
        ORDER BY d.demand_date DESC
    </select>

<!--    根据要货单id获取送货单详情-->
    <select id="getDeliveryDetailByDemandId" resultType="com.hvisions.purchase.dto.purchase.daily.delivery.detail.DailyDeliveryDetailDTO">
        SELECT ddd.*
        FROM `t_mp_daily_delivery_detail` ddd
        LEFT JOIN t_mp_delivery_item di ON di.id = ddd.delivery_item_id
        LEFT JOIN t_mp_daily_delivery	dd ON dd.id = di.delivery_id
        WHERE dd.demand_id = #{id}
    </select>

    <!-- 根据物料类型获取所有的物料id -->
    <select id="getMaterialIds" resultType="java.lang.Integer">
        SELECT m.id
        FROM materials.hv_bm_material m
        WHERE m.material_type = (SELECT t.id FROM materials.hv_bm_material_type t WHERE t.material_type_code = #{type})
    </select>

    <!-- 获取物料未送货数量、库存，数量总和 -->
    <select id="getQuantitySum"
            resultType="com.hvisions.purchase.dto.purchase.demand.order.QuantitySumDTO">
        SELECT
            (
                SELECT SUM((p.procurement_number - di.arrival_qty)) FROM t_mp_procurement_planning p
                LEFT JOIN (
                    SELECT n1.planning_id, SUM( IFNULL(n3.arrival_weight, 0) ) arrival_qty
                    FROM t_mp_purchase_order n1
                    LEFT JOIN t_mp_purchase_order_detail n2 ON n1.id = n2.order_id AND n2.is_new = 1
                    LEFT JOIN t_mp_delivery_item n3 ON n2.id = n3.order_detail_id
                    GROUP BY n1.planning_id
                ) di ON di.planning_id = p.id
                WHERE p.material_id IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND p.deleted = 0 AND p.state = 3
            ) perform_qty,
            (
                SELECT IFNULL(SUM(p.procurement_number), 0) FROM t_mp_procurement_planning p
                WHERE p.material_id IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND p.deleted = 0 AND p.state = 2
            ) wait_qty,
            (
                SELECT IFNULL(SUM(w.stock_quantity), 0) FROM brewage_rawmaterial_production.t_mpd_warehouse_data w
                WHERE w.material_id IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND w.deleted = 0
            ) stock_quantity
    </select>


    <select id="getMaterialStockDetail" resultType="com.hvisions.purchase.dto.purchase.demand.order.MaterialStockDetailDTO">
        SELECT a.*,IF(b.stock IS NULL,a.plan_total,a.plan_total - b.stock) plan_remain_total,IFNULL(c.undelivered_total,0) undelivered_total,
            (
                SELECT SUM(wd.stock_quantity) stock
                FROM brewage_rawmaterial_production.t_mpd_warehouse_data wd
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m1 ON wd.storage_id = m1.id AND m1.deleted = 0
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m2 ON m2.id = m1.parent_id AND m2.deleted = 0
                WHERE wd.deleted = 0 AND m2.`code` = "GL01" AND a.material_type_name = "高粱"
            ) gl1,
            (
                SELECT SUM(wd.stock_quantity) stock
                FROM brewage_rawmaterial_production.t_mpd_warehouse_data wd
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m1 ON wd.storage_id = m1.id AND m1.deleted = 0
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m2 ON m2.id = m1.parent_id AND m2.deleted = 0
                WHERE wd.deleted = 0 AND m2.`code` = "GL02" AND a.material_type_name = "高粱"
            ) gl2,
            (
                SELECT SUM(wd.stock_quantity) stock
                FROM brewage_rawmaterial_production.t_mpd_warehouse_data wd
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m1 ON wd.storage_id = m1.id AND m1.deleted = 0
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m2 ON m2.id = m1.parent_id AND m2.deleted = 0
                WHERE wd.deleted = 0 AND m2.`code` = "DK01" AND a.material_type_name = "稻壳"
            ) dk1,
            (
                SELECT SUM(wd.stock_quantity) stock
                FROM brewage_rawmaterial_production.t_mpd_warehouse_data wd
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m1 ON wd.storage_id = m1.id AND m1.deleted = 0
                LEFT JOIN brewage_rawmaterial_production.t_mpd_rl_management m2 ON m2.id = m1.parent_id AND m2.deleted = 0
                WHERE wd.deleted = 0 AND m2.`code` = "DK02" AND a.material_type_name = "稻壳"
            ) dk2

        FROM
            (
            SELECT mt.material_type_name,IFNULL( SUM(pp.procurement_number),0)  plan_total
            FROM t_mp_procurement_planning pp
            LEFT JOIN materials.hv_bm_material m ON m.id = pp.material_id
            LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
            WHERE pp.deleted = 0
            GROUP BY mt.material_type_code
            ) a
        LEFT JOIN
            (SELECT
                mt.material_type_name,IFNULL(SUM(di.inbound_weight),0) stock

                FROM t_mp_delivery_item di

                LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
                LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
                LEFT JOIN t_mp_procurement_planning pp ON pp.id = po.planning_id AND pp.deleted = 0
                LEFT JOIN materials.hv_bm_material m ON m.id = dd.material_id
                LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
                WHERE di.deleted = 0
                GROUP BY mt.material_type_code
            ) b ON a.material_type_name = b.material_type_name

        LEFT JOIN (
             SELECT a.material_type_name,IF(IFNULL((SUM(a.deliver_quantity) - SUM(a.leave_net_weight)),0) > 0,IFNULL((SUM(a.deliver_quantity) - SUM(a.leave_net_weight)),0),0) undelivered_total
             FROM (

                    SELECT any_value(mt.material_type_name) material_type_name,dd.deliver_quantity,IFNULL(SUM(ddd.leave_net_weight) ,0) leave_net_weight
                    FROM t_mp_daily_delivery_detail ddd
                    LEFT JOIN t_mp_delivery_item di ON ddd.delivery_item_id = di.id AND di.deleted = 0
                    LEFT JOIN t_mp_daily_delivery dd ON dd.id = di.delivery_id AND dd.deleted = 0
                    LEFT JOIN t_mp_demand_order dor ON dor.id = dd.demand_id AND dor.deleted = 0
                    LEFT JOIN materials.hv_bm_material m ON m.id = ddd.material_id
                    LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
                    WHERE ddd.deleted = 0
                    AND date_format(dor.demand_date,'%Y-%m-%d')  = date_format(now(),'%Y-%m-%d')
                    AND ddd.material_code != "0000" AND ddd.material_name != "丢糟"
                    GROUP BY dd.id
             ) a
            GROUP BY a.material_type_name
        ) c ON a.material_type_name = c.material_type_name
    </select>
    <select id="selectDatesByList" resultType="java.lang.String">
        select DISTINCT DATE_FORMAT(demand_date, '%Y-%m-%d') demand_date from t_mp_demand_order where deleted = 0
        <if test="idsList != null and idsList.size() > 0">
            and id not IN
            <foreach collection="idsList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dateList != null and dateList.size() > 0">
            and demand_date IN
            <foreach collection="dateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="isXm != null and isXm == 1">
            and (material_code = '11000565' OR material_code = '11000566' OR material_code = '11000572')
        </if>
        <if test="isXm != null and isXm == 2">
            and (material_code = '11000564' OR material_code = '11000543' OR material_code = '11000544' OR material_code = '11000592' OR material_code = '11000560')
        </if>
    </select>

</mapper>
