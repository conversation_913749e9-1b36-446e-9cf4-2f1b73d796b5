package com.hvisions.rawmaterial.enums.unified;

/**
 * 业务系统枚举
 * 定义原料生产系统中所有的业务系统类型
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public enum BusinessSystemEnum {

    /**
     * 中粮系统
     */
    ZHONGLIANG("ZHONGLIANG", "中粮"),
    
    /**
     * 布勒系统
     */
    BULER("BULER", "布勒"),
    
    /**
     * 捷赛系统
     */
    JIESAI("JIESAI", "捷赛");

    /**
     * 业务系统代码
     */
    private final String code;
    
    /**
     * 业务系统描述
     */
    private final String description;

    BusinessSystemEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 业务系统代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BusinessSystemEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BusinessSystemEnum businessSystem : values()) {
            if (businessSystem.getCode().equals(code)) {
                return businessSystem;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     * 
     * @param description 业务系统描述
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BusinessSystemEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (BusinessSystemEnum businessSystem : values()) {
            if (businessSystem.getDescription().equals(description)) {
                return businessSystem;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return code + " - " + description;
    }
}
