package com.hvisions.rawmaterial.enums;

public enum MaterialTypeEnum {
    SORGHUM("SORGHUM", 0, "高粱"),
    RICE_HUSK("RICE_HUSK", 1, "稻壳"),
    FLOUR("FLOUR", 2, "小麦"),
    ;

    private String code;
    private Integer index;
    private String desc;

    MaterialTypeEnum(String code, Integer index, String desc) {
        this.code = code;
        this.index = index;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public Integer getIndex() {
        return index;
    }

    public static MaterialTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(MaterialTypeEnum.SORGHUM);
    }
}
