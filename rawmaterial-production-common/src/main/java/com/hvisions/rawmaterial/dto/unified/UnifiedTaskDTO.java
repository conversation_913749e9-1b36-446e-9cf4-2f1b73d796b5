package com.hvisions.rawmaterial.dto.unified;

import com.hvisions.rawmaterial.dto.unified.base.BaseTaskDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 统一任务DTO - 合并所有任务类型
 * 继承基础任务字段，所有扩展字段平铺展开
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "统一任务DTO")
public class UnifiedTaskDTO extends BaseTaskDTO {

    // ==================== 高粱业务扩展字段 ====================


    // ==================== 通用扩展字段 ====================

    //@ApiModelProperty(value = "数据类型：TASK-任务，REALTIME-实时数据")
    //private String dataType;

    @ApiModelProperty(value = "扩展字段1-日志ID")
    private String extField1;

    @ApiModelProperty(value = "扩展字段2")
    private String extField2;

    @ApiModelProperty(value = "扩展字段3")
    private String extField3;

    @ApiModelProperty(value = "扩展字段4")
    private String extField4;

    @ApiModelProperty(value = "扩展字段5")
    private String extField5;

    @ApiModelProperty(value = "扩展字段6")
    private Integer extField6;

    @ApiModelProperty(value = "扩展字段7")
    private Integer extField7;
}
