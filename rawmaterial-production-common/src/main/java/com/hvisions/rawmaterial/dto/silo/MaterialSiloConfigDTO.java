package com.hvisions.rawmaterial.dto.silo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: 物料筒仓配置DTO
 * @author: z19235
 * @time: 2025/01/01
 */
@Data
@ApiModel(description = "物料筒仓配置DTO")
public class MaterialSiloConfigDTO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "筒仓编码", required = true)
    @NotBlank(message = "筒仓编码不能为空")
    private String code;

    @ApiModelProperty(value = "筒仓名称", required = true)
    @NotBlank(message = "筒仓名称不能为空")
    private String name;

    @ApiModelProperty(value = "ERP仓库编码")
    private String erpCode;

    @ApiModelProperty(value = "最大容量")
    private BigDecimal maxCapacity;

    @ApiModelProperty(value = "安全库存")
    private BigDecimal safetyStock;

    @ApiModelProperty(value = "物料类型：0-高粱，1-稻壳，2-小麦", required = true)
    @NotNull(message = "物料类型不能为空")
    private Integer materialType;

    @ApiModelProperty(value = "父节点ID")
    private Integer parentId;

    @ApiModelProperty(value = "仓库ID")
    private Integer warehouseId;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "中控对应数据")
    private String centralData;

    @ApiModelProperty(value = "状态：0-未锁定，1-已锁定")
    private String state;

    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer workshopId;

    /**
     * 父节点code
     */
    @ApiModelProperty(value = "父节点code")
    private String parentCode;
}
