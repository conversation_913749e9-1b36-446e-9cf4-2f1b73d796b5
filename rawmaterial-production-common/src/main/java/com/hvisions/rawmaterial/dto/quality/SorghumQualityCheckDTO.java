package com.hvisions.rawmaterial.dto.quality;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 高粱筒仓快检数据DTO
 * 用于处理高粱二期筒仓进出快检数据对接（淀粉、水分、蛋白质）
 *
 * <AUTHOR>
 * @date 2025-09-23
 */
@Data
@ApiModel(description = "高粱筒仓快检数据DTO")
public class SorghumQualityCheckDTO {

    @ApiModelProperty(value = "业务系统", example = "ZHONGLIANG", required = true)
    @NotBlank(message = "业务系统不能为空")
    private String businessSystem;

    @ApiModelProperty(value = "物料类型", example = "SORGHUM")
    private String materialType;

    @ApiModelProperty(value = "任务类型", required = true)
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    @ApiModelProperty(value = "数据类型", example = "TASK")
    private String dataType;

    @ApiModelProperty(value = "任务号", required = true)
    @NotBlank(message = "任务号不能为空")
    private String taskNo;

    @ApiModelProperty(value = "筒仓号")
    private String siloNo;

    @ApiModelProperty(value = "筒仓名称")
    private String siloName;

    @ApiModelProperty(value = "快检地点（进、出）")
    private String qualityCheckLocation;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "淀粉含量(%)")
    private BigDecimal starchContent;

    @ApiModelProperty(value = "水分含量(%)")
    private BigDecimal moistureContent;

    @ApiModelProperty(value = "蛋白质含量(%)")
    private BigDecimal proteinContent;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "检测人员")
    private String inspector;

    @ApiModelProperty(value = "检测设备")
    private String equipment;

    @ApiModelProperty(value = "检测方法")
    private String method;

    @ApiModelProperty(value = "样品批次号")
    private String batchNo;

    @ApiModelProperty(value = "检测状态：0-待检测，1-检测中，2-检测完成，3-检测异常")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    @ApiModelProperty(value = "中控任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "唯一标识")
    private String uniqueId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "日志ID")
    private String logId;
}
