package com.hvisions.rawmaterial.dto.unified;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ApiModel(description = "统一物料信息")
public class UnifiedMaterialDTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1", readOnly = true)
    protected Integer id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", notes = "创建记录时传递", readOnly = true)
    protected Integer creatorId = 0;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", notes = "更新记录时传递", readOnly = true)
    protected Integer updaterId = 0;


    /**
     * 系统代码
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "系统代码", readOnly = true)
    protected String siteNum;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @Length(min = 1, max = 100, message = "物料编码支持1-100字符")
    @ApiModelProperty(value = "物料编码", required = true, readOnly = true)
    String materialCode;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不允许为空")
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料类型
     */
    @NotNull(message = "物料类型不允许为空")
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "物料类型Id", required = true)
    private Integer materialType;
    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    /**
     * 物料分组
     */
    @ApiModelProperty(value = "物料分组ID")
    @ExcelAnnotation(ignore = true)
    private Integer materialGroup;


    /***
     * 特征值
     */
    @ApiModelProperty(value = "特征值")
    private String eigenvalue;

    /**
     * 单位
     */
    @NotNull(message = "计量单位不允许为空")
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "单位ID")
    private Integer uom;
    /**
     * 是否追溯
     */
    @ApiModelProperty(value = "是否追溯")
    private Boolean serialNumberProfile;

    /**
     * 图片ID
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "图片ID")
    private Integer photoId;

    /**
     * 物料类型名称
     */
    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    /**
     * 计量单位名称
     */
    @ApiModelProperty(value = "计量单位名称")
    private String uomName;

    /**
     * 计量单位编码
     */
    @ApiModelProperty(value = "计量单位编码")
    private String uomCode;

    /**
     * 物料分组编码
     */
    @ApiModelProperty(value = "物料分组编码")
    private String materialGroupCode;
    /**
     * 物料分组描述
     */
    @ApiModelProperty(value = "物料分组描述")
    private String materialGroupDesc;

    /**
     * 物料分组描述
     */
    @ApiModelProperty(value = "物料分组名称")
    private String materialGroupName;

    /**
     * 与物料绑定的bom的编码
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "与物料绑定的bom的编码")
    private String bomCode;

    /**
     * bomId
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "bomId")
    private Integer bomId;

    /**
     * bom名称
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "bomName")
    private String bomName;
    /**
     * bom名称
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "bomName")
    private String bomVersion;
}
