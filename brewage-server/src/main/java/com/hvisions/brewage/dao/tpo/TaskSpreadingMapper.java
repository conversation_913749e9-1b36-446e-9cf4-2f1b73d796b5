package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;import com.hvisions.brewage.dto.tpo.TaskSpreadingQueryDTO;import com.hvisions.brewage.entity.tpo.TaskSpreading;import com.hvisions.brewage.vo.tpo.TaskSpreadingVO;import org.apache.ibatis.annotations.Param;import java.util.List;

public interface TaskSpreadingMapper extends BaseMapper<TaskSpreading> {
    /**
     * 查询配置最大的流水号
     *
     * @return
     */
    Integer selectMaxTaskNo(@Param("typeName") String typeName);

    /**
     * 分页查询
     *
     * @param page
     * @param queryDTO
     * @return
     */
    List<TaskSpreadingVO> selectPageList(Page<TaskSpreadingVO> page, @Param("query") TaskSpreadingQueryDTO queryDTO);

    /**
     * 根据iot传的窖号查询连窖号
     *
     * @param pitNo iot传的窖号
     * @return
     */
    String selectPitNo(@Param("pitNo") String pitNo);

    String selectPitNoReTheRow(@Param("targetPitNo") String targetPitNo);
}