package com.hvisions.brewage.dao.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.base.TeamScheduledOriginalQueryDTO;
import com.hvisions.brewage.dto.base.TeamScheduledQueryDTO;
import com.hvisions.brewage.entity.base.TeamScheduled;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TeamScheduledMapper extends BaseMapper<TeamScheduled> {

    /**
     * 查询排班管理列表
     * @param queryDTO
     * @return
     */
    List<TeamScheduled> selectTeamScheduledList(@Param("query") TeamScheduledQueryDTO queryDTO);

    /**
     * 分页查询排班管理列表-原始数据
     * @param page
     * @param queryDTO
     * @return
     */
    List<TeamScheduled> selectPageList(Page<TeamScheduled> page,@Param("query") TeamScheduledOriginalQueryDTO queryDTO);

    /**
     * 查询人资共享车间|中心编码
     * @param locationId
     * @return
     */
    String selectYzgxCode(Integer locationId);
}