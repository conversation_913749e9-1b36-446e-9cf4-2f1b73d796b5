package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;import com.hvisions.brewage.dto.tpo.TaskLoadingDetailQueryDTO;import com.hvisions.brewage.entity.tpo.TaskLoadingDetail;import com.hvisions.brewage.vo.tpo.TaskLoadingDetailVO;import org.apache.ibatis.annotations.Param;import java.util.List;

public interface TaskLoadingDetailMapper extends BaseMapper<TaskLoadingDetail> {
    /**
     * 分页查询
     *
     * @param page
     * @param queryDTO
     * @return
     */
    List<TaskLoadingDetailVO> selectPageList(Page<TaskLoadingDetailVO> page, @Param("query") TaskLoadingDetailQueryDTO queryDTO);
}