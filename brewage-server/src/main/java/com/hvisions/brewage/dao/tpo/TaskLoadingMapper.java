package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TaskLoadingIdentifyQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskLoadingPDAQueryDTO;import com.hvisions.brewage.dto.tpo.TaskLoadingQueryDTO;import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.vo.tpo.IdentifyVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingSourcePitNoVO;import com.hvisions.brewage.vo.tpo.TaskLoadingTargetPitNoVO;import com.hvisions.brewage.vo.tpo.TaskLoadingVO;import org.apache.ibatis.annotations.Param;import java.util.List;

public interface TaskLoadingMapper extends BaseMapper<TaskLoading> {
    /**
     * 查询配置最大的流水号
     *
     * @return
     */
    Integer selectMaxTaskNo(@Param("typeName") String typeName);

    /**
     * 分页查询-WEB
     *
     * @param page
     * @param queryDTO
     * @return
     */
    List<TaskLoadingVO> selectPageList(Page<TaskLoadingVO> page, @Param("query") TaskLoadingQueryDTO queryDTO);

    /**
     * 分页查询-PDA
     *
     * @param page
     * @param queryDTO
     * @return
     */
    List<TaskLoadingVO> selectPagePDAList(Page<TaskLoadingVO> page, @Param("query") TaskLoadingPDAQueryDTO queryDTO);

    /**
     * 查询入窖确认入窖窖池号
     *
     * @param materialType 糟源类别
     * @return
     */
    List<TaskLoadingTargetPitNoVO> selectTargetPitNoList(@Param("materialType") String materialType);

    /**
     * 查询来源窖号
     *
     * @param id 入窖任务id
     * @return
     */
    List<TaskLoadingSourcePitNoVO> selectSourcePitNoList(@Param("id") Integer id);

    /**
     * 查询待入窖鉴定列表
     * @param page
     * @param queryDTO
     * @return
     */
    List<IdentifyVO> selectPageIdentifyList(Page<IdentifyVO> page,@Param("query") TaskLoadingIdentifyQueryDTO queryDTO);
}