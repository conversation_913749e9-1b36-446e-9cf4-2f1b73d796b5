package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.entity.tpo.TaskSpreadingDetail;
import com.hvisions.brewage.vo.tpo.MachineBindVO;
import org.apache.ibatis.annotations.Param;

public interface TaskSpreadingDetailMapper extends BaseMapper<TaskSpreadingDetail> {
    /**
     * 入窖窖号+糟源类别为空+订单状态等于-1
     *
     * @param pitNo 入窖窖号
     * @return
     */
    String selectWorkshopPitOrderOne(@Param("pitNo") String pitNo);

    /**
     * 第二次：根据入窖窖号+糟源类别+订单状态等于0
     *
     * @param pitNo             入窖窖号
     * @param enterMaterialType 入窖糟源
     * @return
     */
    String selectWorkshopPitOrderTwo(@Param("pitNo") String pitNo, @Param("enterMaterialType") String enterMaterialType);

    /**
     * 查询层次:入窖窖号+订单状态（-1和0和1）
     *
     * @param pitNo
     * @return
     */
    Integer selectWorkshopPitOrderLayer(@Param("pitNo") String pitNo);

    /**
     * 查询糟源类别id
     *
     * @param materialType 糟源类别
     * @return
     */
    Integer selectVinasseId(@Param("materialType") String materialType);

    /**
     * 查询糟源类别
     *
     * @param vinasseId 糟源类别Id
     * @return
     */
    String selectMaterialType(@Param("vinasseId") Integer vinasseId);

    /**
     * 查询较次订单对应的区域
     *
     * @param pitOrder 窖池订单号
     * @return
     */
    String selectOrderLintName(@Param("orderCode") String pitOrder);

    /**
     * 根据摊晾机编号，查询曲粉物料编码
     * @param centerId 中心id
     * @param locationId 车间id
     * @param spreaderNo iot摊晾机编号
     * @return
     */
    MachineBindVO selectQdMaterialCode(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId, @Param("spreaderNo") String spreaderNo);

    /**
     * 根据摊晾机编号，查询曲粉id
     * @param centerId 中心id
     * @param locationId 车间id
     * @param spreaderNo iot摊晾机编号
     * @return
     */
    Integer selectQuDouId(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId, @Param("spreaderNo") String spreaderNo);

    /**
     * 修改曲斗更换的物料批次
     * @param machineBindLogId  曲斗更换日志id
     * @param batch 批次
     * @param materialId 物料id
     */
    void updateMachineBindLog(@Param("machineBindLogId") Integer machineBindLogId,@Param("batch") String batch,@Param("materialId") Integer materialId);

    /**
     * 根据物料id查询物料编码
     * @param materialId 物料id
     * @return
     */
    String selectMaterialIdByCode(@Param("materialId") Integer materialId);
}