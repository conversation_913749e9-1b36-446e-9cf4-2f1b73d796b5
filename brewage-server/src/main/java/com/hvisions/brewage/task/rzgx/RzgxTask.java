package com.hvisions.brewage.task.rzgx;

import com.hvisions.brewage.aop.LoggingContext;
import com.hvisions.brewage.service.rzgx.RzgxSendService;
import com.hvisions.brewage.service.tpo.TaskInspectionService;
import com.hvisions.brewage.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: RzgxTask
 * @description:
 * @date 2025/9/15 16:48
 */
@Slf4j
@Component
@EnableScheduling
public class RzgxTask {
    @Resource
    private RzgxSendService rzgxSendService;

    /**
     * 每月6日凌晨1点执行同步上个月排班数据
     * @return
     */
    @Scheduled(cron = "0 0 1 6 * ? ")
    @SchedulerLock(name = "taskAddSyncRzgxScheduled")
    public void taskAddSyncRzgxScheduled(){
        LoggingContext.startLoggingContext();
        log.info("每月6日凌晨1点执行同步上个月排班数据-开始");
        String[] monthRange = DateUtil.getLastMonthRange();
        rzgxSendService.mesToSyncRzgxScheduled(null,null,monthRange[0],monthRange[1]);
        log.info("每月6日凌晨1点执行同步上个月排班数据-结束");
    }

    /**
     * 每天凌晨1点执行同步当日排班数据
     * @return
     */
    @Scheduled(cron = "0 0 1 * * ? ")
    @SchedulerLock(name = "taskAddSyncRzgxScheduledDT")
    public void taskAddSyncRzgxScheduledDT(){
        LoggingContext.startLoggingContext();
        log.info("每天凌晨1点执行同步当日排班数据-开始");
        String currentDate = DateUtil.currentDate();
        rzgxSendService.mesToSyncRzgxScheduled(null,null,currentDate,currentDate);
        log.info("每天凌晨1点执行同步当日排班数据-结束");
    }

    /**
     * 每天中午12点执行同步昨日排班数据
     * @return
     */
    @Scheduled(cron = "0 0 12 * * ? ")
    @SchedulerLock(name = "taskAddSyncRzgxScheduledZR")
    public void taskAddSyncRzgxScheduledZR(){
        LoggingContext.startLoggingContext();
        log.info("每天中午12点执行同步昨日排班数据-开始");
        String yesterdayDate = DateUtil.getYesterdayDate();
        rzgxSendService.mesToSyncRzgxScheduled(null,null,yesterdayDate,yesterdayDate);
        log.info("每天中午12点执行同步昨日排班数据-结束");
    }

    /**
     * 每个月1号凌晨1点执行同步班组数据
     * @return
     */
    @Scheduled(cron = "0 0 1 1 * ? ")
    @SchedulerLock(name = "taskAddTaskSyncRzgxTeam")
    public void taskAddTaskSyncRzgxTeam(){
        LoggingContext.startLoggingContext();
        log.info("每个月1号凌晨1点执行同步班组数据-开始");
        rzgxSendService.mesToSyncRzgxTeam();
        log.info("每个月1号凌晨1点执行同步班组数据-结束");
    }
}
