package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskShangzhengDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskShangzhengMapper;
import com.hvisions.brewage.dto.tpo.TaskShangzhengDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskShangzhengDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskShangzheng;
import com.hvisions.brewage.entity.tpo.TaskShangzhengDetail;
import com.hvisions.brewage.enums.LogCaptureEnum;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskShangzhengDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskShangzhengDetailService
 * @description: 上甑任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskShangzhengDetailService {

    @Resource
    private TaskShangzhengDetailMapper taskShangzhengDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskShangzhengMapper taskShangzhengMapper;
    @Resource
    private TaskShangzhengService taskShangzhengService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询上甑任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskShangzhengDetailVO> findPageList(TaskShangzhengDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskShangzhengDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskShangzhengDetail::getDeleted, 0)
                .eq(TaskShangzhengDetail::getTaskShangzhengId, queryDTO.getTaskShangzhengId());
        IPage<TaskShangzhengDetail> page = taskShangzhengDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskShangzhengDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskShangzhengDetailVO.class);
        
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskShangzhengDetailAddDTO addDTO) {
        log.info("调用新增上甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增上甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskShangzhengDetail shangzhengDetail = DtoMapper.convert(addDTO, TaskShangzhengDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskShangzhengMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskShangzheng> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskShangzheng::getDeleted, 0)
                .eq(TaskShangzheng::getPitNo, pitNo)
                .eq(TaskShangzheng::getVinasseName, addDTO.getOutMaterialType())
                .orderByDesc(TaskShangzheng::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskShangzheng taskShangzheng = taskShangzhengMapper.selectOne(wrapper);
        if (null != taskShangzheng) {

            //判断任务是否是待执行
            if (taskShangzheng.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskShangzheng.setState(TaskStatus.IN_PROGRESS.getCode());
                taskShangzheng.setStartTime(new Date());
                taskShangzhengMapper.updateById(taskShangzheng);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskShangzheng.getCenterId(), taskShangzheng.getLocationId(), taskShangzheng.getPitOrder(), ExecutionStatus.STEAMING.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskShangzhengService.endTaskShangzheng(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskShangzhengDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskShangzhengDetail::getDeleted, 0)
                    .eq(TaskShangzhengDetail::getTaskShangzhengId,taskShangzheng.getId());
            List<TaskShangzhengDetail> list = taskShangzhengDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskShangzheng.getPitOrder(), ExecutionStatus.STEAMING.getStatus(),1);
            }
        }

        shangzhengDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        shangzhengDetail.setCreateTime(new Date());
        shangzhengDetail.setCreatorId(userId);
        shangzhengDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskShangzhengDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskShangzhengDetail::getDeleted, 0)
                .eq(TaskShangzhengDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskShangzhengDetail taskShangzhengDetail = taskShangzhengDetailMapper.selectOne(wrapperDetail);
        if (null == taskShangzhengDetail) {
            if (null != taskShangzheng) {
                shangzhengDetail.setTaskShangzhengId(taskShangzheng.getId());
            }

            taskShangzhengDetailMapper.insert(shangzhengDetail);
        } else {
            if (null != taskShangzheng) {
                shangzhengDetail.setTaskShangzhengId(taskShangzheng.getId());
            }
            shangzhengDetail.setId(taskShangzhengDetail.getId());
            taskShangzhengDetailMapper.updateById(shangzhengDetail);
        }
        //校验参数是否有工艺异常，有则告警
        //1.上甑时长
//        if(!StringUtils.isEmpty(addDTO.getDurationOfSteaming())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SHANGZHENG,
//                    RouteParameterWarnParamEnum.SZ_DurationOfSteaming.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getDurationOfSteaming())
//            );
//        }
//        //2.压汽深度
//        if(!StringUtils.isEmpty(addDTO.getSteamPressureDepth())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SHANGZHENG,
//                    RouteParameterWarnParamEnum.SZ_SteamPressureDepth.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getSteamPressureDepth())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskShangzhengDetailAddDTO> addDTOList) {
        log.info("调用批量新增上甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增上甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增上甑任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskShangzhengDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增上甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.SHANGZHENG_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskShangzheng_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskShangzheng_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除上甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除上甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskShangzhengDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
