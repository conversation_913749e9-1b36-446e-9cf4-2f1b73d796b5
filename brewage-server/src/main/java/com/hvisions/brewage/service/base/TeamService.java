package com.hvisions.brewage.service.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dao.base.TeamMapper;
import com.hvisions.brewage.dao.base.TeamUserMapper;
import com.hvisions.brewage.dto.base.TeamQueryDTO;
import com.hvisions.brewage.dto.base.TeamUserQueryDTO;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.entity.base.Team;
import com.hvisions.brewage.entity.base.TeamUser;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.base.ProdConfigItemVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamService
 * @description: 班组管理Service
 * @date 2025/9/15 16:26
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TeamService {
    @Resource
    private TeamMapper teamMapper;

    @Resource
    private TeamUserMapper teamUserMapper;

    @Resource
    private BaseWrapper baseWrapper;


    /**
     * 查询班组列表
     *
     * @param queryDTO
     * @return
     */
    public Page<Team> findTeamPageList(TeamQueryDTO queryDTO) {
        LambdaQueryWrapper<Team> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Team::getDeleted, 0)
                .like(!StringUtils.isEmpty(queryDTO.getGroupName()), Team::getGroupName, queryDTO.getGroupName());
        IPage<Team> page = teamMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, Team.class);
    }

    /**
     * 查询班组员工列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TeamUser> findTeamUserPageList(TeamUserQueryDTO queryDTO) {
        LambdaQueryWrapper<TeamUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamUser::getDeleted, 0)
                .eq(TeamUser::getTeamId, queryDTO.getTeamId())
                .like(!StringUtils.isEmpty(queryDTO.getStaffName()), TeamUser::getStaffName, queryDTO.getStaffName());
        IPage<TeamUser> page = teamUserMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, TeamUser.class);
    }
}
