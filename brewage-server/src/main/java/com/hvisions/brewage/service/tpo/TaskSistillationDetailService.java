package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskSistillationDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSistillationDetailParameterMapper;
import com.hvisions.brewage.dao.tpo.TaskSistillationMapper;
import com.hvisions.brewage.dto.tpo.TaskSistillationDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSistillationDetailParameterAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSistillationDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskSistillation;
import com.hvisions.brewage.entity.tpo.TaskSistillationDetail;
import com.hvisions.brewage.entity.tpo.TaskSistillationDetailParameter;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.operlog.utils.StringUtils;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskSistillationDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSistillationDetailService
 * @description: 蒸馏任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSistillationDetailService {

    @Resource
    private TaskSistillationDetailMapper taskSistillationDetailMapper;
    @Resource
    private TaskSistillationDetailParameterMapper taskSistillationDetailParameterMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskSistillationMapper taskSistillationMapper;
    @Resource
    private TaskSistillationService taskSistillationService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private TaskOrderBatchService taskOrderBatchService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询蒸馏任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskSistillationDetailVO> findPageList(TaskSistillationDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskSistillationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSistillationDetail::getDeleted, 0)
                .eq(TaskSistillationDetail::getTaskSistillationId, queryDTO.getTaskSistillationId());
        IPage<TaskSistillationDetail> page = taskSistillationDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskSistillationDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskSistillationDetailVO.class);

        if (!CollectionUtils.isEmpty(detailVOPage.getRecords())) {
            detailVOPage.getRecords().forEach(v -> {
                LambdaQueryWrapper<TaskSistillationDetailParameter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskSistillationDetailParameter::getTaskSistillationDetailId, v.getId())
                        .eq(TaskSistillationDetailParameter::getDeleted, 0);
                List<TaskSistillationDetailParameter> parameterList = taskSistillationDetailParameterMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(parameterList)) {
                    //循环水回水温度
                    List<TaskSistillationDetailParameter> conveyorLeftGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CirculatingWaterReturnTemperature)).collect(Collectors.toList());
                    List<TaskSistillationDetailParameterAddDTO> conveyorLeftGrainTempConvert = DtoMapper.convertList(conveyorLeftGrainTemp, TaskSistillationDetailParameterAddDTO.class);
                    v.setCirculatingWaterReturnTemperatureTemp(conveyorLeftGrainTempConvert);

                    //底锅水温度
                    List<TaskSistillationDetailParameter> conveyorRightGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.BottomPotWaterTemperature)).collect(Collectors.toList());
                    List<TaskSistillationDetailParameterAddDTO> conveyorRightGrainTempConvert = DtoMapper.convertList(conveyorRightGrainTemp, TaskSistillationDetailParameterAddDTO.class);
                    v.setBottomPotWaterTemperatureTemp(conveyorRightGrainTempConvert);

                }
            });
        }
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskSistillationDetailAddDTO addDTO) {
        log.info("调用新增蒸馏任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增蒸馏任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskSistillationDetail sistillationDetail = DtoMapper.convert(addDTO, TaskSistillationDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskSistillationMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskSistillation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSistillation::getDeleted, 0)
                .eq(TaskSistillation::getPitNo, pitNo)
                .eq(TaskSistillation::getVinasseName, addDTO.getOutMaterialType())
                .orderByDesc(TaskSistillation::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskSistillation taskSistillation = taskSistillationMapper.selectOne(wrapper);
        if (null != taskSistillation) {
            //判断任务是否是待执行
            if (taskSistillation.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskSistillation.setState(TaskStatus.IN_PROGRESS.getCode());
                taskSistillation.setStartTime(new Date());
                taskSistillationMapper.updateById(taskSistillation);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskSistillation.getCenterId(), taskSistillation.getLocationId(), taskSistillation.getPitOrder(), ExecutionStatus.DISTILLATION.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskSistillationService.endTaskSistillation(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskSistillationDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskSistillationDetail::getDeleted, 0)
                    .eq(TaskSistillationDetail::getTaskSistillationId,taskSistillation.getId());
            List<TaskSistillationDetail> list = taskSistillationDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskSistillation.getPitOrder(), ExecutionStatus.DISTILLATION.getStatus(),1);
            }
            //传了单甑稻壳实际值
            if (!StringUtils.isEmpty(addDTO.getHj1SingleContainerUsage())) {

                taskOrderBatchService.addTurnoverUseMaterial(addDTO.getIotTaskNo(),
                        ExecutionStatus.DISTILLATION.getStatus(),
                        taskSistillation.getCenterId(),
                        taskSistillation.getLocationId(),
                        new BigDecimal(addDTO.getHj1SingleContainerUsage()),
                        "",
                        OtherEnum.HJ,
                        OtherEnum.HJ1,
                        "1"
                );
            }
        }

        sistillationDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        sistillationDetail.setCreateTime(new Date());
        sistillationDetail.setCreatorId(userId);
        sistillationDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskSistillationDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskSistillationDetail::getDeleted, 0)
                .eq(TaskSistillationDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskSistillationDetail taskSistillationDetail = taskSistillationDetailMapper.selectOne(wrapperDetail);
        if (null == taskSistillationDetail) {
            if (null != taskSistillation) {
                sistillationDetail.setTaskSistillationId(taskSistillation.getId());
            }

            taskSistillationDetailMapper.insert(sistillationDetail);
        } else {
            if (null != taskSistillation) {
                sistillationDetail.setTaskSistillationId(taskSistillation.getId());
            }
            sistillationDetail.setId(taskSistillationDetail.getId());
            taskSistillationDetailMapper.updateById(sistillationDetail);
        }

        //插入参数信息
        //循环水回水温度
        if (!CollectionUtils.isEmpty(addDTO.getCirculatingWaterReturnTemperatureTemp())) {
            addSistillationDetailParameter(sistillationDetail.getId(), TaskSpreadingParameterEnum.CirculatingWaterReturnTemperature, addDTO.getCirculatingWaterReturnTemperatureTemp());
        }

        //插入参数信息
        //底锅水温度
        if (!CollectionUtils.isEmpty(addDTO.getBottomPotWaterTemperatureTemp())) {
            addSistillationDetailParameter(sistillationDetail.getId(), TaskSpreadingParameterEnum.BottomPotWaterTemperature, addDTO.getBottomPotWaterTemperatureTemp());
        }

        //校验参数是否有工艺异常，有则告警
        //1.黄水单甑使用量
//        if(!StringUtils.isEmpty(addDTO.getYellowWaterUsage())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SISTILLATION,
//                    RouteParameterWarnParamEnum.ZL_YellowWaterUsage.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getYellowWaterUsage())
//            );
//        }
//        //2.尾酒单甑使用量
//        if(!StringUtils.isEmpty(addDTO.getTailWineUsage())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SISTILLATION,
//                    RouteParameterWarnParamEnum.ZL_TailWineUsage.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getTailWineUsage())
//            );
//        }
//        //3.HJ1单甑使用量
//        if(!StringUtils.isEmpty(addDTO.getHj1SingleContainerUsage())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SISTILLATION,
//                    RouteParameterWarnParamEnum.ZL_Hj1SingleContainerUsage.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getHj1SingleContainerUsage())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSistillationDetailAddDTO> addDTOList) {
        log.info("调用批量新增蒸馏任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增蒸馏任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增蒸馏任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskSistillationDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增蒸馏任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.SISTILLATION_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskSistillation_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskSistillation_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除蒸馏任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除蒸馏任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskSistillationDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 插入详情参数
     *
     * @param taskSistillationDetailId 详情id
     * @param metricName            采集项名称
     * @param addDTOList            采集数据
     */
    private void addSistillationDetailParameter(Integer taskSistillationDetailId, String metricName, List<TaskSistillationDetailParameterAddDTO> addDTOList) {
        addDTOList.forEach(v -> {
            TaskSistillationDetailParameter sistillationDetailParameter = new TaskSistillationDetailParameter();
            sistillationDetailParameter.setCollectionTime(v.getCollectionTime());
            sistillationDetailParameter.setMeasuredValue(v.getMeasuredValue());
            sistillationDetailParameter.setTaskSistillationDetailId(taskSistillationDetailId);
            sistillationDetailParameter.setMetricName(metricName);
            taskSistillationDetailParameterMapper.insert(sistillationDetailParameter);
        });
    }
}
