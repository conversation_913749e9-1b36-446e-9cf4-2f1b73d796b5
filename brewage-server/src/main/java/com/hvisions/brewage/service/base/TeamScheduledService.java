package com.hvisions.brewage.service.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dao.base.TeamScheduledMapper;
import com.hvisions.brewage.dto.base.TeamScheduledOriginalQueryDTO;
import com.hvisions.brewage.dto.base.TeamScheduledQueryDTO;
import com.hvisions.brewage.entity.base.Team;
import com.hvisions.brewage.entity.base.TeamScheduled;
import com.hvisions.brewage.vo.base.TeamScheduledShiftUserVO;
import com.hvisions.brewage.vo.base.TeamScheduledShiftVO;
import com.hvisions.brewage.vo.base.TeamScheduledVO;
import com.hvisions.brewage.vo.tpo.TaskDiscardVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledService
 * @description: 排班管理Service
 * @date 2025/9/15 18:27
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TeamScheduledService {
    @Resource
    private TeamScheduledMapper teamScheduledMapper;

    /**
     * 查询排班管理列表
     *
     * @param queryDTO
     * @return
     */
    public List<TeamScheduledVO> findTeamScheduledList(TeamScheduledQueryDTO queryDTO) {

        List<TeamScheduledVO> scheduledVOList = new ArrayList<>();

        List<TeamScheduled> scheduledList = teamScheduledMapper.selectTeamScheduledList(queryDTO);

        Map<Date, List<TeamScheduled>> listMap = scheduledList.stream().collect(Collectors.groupingBy(TeamScheduled::getCalendarDate));
        listMap.forEach((k, v) -> {
            TeamScheduledVO scheduledVO = new TeamScheduledVO();
            scheduledVO.setCalendarDate(k);

            //取班次
            List<TeamScheduledShiftVO> shiftVOList = DtoMapper.convertList(v, TeamScheduledShiftVO.class);
            List<TeamScheduledShiftVO> shiftVODistinctList = shiftVOList.stream().distinct().collect(Collectors.toList());


            shiftVODistinctList.forEach(v2 -> {
                //取人员
                List<TeamScheduled> teamScheduledUserList = scheduledList.stream().filter(item -> item.getCalendarDate().equals(v2.getCalendarDate())
                        && item.getGroupCode().equals(v2.getGroupCode())
                        && item.getShiftId().equals(v2.getShiftId())
                ).collect(Collectors.toList());

                List<TeamScheduledShiftUserVO> shiftUserVOList = DtoMapper.convertList(teamScheduledUserList, TeamScheduledShiftUserVO.class);
                v2.setShiftUserVOList(shiftUserVOList);
            });

            scheduledVO.setShiftVOList(shiftVODistinctList);

            scheduledVOList.add(scheduledVO);
        });

        return scheduledVOList;
    }

    /**
     * 分页查询排班管理列表-原始数据
     * @param queryDTO
     * @return
     */
    public Page<TeamScheduled> findPageTeamScheduledOriginalList(TeamScheduledOriginalQueryDTO queryDTO) {
        Page<TeamScheduled> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TeamScheduled> inspectionVOList = teamScheduledMapper.selectPageList(page, queryDTO);

        page.setRecords(inspectionVOList);
        return page;
    }
}
