package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.brewage.dto.tpo.TaskPitBatchesUpdateDTO;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderPotTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPitBatchesUpdateService
 * @description: 窖池甑口更新Service
 * @date 2025/7/21 14:39
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskPitBatchesUpdateService {
    @Resource
    private TaskLoadingService taskLoadingService;
    @Resource
    private TaskPileUpService taskPileUpService;

    @Resource
    private WorkshopPitOrderPotTaskService workshopPitOrderPotTaskService;

    /**
     * 窖池甑口更新
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String sendTaskPitBatchesUpdate(TaskPitBatchesUpdateDTO updateDTO){
        log.info("进入统一更改窖池甑口更新入口，参数---》{}", JSONObject.toJSONString(updateDTO));

        log.info("开始执行韩明华更改窖池甑口逻辑");
        //更改入窖任务、摊晾任务、丢糟任务的甑口数据----韩明华
        taskLoadingService.updatePitNo(updateDTO);

        log.info("开始执行林远志更改窖池甑口逻辑");
        //更改甑口任务、窖池订单等数据----林远志
        workshopPitOrderPotTaskService.updatePitNo(updateDTO);

        log.info("开始执行王佳更改窖池甑口逻辑");
        //更改下糟等任务数据----王佳
        taskPileUpService.updatePitNo(updateDTO);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }
}
