package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskLoadingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskLoadingMapper;
import com.hvisions.brewage.dto.tpo.TaskLoadingDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskLoadingDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.entity.tpo.TaskLoadingDetail;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.TaskBusiness;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.vo.tpo.TaskLoadingDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingDetailService
 * @description: 入窖任务详情Service
 * @date 2025/7/9 10:16
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskLoadingDetailService {
    @Resource
    private TaskLoadingDetailMapper taskLoadingDetailMapper;

    @Resource
    private TaskLoadingMapper taskLoadingMapper;

    @Resource
    private UserAuditorAware userAuditorAware;


    /**
     * 查询入窖任务详情列表
     * @param queryDTO
     * @return
     */
    public Page<TaskLoadingDetailVO> findPageList(TaskLoadingDetailQueryDTO queryDTO) {
        Page<TaskLoadingDetailVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        List<TaskLoadingDetailVO> taskLoadingDetailVOList = taskLoadingDetailMapper.selectPageList(page, queryDTO);
        page.setRecords(taskLoadingDetailVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskLoadingDetailAddDTO addDTO,Integer taskLoadingId) {
        log.info("调用新增入窖任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增入窖任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskLoadingDetail taskLoadingDetail = DtoMapper.convert(addDTO, TaskLoadingDetail.class);

        //挂到对应的任务上
        TaskLoading taskLoading = taskLoadingMapper.selectById(taskLoadingId);
        if (null != taskLoading) {
            //判断任务是否是待执行
            if (taskLoading.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                taskLoading.setStatus(TaskStatus.IN_PROGRESS.getName());
                taskLoading.setStartTime(new Date());
                taskLoadingMapper.updateById(taskLoading);
            }
        }

        taskLoadingDetail.setRecordTime(new Date());
        taskLoadingDetail.setDataSource(TaskBusiness.TASK_LOADING_DETAIL.getTaskType());
        taskLoadingDetail.setCreateTime(new Date());
        taskLoadingDetail.setCreatorId(userId);

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskLoadingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskLoadingDetail::getDeleted, 0)
                .eq(TaskLoadingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskLoadingDetail taskLoadingDetail1 = taskLoadingDetailMapper.selectOne(wrapperDetail);
        if(null==taskLoadingDetail1){
            taskLoadingDetail.setTaskLoadingId(null!=taskLoading?taskLoading.getId():null);
            taskLoadingDetailMapper.insert(taskLoadingDetail);
        }else {
            taskLoadingDetail.setId(taskLoadingDetail1.getId());

            //有些数据不能在进行更改
            taskLoadingDetail.setPitNo(taskLoadingDetail1.getPitNo());
            taskLoadingDetail.setOutMaterialType(taskLoadingDetail1.getOutMaterialType());
            taskLoadingDetail.setEnterMaterialType(taskLoadingDetail1.getEnterMaterialType());
            taskLoadingDetail.setMaterialLayer(taskLoadingDetail1.getMaterialLayer());
            taskLoadingDetail.setGrainInputType(taskLoadingDetail1.getGrainInputType());
            taskLoadingDetail.setSteamingTaskNo(taskLoadingDetail1.getSteamingTaskNo());
            taskLoadingDetail.setSourcePitNo(taskLoadingDetail1.getSourcePitNo());
            taskLoadingDetail.setTargetPitNo(taskLoadingDetail1.getTargetPitNo());
            taskLoadingDetailMapper.updateById(taskLoadingDetail);
        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除入窖任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除入窖任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskLoadingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
