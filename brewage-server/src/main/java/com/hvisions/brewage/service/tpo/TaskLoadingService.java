package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.common.constant.MqQueueConstant;
import com.hvisions.brewage.common.entity.MessageConsume;
import com.hvisions.brewage.common.utils.AmqpTemplateUtil;
import com.hvisions.brewage.dao.tpo.TaskLoadingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskLoadingMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingDetailMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.entity.tpo.TaskLoadingDetail;
import com.hvisions.brewage.entity.tpo.WorkshopPitOrderInappraise;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.feign.message.MessageConsumeClient;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderInAppraiseMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.dto.BaseOrderMessageSapDTO;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.IdentifyVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingSourcePitNoVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingTargetPitNoVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingService
 * @description: 入窖任务Service
 * @date 2025/7/8 16:50
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskLoadingService {
    @Resource
    private TaskLoadingMapper taskLoadingMapper;

    @Resource
    private TaskLoadingDetailMapper taskLoadingDetailMapper;

    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TaskSpreadingDetailMapper taskSpreadingDetailMapper;

    @Resource
    private RouteParameterWarnService routeParameterWarnService;

    @Resource
    private WorkshopPitOrderInAppraiseMapper workshopPitOrderInAppraiseMapper;

    @Resource
    private MessageConsumeClient messageConsumeClient;

    @Resource
    private WorkshopFullPitMapper workshopFullPitMapper;

    @Resource
    private TaskSealingService taskSealingService;

    @Resource
    private TaskSpreadingDetailService taskSpreadingDetailService;

    @Resource
    private TaskDiscardDetailService taskDiscardDetailService;

    @Resource
    private TaskPitBatchesUpdateService taskPitBatchesUpdateService;

    @Resource
    private WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;

    /**
     * 查询入窖任务列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskLoadingVO> findPageList(TaskLoadingQueryDTO queryDTO) {
        Page<TaskLoadingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskLoadingVO> spreadingVOList = taskLoadingMapper.selectPageList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    public Integer add(TaskLoadingAddDTO addDTO) {
        log.info("调用新增入窖任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增入窖任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskLoading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskLoading::getPitOrder, addDTO.getPitOrder())
                .eq(TaskLoading::getDeleted, 0);
        Integer count = taskLoadingMapper.selectCount(wrapper);
        if (count > 0) {
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskLoading taskLoading = DtoMapper.convert(addDTO, TaskLoading.class);

        //查询配置最大的流水号
        Integer maxNum = taskLoadingMapper.selectMaxTaskNo(ProdConfigEnum.RJ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.RJ, maxNum);
        taskLoading.setTaskNo(taskNo);

        taskLoading.setTaskName(TaskBusiness.TASK_LOADING.getTaskName());
        taskLoading.setTaskType(TaskBusiness.TASK_LOADING.getTaskType());
        taskLoading.setStatus(TaskStatus.IN_PROGRESS.getName());
        taskLoading.setStartTime(new Date());
        taskLoading.setCreateTime(new Date());
        taskLoading.setCreatorId(userId);


        //空窖时长有工艺管控要求，需要进行工艺告警
        BigDecimal cellarTime = emptyCellarTime(addDTO.getPitOrder(), addDTO.getPitNo());
        log.info("查询出来的空窖时长为：-------》窖池订单号:{},窖池号：{}，空窖时长：{}", addDTO.getPitOrder(), addDTO.getPitNo(), cellarTime);
        if (cellarTime.compareTo(BigDecimal.ZERO) != 0) {
            RouteParameterWarnParamEnum warnParamEnum = RouteParameterWarnParamEnum.RJ_EMPTY_TIME;
            routeParameterWarnService.judgeRouteParameterWarn(warnParamEnum.getBusinessModule(),
                    warnParamEnum.getParamCode(),
                    addDTO.getPitOrder(),
                    addDTO.getMaterialType(),
                    addDTO.getMaterialType(),
                    cellarTime
            );
        }
        taskLoading.setEmptyDuration(cellarTime);
        taskLoadingMapper.insert(taskLoading);


        log.info("判断对应的窖池订单是否有糟源类型，无则给插入进去");
        LambdaQueryWrapper<TPoWorkshopPitOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TPoWorkshopPitOrder::getOrderCode, addDTO.getPitOrder())
                .eq(TPoWorkshopPitOrder::getIsDeleted, 0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(queryWrapper);
        if (null == workshopPitOrder.getVinasseId()) {
            log.info("对应的窖池订单没有糟源类型，插入糟源，窖池订单号：{}，糟源类型：{}", addDTO.getPitOrder(), addDTO.getMaterialType());
            Integer vinasseId = taskSpreadingDetailMapper.selectVinasseId(addDTO.getMaterialType());
            workshopPitOrder.setVinasseId(vinasseId);
            workshopPitOrderMapper.updateById(workshopPitOrder);
        }

        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(taskLoading.getPitOrder(), ExecutionStatus.CELLAR_IN.getStatus(),1);

        //创建PDA个人任务执行进度
        taskProcessRecordsService.addTask(taskNo,taskLoading.getPitNo(),ExecutionStatus.CELLAR_IN.getStatus(),taskLoading.getCenterId(),taskLoading.getLocationId());

        //将窖池订单，同步给sap------林远志写
        log.info("调用新增入窖任务列表，将窖池订单同步给sap，参数----》{}",JSONObject.toJSONString(workshopPitOrder));
        sendCreateOrderToSap(workshopPitOrder);
        return taskLoading.getId();
    }

    /**
     * 查询空窖时长小时差值（保留1位小数）
     *
     * @param pitOrder 窖池订单号
     * @param pitNo    窖池号
     * @return
     */
    public BigDecimal emptyCellarTime(String pitOrder, String pitNo) {
        //根据窖池订单和糟源类别去查询是否有空窖时长，如果有则取，没有则查询第二层订单,状态为空窖的
        if (!StringUtils.isEmpty(pitOrder)) {
            LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TPoWorkshopPitOrder::getOrderCode, pitOrder)
                    .eq(TPoWorkshopPitOrder::getIsDeleted, 0);
            TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectOne(wrapper);
            if (null == tPoWorkshopPitOrder || null == tPoWorkshopPitOrder.getEmptyStartTime()) {
                return BigDecimal.ZERO;
            }

            Date emptyStartTime = tPoWorkshopPitOrder.getEmptyStartTime();

            //计算差值
            return DateUtil.calculateHoursDifference(emptyStartTime, new Date());
        } else {
            //根据连窖号查询窖池id
            LambdaQueryWrapper<TPoWorkshopFullPit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TPoWorkshopFullPit::getFullPitId, pitNo)
                    .eq(TPoWorkshopFullPit::getIsDeleted, 0);
            TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectOne(wrapper);

            //根据窖池号、订单状态（空窖）、层次2，查询窖池订单空窖时长
            LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TPoWorkshopPitOrder::getPitId, workshopFullPit.getId())
                    .eq(TPoWorkshopPitOrder::getIsDeleted, 0)
                    .eq(TPoWorkshopPitOrder::getPitStatus, 0)
                    .eq(TPoWorkshopPitOrder::getLayer, 2)
                    .last("limit 1");
            TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
            if (null == tPoWorkshopPitOrder || null == tPoWorkshopPitOrder.getEmptyStartTime()) {
                return BigDecimal.ZERO;
            }

            Date emptyStartTime = tPoWorkshopPitOrder.getEmptyStartTime();

            //计算差值
            return DateUtil.calculateHoursDifference(emptyStartTime, new Date());
        }
    }

    /**
     * @param workshopPitOrder
     */
    private void sendCreateOrderToSap(TPoWorkshopPitOrder workshopPitOrder) {
        // 获取连窖表数据
        TPoWorkshopFullPit fullPit = workshopFullPitMapper.selectById(workshopPitOrder.getPitId());
        List<TPoWorkshopPitOrderSap> tPoWorkshopPitOrderSaps = workshopPitOrderSapMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderSap>().eq(TPoWorkshopPitOrderSap::getOrderCodeId, workshopPitOrder.getId()));
        for (TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap : tPoWorkshopPitOrderSaps) {
            BaseOrderMessageSapDTO messageSapDTO = new BaseOrderMessageSapDTO();
            messageSapDTO.setId(tPoWorkshopPitOrderSap.getId());
            messageSapDTO.setSapOrderCode(tPoWorkshopPitOrderSap.getOrderCode());
            messageSapDTO.setFullPitId(fullPit.getFullPitId());
            String headKey = MqQueueConstant.ORDER_CREATE_SAP + "_" + MqQueueConstant.OPERATE_TYPE_SYNC + "_" + tPoWorkshopPitOrderSap.getId();
            //发送同步请求到mq
            MessageConsume messageConsume = AmqpTemplateUtil.init(MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, MqQueueConstant.ORDER_CREATE_SAP, JSONObject.toJSONString(messageSapDTO), headKey, MqQueueConstant.OPERATE_TYPE_SYNC);
            //消息投递
            messageConsumeClient.addMessage(messageConsume);
            AmqpTemplateUtil.sendMessageToMq(MqQueueConstant.ORDER_CREATE_SAP, MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, JSONObject.toJSONString(messageConsume));
        }
    }

    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskLoadingAddDTO> addDTOList) {
        log.info("调用批量新增入窖任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增入窖任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 入窖确认
     *
     * @param updateDTO
     * @return
     */
    public String update(TaskLoadingUpdateDTO updateDTO) {
        log.info("调用入窖确认，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用入窖确认，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskLoading> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskLoading::getPitOrder, updateDTO.getPitOrder())
                .eq(TaskLoading::getDeleted, 0);
        TaskLoading taskLoading = taskLoadingMapper.selectOne(queryWrapper);
        if (null == taskLoading || taskLoading.getDeleted()) {
            log.info("没有入窖任务，进行入窖任务、封窖任务创建");
            TaskLoadingAddDTO loadingAddDTO = DtoMapper.convert(updateDTO, TaskLoadingAddDTO.class);

            //创建入窖任务
            add(loadingAddDTO);

            //创建封窖任务
            addTaskSealing(updateDTO.getCenterId(), updateDTO.getLocationId(), updateDTO.getPitNo(), updateDTO.getMaterialType(), updateDTO.getPitOrder());

            taskLoading = taskLoadingMapper.selectOne(queryWrapper);
        }

        taskLoading.setLoosePackingStatus(updateDTO.getLoosePackingStatus());
        taskLoading.setLoadingTemperature(updateDTO.getLoadingTemperature());
        taskLoading.setDensePackingStatus(updateDTO.getDensePackingStatus());
        taskLoading.setUpdaterId(userId);
        taskLoading.setUpdateTime(new Date());
        taskLoadingMapper.updateById(taskLoading);

        //修改详情数据
        LambdaQueryWrapper<TaskLoadingDetail> wrapper = new LambdaQueryWrapper<TaskLoadingDetail>();
        wrapper.eq(TaskLoadingDetail::getDeleted, 0)
                .in(TaskLoadingDetail::getId, updateDTO.getTaskLoadingDetailIds())
                .eq(TaskLoadingDetail::getIsPitConfirmed, false);
        List<TaskLoadingDetail> loadingDetailList = taskLoadingDetailMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(loadingDetailList)) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("过程数据已确认"));
        }


        //修改窖池订单的糟醅排次信息
        LambdaQueryWrapper<TPoWorkshopPitOrder> pitOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pitOrderLambdaQueryWrapper.eq(TPoWorkshopPitOrder::getOrderCode, updateDTO.getPitOrder())
                .eq(TPoWorkshopPitOrder::getIsDeleted, 0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(pitOrderLambdaQueryWrapper);
        if (null != workshopPitOrder) {
            log.info("修改窖池订单的糟醅排次信息,窖池订单号：{}，排次年份:{},糟源排次ID：{}", updateDTO.getPitOrder(), updateDTO.getCycleYear(), updateDTO.getCycleNoId());
            workshopPitOrder.setCycleYear(updateDTO.getCycleYear());
            workshopPitOrder.setCycleNoId(updateDTO.getCycleNoId());
            workshopPitOrderMapper.updateById(workshopPitOrder);
        }

        loadingDetailList.forEach(v -> {
            v.setIsPitConfirmed(true);
            v.setConfirmedById(userId);
            v.setConfirmationTime(new Date());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            taskLoadingDetailMapper.updateById(v);
        });


        //更改了窖池号---修改对应任务的甑口窖池关联关系
        TaskPitBatchesUpdateDTO batchesUpdateDTO = new TaskPitBatchesUpdateDTO();
        List<String> iotTaskNo = loadingDetailList.stream().map(TaskLoadingDetail::getIotTaskNo).collect(Collectors.toList());
        batchesUpdateDTO.setIotTaskNo(iotTaskNo);
        batchesUpdateDTO.setInPitOrder(taskLoading.getPitOrder());
        taskPitBatchesUpdateService.sendTaskPitBatchesUpdate(batchesUpdateDTO);

        //进行工艺参数告警：入窖温度
        if (!StringUtils.isEmpty(updateDTO.getLoadingTemperature())) {
            log.info("入窖确认中入窖温度参数有工艺管控要求，需要进行参数预警");
            RouteParameterWarnParamEnum warnParamEnum = RouteParameterWarnParamEnum.RJ_LOADING_TEMPERATURE;
            routeParameterWarnService.judgeRouteParameterWarn(warnParamEnum.getBusinessModule(),
                    warnParamEnum.getParamCode(),
                    taskLoading.getPitOrder(),
                    taskLoading.getMaterialType(),
                    taskLoading.getMaterialType(),
                    new BigDecimal(updateDTO.getLoadingTemperature())
            );
        }

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 更改窖池号
     *
     * @param updateDTO
     * @return
     */
    public String updatePitNo(TaskPitBatchesUpdateDTO updateDTO) {
        //改入
        if (!StringUtils.isEmpty(updateDTO.getInPitOrder())) {
            updatePitNoIn(updateDTO);
        }

        //改出
        if (!StringUtils.isEmpty(updateDTO.getOutPitOrder())) {
            updatePitNoOut(updateDTO);
        }

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }


    /**
     * 更改窖池号-改入
     *
     * @param updateDTO
     * @return
     */
    public String updatePitNoIn(TaskPitBatchesUpdateDTO updateDTO) {
        log.info("调用更改窖池号-改入，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用更改窖池号-改入，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskLoading> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskLoading::getPitOrder, updateDTO.getInPitOrder())
                .eq(TaskLoading::getDeleted, 0);
        TaskLoading taskLoading = taskLoadingMapper.selectOne(queryWrapper);
        if (null == taskLoading || taskLoading.getDeleted()) {
            log.info("没有入窖任务，进行入窖任务、封窖任务创建");
            LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TPoWorkshopPitOrder::getOrderCode, updateDTO.getInPitOrder())
                    .eq(TPoWorkshopPitOrder::getIsDeleted, 0);
            TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper);

            //查询糟源类别
            String materialType = taskSpreadingDetailMapper.selectMaterialType(workshopPitOrder.getVinasseId());
            //根据连窖号查询窖池id
            TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectById(workshopPitOrder.getPitId());

            TaskLoadingAddDTO loadingAddDTO = new TaskLoadingAddDTO();
            loadingAddDTO.setCenterId(workshopPitOrder.getCenterId());
            loadingAddDTO.setLocationId(workshopPitOrder.getLocationId());
            loadingAddDTO.setPitOrder(updateDTO.getInPitOrder());
            loadingAddDTO.setMaterialType(materialType);
            loadingAddDTO.setPitNo(workshopFullPit.getFullPitId());

            //创建入窖任务
            add(loadingAddDTO);

            //创建封窖任务
            addTaskSealing(loadingAddDTO.getCenterId(), loadingAddDTO.getLocationId(), loadingAddDTO.getPitNo(), loadingAddDTO.getMaterialType(), loadingAddDTO.getPitOrder());

            taskLoading = taskLoadingMapper.selectOne(queryWrapper);
        }

        taskLoading.setUpdaterId(userId);
        taskLoading.setUpdateTime(new Date());
        taskLoadingMapper.updateById(taskLoading);

        //修改详情数据
        LambdaQueryWrapper<TaskLoadingDetail> wrapper = new LambdaQueryWrapper<TaskLoadingDetail>();
        wrapper.eq(TaskLoadingDetail::getDeleted, 0)
                .in(TaskLoadingDetail::getIotTaskNo, updateDTO.getIotTaskNo());
        List<TaskLoadingDetail> loadingDetailList = taskLoadingDetailMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(loadingDetailList)) {
            TaskLoading finalTaskLoading = taskLoading;
            loadingDetailList.forEach(v -> {
                //更改了窖池号
                if (!v.getTaskLoadingId().equals(finalTaskLoading.getId())) {

                    //判断任务是否是待执行
                    if (finalTaskLoading.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                        finalTaskLoading.setStatus(TaskStatus.IN_PROGRESS.getName());
                        finalTaskLoading.setStartTime(new Date());
                    }
                    taskLoadingMapper.updateById(finalTaskLoading);

                    v.setTargetPitNo(finalTaskLoading.getPitNo());
                    v.setEnterMaterialType(finalTaskLoading.getMaterialType());
                    v.setTaskLoadingId(finalTaskLoading.getId());
                    v.setUpdaterId(userId);
                    v.setUpdateTime(new Date());
                    taskLoadingDetailMapper.updateById(v);


                    TaskLoading taskLoading1 = taskLoadingMapper.selectById(v.getTaskLoadingId());
                    //计算入窖地温平均值
                    updateTaskLoadingDetail(taskLoading1);

                    //计算入窖地温平均值
                    updateTaskLoadingDetail(finalTaskLoading);

                    //修改摊晾任务的明细数据-改入
                    taskSpreadingDetailService.updateTaskSpreadingDetailIn(v.getIotTaskNo(), v.getEnterMaterialType(), v.getTargetPitNo());
                }
            });
        }
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 计算入窖地温平均值
     *
     * @param taskLoading
     */
    private void updateTaskLoadingDetail(TaskLoading taskLoading) {
        //计算入窖地温平均值
        LambdaQueryWrapper<TaskLoadingDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskLoadingDetail::getDeleted, 0)
                .eq(TaskLoadingDetail::getTaskLoadingId, taskLoading.getId());
        List<TaskLoadingDetail> taskLoadingDetails = taskLoadingDetailMapper.selectList(lambdaQueryWrapper);
        BigDecimal average = taskLoadingDetails.stream()
                .map(item -> Optional.ofNullable(item.getGroundTemp()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                //保留两位小数
                .divide(new BigDecimal(taskLoadingDetails.size()), 2, RoundingMode.HALF_UP);
        ;

        taskLoading.setGroundTemp(average);
        taskLoadingMapper.updateById(taskLoading);
    }


    /**
     * 更改窖池号-改出
     *
     * @param updateDTO
     * @return
     */
    public String updatePitNoOut(TaskPitBatchesUpdateDTO updateDTO) {
        log.info("调用更改窖池号-改出，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用更改窖池号-改出，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TPoWorkshopPitOrder::getOrderCode, updateDTO.getOutPitOrder())
                .eq(TPoWorkshopPitOrder::getIsDeleted, 0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
        if (null != workshopPitOrder) {
            //查询糟源类别
            String materialType = taskSpreadingDetailMapper.selectMaterialType(workshopPitOrder.getVinasseId());

            //查询对应的窖池号
            String sourcePitNo = workshopFullPitMapper.selectById(workshopPitOrder.getPitId()).getFullPitId();

            LambdaQueryWrapper<TaskLoadingDetail> wrapper = new LambdaQueryWrapper<TaskLoadingDetail>();
            wrapper.eq(TaskLoadingDetail::getDeleted, 0)
                    .in(TaskLoadingDetail::getIotTaskNo, updateDTO.getIotTaskNo());
            List<TaskLoadingDetail> loadingDetailList = taskLoadingDetailMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(loadingDetailList)) {
                loadingDetailList.forEach(v -> {
                    v.setOutMaterialType(materialType);
                    v.setSourcePitNo(sourcePitNo);
                    taskLoadingDetailMapper.updateById(v);
                });

            } else {
                log.info("调用更改窖池号-改出-没有获取到入窖任务的详细数据，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
            }

            updateDTO.getIotTaskNo().forEach(v->{
                //修改摊晾任务的明细数据-改出
                taskSpreadingDetailService.updateTaskSpreadingDetailOut(v, materialType, sourcePitNo, updateDTO.getOutPitOrder());

                //修改丢糟任务的明细数据-改出
                taskDiscardDetailService.updateTaskSpreadingDetailOut(v,materialType, sourcePitNo, updateDTO.getOutPitOrder());
            });

        } else {
            log.info("调用更改窖池号-改出-没有获取到更改后的窖池订单任务数据，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        }


        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 创建封窖任务
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param materialType 糟源类别
     * @param pitOrder     窖池订单号
     * @return
     */
    public String addTaskSealing(Integer centerId, Integer locationId, String pitNo, String materialType, String pitOrder) {
        TaskSealingAddDTO addDTO = new TaskSealingAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setMaterialType(materialType);
        addDTO.setPitOrder(pitOrder);
        return taskSealingService.add(addDTO);
    }

    /**
     * 删除入窖任务列表
     *
     * @param ids 入窖任务主键id
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("删除入窖任务列表，传入参数：{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("删除入窖任务列表，获取到当前登录用户，用户id：{}", userId);
        List<TaskLoading> loadingList = taskLoadingMapper.selectBatchIds(ids);
        loadingList.forEach(v->{
            //删除PDA个人任务执行进度
            taskProcessRecordsService.deleteTask(v.getTaskNo(),ExecutionStatus.CELLAR_IN.getStatus());
        });

        taskLoadingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 查询入窖确认入窖窖池号
     *
     * @param materialType 糟源类别
     * @return
     */
    public List<TaskLoadingTargetPitNoVO> findTargetPitNoList(String materialType) {
        return taskLoadingMapper.selectTargetPitNoList(materialType);
    }

    /**
     * 查询入窖任务列表-PDA
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskLoadingVO> findPDAPageList(TaskLoadingPDAQueryDTO queryDTO) {
        Page<TaskLoadingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskLoadingVO> spreadingVOList = taskLoadingMapper.selectPagePDAList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }

    /**
     * 结束入窖任务
     *
     * @param pitOrder 窖池订单号
     * @return
     */
    public String closeTask(String pitOrder) {
        log.info("结束入窖任务，传入参数：窖池订单号：{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("结束入窖任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskLoading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskLoading::getPitOrder, pitOrder)
                .in(TaskLoading::getStatus, TaskStatus.PENDING_EXE.getName(), TaskStatus.IN_PROGRESS.getName())
                .eq(TaskLoading::getDeleted, 0);
        TaskLoading taskLoading = taskLoadingMapper.selectOne(wrapper);
        if(null!=taskLoading){
            taskLoading.setStatus(TaskStatus.COMPLETED.getName());
            taskLoading.setEndTime(new Date());
            taskLoading.setUpdaterId(userId);
            taskLoading.setUpdateTime(new Date());
            taskLoadingMapper.updateById(taskLoading);

            //结束PDA个人任务执行进度
            taskProcessRecordsService.closeTask(taskLoading.getTaskNo(),ExecutionStatus.CELLAR_IN.getStatus());
        }

        //修改窖池执行状态为入窖完成
        LambdaUpdateWrapper<TPoWorkshopPitOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TPoWorkshopPitOrder::getOrderCode, pitOrder)
                .eq(TPoWorkshopPitOrder::getIsDeleted, 0)
                .eq(TPoWorkshopPitOrder::getOrderStatus, 5)
                .eq(TPoWorkshopPitOrder::getPitStatus, 0)
                .set(TPoWorkshopPitOrder::getOrderStatus, 0)
                .set(TPoWorkshopPitOrder::getInPitDate, new Date());
        workshopPitOrderMapper.update(null, updateWrapper);

        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(pitOrder, ExecutionStatus.CELLAR_IN.getStatus(),9);

        return OperationResult.OTHER_SUCCESS.getDescription("结束入窖任务");
    }

    /**
     * 入窖鉴定
     *
     * @param identifyDTO
     * @return
     */
    public String identify(TaskLoadingIdentifyDTO identifyDTO) {
        log.info("调用入窖鉴定，传入参数：{}", JSONObject.toJSONString(identifyDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("结束入窖任务，获取到当前登录用户，用户id：{}", userId);

        TaskLoading taskLoading = taskLoadingMapper.selectById(identifyDTO.getId());
        if (null == taskLoading || taskLoading.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(identifyDTO.getId()));
        }

        //查询明细数据
        List<TaskLoadingDetail> loadingDetailList = taskLoadingDetailMapper.selectBatchIds(identifyDTO.getDetailsId());
        loadingDetailList.forEach(v->{
            //判断是否已鉴定
            if (v.getIsIdentify()) {
                FailureCode updateFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("入窖鉴定", "同个甑口任务号入窖鉴定只能一次"));
            }


            WorkshopPitOrderInappraise inappraise = DtoMapper.convert(identifyDTO, WorkshopPitOrderInappraise.class);
            inappraise.setUser(userId);
            inappraise.setIotTaskNo(v.getIotTaskNo());
            if(null!=identifyDTO.getSenseForm()){
                inappraise.setSenseForm(JSONObject.toJSONString(identifyDTO.getSenseForm()));
            }

            // 同个甑口任务号入窖鉴定只能一次
            Integer num = workshopPitOrderInAppraiseMapper.checkIotTaskNo(v.getIotTaskNo());
            if (num != null && num > 0) {
                throw new RuntimeException("同个甑口任务号入窖鉴定只能一次");
            }
            //新增
            workshopPitOrderInAppraiseMapper.insert(inappraise);

            //修改状态为已鉴定
            v.setIsIdentify(true);
            taskLoadingDetailMapper.updateById(v);

        });

        return OperationResult.OTHER_SUCCESS.getDescription("入窖鉴定");
    }

    /**
     * 查询来源窖号
     *
     * @param id 入窖任务id
     * @return
     */
    public List<TaskLoadingSourcePitNoVO> findSourcePitNoList(Integer id) {
        return taskLoadingMapper.selectSourcePitNoList(id);
    }

    /**
     * 待入窖鉴定列表
     * @param queryDTO 入窖任务id
     * @return
     */
    public Page<IdentifyVO> findPageIdentifyList(TaskLoadingIdentifyQueryDTO queryDTO) {
        Page<IdentifyVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<IdentifyVO> spreadingVOList = taskLoadingMapper.selectPageIdentifyList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }
}
