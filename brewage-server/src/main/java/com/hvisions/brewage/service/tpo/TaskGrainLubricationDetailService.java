package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskGrainLubricationDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskGrainLubricationMapper;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskGrainLubrication;
import com.hvisions.brewage.entity.tpo.TaskGrainLubricationDetail;
import com.hvisions.brewage.enums.LogCaptureEnum;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.OtherEnum;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskGrainLubricationDetailVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.materialsmsd.materials.dto.BaseMaterialDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingDetailService
 * @description: 润粮任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskGrainLubricationDetailService {

    @Resource
    private TaskGrainLubricationDetailMapper taskGrainLubricationDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskGrainLubricationMapper taskGrainLubricationMapper;
    @Resource
    private TaskGrainLubricationService taskGrainLubricationService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private TaskOrderBatchService taskOrderBatchService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询润粮任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskGrainLubricationDetailVO> findPageList(TaskGrainLubricationDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskGrainLubricationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskGrainLubricationDetail::getDeleted, 0)
                .eq(TaskGrainLubricationDetail::getTaskGrainLubricationId, queryDTO.getTaskGrainLubricationId());
        IPage<TaskGrainLubricationDetail> page = taskGrainLubricationDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskGrainLubricationDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskGrainLubricationDetailVO.class);
        
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskGrainLubricationDetailAddDTO addDTO) {
        log.info("调用新增润粮任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增润粮任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskGrainLubricationDetail grainLubricationDetail = DtoMapper.convert(addDTO, TaskGrainLubricationDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskGrainLubricationMapper.selectPitNo(addDTO.getPitNo());
        if(StringUtil.isEmpty(pitNo)){
            throw new BaseKnownException(10000, "获取连窖号失败");
        }
//        TaskGrainLubrication taskGrainLubrication = new TaskGrainLubrication();
//        if(addDTO.getTaskGrainLubricationId() == null){
            LambdaQueryWrapper<TaskGrainLubrication> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskGrainLubrication::getDeleted, 0)
                    .eq(TaskGrainLubrication::getPitNo, pitNo)
                    .eq(TaskGrainLubrication::getVinasseName, addDTO.getOutMaterialType())
                    .orderByDesc(TaskGrainLubrication::getCreateTime)
                    .last("limit 1");

            //挂到对应的任务上
        TaskGrainLubrication taskGrainLubrication = taskGrainLubricationMapper.selectOne(wrapper);
//        }else{
//            taskGrainLubrication = taskGrainLubricationMapper.selectById(addDTO.getTaskGrainLubricationId());
//        }

        if (null != taskGrainLubrication) {

            //判断任务是否是待执行
            if (taskGrainLubrication.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskGrainLubrication.setState(TaskStatus.IN_PROGRESS.getCode());
                taskGrainLubrication.setStartTime(new Date());
                taskGrainLubricationMapper.updateById(taskGrainLubrication);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskGrainLubrication.getCenterId(), taskGrainLubrication.getLocationId(), taskGrainLubrication.getPitOrder(), ExecutionStatus.GRAIN_MOISTENING.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskGrainLubricationService.endTaskGrainLubrication(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskGrainLubricationDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskGrainLubricationDetail::getDeleted, 0)
                    .eq(TaskGrainLubricationDetail::getTaskGrainLubricationId,taskGrainLubrication.getId());
            List<TaskGrainLubricationDetail> list = taskGrainLubricationDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskGrainLubrication.getPitOrder(), ExecutionStatus.GRAIN_MOISTENING.getStatus(),1);
            }
            //传了单甑高粱实际值
            if (!StringUtils.isEmpty(addDTO.getSingleContainerSorghumActualValue())) {
                //找原辅料生产找到最新一条发放记录的物料
                List<BaseMaterialDTO> material = taskGrainLubricationMapper.getMaterial(addDTO.getMaterialName());
                if(material.size()>0){
                    BaseMaterialDTO dto = material.get(0);
                    taskOrderBatchService.addTurnoverUseMaterial(addDTO.getIotTaskNo(),
                            ExecutionStatus.GRAIN_MOISTENING.getStatus(),
                            taskGrainLubrication.getCenterId(),
                            taskGrainLubrication.getLocationId(),
                            new BigDecimal(addDTO.getSingleContainerSorghumActualValue()),
                            "",
                            OtherEnum.GL,
                            dto.getMaterialCode(),
                            "0"
                    );
                }

            }
        }

        grainLubricationDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        grainLubricationDetail.setCreateTime(new Date());
        grainLubricationDetail.setCreatorId(userId);
        grainLubricationDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskGrainLubricationDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskGrainLubricationDetail::getDeleted, 0)
                .eq(TaskGrainLubricationDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskGrainLubricationDetail taskGrainLubricationDetail = taskGrainLubricationDetailMapper.selectOne(wrapperDetail);
        if (null == taskGrainLubricationDetail) {
            if (null != taskGrainLubrication) {
                grainLubricationDetail.setTaskGrainLubricationId(taskGrainLubrication.getId());
            }

            taskGrainLubricationDetailMapper.insert(grainLubricationDetail);
        } else {
            if (null != taskGrainLubrication) {
                grainLubricationDetail.setTaskGrainLubricationId(taskGrainLubrication.getId());
            }
            grainLubricationDetail.setId(taskGrainLubricationDetail.getId());
            taskGrainLubricationDetailMapper.updateById(grainLubricationDetail);
        }
        //校验参数是否有工艺异常，有则告警
        //1.单甑稻壳用量-设定值
//        if(!StringUtils.isEmpty(addDTO.getSingleContainerRiceHuskSettingValue())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.DISHMIXING,
//                    RouteParameterWarnParamEnum.BZ_SingleContainerRiceHuskSettingValue.getParamCode(),
//                    pitOrder.getOrderCode(),
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getSingleContainerRiceHuskSettingValue())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskGrainLubricationDetailAddDTO> addDTOList) {
        log.info("调用批量新增润粮任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增润粮任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增润粮任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskGrainLubricationDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增润粮任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.GRAINLUBRICATION_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskGrainLubrication_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskGrainLubrication_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除润粮任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除润粮任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskGrainLubricationDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
