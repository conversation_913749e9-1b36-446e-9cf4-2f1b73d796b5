package com.hvisions.brewage.service.rzgx;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.brewage.dao.base.TeamMapper;
import com.hvisions.brewage.dao.base.TeamScheduledMapper;
import com.hvisions.brewage.dao.base.TeamUserMapper;
import com.hvisions.brewage.dto.rzgx.SzgxTeamQueryDTO;
import com.hvisions.brewage.dto.rzgx.RzgxTeamScheduledQueryDTO;
import com.hvisions.brewage.entity.base.Team;
import com.hvisions.brewage.entity.base.TeamScheduled;
import com.hvisions.brewage.entity.base.TeamUser;
import com.hvisions.brewage.enums.FailureCode;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.StateCodeEnum;
import com.hvisions.brewage.esb.EsbInterface;
import com.hvisions.brewage.esb.enums.EsbConsumerEnum;
import com.hvisions.brewage.esb.vo.EsbResultVO;
import com.hvisions.brewage.esb.vo.ResultVO;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.vo.rzgx.RzgxTeamUserVO;
import com.hvisions.brewage.vo.rzgx.RzgxTeamVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: RzgxSendService
 * @description:
 * @date 2025/9/5 14:48
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class RzgxSendService {

    @Resource
    private EsbInterface esbInterface;

    @Resource
    private LogService logService;

    @Resource
    private TeamScheduledMapper teamScheduledMapper;

    @Resource
    private TeamMapper teamMapper;

    @Resource
    private TeamUserMapper teamUserMapper;


    /**
     * MES拉取人资共享人员排班信息
     *
     * @param centerId 中心id
     * @param locationId 车间id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    public String mesToSyncRzgxScheduled(Integer centerId,Integer locationId,String startDate, String endDate) {
        RzgxTeamScheduledQueryDTO queryDTO = new RzgxTeamScheduledQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);

        if(null!=centerId){
            String centerCode=teamScheduledMapper.selectYzgxCode(centerId);
            queryDTO.setCenterCode(centerCode);
        }

        if(null!=locationId){
            String workshopCode=teamScheduledMapper.selectYzgxCode(locationId);
            queryDTO.setWorkshopCode(workshopCode);
        }

        ResultVO resultVO = sendEsb(EsbConsumerEnum.LZLJ2110, queryDTO);

        String toJSONString = JSONObject.toJSONString(resultVO.getData());

        List<TeamScheduled> scheduledList = JSONObject.parseArray(toJSONString, TeamScheduled.class);

        //删除以前的数据，查询新的数据进去
        LambdaQueryWrapper<TeamScheduled> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamScheduled::getDeleted, 0)
                .ge(TeamScheduled::getCalendarDate, startDate)
                .le(TeamScheduled::getCalendarDate, endDate);
        teamScheduledMapper.delete(wrapper);

        scheduledList.forEach(v -> {
            teamScheduledMapper.insert(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("同步人资共享系统人员排班数据");
    }

    /**
     * MES拉取人资共享班组信息
     *
     * @return
     */
    public String mesToSyncRzgxTeam() {
        ResultVO resultVO = sendEsb(EsbConsumerEnum.LZLJ2111, new SzgxTeamQueryDTO());
        String toJSONString = JSONObject.toJSONString(resultVO.getData());

        List<RzgxTeamVO> scheduledList = JSONObject.parseArray(toJSONString, RzgxTeamVO.class);


        //删除以前的数据，查询新的数据进去
        LambdaQueryWrapper<Team> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Team::getDeleted, 0);
        teamMapper.delete(wrapper);

        LambdaQueryWrapper<TeamUser> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TeamUser::getDeleted, 0);
        teamUserMapper.delete(wrapper1);

        //新增数据
        scheduledList.forEach(v -> {
            Team team = DtoMapper.convert(v, Team.class);
            teamMapper.insert(team);
            List<RzgxTeamUserVO> teamUserVOList = v.getMembers();
            if (!CollectionUtils.isEmpty(teamUserVOList)) {
                List<TeamUser> teamUserList = DtoMapper.convertList(teamUserVOList, TeamUser.class);
                teamUserList.forEach(v2 -> {
                    v2.setTeamId(team.getId());
                    teamUserMapper.insert(v2);
                });
            }
        });

        return OperationResult.OTHER_SUCCESS.getDescription("同步人资共享系统班组数据");
    }


    private ResultVO sendEsb(String interfaceCode, Object data) {
        EsbResultVO esbResultVO = esbInterface.sendPost(interfaceCode, data);
        ResultVO resultVO = JSONObject.parseObject(esbResultVO.getResultVO(), ResultVO.class);

        if (!StateCodeEnum.RZGX_OK.equals(resultVO.getCode())) {
            //插入日志
            logService.addLogCaptureESB(esbResultVO.getRequestParam(), interfaceCode, resultVO.getMessage());

            FailureCode addFailedDataExists = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(addFailedDataExists.getCode(), addFailedDataExists.getDescription("调用人资共享系统", "接口编号=" + interfaceCode + "异常原因：=" + resultVO.getMessage()));
        }

        //插入日志
        logService.addLogCaptureESB(esbResultVO.getRequestParam(), EsbConsumerEnum.LZLJ2111, null);

        return resultVO;
    }
}
