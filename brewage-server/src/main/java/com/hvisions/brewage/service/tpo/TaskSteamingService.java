package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskSteamingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSteamingMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSteamingAddDTO;
import com.hvisions.brewage.entity.tpo.TaskSistillation;
import com.hvisions.brewage.entity.tpo.TaskSteaming;
import com.hvisions.brewage.entity.tpo.TaskSteamingDetail;
import com.hvisions.brewage.entity.tpo.TaskWinePickingDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskSteamingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSteamingService
 * @description: 出甑任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSteamingService {

    @Resource
    private TaskSteamingMapper taskSteamingMapper;
    @Resource
    private TaskSteamingDetailMapper taskSteamingDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private LogService logService;

    /**
     * 查询出甑任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskSteamingVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskSteamingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskSteamingVO> taskSteamingVOList = taskSteamingMapper.selectPageList(page, queryDTO);
        taskSteamingVOList.forEach(item->{
            LambdaQueryWrapper<TaskSteamingDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskSteamingDetail::getTaskSteamingId, item.getId())
                    .eq(TaskSteamingDetail::getDeleted,0);
            List<TaskSteamingDetail> list = taskSteamingDetailMapper.selectList(wrapper);
            if(list.size()>0){

                Double oneMeasureWaterWeight = list.stream().mapToDouble(x-> Double.parseDouble(x.getOneMeasureWaterWeight())).sum();
                Double twoMeasureWaterWeight = list.stream().mapToDouble(x-> Double.parseDouble(x.getTwoMeasureWaterWeight())).sum();
                item.setMeasureWaterWeight(String.valueOf(oneMeasureWaterWeight+twoMeasureWaterWeight));
            }
        });
        page.setRecords(taskSteamingVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskSteamingAddDTO addDTO) {
        log.info("调用新增出甑任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增出甑任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskSteaming> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSteaming::getPitOrder,addDTO.getPitOrder())
                .eq(TaskSteaming::getDeleted,0);
        Integer count = taskSteamingMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskSteaming taskSteaming = DtoMapper.convert(addDTO, TaskSteaming.class);

        //查询配置最大的流水号
        Integer maxNum = taskSteamingMapper.selectMaxTaskNo(ProdConfigEnum.ZZ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.ZZ, maxNum);
        taskSteaming.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskSteaming.setTaskType(TaskBusiness.SISTILLATION.getTaskType());
        taskSteaming.setState(TaskStatus.PENDING_EXE.getCode());
        taskSteaming.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskSteaming.setCreateTime(new Date());
        taskSteaming.setCreatorId(userId);

        taskSteamingMapper.insert(taskSteaming);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSteamingAddDTO> addDTOList) {
        log.info("调用批量新增出甑任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增出甑任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskSteamingAddDTO updateDTO) {
        log.info("调用修改出甑任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改出甑任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskSteaming taskSteaming = taskSteamingMapper.selectById(updateDTO.getId());
        if (null == taskSteaming || taskSteaming.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskSteaming);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskSteamingMapper.updateById(taskSteaming);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除出甑任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除出甑任务列表，获取到当前登录用户，用户id：{}", userId);
        taskSteamingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    public String endTaskSteaming(String pitOrder) {
        log.info("调用结束出甑任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束出甑任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskSteaming> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSteaming::getPitOrder,pitOrder)
                .ne(TaskSteaming::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskSteaming::getDeleted,0);
        List<TaskSteaming> list = taskSteamingMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束出甑任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskSteamingMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束出甑任务成功");
    }
}
