package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskSistillationDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSistillationMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSistillationAddDTO;
import com.hvisions.brewage.entity.tpo.TaskGrainLubricationDetail;
import com.hvisions.brewage.entity.tpo.TaskSistillation;
import com.hvisions.brewage.entity.tpo.TaskSistillationDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskSistillationVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSistillationService
 * @description: 蒸馏任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSistillationService {

    @Resource
    private TaskSistillationMapper taskSistillationMapper;
    @Resource
    private TaskSistillationDetailMapper taskSistillationDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private LogService logService;

    /**
     * 查询蒸馏任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskSistillationVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskSistillationVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskSistillationVO> taskSistillationVOList = taskSistillationMapper.selectPageList(page, queryDTO);
        taskSistillationVOList.forEach(item->{
            LambdaQueryWrapper<TaskSistillationDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskSistillationDetail::getTaskSistillationId, item.getId())
                    .eq(TaskSistillationDetail::getDeleted,0);
            List<TaskSistillationDetail> list = taskSistillationDetailMapper.selectList(wrapper);
            if(list.size()>0){

                Double hj1SingleContainerUsage = list.stream().mapToDouble(x-> Double.parseDouble(x.getHj1SingleContainerUsage())).sum();
                Double yellowWaterUsage = list.stream().mapToDouble(x-> Double.parseDouble(x.getYellowWaterUsage())).sum();
                Double tailWineUsage = list.stream().mapToDouble(x-> Double.parseDouble(x.getTailWineUsage())).sum();
                Double ysageOfCirculatingWater = list.stream().mapToDouble(x-> Double.parseDouble(x.getYsageOfCirculatingWater())).sum();
                item.setHj1SingleContainerUsage(String.valueOf(hj1SingleContainerUsage));
                item.setYellowWaterUsage(String.valueOf(yellowWaterUsage));
                item.setTailWineUsage(String.valueOf(tailWineUsage));
                item.setYsageOfCirculatingWater(String.valueOf(ysageOfCirculatingWater));
            }
        });
        page.setRecords(taskSistillationVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskSistillationAddDTO addDTO) {
        log.info("调用新增蒸馏任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增蒸馏任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskSistillation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSistillation::getPitOrder,addDTO.getPitOrder())
                .eq(TaskSistillation::getDeleted,0);
        Integer count = taskSistillationMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskSistillation taskSistillation = DtoMapper.convert(addDTO, TaskSistillation.class);

        //查询配置最大的流水号
        Integer maxNum = taskSistillationMapper.selectMaxTaskNo(ProdConfigEnum.ZL);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.ZL, maxNum);
        taskSistillation.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskSistillation.setTaskType(TaskBusiness.SISTILLATION.getTaskType());
        taskSistillation.setState(TaskStatus.PENDING_EXE.getCode());
        taskSistillation.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskSistillation.setCreateTime(new Date());
        taskSistillation.setCreatorId(userId);

        taskSistillationMapper.insert(taskSistillation);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSistillationAddDTO> addDTOList) {
        log.info("调用批量新增蒸馏任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增蒸馏任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskSistillationAddDTO updateDTO) {
        log.info("调用修改蒸馏任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改蒸馏任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskSistillation taskSistillation = taskSistillationMapper.selectById(updateDTO.getId());
        if (null == taskSistillation || taskSistillation.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskSistillation);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskSistillationMapper.updateById(taskSistillation);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除蒸馏任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除蒸馏任务列表，获取到当前登录用户，用户id：{}", userId);
        taskSistillationMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    public String endTaskSistillation(String pitOrder) {
        log.info("调用结束蒸馏任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束蒸馏任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskSistillation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSistillation::getPitOrder,pitOrder)
                .ne(TaskSistillation::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskSistillation::getDeleted,0);
        List<TaskSistillation> list = taskSistillationMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束蒸馏任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskSistillationMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束蒸馏任务成功");
    }
}
