package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskDishMixingMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskDishMixingAddDTO;
import com.hvisions.brewage.entity.tpo.TaskDishMixing;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskDishMixingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingService
 * @description: 拌糟任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDishMixingService {

    @Resource
    private TaskDishMixingMapper taskDishMixingMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    /**
     * 查询拌糟任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskDishMixingVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskDishMixingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskDishMixingVO> taskDishMixingVOList = taskDishMixingMapper.selectPageList(page, queryDTO);

        page.setRecords(taskDishMixingVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskDishMixingAddDTO addDTO) {
        log.info("调用新增拌糟任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增拌糟任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskDishMixing> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDishMixing::getPitOrder,addDTO.getPitOrder())
                .eq(TaskDishMixing::getDeleted,0);
        Integer count = taskDishMixingMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskDishMixing taskDishMixing = DtoMapper.convert(addDTO, TaskDishMixing.class);

        //查询配置最大的流水号
        Integer maxNum = taskDishMixingMapper.selectMaxTaskNo(ProdConfigEnum.DZ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.DZ, maxNum);
        taskDishMixing.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskDishMixing.setTaskType(TaskBusiness.DISHMIXING.getTaskType());
        taskDishMixing.setState(TaskStatus.PENDING_EXE.getCode());
        taskDishMixing.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskDishMixing.setCreateTime(new Date());
        taskDishMixing.setCreatorId(userId);

        taskDishMixingMapper.insert(taskDishMixing);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskDishMixingAddDTO> addDTOList) {
        log.info("调用批量新增拌糟任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增拌糟任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskDishMixingAddDTO updateDTO) {
        log.info("调用修改拌糟任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改拌糟任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskDishMixing taskDishMixing = taskDishMixingMapper.selectById(updateDTO.getId());
        if (null == taskDishMixing || taskDishMixing.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskDishMixing);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskDishMixingMapper.updateById(taskDishMixing);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除拌糟任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除拌糟任务列表，获取到当前登录用户，用户id：{}", userId);
        taskDishMixingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    public String endTaskDishMixing(String pitOrder) {
        log.info("调用结束拌糟任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束拌糟任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskDishMixing> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDishMixing::getPitOrder,pitOrder)
                .ne(TaskDishMixing::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskDishMixing::getDeleted,0);
        List<TaskDishMixing> list = taskDishMixingMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束拌糟任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskDishMixingMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束拌糟任务成功");
    }
}
