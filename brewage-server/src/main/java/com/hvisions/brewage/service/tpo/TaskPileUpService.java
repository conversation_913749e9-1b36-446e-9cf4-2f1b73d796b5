package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.*;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskPileUpAddDTO;
import com.hvisions.brewage.dto.tpo.TaskPitBatchesUpdateDTO;
import com.hvisions.brewage.entity.tpo.*;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.brewage.vo.tpo.TaskPileUpVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPileUpService
 * @description: 下糟任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskPileUpService {

    @Resource
    private TaskPileUpMapper taskPileUpMapper;
    @Resource
    private TaskPileUpDetailMapper taskPileUpDetailMapper;
    @Resource
    private TaskGrainLubricationMapper taskGrainLubricationMapper;
    @Resource
    private TaskGrainLubricationDetailMapper taskGrainLubricationDetailMapper;
    @Resource
    private TaskDishMixingMapper taskDishMixingMapper;
    @Resource
    private TaskDishMixingDetailMapper taskDishMixingDetailMapper;
    @Resource
    private TaskShangzhengMapper taskShangzhengMapper;
    @Resource
    private TaskShangzhengDetailMapper taskShangzhengDetailMapper;
    @Resource
    private TaskSistillationMapper taskSistillationMapper;
    @Resource
    private TaskSistillationDetailMapper taskSistillationDetailMapper;
    @Resource
    private TaskWinePickingMapper taskWinePickingMapper;
    @Resource
    private TaskWinePickingDetailMapper taskWinePickingDetailMapper;
    @Resource
    private TaskSteamingMapper taskSteamingMapper;
    @Resource
    private TaskSteamingDetailMapper taskSteamingDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;
    @Resource
    private TaskSpreadingDetailMapper taskSpreadingDetailMapper;

    /**
     * 查询下糟任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskPileUpVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskPileUpVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskPileUpVO> taskPileUpVOList = taskPileUpMapper.selectPageList(page, queryDTO);
        taskPileUpVOList.forEach(taskPileUp->{
            LambdaQueryWrapper<TaskPileUpDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskPileUpDetail::getTaskPileUpId, taskPileUp.getId())
                    .eq(TaskPileUpDetail::getDeleted,0);
           List<TaskPileUpDetail> list = taskPileUpDetailMapper.selectList(wrapper);
           if(list.size()>0){
               taskPileUp.setZengkouNumber(String.valueOf(list.size()));
               Double fermentedGrainsWeight = list.stream().mapToDouble(x-> Double.parseDouble(x.getTotalWeight())).sum();
               Double fermentedGrainsVolume = list.stream().mapToDouble(x-> Double.parseDouble(x.getFermentativeMaterialVolume())).sum();
               taskPileUp.setFermentedGrainsWeight(String.valueOf(fermentedGrainsWeight));
               taskPileUp.setFermentedGrainsVolume(String.valueOf(fermentedGrainsVolume));
           }
        });
        page.setRecords(taskPileUpVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskPileUpAddDTO addDTO) {
        log.info("调用新增下糟任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增下糟任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskPileUp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskPileUp::getPitOrder,addDTO.getPitOrder())
                .eq(TaskPileUp::getDeleted,0);
        Integer count = taskPileUpMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskPileUp taskPileUp = DtoMapper.convert(addDTO, TaskPileUp.class);

        //查询配置最大的流水号
        Integer maxNum = taskPileUpMapper.selectMaxTaskNo(ProdConfigEnum.DZ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.DZ, maxNum);
        taskPileUp.setTaskNo(taskNo);

//        TaskPileUp.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskPileUp.setTaskType(TaskBusiness.TASK_PILE_UP.getTaskType());
        taskPileUp.setState(TaskStatus.PENDING_EXE.getCode());
        taskPileUp.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskPileUp.setCreateTime(new Date());
        taskPileUp.setCreatorId(userId);

        taskPileUpMapper.insert(taskPileUp);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskPileUpAddDTO> addDTOList) {
        log.info("调用批量新增下糟任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增下糟任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskPileUpAddDTO updateDTO) {
        log.info("调用修改下糟任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改下糟任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskPileUp taskPileUp = taskPileUpMapper.selectById(updateDTO.getId());
        if (null == taskPileUp || taskPileUp.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskPileUp);
//        TaskPileUp.setStartTime(updateDTO.getStartTime());
//        TaskPileUp.setEndTime(updateDTO.getEndTime());
//        TaskPileUp.setUpdaterId(userId);
//        TaskPileUp.setUpdateTime(new Date());
        taskPileUpMapper.updateById(taskPileUp);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除下糟任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除下糟任务列表，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskPileUp> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TaskPileUp::getId,ids)
                .ne(TaskPileUp::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskPileUp::getDeleted,0);
        List<TaskPileUp> taskPileUpList = taskPileUpMapper.selectList(wrapper);
        taskPileUpList.forEach(v->{
            v.setDeleted(true);
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            taskPileUpMapper.updateById(v);
        });

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 更改窖池号
     * @param updateDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String updatePitNo(TaskPitBatchesUpdateDTO updateDTO){
        //改出
        if(StringUtil.isNotEmpty(updateDTO.getOutPitOrder())){
            //下糟
            updatePitNoOutTaskPileUpDetail(updateDTO);
            //润粮
            updatePitNoOutTaskDishMixingDetail(updateDTO);
            //拌糟
            updatePitNoOutTaskGrainLubricationDetail(updateDTO);
            //上甑
            updatePitNoOutTaskShangzhengDetail(updateDTO);
            //蒸馏
            updatePitNoOutTaskSistillationDetail(updateDTO);
            //摘酒
            updatePitNoOutTaskWinePickingDetail(updateDTO);
            //出甑
            updatePitNoOutTaskSteamingDetail(updateDTO);
        }

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    private void updatePitNoOutTaskSteamingDetail(TaskPitBatchesUpdateDTO updateDTO) {
        //出甑
        LambdaQueryWrapper<TaskSteaming> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskSteaming::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskSteaming::getDeleted,0);
        TaskSteaming taskSteaming = taskSteamingMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskSteamingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSteamingDetail::getDeleted,0)
                .in(TaskSteamingDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskSteamingDetail> list = taskSteamingDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskSteaming != null){
                    item.setTaskSteamingId(taskSteaming.getId());
                    item.setPitNo(taskSteaming.getPitNo());

                    taskSteamingDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskWinePickingDetail(TaskPitBatchesUpdateDTO updateDTO) {
        //摘酒
        LambdaQueryWrapper<TaskWinePicking> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskWinePicking::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskWinePicking::getDeleted,0);
        TaskWinePicking taskWinePicking = taskWinePickingMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskWinePickingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWinePickingDetail::getDeleted,0)
                .in(TaskWinePickingDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskWinePickingDetail> list = taskWinePickingDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskWinePicking != null){
                    item.setTaskWinePickingId(taskWinePicking.getId());
                    item.setPitNo(taskWinePicking.getPitNo());

                    taskWinePickingDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskSistillationDetail(TaskPitBatchesUpdateDTO updateDTO) {
        //蒸馏
        LambdaQueryWrapper<TaskSistillation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskSistillation::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskSistillation::getDeleted,0);
        TaskSistillation taskSistillation = taskSistillationMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskSistillationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSistillationDetail::getDeleted,0)
                .in(TaskSistillationDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskSistillationDetail> list = taskSistillationDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskSistillation != null){
                    item.setTaskSistillationId(taskSistillation.getId());
                    item.setPitNo(taskSistillation.getPitNo());

                    taskSistillationDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskShangzhengDetail(TaskPitBatchesUpdateDTO updateDTO) {
        //上甑
        LambdaQueryWrapper<TaskShangzheng> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskShangzheng::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskShangzheng::getDeleted,0);
        TaskShangzheng taskShangzheng = taskShangzhengMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskShangzhengDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskShangzhengDetail::getDeleted,0)
                .in(TaskShangzhengDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskShangzhengDetail> list = taskShangzhengDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskShangzheng != null){
                    item.setTaskShangzhengId(taskShangzheng.getId());
                    item.setPitNo(taskShangzheng.getPitNo());

                    taskShangzhengDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskGrainLubricationDetail(TaskPitBatchesUpdateDTO updateDTO) {
        //拌糟
        LambdaQueryWrapper<TaskGrainLubrication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskGrainLubrication::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskGrainLubrication::getDeleted,0);
        TaskGrainLubrication taskGrainLubrication = taskGrainLubricationMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskGrainLubricationDetail> wrapper = new LambdaQueryWrapper<TaskGrainLubricationDetail>();
        wrapper.eq(TaskGrainLubricationDetail::getDeleted,0)
                .in(TaskGrainLubricationDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskGrainLubricationDetail> list = taskGrainLubricationDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskGrainLubrication != null){
                    item.setTaskGrainLubricationId(taskGrainLubrication.getId());
                    item.setPitNo(taskGrainLubrication.getPitNo());

                    taskGrainLubricationDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskPileUpDetail(TaskPitBatchesUpdateDTO updateDTO) {

        //下糟
        LambdaQueryWrapper<TaskPileUp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskPileUp::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskPileUp::getDeleted,0);
        TaskPileUp taskPileUp = taskPileUpMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskPileUpDetail> wrapper = new LambdaQueryWrapper<TaskPileUpDetail>();
        wrapper.eq(TaskPileUpDetail::getDeleted,0)
                .in(TaskPileUpDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskPileUpDetail> pileDetailList = taskPileUpDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(pileDetailList)){
            pileDetailList.forEach(item->{
                if(taskPileUp != null){
                    item.setTaskPileUpId(taskPileUp.getId());
                    item.setPitNo(taskPileUp.getPitNo());
//                    LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper2 = new LambdaQueryWrapper<>();
//                    wrapper2.eq(TPoWorkshopPitOrder::getOrderCode,updateDTO.getInPitOrder())
//                            .eq(TPoWorkshopPitOrder::getIsDeleted,0);
//                    TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper2);
//                    //查询糟源类别
//                    String materialType = taskSpreadingDetailMapper.selectMaterialType(workshopPitOrder.getVinasseId());
//                    item.setEnterMaterialType(materialType);
//                    item.setOutMaterialType(updateDTO.getVinasse());
                    taskPileUpDetailMapper.updateById(item);
                }
            });
        }
    }

    private void updatePitNoOutTaskDishMixingDetail(TaskPitBatchesUpdateDTO updateDTO) {

        //润粮
        LambdaQueryWrapper<TaskDishMixing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskDishMixing::getPitOrder,updateDTO.getOutPitOrder())
                .eq(TaskDishMixing::getDeleted,0);
        TaskDishMixing taskDishMixing = taskDishMixingMapper.selectOne(queryWrapper);

        LambdaQueryWrapper<TaskDishMixingDetail> wrapper = new LambdaQueryWrapper<TaskDishMixingDetail>();
        wrapper.eq(TaskDishMixingDetail::getDeleted,0)
                .in(TaskDishMixingDetail::getIotTaskNo,updateDTO.getIotTaskNo());
        List<TaskDishMixingDetail> list = taskDishMixingDetailMapper.selectList(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(item->{
                if(taskDishMixing != null){
                    item.setTaskDishMixingId(taskDishMixing.getId());
                    item.setPitNo(taskDishMixing.getPitNo());

                    taskDishMixingDetailMapper.updateById(item);
                }
            });
        }
    }

    public String endTaskPileUp(String pitOrder) {
        log.info("调用结束下糟任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束下糟任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskPileUp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskPileUp::getPitOrder,pitOrder)
                .ne(TaskPileUp::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskPileUp::getDeleted,0);
        List<TaskPileUp> list = taskPileUpMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束下糟任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskPileUpMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束下糟任务成功");
    }
}
