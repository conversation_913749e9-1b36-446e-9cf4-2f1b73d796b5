package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.MaintainTaskDetailMapper;
import com.hvisions.brewage.dao.tpo.MaintainTaskMapper;
import com.hvisions.brewage.dto.tpo.MaintainTaskDetailAddDTO;
import com.hvisions.brewage.dto.tpo.MaintainTaskDetailUpdateDTO;
import com.hvisions.brewage.entity.tpo.TaskMaintain;
import com.hvisions.brewage.entity.tpo.TaskMaintainDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.feign.technology.ProcessAlarmClient;
import com.hvisions.brewage.mkwine.enums.CheckCycleTimeUnitEnum;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.OrderExecutionTaskVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.OrderTaskDetailVo;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.OrderTaskVo;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.technology.dto.ProcessAlarmAddDTO;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.validation.Validator;
import com.hvisions.brewage.vo.tpo.MaintainTaskDetailVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MaintainTaskDetailService
 * @description: 窖池养护任务详情Service
 * @date 2025/6/30 11:08
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MaintainTaskDetailService {
    @Resource
    private MaintainTaskDetailMapper maintainTaskDetailMapper;

    @Resource
    private MaintainTaskMapper maintainTaskMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private RouteParameterWarnService routeParameterWarnService;

    @Resource
    private ProcessAlarmClient processAlarmClient;

    /**
     * 查询窖池养护任务详情列表
     *
     * @param maintainTaskId
     * @return
     */
    public List<MaintainTaskDetailVO> findList(Integer maintainTaskId) {
        LambdaQueryWrapper<TaskMaintainDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskMaintainDetail::getDeleted, 0)
                .eq(TaskMaintainDetail::getTaskMaintainId, maintainTaskId);
        List<TaskMaintainDetail> taskDetailList = maintainTaskDetailMapper.selectList(wrapper);
        return DtoMapper.convertList(taskDetailList, MaintainTaskDetailVO.class);

    }

    /**
     * 新增
     *
     * @param addDTOList
     * @return
     */
    public String add(List<MaintainTaskDetailAddDTO> addDTOList) {
        //校验参数不能为空
        Validator<MaintainTaskDetailAddDTO> validatorParameter = new Validator<>();
        validatorParameter.validate(addDTOList);

        log.info("调用新增窖池养护任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增窖池养护任务详情列表，获取到当前登录用户，用户id：{}", userId);


        addDTOList.forEach(v -> {
            //判断养护任务是否已完成
            TaskMaintain taskMaintain = maintainTaskMapper.selectById(v.getTaskMaintainId());
            if (null == taskMaintain || taskMaintain.getDeleted()) {
                FailureCode updateFailed = FailureCode.ADD_FAILED_DATA_NOT_EXISTS;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(v.getTaskMaintainId()));
            }

            if (taskMaintain.getStatus().equals(TaskStatus.COMPLETED.getName())) {
                FailureCode updateFailed = FailureCode.ADD_FAILED_DATA_EXISTS_MSG;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("ID=" + v.getTaskMaintainId() + "已完成养护"));
            }

            //查询是否存在正在养护的养护详情任务
            LambdaQueryWrapper<TaskMaintainDetail> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TaskMaintainDetail::getTaskMaintainId, v.getTaskMaintainId())
                    .eq(TaskMaintainDetail::getDeleted, 0)
                    .isNull(TaskMaintainDetail::getMaintainEndTime)
                    .orderByDesc(TaskMaintainDetail::getMaintenanceNum)
                    .last("LIMIT 1");
            TaskMaintainDetail taskMaintainDetail = maintainTaskDetailMapper.selectOne(wrapper1);
            if (null != taskMaintainDetail) {
                FailureCode updateFailed = FailureCode.ADD_FAILED_DATA_EXISTS_MSG;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("ID=" + v.getTaskMaintainId() + "养护任务还未完成"));
            }

            //查询当前的养护次数
            LambdaQueryWrapper<TaskMaintainDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskMaintainDetail::getTaskMaintainId, v.getTaskMaintainId())
                    .eq(TaskMaintainDetail::getDeleted, 0)
                    .orderByDesc(TaskMaintainDetail::getMaintenanceNum)
                    .last("LIMIT 1");
            TaskMaintainDetail taskDetail = maintainTaskDetailMapper.selectOne(wrapper);


            //算预计开始时间
            //Date date = maintainTaskDetailMapper.selectTaskYjTime(v.getTaskMaintainId());
            TaskMaintainDetail convert = DtoMapper.convert(v, TaskMaintainDetail.class);
            convert.setMaintainExpectStartTime(taskMaintain.getMaintainExpectStartTime());
            convert.setCreateTime(new Date());
            convert.setCreatorId(userId);
            convert.setMaintainStartTime(new Date());
            convert.setMaintenanceNum(null == taskDetail ? 1 : taskDetail.getMaintenanceNum() + 1);
            maintainTaskDetailMapper.insert(convert);

            //判断是否超过工艺标准配置的值，如果超过了则进行参数告警----代澳旗说用量不需要告警
           // judgeRouteParameterWarn(v.getDaquUsageType(), v.getDaquUsageNum(),taskMaintain.getPitOrder(),taskMaintain.getPitNo(),taskMaintain.getCenterId(),taskMaintain.getLocationId());

            if (taskMaintain.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                taskMaintain.setStatus(TaskStatus.IN_PROGRESS.getName());
            }

            if (null == taskMaintain.getStartTime()) {
                taskMaintain.setStartTime(new Date());
            }

            taskMaintain.setUpdaterId(userId);
            taskMaintain.setUpdateTime(new Date());
            taskMaintain.setSubtaskStatus(TaskStatus.IN_PROGRESS.getName());
            maintainTaskMapper.updateById(taskMaintain);
        });

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 修改窖池养护任务详情列表
     *
     * @param updateDTO
     * @return
     */
    public String update(MaintainTaskDetailUpdateDTO updateDTO) {
        log.info("调用修改窖池养护任务详情列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改窖池养护任务详情列表，获取到当前登录用户，用户id：{}", userId);

        Integer maintainTaskId = updateDTO.getTaskMaintainId();
        TaskMaintain taskMaintain = maintainTaskMapper.selectById(maintainTaskId);
        if (null == taskMaintain || taskMaintain.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(maintainTaskId));
        }

        //判断养护任务是否不等于执行中 && 不等于已完成
        boolean flag = !taskMaintain.getStatus().equals(TaskStatus.IN_PROGRESS.getName()) && !taskMaintain.getStatus().equals(TaskStatus.COMPLETED.getName());
        if (flag) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("只能修改执行中或已完成的窖池养护任务"));
        }


        TaskMaintainDetail taskMaintainDetail = maintainTaskDetailMapper.selectById(updateDTO.getId());
        if (null == taskMaintainDetail || taskMaintainDetail.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }

        TaskMaintainDetail convert = DtoMapper.convert(updateDTO, TaskMaintainDetail.class);
        convert.setUpdaterId(userId);
        convert.setUpdateTime(new Date());

        maintainTaskDetailMapper.updateById(convert);


        //判断是否超过工艺标准配置的值，如果超过了则进行参数告警----代澳旗说用量不需要告警
        //judgeRouteParameterWarn(updateDTO.getDaquUsageType(), updateDTO.getDaquUsageNum(),taskMaintain.getPitOrder(),taskMaintain.getPitNo(),taskMaintain.getCenterId(),taskMaintain.getLocationId());

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 判断是否超过工艺标准配置的值，如果超过了则进行参数告警
     *
     * @param daquUsageType 养护用曲类型(物料编码)
     * @param daquUsageNum  养护用曲量(kg)
     */
    public void judgeRouteParameterWarn(String daquUsageType, BigDecimal daquUsageNum,String pitOrder, String pitNo,Integer centerId,Integer locationId) {
        log.info("窖池养护任务详情,判断是否需要调用工艺接口，养护用曲类型：{}，养护用曲量(kg)：{}", daquUsageType, daquUsageNum);
        //查询用量是否超过了上下限
        BigDecimal formulaDosage = maintainTaskMapper.selectMaintainFormula(daquUsageType);
        if(null!=formulaDosage && formulaDosage.compareTo(daquUsageNum)!=0){
            ProcessAlarmAddDTO addDTO = new ProcessAlarmAddDTO();
            //查询中心名称
            String centerName=maintainTaskMapper.selectCenterOrLocationName(centerId);
            addDTO.setWorkshop(centerName);
            //查询车间名称
            String locationName=maintainTaskMapper.selectCenterOrLocationName(locationId);
            addDTO.setCenter(locationName);
            RouteParameterWarnParamEnum paramEnum = RouteParameterWarnParamEnum.QUANTITY_INVESTED;
            addDTO.setRouteCode(paramEnum.getBusinessModule());
            addDTO.setRouteName(paramEnum.getBusinessModule());
            addDTO.setOperationCode(paramEnum.getBusinessModule());
            addDTO.setOperationName(paramEnum.getBusinessModule());
            addDTO.setParamCode(paramEnum.getParamCode());
            addDTO.setParamName(paramEnum.getParamName());
            addDTO.setCellarOrder(pitOrder);
            addDTO.setPitNo(pitNo);
            addDTO.setSetValue(formulaDosage);
            addDTO.setActualValue(daquUsageNum);
            addDTO.setUpperRangeLimit(formulaDosage);
            addDTO.setLowerRangeLimit(formulaDosage);

            //判断是否高于上限
            addDTO.setOverproofValuse(daquUsageNum.subtract(formulaDosage));
            addDTO.setReportTime(new Date());

            log.info("窖池养护任务详情调用工艺告警相关的接口，传入参数---------》{}", JSONObject.toJSONString(addDTO));
            processAlarmClient.addProcessAlarm(addDTO);
        }else {
            log.info("填入的值没有超过上下限，无需告警，填入用量：{}，配置用量:{}",daquUsageNum,formulaDosage);
        }
    }

    /**
     * 删除窖池养护任务详情列表
     *
     * @param ids 窖池养护任务详情主键id
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("删除窖池养护任务详情列表，传入参数：{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("删除窖池养护任务详情列表，获取到当前登录用户，用户id：{}", userId);
        maintainTaskDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 结束养护任务详情
     *
     * @param maintainTaskIds
     * @return
     */
    public String end(List<Integer> maintainTaskIds) {
        log.info("结束养护任务详情，传入参数：{}", JSONObject.toJSONString(maintainTaskIds));
        Integer userId = userAuditorAware.getUserId();
        log.info("结束养护任务详情，获取到当前登录用户，用户id：{}", userId);

        maintainTaskIds.forEach(v -> {
            //判断养护任务是否已完成
            TaskMaintain taskMaintain = maintainTaskMapper.selectById(v);
            if (null == taskMaintain || taskMaintain.getDeleted()) {
                FailureCode updateFailed = FailureCode.OTHER_FAILED_NOT_FOUND;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("结束养护任务", v));
            }

            if (taskMaintain.getStatus().equals(TaskStatus.COMPLETED.getName())) {
                FailureCode updateFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("结束养护任务", "ID=" + v + "已完成养护"));
            }

            //判断是否存在正在执行的养护详情任务
            LambdaQueryWrapper<TaskMaintainDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskMaintainDetail::getTaskMaintainId, v)
                    .eq(TaskMaintainDetail::getDeleted, 0)
                    .isNull(TaskMaintainDetail::getMaintainEndTime)
                    .orderByDesc(TaskMaintainDetail::getMaintenanceNum)
                    .last("LIMIT 1");
            TaskMaintainDetail taskMaintainDetail = maintainTaskDetailMapper.selectOne(wrapper);
            if (null == taskMaintainDetail) {
                FailureCode updateFailed = FailureCode.OTHER_FAILED;
                throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("结束养护任务", "ID=" + v + "没有正在执行的养护详情任务"));
            }

            taskMaintainDetail.setMaintainEndTime(new Date());
            taskMaintainDetail.setUpdateTime(new Date());
            taskMaintainDetail.setUpdaterId(userId);
            maintainTaskDetailMapper.updateById(taskMaintainDetail);

            //算下阶段养护的时间
            taskMaintain.setMaintainExpectStartTime(DateUtil.addDate(new Date(), 4, CheckCycleTimeUnitEnum.hours));
            taskMaintain.setSubtaskStatus(TaskStatus.NOT_STARTED.getName());
            taskMaintain.setUpdaterId(userId);
            taskMaintain.setUpdateTime(new Date());
            maintainTaskMapper.updateById(taskMaintain);
        });


        return OperationResult.OTHER_SUCCESS.getDescription("结束养护任务");
    }

    /**
     * 批量修改
     *
     * @param updateDTO
     * @return
     */
    public String updateList(List<MaintainTaskDetailUpdateDTO> updateDTO) {
        updateDTO.forEach(this::update);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 根据订单查询养护任务
     * @return
     */
    public OrderExecutionTaskVO selectMaintainTaskByOrder(String orderCode) {
        OrderExecutionTaskVO orderExecutionTaskVO = new OrderExecutionTaskVO();
        List<OrderTaskVo> taskVoList = maintainTaskDetailMapper.selectTaskByOrder(orderCode);
//        List<OrderTaskDetailVo> taskDetailVos = maintainTaskMapper.selectTaskByOrder();
        orderExecutionTaskVO.setTaskVoList(taskVoList);
//        orderExecutionTaskVO.setTaskDetailVos(taskDetailVos);
        return orderExecutionTaskVO;
    }
}
