package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskDiscardMapper;
import com.hvisions.brewage.dao.tpo.TaskPileUpDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskPileUpMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.tpo.TaskDiscardAddDTO;
import com.hvisions.brewage.dto.tpo.TaskPileUpDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskPileUpDetailQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingAddDTO;
import com.hvisions.brewage.entity.tpo.TaskDiscard;
import com.hvisions.brewage.entity.tpo.TaskPileUp;
import com.hvisions.brewage.entity.tpo.TaskPileUpDetail;
import com.hvisions.brewage.entity.tpo.TaskSpreading;
import com.hvisions.brewage.enums.LogCaptureEnum;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.RouteParameterWarnParamEnum;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.WorkshopPitVO;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.operlog.utils.StringUtils;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskPileUpDetailVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPileUpDetailService
 * @description: 下糟任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskPileUpDetailService {

    @Resource
    private TaskPileUpDetailMapper taskPileUpDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskPileUpMapper taskPileUpMapper;
    @Resource
    private TaskPileUpService taskPileUpService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private TaskDiscardService taskDiscardService;
    @Resource
    private TaskSpreadingService taskSpreadingService;
    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private TaskDiscardMapper taskDiscardMapper;
    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;
    @Resource
    private WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询下糟任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskPileUpDetailVO> findPageList(TaskPileUpDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskPileUpDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskPileUpDetail::getDeleted, 0)
                .eq(TaskPileUpDetail::getTaskPileUpId, queryDTO.getTaskPileUpId());
        IPage<TaskPileUpDetail> page = taskPileUpDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskPileUpDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskPileUpDetailVO.class);
        
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskPileUpDetailAddDTO addDTO) {
        log.info("调用新增下糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增下糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskPileUpDetail pileUpDetail = DtoMapper.convert(addDTO, TaskPileUpDetail.class);

        //通过IOT任务号查甑口任务，没找到就插入
        LambdaQueryWrapper<TPoWorkshopPitOrderPotTask> wrapperTask = new LambdaQueryWrapper<>();
        wrapperTask.eq(TPoWorkshopPitOrderPotTask::getIsDeleted, 0)
                .eq(TPoWorkshopPitOrderPotTask::getIotTaskNo,addDTO.getIotTaskNo());
        List<TPoWorkshopPitOrderPotTask> task = workshopPitOrderPotTaskMapper.selectList(wrapperTask);
        if(task.size()<=0){
            TPoWorkshopPitOrderPotTask tPoWorkshopPitOrderPotTask = new TPoWorkshopPitOrderPotTask();
            tPoWorkshopPitOrderPotTask.setIotTaskNo(addDTO.getIotTaskNo());
            tPoWorkshopPitOrderPotTask.setIsCallback(false);
            workshopPitOrderPotTaskMapper.insert(tPoWorkshopPitOrderPotTask);
        }

        //根据iot传的单窖号查询连窖号
        WorkshopPitVO workshopPit = taskPileUpMapper.selectPitNo(addDTO.getPitNo());
        if(StringUtil.isEmpty(workshopPit)){
            throw new BaseKnownException(10000, "获取连窖号失败");
        }
        Integer vinasseId = taskPileUpDetailMapper.findVinasseSourceIdByBaseId(addDTO.getOutMaterialType(),1);

        LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TPoWorkshopPitOrder::getIsDeleted, 0)
                .eq(TPoWorkshopPitOrder::getPitId, workshopPit.getId())
                .eq(TPoWorkshopPitOrder::getVinasseId, vinasseId)
                .apply("(pit_status = 2 or pit_status = 3 or pit_status = 4 or pit_status = 9)");
                //.orderByDesc(TPoWorkshopPitOrder::getCreateTime)
                //.last("limit 1");
        TPoWorkshopPitOrder pitOrder = workshopPitOrderMapper.selectOne(wrapper);
        if(pitOrder != null){
            LambdaQueryWrapper<TaskPileUp> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(TaskPileUp::getDeleted, 0)
                    .eq(TaskPileUp::getPitOrder,pitOrder.getOrderCode())
                    .orderByDesc(TaskPileUp::getCreateTime)
                    .last("limit 1");

            //挂到对应的任务上
            TaskPileUp taskPileUp = taskPileUpMapper.selectOne(wrapper2);
            if (null != taskPileUp) {

                //判断任务是否是待执行
                if (taskPileUp.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                    taskPileUp.setState(TaskStatus.IN_PROGRESS.getCode());
                    taskPileUp.setStartTime(new Date());
                    taskPileUpMapper.updateById(taskPileUp);
                }
                //判断是否要更改上一个订单状态为已完成
                TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskPileUp.getCenterId(), taskPileUp.getLocationId(), taskPileUp.getPitOrder(), ExecutionStatus.DISTILLATION.getStatus());
                if(taskCurrentCenterVO.getIsChange()){
                    //关闭上一个任务
                    taskPileUpService.endTaskPileUp(taskCurrentCenterVO.getUpperPitOrder());
                }

                if(!addDTO.getEnterMaterialType().equals("丢糟")) {
                    LambdaQueryWrapper<TaskPileUpDetail> wrapper3 = new LambdaQueryWrapper<>();
                    wrapper3.eq(TaskPileUpDetail::getDeleted, 0)
                            .eq(TaskPileUpDetail::getTaskPileUpId,taskPileUp.getId());
                    List<TaskPileUpDetail> list = taskPileUpDetailMapper.selectList(wrapper3);
                    if(list.size() <= 0){
                        //追加窖池订单任务完成状态
                        //workshopPitOrderService.putExecutionStatus(pitOrder.getOrderCode(), ExecutionStatus.DISTILLATION.getStatus(),1);
                        //第一次接收数据
                        //下糟环节，MES系统需根据HMI设置的起窖号、糟源类型，执行后产生的第一条执行记录创建摊晾任务
                        //查询摊晾任务是否已经创建过了
                        LambdaQueryWrapper<TaskSpreading> wrapper6 = new LambdaQueryWrapper<>();
                        wrapper6.eq(TaskSpreading::getPitOrder,taskPileUp.getPitOrder())
                                .eq(TaskSpreading::getDeleted,0);
                        Integer count = taskSpreadingMapper.selectCount(wrapper6);
                        if(count == 0){
                            TaskSpreadingAddDTO taskSpreadingAddDTO = new TaskSpreadingAddDTO();
                            taskSpreadingAddDTO.setCenterId(taskPileUp.getCenterId());
                            taskSpreadingAddDTO.setLocationId(taskPileUp.getLocationId());
                            taskSpreadingAddDTO.setPitNo(taskPileUp.getPitNo());
                            taskSpreadingAddDTO.setPitOrder(taskPileUp.getPitOrder());
                            taskSpreadingAddDTO.setMaterialType(taskPileUp.getVinasseName());
                            taskSpreadingService.add(taskSpreadingAddDTO);
                        }

//                        //找同基地下的糟源Id赋值窖池订单的下一个订单的入窖糟源id
//                        Integer vinasseSourceId = taskPileUpDetailMapper.findVinasseSourceIdByCenterId(addDTO.getEnterMaterialType(),taskPileUp.getCenterId());
//                        TPoWorkshopPitOrder tPoWorkshopPitOrder = taskPileUpDetailMapper.findTPoWorkshopPitOrder(taskPileUp.getPitOrder());
//                        tPoWorkshopPitOrder.setBelowOutVinasseId(vinasseSourceId);
//                        workshopPitOrderMapper.updateById(tPoWorkshopPitOrder);

                    }
                }else{
                    LambdaQueryWrapper<TaskPileUpDetail> wrapper4 = new LambdaQueryWrapper<>();
                    wrapper4.eq(TaskPileUpDetail::getDeleted, 0)
                            .eq(TaskPileUpDetail::getTaskPileUpId,taskPileUp.getId())
                            .eq(TaskPileUpDetail::getEnterMaterialType,"丢糟");
                    List<TaskPileUpDetail> lists = taskPileUpDetailMapper.selectList(wrapper4);
                    if(lists.size() <= 0){
                        //第一次接收丢糟数据
                        //下糟环节（获取窖池号、糟源类型执行的第一条记录），入窖糟源为丢糟类型的，自动创建丢糟任务。
                        if(addDTO.getEnterMaterialType().equals("丢糟")) {
                            //查询丢糟任务是否已经创建过了
                            LambdaQueryWrapper<TaskDiscard> wrapper5 = new LambdaQueryWrapper<>();
                            wrapper5.eq(TaskDiscard::getPitOrder,taskPileUp.getPitOrder())
                                    .eq(TaskDiscard::getDeleted,0);
                            Integer count = taskDiscardMapper.selectCount(wrapper5);
                            if(count == 0){
                                TaskDiscardAddDTO taskDiscardAddDTO = new TaskDiscardAddDTO();
                                taskDiscardAddDTO.setCenterId(taskPileUp.getCenterId());
                                taskDiscardAddDTO.setLocationId(taskPileUp.getLocationId());
                                taskDiscardAddDTO.setPitNo(taskPileUp.getPitNo());
                                taskDiscardAddDTO.setPitOrder(taskPileUp.getPitOrder());
                                taskDiscardAddDTO.setMaterialType(taskPileUp.getVinasseName());
                                taskDiscardService.add(taskDiscardAddDTO);
                            }

                        }
                        //找同基地下的糟源Id赋值窖池订单的下一个订单的入窖糟源id
//                        Integer vinasseSourceId = taskPileUpDetailMapper.findVinasseSourceIdByCenterId(addDTO.getEnterMaterialType(),taskPileUp.getCenterId());
//                        TPoWorkshopPitOrder tPoWorkshopPitOrder = taskPileUpDetailMapper.findTPoWorkshopPitOrder(taskPileUp.getPitOrder());
//                        tPoWorkshopPitOrder.setBelowOutVinasseId(vinasseSourceId);
//                        workshopPitOrderMapper.updateById(tPoWorkshopPitOrder);
                    }
                }

            }

            pileUpDetail.setRecordTime(new Date());
            //taskPileUpDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
            pileUpDetail.setCreateTime(new Date());
            pileUpDetail.setCreatorId(userId);
            pileUpDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

            //查询任务信息是否已存在，存在则更新，不存在则添加
            LambdaQueryWrapper<TaskPileUpDetail> wrapperDetail = new LambdaQueryWrapper<>();
            wrapperDetail.eq(TaskPileUpDetail::getDeleted, 0)
                    .eq(TaskPileUpDetail::getIotTaskNo, addDTO.getIotTaskNo());
            TaskPileUpDetail taskPileUpDetail = taskPileUpDetailMapper.selectOne(wrapperDetail);
            if (null == taskPileUpDetail) {
                if (null != taskPileUp) {
                    pileUpDetail.setTaskPileUpId(taskPileUp.getId());
                }
                taskPileUpDetailMapper.insert(pileUpDetail);
            } else {
                pileUpDetail.setId(taskPileUpDetail.getId());
                if(null != taskPileUp){
                    pileUpDetail.setTaskPileUpId(taskPileUp.getId());
                }
                taskPileUpDetailMapper.updateById(pileUpDetail);
            }

            //校验参数是否有工艺异常，有则告警
            //1.单甑糟醅重量
            if(!StringUtils.isEmpty(addDTO.getTotalWeight())){
                if (null != taskPileUp) {
                    RouteParameterWarnParamEnum warnParamEnum = RouteParameterWarnParamEnum.DZ_FermentedGrainsWeight;
                    routeParameterWarnService.judgeRouteParameterWarn(warnParamEnum.getBusinessModule(),
                            warnParamEnum.getParamCode(),
                            pitOrder.getOrderCode(),
                            addDTO.getOutMaterialType(),
                            addDTO.getEnterMaterialType(),
                            new BigDecimal(addDTO.getTotalWeight())
                    );
                }

            }

            //2.单窖涨幅
//        if(!StringUtils.isEmpty(addDTO.getYeastAmount())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.SPREADING,
//                    RouteParameterWarnParamEnum.TL_TEMPERATURE.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getYeastAmount())
//            );
//        }
        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskPileUpDetailAddDTO> addDTOList) {
        log.info("调用批量新增下糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增下糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增下糟任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskPileUpDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增下糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.PILEUP_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.TaskPileUp_IOTTOMES_EXCHANGE,
                ProdMqConsts.TaskPileUp_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除下糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除下糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskPileUpDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
