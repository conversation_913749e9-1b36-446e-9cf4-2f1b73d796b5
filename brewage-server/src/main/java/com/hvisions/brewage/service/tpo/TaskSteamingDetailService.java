package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskSteamingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSteamingDetailParameterMapper;
import com.hvisions.brewage.dao.tpo.TaskSteamingMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.*;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.operlog.utils.StringUtils;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskSteamingDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSteamingDetailService
 * @description: 出甑任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSteamingDetailService {

    @Resource
    private TaskSteamingDetailMapper taskSteamingDetailMapper;
    @Resource
    private TaskSteamingDetailParameterMapper taskSteamingDetailParameterMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskSteamingMapper taskSteamingMapper;
    @Resource
    private TaskSteamingService taskSteamingService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private TaskDiscardDetailService taskDiscardDetailService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询出甑任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskSteamingDetailVO> findPageList(TaskSteamingDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskSteamingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSteamingDetail::getDeleted, 0)
                .eq(TaskSteamingDetail::getTaskSteamingId, queryDTO.getTaskSteamingId());
        IPage<TaskSteamingDetail> page = taskSteamingDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskSteamingDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskSteamingDetailVO.class);

        if (!CollectionUtils.isEmpty(detailVOPage.getRecords())) {
            detailVOPage.getRecords().forEach(v -> {
                LambdaQueryWrapper<TaskSteamingDetailParameter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskSteamingDetailParameter::getTaskSteamingDetailId, v.getId())
                        .eq(TaskSteamingDetailParameter::getDeleted, 0);
                List<TaskSteamingDetailParameter> parameterList = taskSteamingDetailParameterMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(parameterList)) {
                    //量水桶量水温度
                    List<TaskSteamingDetailParameter> conveyorLeftGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.TemperatureMeasuringBucket)).collect(Collectors.toList());
                    List<TaskSteamingDetailParameterAddDTO> conveyorLeftGrainTempConvert = DtoMapper.convertList(conveyorLeftGrainTemp, TaskSteamingDetailParameterAddDTO.class);
                    v.setTemperatureMeasuringBucketTemp(conveyorLeftGrainTempConvert);

                }
            });
        }
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskSteamingDetailAddDTO addDTO) {
        log.info("调用新增出甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增出甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskSteamingDetail steamingDetail = DtoMapper.convert(addDTO, TaskSteamingDetail.class);

        //丢糟详细记录来源入窖糟源类型为“丢糟”的出甑记录
        if(addDTO.getEnterMaterialType().equals("丢糟")){
            TaskDiscardDetailAddDTO taskDiscardDetailAddDTO = DtoMapper.convert(addDTO, TaskDiscardDetailAddDTO.class);
            taskDiscardDetailAddDTO.setDeviceNo("");
            taskDiscardDetailAddDTO.setLostGrainOutletCount(1);
            taskDiscardDetailAddDTO.setLostGrainTime(new Date());
            taskDiscardDetailAddDTO.setRecordTime(new Date());
            taskDiscardDetailAddDTO.setDataSource("IOT");
            taskDiscardDetailService.add(taskDiscardDetailAddDTO);
        }
        //根据iot传的窖号查询连窖号
        String pitNo = taskSteamingMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskSteaming> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSteaming::getDeleted, 0)
                .eq(TaskSteaming::getPitNo, pitNo)
                .eq(TaskSteaming::getVinasseName, addDTO.getOutMaterialType())
                .orderByDesc(TaskSteaming::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskSteaming taskSteaming = taskSteamingMapper.selectOne(wrapper);
        if (null != taskSteaming) {

            //判断任务是否是待执行
            if (taskSteaming.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskSteaming.setState(TaskStatus.IN_PROGRESS.getCode());
                taskSteaming.setStartTime(new Date());
                taskSteamingMapper.updateById(taskSteaming);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskSteaming.getCenterId(), taskSteaming.getLocationId(), taskSteaming.getPitOrder(), ExecutionStatus.STEAMED_OUT.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskSteamingService.endTaskSteaming(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskSteamingDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskSteamingDetail::getDeleted, 0)
                    .eq(TaskSteamingDetail::getTaskSteamingId,taskSteaming.getId());
            List<TaskSteamingDetail> list = taskSteamingDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskSteaming.getPitOrder(), ExecutionStatus.STEAMED_OUT.getStatus(),1);
            }
        }

        steamingDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        steamingDetail.setCreateTime(new Date());
        steamingDetail.setCreatorId(userId);
        steamingDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskSteamingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskSteamingDetail::getDeleted, 0)
                .eq(TaskSteamingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskSteamingDetail taskSteamingDetail = taskSteamingDetailMapper.selectOne(wrapperDetail);
        if (null == taskSteamingDetail) {
            if (null != taskSteaming) {
                steamingDetail.setTaskSteamingId(taskSteaming.getId());
            }

            taskSteamingDetailMapper.insert(steamingDetail);
        } else {
            if (null != taskSteaming) {
                steamingDetail.setTaskSteamingId(taskSteaming.getId());
            }
            steamingDetail.setId(taskSteamingDetail.getId());
            taskSteamingDetailMapper.updateById(steamingDetail);
        }

        //插入参数信息
        //量水桶量水温度
        if (!CollectionUtils.isEmpty(addDTO.getTemperatureMeasuringBucketTemp())) {
            addSteamingDetailParameter(steamingDetail.getId(), TaskSpreadingParameterEnum.TemperatureMeasuringBucket, addDTO.getTemperatureMeasuringBucketTemp());
        }

        //校验参数是否有工艺异常，有则告警
        //1.蒸粮时长
//        if(!StringUtils.isEmpty(addDTO.getSteamingTime())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_SteamingTime.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getSteamingTime())
//            );
//        }
//        //2.用汽时长
//        if(!StringUtils.isEmpty(addDTO.getSteamUsageDuration())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_SteamUsageDuration.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getSteamUsageDuration())
//            );
//        }
//        //3.量水单甑使用量
//        if(!StringUtils.isEmpty(addDTO.getFlowMeasurementUsage())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_FlowMeasurementUsage.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getFlowMeasurementUsage())
//            );
//        }
//        //4.第一次量水重量
//        if(!StringUtils.isEmpty(addDTO.getOneMeasureWaterWeight())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_OneMeasureWaterWeight.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getOneMeasureWaterWeight())
//            );
//        }
//        //5.第一次量水温度
//        if(!StringUtils.isEmpty(addDTO.getOneMeasureWaterTemperature())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_OneMeasureWaterTemperature.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getOneMeasureWaterTemperature())
//            );
//        }
//        //6.第二次量水温度
//        if(!StringUtils.isEmpty(addDTO.getTwoMeasureWaterTemperature())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_TwoMeasureWaterTemperature.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getTwoMeasureWaterTemperature())
//            );
//        }
//        //7.底锅水排放次数
//        if(!StringUtils.isEmpty(addDTO.getBottomPotWaterEmissionNumber())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.STEAMING,
//                    RouteParameterWarnParamEnum.CZ_BottomPotWaterEmissionNumber.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getBottomPotWaterEmissionNumber())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSteamingDetailAddDTO> addDTOList) {
        log.info("调用批量新增出甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增出甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增出甑任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskSteamingDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增出甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.STEAMING_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskSteaming_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskSteaming_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除出甑任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除出甑任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskSteamingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 插入详情参数
     *
     * @param taskSteamingDetailId 详情id
     * @param metricName            采集项名称
     * @param addDTOList            采集数据
     */
    private void addSteamingDetailParameter(Integer taskSteamingDetailId, String metricName, List<TaskSteamingDetailParameterAddDTO> addDTOList) {
        addDTOList.forEach(v -> {
            TaskSteamingDetailParameter steamingDetailParameter = new TaskSteamingDetailParameter();
            steamingDetailParameter.setCollectionTime(v.getCollectionTime());
            steamingDetailParameter.setMeasuredValue(v.getMeasuredValue());
            steamingDetailParameter.setTaskSteamingDetailId(taskSteamingDetailId);
            steamingDetailParameter.setMetricName(metricName);
            taskSteamingDetailParameterMapper.insert(steamingDetailParameter);
        });
    }
}
