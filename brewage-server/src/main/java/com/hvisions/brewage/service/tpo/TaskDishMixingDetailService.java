package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskDishMixingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskDishMixingMapper;
import com.hvisions.brewage.dto.tpo.TaskDishMixingDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskDishMixingDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskDishMixing;
import com.hvisions.brewage.entity.tpo.TaskDishMixingDetail;
import com.hvisions.brewage.enums.LogCaptureEnum;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.OtherEnum;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskDishMixingDetailVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingDetailService
 * @description: 拌糟任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDishMixingDetailService {

    @Resource
    private TaskDishMixingDetailMapper taskDishMixingDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskDishMixingMapper taskDishMixingMapper;
    @Resource
    private TaskDishMixingService taskDishMixingService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private TaskOrderBatchService taskOrderBatchService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询拌糟任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskDishMixingDetailVO> findPageList(TaskDishMixingDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskDishMixingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDishMixingDetail::getDeleted, 0)
                .eq(TaskDishMixingDetail::getTaskDishMixingId, queryDTO.getTaskDishMixingId());
        IPage<TaskDishMixingDetail> page = taskDishMixingDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskDishMixingDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskDishMixingDetailVO.class);
        
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskDishMixingDetailAddDTO addDTO) {
        log.info("调用新增拌糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增拌糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskDishMixingDetail dishMixingDetail = DtoMapper.convert(addDTO, TaskDishMixingDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskDishMixingMapper.selectPitNo(addDTO.getPitNo());
        if(StringUtil.isEmpty(pitNo)){
            throw new BaseKnownException(10000, "获取连窖号失败");
        }
        LambdaQueryWrapper<TaskDishMixing> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDishMixing::getDeleted, 0)
                .eq(TaskDishMixing::getPitNo, pitNo)
                .eq(TaskDishMixing::getVinasseName, addDTO.getOutMaterialType())
                .orderByDesc(TaskDishMixing::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskDishMixing taskDishMixing = taskDishMixingMapper.selectOne(wrapper);
        if (null != taskDishMixing) {

            //判断任务是否是待执行
            if (taskDishMixing.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskDishMixing.setState(TaskStatus.IN_PROGRESS.getCode());
                taskDishMixing.setStartTime(new Date());
                taskDishMixingMapper.updateById(taskDishMixing);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskDishMixing.getCenterId(), taskDishMixing.getLocationId(), taskDishMixing.getPitOrder(), ExecutionStatus.MIXING.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskDishMixingService.endTaskDishMixing(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskDishMixingDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskDishMixingDetail::getDeleted, 0)
                    .eq(TaskDishMixingDetail::getTaskDishMixingId,taskDishMixing.getId());
            List<TaskDishMixingDetail> list = taskDishMixingDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskDishMixing.getPitOrder(), ExecutionStatus.MIXING.getStatus(),1);
            }
            //传了单甑稻壳实际值
            if (!StringUtils.isEmpty(addDTO.getSingleContainerRiceHuskActualValue())) {

                //如果入窖糟源是丢糟，那么物料就是出窖投入，其他则是入窖投入
                String inputType ="丢糟".equals(addDTO.getEnterMaterialType())?"1":"0";
                taskOrderBatchService.addTurnoverUseMaterial(addDTO.getIotTaskNo(),
                        ExecutionStatus.MIXING.getStatus(),
                        taskDishMixing.getCenterId(),
                        taskDishMixing.getLocationId(),
                        new BigDecimal(addDTO.getSingleContainerRiceHuskActualValue()),
                        "",
                        OtherEnum.DK,
                        OtherEnum.DKCODE,
                        inputType
                );
            }
        }

        dishMixingDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        dishMixingDetail.setCreateTime(new Date());
        dishMixingDetail.setCreatorId(userId);
        dishMixingDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskDishMixingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskDishMixingDetail::getDeleted, 0)
                .eq(TaskDishMixingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskDishMixingDetail taskDishMixingDetail = taskDishMixingDetailMapper.selectOne(wrapperDetail);
        if (null == taskDishMixingDetail) {
            if (null != taskDishMixing) {
                dishMixingDetail.setTaskDishMixingId(taskDishMixing.getId());
            }

            taskDishMixingDetailMapper.insert(dishMixingDetail);
        } else {
            if(null != taskDishMixing){
                dishMixingDetail.setTaskDishMixingId(taskDishMixing.getId());
            }
            dishMixingDetail.setId(taskDishMixingDetail.getId());
            taskDishMixingDetailMapper.updateById(dishMixingDetail);
        }
        //校验参数是否有工艺异常，有则告警
//        //1.单甑稻壳用量-设定值
//        if(!StringUtils.isEmpty(addDTO.getSingleContainerRiceHuskSettingValue())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.DISHMIXING,
//                    RouteParameterWarnParamEnum.BZ_SingleContainerRiceHuskSettingValue.getParamCode(),
//                    pitOrder.getOrderCode(),
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getSingleContainerRiceHuskSettingValue())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskDishMixingDetailAddDTO> addDTOList) {
        log.info("调用批量新增拌糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增拌糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增拌糟任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskDishMixingDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增拌糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.DISHMIXING_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskDishMixing_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskDishMixing_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除拌糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除拌糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskDishMixingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
