package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.base.GroundTemperatureConfigMapper;
import com.hvisions.brewage.dao.base.TemperatureHumidityPointConfigMapper;
import com.hvisions.brewage.dao.tpo.TaskLoadingMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingDetailParameterMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.base.WSDSendCellIdDTO;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.base.GroundTemperatureConfig;
import com.hvisions.brewage.entity.base.TemperatureHumidityPointConfig;
import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.entity.tpo.TaskSpreading;
import com.hvisions.brewage.entity.tpo.TaskSpreadingDetail;
import com.hvisions.brewage.entity.tpo.TaskSpreadingDetailParameter;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.esb.enums.EsbConsumerEnum;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.service.base.EsbToQfwsdqRequestService;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.base.WSDResultVO;
import com.hvisions.brewage.vo.tpo.MachineBindVO;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskSpreadingDetailVO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.client.IssueOrderDetailClient;
import com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueOrderDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailService
 * @description: 摊晾任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSpreadingDetailService {
    @Resource
    private TaskSpreadingDetailMapper taskSpreadingDetailMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private BaseWrapper baseWrapper;

    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;

    @Resource
    private TaskSpreadingService taskSpreadingService;

    @Resource
    private LogService logService;

    @Resource
    private TaskLoadingMapper taskLoadingMapper;

    @Resource
    private TaskSpreadingDetailParameterMapper taskSpreadingDetailParameterMapper;

    @Autowired
    public WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private WorkshopFullPitMapper workshopFullPitMapper;

    @Resource
    private TaskLoadingService taskLoadingService;

    @Resource
    private TaskSealingService taskSealingService;

    @Resource
    private TaskLoadingDetailService taskLoadingDetailService;

    @Resource
    private RouteParameterWarnService routeParameterWarnService;

    @Resource
    private MaintainTaskService maintainTaskService;

    @Resource
    private GroundTemperatureConfigMapper groundTemperatureConfigMapper;

    @Resource
    private EsbToQfwsdqRequestService esbToQfwsdqRequestService;

    @Resource
    private TemperatureHumidityPointConfigMapper temperatureHumidityPointConfigMapper;

    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;

    @Resource
    private TaskOrderBatchService taskOrderBatchService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private IssueOrderDetailClient issueOrderDetailClient;

    /**
     * 查询摊晾任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskSpreadingDetailVO> findPageList(TaskSpreadingDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getDeleted, 0)
                .eq(TaskSpreadingDetail::getTaskSpreadingId, queryDTO.getTaskSpreadingId());
        IPage<TaskSpreadingDetail> page = taskSpreadingDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskSpreadingDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskSpreadingDetailVO.class);

        if (!CollectionUtils.isEmpty(detailVOPage.getRecords())) {
            detailVOPage.getRecords().forEach(v -> {
                LambdaQueryWrapper<TaskSpreadingDetailParameter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskSpreadingDetailParameter::getTaskSpreadingDetailId, v.getId())
                        .eq(TaskSpreadingDetailParameter::getDeleted, 0);
                List<TaskSpreadingDetailParameter> parameterList = taskSpreadingDetailParameterMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(parameterList)) {

                    //摊晾机输送链板左侧糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorLeftGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORLEFTGRAINTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorLeftGrainTempConvert = DtoMapper.convertList(conveyorLeftGrainTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorLeftGrainTemp(conveyorLeftGrainTempConvert);

                    //摊晾机输送链板右侧糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorRightGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORRIGHTGRAINTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorRightGrainTempConvert = DtoMapper.convertList(conveyorRightGrainTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorRightGrainTemp(conveyorRightGrainTempConvert);

                    //摊晾机输送链板前端糟醅温度
                    List<TaskSpreadingDetailParameter> conveyorFrontTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONSUME)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorFrontTempConvert = DtoMapper.convertList(conveyorFrontTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorFrontTemp(conveyorFrontTempConvert);

                    //摊晾温度1
                    List<TaskSpreadingDetailParameter> conveyorMidTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORMIDTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorMidTempConvert = DtoMapper.convertList(conveyorMidTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorMidTemp(conveyorMidTempConvert);

                    //摊晾温度2
                    List<TaskSpreadingDetailParameter> conveyorRearTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.CONVEYORREARTEMP)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> conveyorRearTempConvert = DtoMapper.convertList(conveyorRearTemp, TaskSpreadingDetailParameterAddDTO.class);
                    v.setConveyorRearTemp(conveyorRearTempConvert);

                    //每台风机运行时长
                    List<TaskSpreadingDetailParameter> fanRuntimePerUnit = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FANRUNTIMEPERUNIT)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> fanRuntimePerUnitConvert = DtoMapper.convertList(fanRuntimePerUnit, TaskSpreadingDetailParameterAddDTO.class);
                    v.setFanRuntimePerUnit(fanRuntimePerUnitConvert);

                    //风机运行转速
                    List<TaskSpreadingDetailParameter> fanSpeed = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FANSPEED)).collect(Collectors.toList());
                    List<TaskSpreadingDetailParameterAddDTO> fanSpeedConvert = DtoMapper.convertList(fanSpeed, TaskSpreadingDetailParameterAddDTO.class);
                    v.setFanSpeed(fanSpeedConvert);
                }
            });
        }

        return detailVOPage;
    }

    /**
     * 新增摊晾任务
     * 1.根据iot传过来的单窖号查询mes系统的连窖号（mes的所有任务都是连窖号），
     * 查询到连窖订单号后，查询是否已经创建了摊晾任务，如果创建了则将iot传过来的这条数据挂在任务下面，如果没查询到则不挂载到任务上，只记录数据到明细表
     * 2.如果找到了对应的任务，需要判断是否是当前连窖号是不是第一次接收iot传过来的数据，如果是需要创建入窖任务、封窖任务(如果创建看第6点)
     * 3.如果当前任务的窖池号与上个任务的窖池号不一致，则需要关闭上一个窖池号的任务
     * 4.判断iot是否传递了单甑加曲量,传了则需要进行对应的批次扣减，并将扣减的结果记录在批次用量表里面
     * 5.参数告警，【摊晾斗内糟醅温度、单甑加曲量】参数有工艺管控要求，若超出工艺管控范围值，则需要进行工艺告警
     * 6.创建入窖任务、封窖任务：
     * 6.1：入窖任务、封窖任务所用的窖池订单，有两种取值方式，
     * 第一种是：入窖窖号+糟源类别为空+订单状态等于-1（新建） 查出来有就用，没有执行第二种
     * 第二种是：入窖窖号+糟源类别+订单状态等于0（入窖），查出来有就用，没有就新增窖池订单
     * 6.2：若查询处理没有窖池订单需要创建窖池订单，在取值层次固定为第2层----陈华确定的
     * 6.2：创建入窖和封窖任务后，需要关闭对应窖池订单的养护任务
     *
     * @param addDTO
     * @return
     */
    public String add(TaskSpreadingDetailAddDTO addDTO) {
        log.info("调用新增摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskSpreadingDetail spreadingDetail = DtoMapper.convert(addDTO, TaskSpreadingDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskSpreadingMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskSpreading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreading::getDeleted, 0)
                .eq(TaskSpreading::getPitNo, pitNo)
                .eq(TaskSpreading::getMaterialType, addDTO.getOutMaterialType())
                .orderByDesc(TaskSpreading::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskSpreading taskSpreading = taskSpreadingMapper.selectOne(wrapper);

        Integer num = 0;

        if (null != taskSpreading) {
            //判断任务是否是待执行
            if (taskSpreading.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                taskSpreading.setStatus(TaskStatus.IN_PROGRESS.getName());
                taskSpreading.setStartTime(new Date());
                taskSpreadingMapper.updateById(taskSpreading);
            }

            //判断是不是第一甑
            LambdaQueryWrapper<TaskSpreadingDetail> wrapperDetail = new LambdaQueryWrapper<>();
            wrapperDetail.eq(TaskSpreadingDetail::getDeleted, 0)
                    .eq(TaskSpreadingDetail::getTaskSpreadingId, taskSpreading.getId());
            taskSpreadingDetailMapper.selectCount(wrapperDetail);

            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskSpreading.getCenterId(), taskSpreading.getLocationId(), taskSpreading.getPitOrder(), ExecutionStatus.COOLING.getStatus());
            if (taskCurrentCenterVO.getIsChange()) {
                //关闭上一个摊晾任务
                taskSpreadingService.endTaskSpreading(taskCurrentCenterVO.getUpperPitOrder());
            }


            //传了单甑加曲量
            if (!StringUtils.isEmpty(addDTO.getYeastAmount())) {
                //查询对应的曲粉物料编码，此逻辑是陈华提出
                String materialCode = findSpreaderNoMaterialCode(taskSpreading.getCenterId(), taskSpreading.getLocationId(), addDTO.getSpreaderNo());

                if (StringUtils.isEmpty(materialCode)) {
                    log.info("单甑加曲量-未获取的曲粉物料编码，无法扣对应的批次信息");
                } else {
                    log.info("单甑加曲量-获取到曲粉物料编码：{},进行批次扣除逻辑",materialCode);
                    //查询窖池订单对应的区域
                    String lintName = taskSpreadingDetailMapper.selectOrderLintName(taskSpreading.getPitOrder());
                    taskOrderBatchService.addTurnoverUseMaterial(addDTO.getIotTaskNo(),
                            ExecutionStatus.COOLING.getStatus(),
                            taskSpreading.getCenterId(),
                            taskSpreading.getLocationId(),
                            new BigDecimal(addDTO.getYeastAmount()),
                            lintName,
                            OtherEnum.DQ,
                            materialCode,
                            "0"
                    );
                }
            }

        }

        spreadingDetail.setRecordTime(new Date());
        spreadingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        spreadingDetail.setCreateTime(new Date());
        spreadingDetail.setCreatorId(userId);
        spreadingDetail.setCellarStatus(TaskStatus.CELLAR_IN_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskSpreadingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskSpreadingDetail::getDeleted, 0)
                .eq(TaskSpreadingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskSpreadingDetail taskSpreadingDetail = taskSpreadingDetailMapper.selectOne(wrapperDetail);
        if (null == taskSpreadingDetail) {
            if (null != taskSpreading) {
                spreadingDetail.setTaskSpreadingId(taskSpreading.getId());
                //查询地温
                String floorTemp = findFloorTemp(taskSpreading.getCenterId());
                spreadingDetail.setFloorTemp(floorTemp);

                //查询湿度
                String humidity = findHumidity(taskSpreading.getCenterId(), taskSpreading.getLocationId(), spreadingDetail.getTargetPitNo());
                spreadingDetail.setHumidity(humidity);
            }

            taskSpreadingDetailMapper.insert(spreadingDetail);
        } else {
            num += 1;
            if (null != taskSpreading && null == spreadingDetail.getTaskSpreadingId()) {
                spreadingDetail.setTaskSpreadingId(taskSpreading.getId());
            }
            spreadingDetail.setId(taskSpreadingDetail.getId());

            //有些数据不能在进行更改
            spreadingDetail.setPitNo(taskSpreadingDetail.getPitNo());
            spreadingDetail.setOutMaterialType(taskSpreadingDetail.getOutMaterialType());
            spreadingDetail.setEnterMaterialType(taskSpreadingDetail.getEnterMaterialType());
            spreadingDetail.setMaterialLayer(taskSpreadingDetail.getMaterialLayer());
            spreadingDetail.setGrainInputType(taskSpreadingDetail.getGrainInputType());
            spreadingDetail.setSteamingTaskNo(taskSpreadingDetail.getSteamingTaskNo());
            spreadingDetail.setSourcePitNo(taskSpreadingDetail.getSourcePitNo());
            spreadingDetail.setTargetPitNo(taskSpreadingDetail.getTargetPitNo());

            taskSpreadingDetailMapper.updateById(spreadingDetail);
        }


        //插入参数信息
        //摊晾机输送链板左侧糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorLeftGrainTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORLEFTGRAINTEMP, addDTO.getConveyorLeftGrainTemp());
        }

        //摊晾机输送链板右侧糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorRightGrainTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORRIGHTGRAINTEMP, addDTO.getConveyorRightGrainTemp());
        }

        //摊晾机输送链板前端糟醅温度
        if (!CollectionUtils.isEmpty(addDTO.getConveyorFrontTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONSUME, addDTO.getConveyorFrontTemp());
        }

        //摊晾温度1
        if (!CollectionUtils.isEmpty(addDTO.getConveyorMidTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORMIDTEMP, addDTO.getConveyorMidTemp());
        }

        //摊晾温度2
        if (!CollectionUtils.isEmpty(addDTO.getConveyorRearTemp())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.CONVEYORREARTEMP, addDTO.getConveyorRearTemp());
        }

        //每台风机运行时长
        if (!CollectionUtils.isEmpty(addDTO.getFanRuntimePerUnit())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.FANRUNTIMEPERUNIT, addDTO.getFanRuntimePerUnit());
        }

        //风机运行转速
        if (!CollectionUtils.isEmpty(addDTO.getFanSpeed())) {
            addSpreadingDetailParameter(spreadingDetail.getId(), TaskSpreadingParameterEnum.FANSPEED, addDTO.getFanSpeed());
        }


        //---------------------- 执行创建入窖任务的逻辑------------------------------
        //入窖窖号：根据iot传的窖号查询连窖号
        String targetPitNo = taskSpreadingMapper.selectPitNo(addDTO.getTargetPitNo());

        //查询是否创建了入窖任务和封窖任务，创建了则不管，没创建则创建,有两种方式：
        //第一次：入窖窖号+糟源类别为空+订单状态等于-1（新建） 查出来有就用，没有执行第二次
        // 第二次：入窖窖号+糟源类别+订单状态等于0（入窖），查出来有就用，没有就新增

        //执行第一次查询
        String orderCode = taskSpreadingDetailMapper.selectWorkshopPitOrderOne(targetPitNo);

        if (StringUtils.isEmpty(orderCode)) {
            //第一次查询出来没有，执行第二次查询
            orderCode = taskSpreadingDetailMapper.selectWorkshopPitOrderTwo(targetPitNo, addDTO.getEnterMaterialType());
        }


        //入窖任务id
        Integer taskLoadingId = 0;

        //代表这是新增的数据，而不是修改的数据
        if (num == 0) {
            if (!StringUtils.isEmpty(orderCode)) {
                log.info("根据窖池号、糟源类型、订单状态：新建或入窖，查询出来已存在窖池订单，无需在创建，窖池号：{}，糟源类型：{},查询出来的窖池订单：{}", taskSpreading.getPitNo(), addDTO.getEnterMaterialType(), orderCode);

                //查询入窖任务是否有数据
                LambdaQueryWrapper<TaskLoading> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskLoading::getPitOrder, orderCode)
                        .eq(TaskLoading::getDeleted, 0);
                TaskLoading taskLoading = taskLoadingMapper.selectOne(queryWrapper);
                if (null == taskLoading) {
                    log.info("根据连窖窖号查询出来没有入窖任务，连窖窖号：{}", orderCode);


                    //创建入窖任务
                    taskLoadingId = addTaskLoading(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

                    //关闭养护任务
                    maintainTaskService.endMaintainTask(orderCode);

                    //创建封窖任务
                    addTaskSealing(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);
                } else {
                    log.info("根据连窖窖号查询出来有入窖任务，入窖任务号：{}", taskLoading.getTaskNo());
                    taskLoadingId = taskLoading.getId();
                }

            } else {
                log.info("根据窖池号、糟源类型、订单状态：新建或入窖，未查询出来窖池订单，需要在创建，窖池号：{}，糟源类型：{}", taskSpreading.getPitNo(), addDTO.getEnterMaterialType());

                //创建窖池订单
                orderCode = addWorkshopPitOrder(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, userId, addDTO.getEnterMaterialType());

                //创建入窖任务
                taskLoadingId = addTaskLoading(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

                //关闭养护任务
                maintainTaskService.endMaintainTask(orderCode);

                //创建封窖任务
                addTaskSealing(taskSpreading.getCenterId(), taskSpreading.getLocationId(), targetPitNo, addDTO.getEnterMaterialType(), orderCode);

            }
        }


        //添加明细数据，进行数据关联
        addTaskLoadingDetail(addDTO, taskLoadingId);


//        //校验参数是否有工艺异常，有则告警---------刚子说所有的工艺告警都由iot单独推送
//        //1.摊晾斗内糟醅温度
//        if (!StringUtils.isEmpty(addDTO.getHopperTemp())) {
//            RouteParameterWarnParamEnum warnParamEnum = RouteParameterWarnParamEnum.TL_TEMPERATURE;
//            routeParameterWarnService.judgeRouteParameterWarn(warnParamEnum.getBusinessModule(),
//                    warnParamEnum.getParamCode(),
//                    taskSpreading.getPitOrder(),
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getHopperTemp())
//            );
//        }
//
//        //2.单甑加曲量
//        if (!StringUtils.isEmpty(addDTO.getYeastAmount())) {
//            RouteParameterWarnParamEnum warnParamEnum = RouteParameterWarnParamEnum.TL_DZJQL;
//            routeParameterWarnService.judgeRouteParameterWarn(warnParamEnum.getBusinessModule(),
//                    warnParamEnum.getParamCode(),
//                    taskSpreading.getPitOrder(),
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getYeastAmount())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 查询iot摊晾机对应的曲粉物料
     *
     * @param centerId   中心id
     * @param locationId 车间id
     * @param spreaderNo iot摊晾机编号
     * @return
     */
    private String findSpreaderNoMaterialCode(Integer centerId, Integer locationId, String spreaderNo) {
        //根据摊晾机编号，查询曲粉物料编码
        MachineBindVO machineBindVO = taskSpreadingDetailMapper.selectQdMaterialCode(centerId, locationId, spreaderNo);
        if (null == machineBindVO) {
            log.info("未查询到绑定记录，直接返回空");
            return null;
        }

        if (StringUtils.isEmpty(machineBindVO.getMaterialCode())) {
            log.info("根据摊晾机编号未曲粉物料编码,参数：中心：{}，车间：{}，iot摊晾机编号：{}", centerId, locationId, spreaderNo);
            //查询曲斗id
            Integer quDouId = taskSpreadingDetailMapper.selectQuDouId(centerId, locationId, spreaderNo);
            if (null == quDouId) {
                log.info("接口iot传下的摊晾机编号，未查询到曲斗id，参数：中心：{}，车间：{}，iot摊晾机编号：{}", centerId, locationId, spreaderNo);
                return null;
            } else {
                log.info("根据曲斗id获取最新的曲斗曲粉需求，参数：曲斗id:{}", quDouId);
                //没有则需要去查询最新的一条数据
                ResultVO<IssueOrderDetailDTO> resultVO = issueOrderDetailClient.getLastIssueOrderDetailByParameterId(quDouId);
                if (resultVO.getCode() != 200 || null == resultVO.getData()) {
                    log.info("调用接口，获取最新的曲斗曲粉需求，返回失败或者未返回数据，曲斗id:{},返回数据：{}，异常原因：{}", quDouId, resultVO.getData(), resultVO.getMessage());
                    return null;
                } else {
                    IssueOrderDetailDTO data = resultVO.getData();
                    if (null == data.getMaterialId()) {
                        log.info("调用接口，获取最新的曲斗曲粉需求，未返回物料id，曲斗id:{}", quDouId);
                        return null;
                    } else {
                        //修改曲斗更换的物料批次
                        taskSpreadingDetailMapper.updateMachineBindLog(machineBindVO.getMachineBindLogId(), data.getBatch(), data.getMaterialId());

                        //查询对应的物料编码
                        return taskSpreadingDetailMapper.selectMaterialIdByCode(data.getMaterialId());
                    }
                }
            }
        } else {
            return machineBindVO.getMaterialCode();
        }
    }


    /**
     * 湿度
     *
     * @param centerId    中心id
     * @param locationId  车间id
     * @param targetPitNo 入窖窖号
     * @return
     */
    private String findHumidity(Integer centerId, Integer locationId, String targetPitNo) {
        //查询入窖窖号排号
        String theRow = taskSpreadingMapper.selectPitNoReTheRow(targetPitNo);

        LambdaQueryWrapper<TemperatureHumidityPointConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TemperatureHumidityPointConfig::getDeleted, 0)
                .eq(TemperatureHumidityPointConfig::getCenterId, centerId)
                .eq(TemperatureHumidityPointConfig::getLocationId, locationId)
                //小于等于
                .le(TemperatureHumidityPointConfig::getCellarRowEnd, theRow)
                //大于等于
                .ge(TemperatureHumidityPointConfig::getCellarRowStart, theRow)
                .last("LIMIT 1");
        TemperatureHumidityPointConfig pointConfig = temperatureHumidityPointConfigMapper.selectOne(wrapper);
        if (null == pointConfig) {
            return null;
        }

        return String.valueOf(pointConfig.getHumidityGpm3());
    }


    /**
     * 地温
     *
     * @param centerId 中心id
     * @return
     */
    private String findFloorTemp(Integer centerId) {
        LambdaQueryWrapper<GroundTemperatureConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GroundTemperatureConfig::getDeleted, 0)
                .eq(GroundTemperatureConfig::getCenterId, centerId);
        GroundTemperatureConfig temperatureConfig = groundTemperatureConfigMapper.selectOne(wrapper);
        if (null != temperatureConfig) {
            try {
                WSDSendCellIdDTO cellIdDTO = new WSDSendCellIdDTO();
                cellIdDTO.setCellId(temperatureConfig.getMonitoringPoint());

                //调用温湿度接口查询地温数据
                WSDResultVO resultVO = esbToQfwsdqRequestService.sendEsb(EsbConsumerEnum.LZLJ2026, cellIdDTO);
                if (!StateCodeEnum.WSD_OK.equals(resultVO.getCode())) {
                    log.error("调用曲房温湿度接口失败，接口编号：{},点位：{}，异常原因--------->{}", EsbConsumerEnum.LZLJ2026, temperatureConfig.getMonitoringPoint(), resultVO.getMessage());
                } else {
                    if (null == resultVO.getData() || null == resultVO.getData().getValueTop()) {
                        return null;
                    }
                    BigDecimal valueTop = resultVO.getData().getValueTop();
                    return String.valueOf(valueTop);
                }
            } catch (Exception e) {
                log.error("调用曲房温湿度接口失败，接口编号：{},点位：{}，异常原因--------->{}", EsbConsumerEnum.LZLJ2026, temperatureConfig.getMonitoringPoint(), e.getMessage());
            }
        }
        return null;
    }

    /**
     * 新增窖池订单
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param userId       创建人
     * @param materialType 糟源类别
     * @return 窖池订单号
     */
    public String addWorkshopPitOrder(Integer centerId, Integer locationId, String pitNo, Integer userId, String materialType) {
        log.info("开始调用新增窖池订单，传入参数-----》中心id：{}，车间ID:{},连窖的窖池号:{},创建人：{}", centerId, locationId, pitNo, userId);
        WorkshopPitOrderDTO workshopPitOrderDTO = new WorkshopPitOrderDTO();

        //根据连窖号查询窖池id
        LambdaQueryWrapper<TPoWorkshopFullPit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TPoWorkshopFullPit::getFullPitId, pitNo)
                .eq(TPoWorkshopFullPit::getIsDeleted, 0);
        TPoWorkshopFullPit workshopFullPit = workshopFullPitMapper.selectOne(wrapper);

        //查询层次:入窖窖号+订单状态（-1和0和1），查出来有就+1，没有就是2
        Integer layer = taskSpreadingDetailMapper.selectWorkshopPitOrderLayer(pitNo);
        log.info("根据入窖窖号：{}，查询出来的层次为：{}", pitNo, layer);
        if (null == layer) {
            layer = 2;
        } else {
            layer = layer + 1;
        }

        //糟源类别id
        Integer vinasseId = taskSpreadingDetailMapper.selectVinasseId(materialType);

        workshopPitOrderDTO.setCenterId(centerId);
        workshopPitOrderDTO.setCreateId(userId);
        //层数固定二层，此逻辑陈华提出
        workshopPitOrderDTO.setLayer(2);
        workshopPitOrderDTO.setLocationId(locationId);
        workshopPitOrderDTO.setOrderCategoryId(1);
        workshopPitOrderDTO.setOrderStatus(5);
        workshopPitOrderDTO.setPitId(workshopFullPit.getId());
        workshopPitOrderDTO.setPitStatus(0);
        workshopPitOrderDTO.setUpperOutVinasseId(vinasseId);
        workshopPitOrderDTO.setVinasseId(vinasseId);
        return workshopPitOrderService.addWorkshopPitOrderRJ(workshopPitOrderDTO);
    }


    /**
     * 创建入窖任务
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param materialType 糟源类别
     * @param pitOrder     窖池订单号
     * @return
     */
    public Integer addTaskLoading(Integer centerId, Integer locationId, String pitNo, String materialType, String pitOrder) {
        TaskLoadingAddDTO addDTO = new TaskLoadingAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setMaterialType(materialType);
        addDTO.setPitOrder(pitOrder);
        return taskLoadingService.add(addDTO);
    }

    /**
     * 创建封窖任务
     *
     * @param centerId     中心id
     * @param locationId   车间ID
     * @param pitNo        连窖的窖池号
     * @param materialType 糟源类别
     * @param pitOrder     窖池订单号
     * @return
     */
    public String addTaskSealing(Integer centerId, Integer locationId, String pitNo, String materialType, String pitOrder) {
        TaskSealingAddDTO addDTO = new TaskSealingAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setMaterialType(materialType);
        addDTO.setPitOrder(pitOrder);
        return taskSealingService.add(addDTO);
    }

    /**
     * 创建入窖详情任务
     *
     * @return
     */
    public String addTaskLoadingDetail(TaskSpreadingDetailAddDTO addDTO, Integer taskLoadingId) {
        TaskLoadingDetailAddDTO detailAddDTO = DtoMapper.convert(addDTO, TaskLoadingDetailAddDTO.class);
        return taskLoadingDetailService.add(detailAddDTO, taskLoadingId);
    }

    /**
     * 插入详情参数
     *
     * @param taskSpreadingDetailId 详情id
     * @param metricName            采集项名称
     * @param addDTOList            采集数据
     */
    private void addSpreadingDetailParameter(Integer taskSpreadingDetailId, String metricName, List<TaskSpreadingDetailParameterAddDTO> addDTOList) {
        addDTOList.forEach(v -> {
            //删除以前的数据
            LambdaQueryWrapper<TaskSpreadingDetailParameter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TaskSpreadingDetailParameter::getTaskSpreadingDetailId, taskSpreadingDetailId);
            taskSpreadingDetailParameterMapper.delete(lambdaQueryWrapper);

            //新增数据
            TaskSpreadingDetailParameter spreadingDetailParameter = new TaskSpreadingDetailParameter();
            spreadingDetailParameter.setCollectionTime(v.getCollectionTime());
            spreadingDetailParameter.setFanNumber(v.getFanNumber());
            spreadingDetailParameter.setMeasuredValue(v.getMeasuredValue());
            spreadingDetailParameter.setTaskSpreadingDetailId(taskSpreadingDetailId);
            spreadingDetailParameter.setMetricName(metricName);
            taskSpreadingDetailParameterMapper.insert(spreadingDetailParameter);
        });
    }

    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSpreadingDetailAddDTO> addDTOList) {
        log.info("调用批量新增摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //循环新增
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增摊晾任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskSpreadingDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.SPREADING_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.TASKSPREADING_IOTTOMES_EXCHANGE,
                ProdMqConsts.TASKSPREADING_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除摊晾任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除摊晾任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskSpreadingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 入窖任务更改窖池-改入
     *
     * @param iotTaskNo         工序任务号(IOT任务号)
     * @param enterMaterialType 入窖糟源类别
     * @param targetPitNo       入窖窖号
     * @return
     */
    public String updateTaskSpreadingDetailIn(String iotTaskNo, String enterMaterialType, String targetPitNo) {
        log.info("摊晾任务更改窖池-改入，刷新摊晾任务明细的数据，传入参数------》工序任务号(IOT任务号)：{}，入窖糟源类别：{}，入窖窖号：{}", iotTaskNo, enterMaterialType, targetPitNo);
        Integer userId = userAuditorAware.getUserId();
        log.info("摊晾任务更改窖池-改入，刷新摊晾任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getIotTaskNo, iotTaskNo)
                .eq(TaskSpreadingDetail::getDeleted, 0);
        TaskSpreadingDetail spreadingDetail = taskSpreadingDetailMapper.selectOne(wrapper);
        spreadingDetail.setEnterMaterialType(enterMaterialType);
        spreadingDetail.setTargetPitNo(targetPitNo);
        spreadingDetail.setUpdaterId(userId);
        spreadingDetail.setUpdateTime(new Date());
        taskSpreadingDetailMapper.updateById(spreadingDetail);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 入窖任务更改窖池-改出
     *
     * @param iotTaskNo       iot任务号
     * @param outMaterialType 出窖糟源类别
     * @param sourcePitNo     出窖窖号
     * @return
     */
    public String updateTaskSpreadingDetailOut(String iotTaskNo, String outMaterialType, String sourcePitNo, String outPitOrder) {
        log.info("摊晾任务更改窖池-改出，刷新摊晾任务明细的数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("摊晾任务更改窖池-改出，刷新摊晾任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskSpreadingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreadingDetail::getIotTaskNo, iotTaskNo)
                .eq(TaskSpreadingDetail::getDeleted, 0);
        TaskSpreadingDetail spreadingDetail = taskSpreadingDetailMapper.selectOne(wrapper);
        if (null != spreadingDetail) {
            //挂载到对应的任务上
            LambdaQueryWrapper<TaskSpreading> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TaskSpreading::getPitOrder, outPitOrder)
                    .eq(TaskSpreading::getDeleted, 0);
            TaskSpreading taskSpreading = taskSpreadingMapper.selectOne(wrapper1);
            if (null != taskSpreading) {
                spreadingDetail.setTaskSpreadingId(taskSpreading.getId());
            } else {
                log.info("摊晾任务更改窖池-改出，刷新摊晾任务明细的数据-没有查询更改后的窖池订单任务数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
            }

            spreadingDetail.setOutMaterialType(outMaterialType);
            spreadingDetail.setSourcePitNo(sourcePitNo);
            spreadingDetail.setUpdaterId(userId);
            spreadingDetail.setUpdateTime(new Date());
            taskSpreadingDetailMapper.updateById(spreadingDetail);
        } else {
            log.info("摊晾任务更改窖池-改出，刷新摊晾任务明细的数据-没有查询到对应的明细数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
        }

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 修改斗内糟醅温度
     *
     * @param tempDTO 摊晾斗内糟醅温度
     * @return
     */
    public String updateHopperTemp(TaskSpreadingHopperTempDTO tempDTO) {
        log.info("摊晾任务修改斗内糟醅温度，传入参数------》摊晾任务详情主键id：{}，摊晾斗内糟醅温度：{}", tempDTO.getIds().toString(), tempDTO.getHopperTemp());
        Integer userId = userAuditorAware.getUserId();
        log.info("摊晾任务更改窖池-改出，刷新摊晾任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        String userName = userAuditorAware.getUserNameById(userId);

        LambdaUpdateWrapper<TaskSpreadingDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(TaskSpreadingDetail::getId, tempDTO.getIds())
                .set(TaskSpreadingDetail::getHopperTemp, tempDTO.getHopperTemp())
                .set(TaskSpreadingDetail::getHopperTempTime, new Date())
                .set(TaskSpreadingDetail::getHopperTempUser, userName)
                .set(TaskSpreadingDetail::getHopperTempUserId, userId)
                .set(TaskSpreadingDetail::getUpdaterId, userId)
                .set(TaskSpreadingDetail::getUpdateTime, new Date());
        taskSpreadingDetailMapper.update(null, wrapper);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }
}
