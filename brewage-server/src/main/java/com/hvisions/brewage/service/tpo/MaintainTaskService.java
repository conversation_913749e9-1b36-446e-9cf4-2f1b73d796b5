package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.MaintainTaskMapper;
import com.hvisions.brewage.dto.tpo.MaintainTaskAddDTO;
import com.hvisions.brewage.dto.tpo.MaintainTaskPdaQueryDTO;
import com.hvisions.brewage.dto.tpo.MaintainTaskQueryDTO;
import com.hvisions.brewage.dto.tpo.MaintainTaskUpdateDTO;
import com.hvisions.brewage.entity.tpo.TaskMaintain;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.operlog.utils.StringUtils;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.MaintainTaskDetailFindVO;
import com.hvisions.brewage.vo.tpo.MaintainTaskPdaVO;
import com.hvisions.brewage.vo.tpo.MaintainTaskVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MaintainTaskService
 * @description: 窖池养护任务Service
 * @date 2025/6/30 11:08
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MaintainTaskService {
    @Resource
    private MaintainTaskMapper maintainTaskMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private BaseWrapper baseWrapper;

    @Resource
    private WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;

    /**
     * 查询窖池养护任务列表
     *
     * @param queryDTO
     * @return
     */
    public Page<MaintainTaskVO> findPageList(MaintainTaskQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskMaintain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskMaintain::getDeleted, 0)
                .eq(null != queryDTO.getCenterId(), TaskMaintain::getCenterId, queryDTO.getCenterId())
                .eq(null != queryDTO.getLocationId(), TaskMaintain::getLocationId, queryDTO.getLocationId())
                .like(!StringUtils.isEmpty(queryDTO.getPitNo()), TaskMaintain::getPitNo, queryDTO.getPitNo())
                .eq(!StringUtils.isEmpty(queryDTO.getStatus()), TaskMaintain::getStatus, queryDTO.getStatus())
                .eq(!StringUtils.isEmpty(queryDTO.getPitOrder()), TaskMaintain::getPitNo, queryDTO.getPitOrder())
                .eq(!StringUtils.isEmpty(queryDTO.getTaskNo()), TaskMaintain::getTaskNo, queryDTO.getTaskNo())
                .between(null != queryDTO.getCreateStartTime(), TaskMaintain::getCreateTime, queryDTO.getCreateStartTime(), queryDTO.getCreateEndTime())
                .orderByDesc(TaskMaintain::getEndTime);
        IPage<TaskMaintain> page = maintainTaskMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<MaintainTaskVO> convertToPage = baseWrapper.convertToPage(page, MaintainTaskVO.class);

        //有数据需要算次数和使用量
        if (convertToPage.getRecords().size() > 0) {
            List<Integer> maintainTaskIds = convertToPage.getRecords().stream().map(MaintainTaskVO::getId).collect(Collectors.toList());
            List<MaintainTaskDetailFindVO> findVOList = maintainTaskMapper.selectTaskDetail(RouteParameterWarnParamEnum.QUANTITY_INVESTED.getParamCode(), maintainTaskIds);
            if (!CollectionUtils.isEmpty(findVOList)) {
                convertToPage.getRecords().forEach(v -> {
                    List<MaintainTaskDetailFindVO> collect = findVOList.stream().filter(item -> item.getTaskMaintainId().equals(v.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        MaintainTaskDetailFindVO maintainTaskDetailFindVO = collect.get(0);
                        v.setStarterCultureUsage(maintainTaskDetailFindVO.getStarterCultureUsage());
                        v.setMaintenanceCount(maintainTaskDetailFindVO.getMaintenanceCount());
                    }
                });
            }

        }
        return convertToPage;

    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    public String add(MaintainTaskAddDTO addDTO) {
        log.info("调用新增窖池养护任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增窖池养护任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询养护任务是否已经创建过,创建过则不能在进行创建
        judgeDoesItExist(addDTO.getPitOrder());

        TaskMaintain task = DtoMapper.convert(addDTO, TaskMaintain.class);
        task.setCreateTime(new Date());
        task.setCreatorId(userId);

        //查询配置最大的流水号
        Integer maxNum = maintainTaskMapper.selectMaxTaskNo(ProdConfigEnum.YH);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.YH, maxNum);

        task.setTaskNo(taskNo);
        task.setMaintainExpectStartTime(addDTO.getEmptyTime());
        task.setStatus(TaskStatus.PENDING_EXE.getName());
        task.setSubtaskStatus(TaskStatus.NOT_STARTED.getName());
        maintainTaskMapper.insert(task);

        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(addDTO.getPitOrder(),ExecutionStatus.MAINTENANCE.getStatus(),1);

        //创建PDA个人任务执行进度
        taskProcessRecordsService.addTask(taskNo,task.getPitNo(),ExecutionStatus.MAINTENANCE.getStatus(),task.getCenterId(),task.getLocationId());

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<MaintainTaskAddDTO> addDTOList) {
        log.info("调用批量新增窖池养护任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增窖池养护任务列表，获取到当前登录用户，用户id：{}", userId);

        List<TaskMaintain> operations = DtoMapper.convertList(addDTOList, TaskMaintain.class);

        //查询配置最大的流水号
        Integer maxNum = maintainTaskMapper.selectMaxTaskNo(ProdConfigEnum.YH);
        for (int i = 0; i < operations.size(); i++) {
            maxNum = maxNum == null ? 0 : maxNum;
            String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.YH, maxNum + i);

            TaskMaintain task = operations.get(i);

            //查询养护任务是否已经创建过,创建过则不能在进行创建
            judgeDoesItExist(task.getPitOrder());

            task.setCreateTime(new Date());
            task.setCreatorId(userId);
            task.setTaskNo(taskNo);
            task.setMaintainExpectStartTime(task.getEmptyTime());
            task.setStatus(TaskStatus.PENDING_EXE.getName());
            task.setSubtaskStatus(TaskStatus.NOT_STARTED.getName());
            maintainTaskMapper.insert(task);
        }
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 查询养护任务是否已经创建过,创建过则不能在进行创建
     *
     * @param pitOrder 窖池订单
     */
    private void judgeDoesItExist(String pitOrder) {
        LambdaQueryWrapper<TaskMaintain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskMaintain::getPitOrder, pitOrder)
                .eq(TaskMaintain::getDeleted, 0);
        TaskMaintain taskMaintain = maintainTaskMapper.selectOne(wrapper);
        if (null != taskMaintain) {
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(pitOrder));
        }
    }


    /**
     * 修改窖池养护任务列表
     *
     * @param updateDTO
     * @return
     */
    public String update(MaintainTaskUpdateDTO updateDTO) {
        log.info("调用修改窖池养护任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改窖池养护任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskMaintain taskMaintain = maintainTaskMapper.selectById(updateDTO.getId());
        if (null == taskMaintain || taskMaintain.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }

        if (!taskMaintain.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("只能修改状态为创建的养护任务"));
        }

        TaskMaintain convert = DtoMapper.convert(updateDTO, TaskMaintain.class);
        convert.setUpdaterId(userId);
        convert.setUpdateTime(new Date());

        maintainTaskMapper.updateById(convert);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除窖池养护任务列表
     *
     * @param ids 窖池养护任务主键id
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("删除窖池养护任务列表，传入参数：{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("删除窖池养护任务列表，获取到当前登录用户，用户id：{}", userId);

        List<TaskMaintain> maintainList = maintainTaskMapper.selectBatchIds(ids);
        maintainList.forEach(v->{
            //删除PDA个人任务执行进度
            taskProcessRecordsService.deleteTask(v.getTaskNo(), ExecutionStatus.MAINTENANCE.getStatus());
        });

        maintainTaskMapper.deleteBatchIds(ids);
        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 结束养护任务
     *
     * @param pitNo
     * @return
     */
    public String endMaintainTask(String pitNo) {
        log.info("调用结束窖池养护任务，传入参数-------》窖池号：{}", pitNo);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束务窖池养护任务，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskMaintain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskMaintain::getPitNo, pitNo)
                .eq(TaskMaintain::getDeleted, 0)
                .ne(TaskMaintain::getStatus, TaskStatus.COMPLETED.getName());
        List<TaskMaintain> maintainList = maintainTaskMapper.selectList(wrapper);

        maintainList.forEach(v->{
            v.setMaintainExpectStartTime(null);
            v.setStatus(TaskStatus.COMPLETED.getName());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            maintainTaskMapper.updateById(v);

            workshopPitOrderService.putExecutionStatus(v.getPitOrder(), ExecutionStatus.MAINTENANCE.getStatus(),9);

            //结束PDA个人任务执行进度
            taskProcessRecordsService.closeTask(v.getTaskNo(), ExecutionStatus.MAINTENANCE.getStatus());
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束养护任务成功");
    }

    /**
     * 查询窖池养护任务列表-PDA
     *
     * @param queryDTO
     * @return
     */
    public Page<MaintainTaskPdaVO> findPagePDAList(MaintainTaskPdaQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskMaintain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskMaintain::getDeleted, 0)
                .eq(null != queryDTO.getCenterId(), TaskMaintain::getCenterId, queryDTO.getCenterId())
                .eq(null != queryDTO.getLocationId(), TaskMaintain::getLocationId, queryDTO.getLocationId())
                .like(!StringUtils.isEmpty(queryDTO.getPitNo()), TaskMaintain::getPitNo, queryDTO.getPitNo())
                .eq(!StringUtils.isEmpty(queryDTO.getPitOrder()), TaskMaintain::getPitNo, queryDTO.getPitOrder())
                .eq(!StringUtils.isEmpty(queryDTO.getTaskNo()), TaskMaintain::getTaskNo, queryDTO.getTaskNo());
        if(!StringUtils.isEmpty(queryDTO.getStatus())){
            if(queryDTO.getStatus().equals(TaskStatus.COMPLETED.getName())){
                wrapper.eq(TaskMaintain::getStatus, TaskStatus.COMPLETED.getName());
            }else {
                wrapper.in(TaskMaintain::getStatus, TaskStatus.PENDING_EXE.getName(), TaskStatus.IN_PROGRESS.getName());
            }
        }
        wrapper.orderByAsc(TaskMaintain::getMaintainExpectStartTime);

        IPage<TaskMaintain> page = maintainTaskMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, MaintainTaskPdaVO.class);
    }
}
