package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.tpo.TaskSpreadingAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingUpdateDTO;
import com.hvisions.brewage.entity.tpo.TaskSpreading;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskSpreadingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingService
 * @description: 摊晾任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSpreadingService {

    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    /**
     * 查询摊晾任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskSpreadingVO> findPageList(TaskSpreadingQueryDTO queryDTO) {
        Page<TaskSpreadingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskSpreadingVO> spreadingVOList = taskSpreadingMapper.selectPageList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskSpreadingAddDTO addDTO) {
        log.info("调用新增摊晾任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增摊晾任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskSpreading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreading::getPitOrder,addDTO.getPitOrder())
                .eq(TaskSpreading::getDeleted,0);
        Integer count = taskSpreadingMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskSpreading taskSpreading = DtoMapper.convert(addDTO, TaskSpreading.class);

        //查询配置最大的流水号
        Integer maxNum = taskSpreadingMapper.selectMaxTaskNo(ProdConfigEnum.TL);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.TL, maxNum);
        taskSpreading.setTaskNo(taskNo);

        taskSpreading.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskSpreading.setTaskType(TaskBusiness.TASK_SPREADING.getTaskType());
        taskSpreading.setStatus(TaskStatus.PENDING_EXE.getName());
        taskSpreading.setCellarStatus(TaskStatus.CELLAR_IN_STATUS.getName());
        taskSpreading.setCreateTime(new Date());
        taskSpreading.setCreatorId(userId);

        taskSpreadingMapper.insert(taskSpreading);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskSpreadingAddDTO> addDTOList) {
        log.info("调用批量新增摊晾任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增摊晾任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskSpreadingUpdateDTO updateDTO) {
        log.info("调用修改摊晾任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改摊晾任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskSpreading taskSpreading = taskSpreadingMapper.selectById(updateDTO.getId());
        if (null == taskSpreading || taskSpreading.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }

        taskSpreading.setStartTime(updateDTO.getStartTime());
        taskSpreading.setEndTime(updateDTO.getEndTime());
        taskSpreading.setUpdaterId(userId);
        taskSpreading.setUpdateTime(new Date());
        taskSpreadingMapper.updateById(taskSpreading);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除摊晾任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除摊晾任务列表，获取到当前登录用户，用户id：{}", userId);

        taskSpreadingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 结束摊晾任务
     * @param pitOrder 窖池订单号
     * @return
     */
    public String endTaskSpreading(String pitOrder) {
        log.info("调用结束摊晾任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束摊晾任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskSpreading> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSpreading::getPitOrder,pitOrder)
                .ne(TaskSpreading::getStatus,TaskStatus.TERMINATED.getName())
                .ne(TaskSpreading::getStatus,TaskStatus.COMPLETED.getName())
                .eq(TaskSpreading::getDeleted,0);
        List<TaskSpreading> taskSpreadingList = taskSpreadingMapper.selectList(wrapper);
        taskSpreadingList.forEach(v->{
            log.info("调用结束摊晾任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setStatus(TaskStatus.COMPLETED.getName());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskSpreadingMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束摊晾任务成功");
    }
}
