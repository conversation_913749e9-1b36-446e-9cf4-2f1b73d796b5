package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskWinePickingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskWinePickingDetailParameterMapper;
import com.hvisions.brewage.dao.tpo.TaskWinePickingMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.*;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.ProdMqConsts;
import com.hvisions.brewage.operlog.utils.StringUtils;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.base.RouteParameterWarnService;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskWinePickingDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskWinePickingDetailService
 * @description: 摘酒任务详情Service
 * @date 2025/7/7 16:41
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskWinePickingDetailService {

    @Resource
    private TaskWinePickingDetailMapper taskWinePickingDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private BaseWrapper baseWrapper;
    @Resource
    private TaskWinePickingMapper taskWinePickingMapper;
    @Resource
    private TaskWinePickingService taskWinePickingService;
    @Resource
    private LogService logService;
    @Resource
    private RouteParameterWarnService routeParameterWarnService;
    @Resource
    private TaskWinePickingDetailParameterMapper taskWinePickingDetailParameterMapper;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 查询摘酒任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskWinePickingDetailVO> findPageList(TaskWinePickingDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskWinePickingDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWinePickingDetail::getDeleted, 0)
                .eq(TaskWinePickingDetail::getTaskWinePickingId, queryDTO.getTaskWinePickingId());
        IPage<TaskWinePickingDetail> page = taskWinePickingDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        Page<TaskWinePickingDetailVO> detailVOPage = baseWrapper.convertToPage(page, TaskWinePickingDetailVO.class);
        if (!CollectionUtils.isEmpty(detailVOPage.getRecords())) {
            detailVOPage.getRecords().forEach(v -> {
                LambdaQueryWrapper<TaskWinePickingDetailParameter> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskWinePickingDetailParameter::getTaskWinePickingDetailId, v.getId())
                        .eq(TaskWinePickingDetailParameter::getDeleted, 0);
                List<TaskWinePickingDetailParameter> parameterList = taskWinePickingDetailParameterMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(parameterList)) {
                    //流酒温度
                    List<TaskWinePickingDetailParameter> conveyorLeftGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FlowingWineTemperatureTemp)).collect(Collectors.toList());
                    List<TaskWinePickingDetailParameterAddDTO> flowingWineTemperatureTempConvert = DtoMapper.convertList(conveyorLeftGrainTemp, TaskWinePickingDetailParameterAddDTO.class);
                    v.setFlowingWineTemperatureTemp(flowingWineTemperatureTempConvert);

                    //流酒速度
                    List<TaskWinePickingDetailParameter> conveyorRightGrainTemp = parameterList.stream().filter(item -> item.getMetricName().equals(TaskSpreadingParameterEnum.FlowingWineSpeedTemp)).collect(Collectors.toList());
                    List<TaskWinePickingDetailParameterAddDTO> flowingWineSpeedTempConvert = DtoMapper.convertList(conveyorRightGrainTemp, TaskWinePickingDetailParameterAddDTO.class);
                    v.setFlowingWineSpeedTemp(flowingWineSpeedTempConvert);

                }
            });
        }
        return detailVOPage;
    }

    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(TaskWinePickingDetailAddDTO addDTO) {
        log.info("调用新增摘酒任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增摘酒任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskWinePickingDetail winePickingDetail = DtoMapper.convert(addDTO, TaskWinePickingDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskWinePickingMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskWinePicking> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWinePicking::getDeleted, 0)
                .eq(TaskWinePicking::getPitNo, pitNo)
                .eq(TaskWinePicking::getVinasseName, addDTO.getOutMaterialType())
                .orderByDesc(TaskWinePicking::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskWinePicking taskWinePicking = taskWinePickingMapper.selectOne(wrapper);
        if (null != taskWinePicking) {

            //判断任务是否是待执行
            if (taskWinePicking.getState().equals(TaskStatus.PENDING_EXE.getCode())) {
                taskWinePicking.setState(TaskStatus.IN_PROGRESS.getCode());
                taskWinePicking.setStartTime(new Date());
                taskWinePickingMapper.updateById(taskWinePicking);
            }
            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskWinePicking.getCenterId(), taskWinePicking.getLocationId(), taskWinePicking.getPitOrder(), ExecutionStatus.LIQUOR_SELECTION.getStatus());
            if(taskCurrentCenterVO.getIsChange()){
                //关闭上一个任务
                taskWinePickingService.endTaskWinePicking(taskCurrentCenterVO.getUpperPitOrder());
            }
            LambdaQueryWrapper<TaskWinePickingDetail> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(TaskWinePickingDetail::getDeleted, 0)
                    .eq(TaskWinePickingDetail::getTaskWinePickingId,taskWinePicking.getId());
            List<TaskWinePickingDetail> list = taskWinePickingDetailMapper.selectList(wrapper3);
            if(list.size() <= 0){
                //追加窖池订单任务完成状态
                //workshopPitOrderService.putExecutionStatus(taskWinePicking.getPitOrder(), ExecutionStatus.LIQUOR_SELECTION.getStatus(),1);
            }
        }

        winePickingDetail.setRecordTime(new Date());
        //TaskDishMixingDetail.setDataSource(TaskBusiness.TASK_SPREADING_DETAIL.getTaskType());
        winePickingDetail.setCreateTime(new Date());
        winePickingDetail.setCreatorId(userId);
        winePickingDetail.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskWinePickingDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskWinePickingDetail::getDeleted, 0)
                .eq(TaskWinePickingDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskWinePickingDetail taskWinePickingDetail = taskWinePickingDetailMapper.selectOne(wrapperDetail);
        if (null == taskWinePickingDetail) {
            if (null != taskWinePicking) {
                winePickingDetail.setTaskWinePickingId(taskWinePicking.getId());
            }

            taskWinePickingDetailMapper.insert(winePickingDetail);
        } else {
            if (null != taskWinePicking) {
                winePickingDetail.setTaskWinePickingId(taskWinePicking.getId());
            }
            winePickingDetail.setId(taskWinePickingDetail.getId());
            taskWinePickingDetailMapper.updateById(winePickingDetail);
        }
        log.info("摘酒任务详情{}", taskWinePickingDetail);
        //插入参数信息
        //流酒温度
        if (!CollectionUtils.isEmpty(addDTO.getFlowingWineTemperatureTemp())) {
            addWinePickingDetailParameter(winePickingDetail.getId(), TaskSpreadingParameterEnum.FlowingWineTemperatureTemp, addDTO.getFlowingWineTemperatureTemp());
        }

        //插入参数信息
        //流酒速度
        if (!CollectionUtils.isEmpty(addDTO.getFlowingWineSpeedTemp())) {
            addWinePickingDetailParameter(winePickingDetail.getId(), TaskSpreadingParameterEnum.FlowingWineSpeedTemp, addDTO.getFlowingWineSpeedTemp());
        }

        //校验参数是否有工艺异常，有则告警
        //1.一段流酒重量
//        if(!StringUtils.isEmpty(addDTO.getWeightFlowingWineOne())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_WeightFlowingWineOne.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getWeightFlowingWineOne())
//            );
//        }
//        //2.二段流酒重量
//        if(!StringUtils.isEmpty(addDTO.getWeightFlowingWineTwo())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_WeightFlowingWineTwo.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getWeightFlowingWineTwo())
//            );
//        }
//        //3.三段流酒重量
//        if(!StringUtils.isEmpty(addDTO.getWeightFlowingWineThree())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_WeightFlowingWineThree.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getWeightFlowingWineThree())
//            );
//        }
//        //4.四段流酒重量
//        if(!StringUtils.isEmpty(addDTO.getWeightFlowingWineFour())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_WeightFlowingWineFour.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getWeightFlowingWineFour())
//            );
//        }
//        //5.一段酒精度
//        if(!StringUtils.isEmpty(addDTO.getAlcoholContentOne())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_AlcoholContentOne.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getAlcoholContentOne())
//            );
//        }
//        //6.二段酒精度
//        if(!StringUtils.isEmpty(addDTO.getAlcoholContentTwo())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_AlcoholContentTwo.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getAlcoholContentTwo())
//            );
//        }
//        //7.三段酒精度
//        if(!StringUtils.isEmpty(addDTO.getAlcoholContentThree())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_AlcoholContentThree.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getAlcoholContentThree())
//            );
//        }
//        //8.四段酒精度
//        if(!StringUtils.isEmpty(addDTO.getAlcoholContentFour())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_AlcoholContentFour.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getAlcoholContentFour())
//            );
//        }
//        //9.流酒温度
//        if(!StringUtils.isEmpty(addDTO.getFlowingWineTemperature())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_FlowingWineTemperature.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getFlowingWineTemperature())
//            );
//        }
//        //10.流酒速度
//        if(!StringUtils.isEmpty(addDTO.getFlowingWineSpeed())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_FlowingWineSpeed.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getFlowingWineSpeed())
//            );
//        }
//        //11.盖盘到流酒时长
//        if(!StringUtils.isEmpty(addDTO.getCoverPlateFlowingWineDuration())){
//            routeParameterWarnService.judgeRouteParameterWarn(BusinessModuleParameterEnum.WINEPICKING,
//                    RouteParameterWarnParamEnum.ZJ_CoverPlateFlowingWineDuration.getParamCode(),
//                    pitNo,
//                    addDTO.getOutMaterialType(),
//                    addDTO.getEnterMaterialType(),
//                    new BigDecimal(addDTO.getCoverPlateFlowingWineDuration())
//            );
//        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 批量新增
     *
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskWinePickingDetailAddDTO> addDTOList) {
        log.info("调用批量新增摘酒任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增摘酒任务详情列表，获取到当前登录用户，用户id：{}", userId);

        //新增数据
        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * IOT同步到MES-调用批量新增摘酒任务详情列表
     *
     * @param addDTOList
     * @return
     */
    public String addListMq(List<TaskWinePickingDetailAddDTO> addDTOList) {
        log.info("IOT同步到MES-调用批量新增摘酒任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.WINEPICKING_IOT_TO_MES);

        log.info("执行mq发送数据到业务模块");
        rabbitTemplate.convertAndSend(ProdMqConsts.taskWinePicking_IOTTOMES_EXCHANGE,
                ProdMqConsts.taskWinePicking_IOTTOMES_SIMPLE_TOPIC,
                JSONArray.toJSONString(addDTOList));
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除摘酒任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除摘酒任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskWinePickingDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 插入详情参数
     *
     * @param taskWinePickingDetailId 详情id
     * @param metricName            采集项名称
     * @param addDTOList            采集数据
     */
    private void addWinePickingDetailParameter(Integer taskWinePickingDetailId, String metricName, List<TaskWinePickingDetailParameterAddDTO> addDTOList) {
        addDTOList.forEach(v -> {
            TaskWinePickingDetailParameter winePickingDetailParameter = new TaskWinePickingDetailParameter();
            winePickingDetailParameter.setCollectionTime(v.getCollectionTime());
            winePickingDetailParameter.setMeasuredValue(v.getMeasuredValue());
            winePickingDetailParameter.setTaskWinePickingDetailId(taskWinePickingDetailId);
            winePickingDetailParameter.setMetricName(metricName);
            taskWinePickingDetailParameterMapper.insert(winePickingDetailParameter);
        });
    }
}
