package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskWinePickingDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskWinePickingMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskWinePickingAddDTO;
import com.hvisions.brewage.entity.tpo.TaskSistillationDetail;
import com.hvisions.brewage.entity.tpo.TaskSteaming;
import com.hvisions.brewage.entity.tpo.TaskWinePicking;
import com.hvisions.brewage.entity.tpo.TaskWinePickingDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskWinePickingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskWinePickingService
 * @description: 摘酒任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskWinePickingService {

    @Resource
    private TaskWinePickingMapper taskWinePickingMapper;
    @Resource
    private TaskWinePickingDetailMapper taskWinePickingDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private LogService logService;

    /**
     * 查询摘酒任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskWinePickingVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskWinePickingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskWinePickingVO> taskWinePickingVOList = taskWinePickingMapper.selectPageList(page, queryDTO);
        taskWinePickingVOList.forEach(item->{
            LambdaQueryWrapper<TaskWinePickingDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskWinePickingDetail::getTaskWinePickingId, item.getId())
                    .eq(TaskWinePickingDetail::getDeleted,0);
            List<TaskWinePickingDetail> list = taskWinePickingDetailMapper.selectList(wrapper);
            if(list.size()>0){

                Double weightFlowingWineOne = list.stream().mapToDouble(x-> Double.parseDouble(x.getWeightFlowingWineOne())).sum();
                Double weightFlowingWineTwo = list.stream().mapToDouble(x-> Double.parseDouble(x.getWeightFlowingWineTwo())).sum();
                Double weightFlowingWineThree = list.stream().mapToDouble(x-> Double.parseDouble(x.getWeightFlowingWineThree())).sum();
                Double weightFlowingWineFour = list.stream().mapToDouble(x-> Double.parseDouble(x.getWeightFlowingWineFour())).sum();
                item.setWeightFlowingWineOne(String.valueOf(weightFlowingWineOne));
                item.setWeightFlowingWineTwo(String.valueOf(weightFlowingWineTwo));
                item.setWeightFlowingWineThree(String.valueOf(weightFlowingWineThree));
                item.setWeightFlowingWineFour(String.valueOf(weightFlowingWineFour));
                item.setWeightFlowingWineTotal(String.valueOf(weightFlowingWineOne+weightFlowingWineTwo+weightFlowingWineThree+weightFlowingWineFour));
            }
        });
        page.setRecords(taskWinePickingVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskWinePickingAddDTO addDTO) {
        log.info("调用新增摘酒任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增摘酒任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskWinePicking> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWinePicking::getPitOrder,addDTO.getPitOrder())
                .eq(TaskWinePicking::getDeleted,0);
        Integer count = taskWinePickingMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskWinePicking taskWinePicking = DtoMapper.convert(addDTO, TaskWinePicking.class);

        //查询配置最大的流水号
        Integer maxNum = taskWinePickingMapper.selectMaxTaskNo(ProdConfigEnum.ZJ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.ZJ, maxNum);
        taskWinePicking.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskWinePicking.setTaskType(TaskBusiness.SHANGZHENG.getTaskType());
        taskWinePicking.setState(TaskStatus.PENDING_EXE.getCode());
        taskWinePicking.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskWinePicking.setCreateTime(new Date());
        taskWinePicking.setCreatorId(userId);

        taskWinePickingMapper.insert(taskWinePicking);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskWinePickingAddDTO> addDTOList) {
        log.info("调用批量新增摘酒任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增摘酒任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskWinePickingAddDTO updateDTO) {
        log.info("调用修改摘酒任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改摘酒任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskWinePicking taskWinePicking = taskWinePickingMapper.selectById(updateDTO.getId());
        if (null == taskWinePicking || taskWinePicking.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskWinePicking);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskWinePickingMapper.updateById(taskWinePicking);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除摘酒任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除摘酒任务列表，获取到当前登录用户，用户id：{}", userId);
        taskWinePickingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    public String endTaskWinePicking(String pitOrder) {
        log.info("调用结束摘酒任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束摘酒任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskWinePicking> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWinePicking::getPitOrder,pitOrder)
                .ne(TaskWinePicking::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskWinePicking::getDeleted,0);
        List<TaskWinePicking> list = taskWinePickingMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束摘酒任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskWinePickingMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束摘酒任务成功");
    }
}
