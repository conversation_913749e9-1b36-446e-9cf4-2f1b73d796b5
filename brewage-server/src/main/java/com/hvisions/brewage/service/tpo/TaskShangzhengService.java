package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskShangzhengMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskShangzhengAddDTO;
import com.hvisions.brewage.entity.tpo.TaskDishMixing;
import com.hvisions.brewage.entity.tpo.TaskShangzheng;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskShangzhengVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskShangzhengService
 * @description: 上甑任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskShangzhengService {

    @Resource
    private TaskShangzhengMapper taskShangzhengMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private LogService logService;

    /**
     * 查询上甑任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskShangzhengVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskShangzhengVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskShangzhengVO> list = taskShangzhengMapper.selectPageList(page, queryDTO);

        page.setRecords(list);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskShangzhengAddDTO addDTO) {
        log.info("调用新增上甑任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增上甑任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskShangzheng> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskShangzheng::getPitOrder,addDTO.getPitOrder())
                .eq(TaskShangzheng::getDeleted,0);
        Integer count = taskShangzhengMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskShangzheng taskShangzheng = DtoMapper.convert(addDTO, TaskShangzheng.class);

        //查询配置最大的流水号
        Integer maxNum = taskShangzhengMapper.selectMaxTaskNo(ProdConfigEnum.SZ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.SZ, maxNum);
        taskShangzheng.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskShangzheng.setTaskType(TaskBusiness.SHANGZHENG.getTaskType());
        taskShangzheng.setState(TaskStatus.PENDING_EXE.getCode());
        taskShangzheng.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskShangzheng.setCreateTime(new Date());
        taskShangzheng.setCreatorId(userId);

        taskShangzhengMapper.insert(taskShangzheng);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskShangzhengAddDTO> addDTOList) {
        log.info("调用批量新增上甑任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增上甑任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskShangzhengAddDTO updateDTO) {
        log.info("调用修改上甑任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改上甑任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskShangzheng taskShangzheng = taskShangzhengMapper.selectById(updateDTO.getId());
        if (null == taskShangzheng || taskShangzheng.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskShangzheng);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskShangzhengMapper.updateById(taskShangzheng);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除上甑任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除上甑任务列表，获取到当前登录用户，用户id：{}", userId);
        taskShangzhengMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    public String endTaskShangzheng(String pitOrder) {
        log.info("调用结束上甑任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束上甑任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskShangzheng> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskShangzheng::getPitOrder,pitOrder)
                .ne(TaskShangzheng::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskShangzheng::getDeleted,0);
        List<TaskShangzheng> list = taskShangzhengMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束上甑任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskShangzhengMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束上甑任务成功");
    }
}
