package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskDiscardDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskDiscardMapper;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.tpo.TaskDiscardDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskDiscardDetailQueryDTO;
import com.hvisions.brewage.entity.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskDiscard;
import com.hvisions.brewage.enums.OperationResult;
import com.hvisions.brewage.enums.TaskBusiness;
import com.hvisions.brewage.enums.TaskStatus;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.vo.tpo.TaskCurrentCenterVO;
import com.hvisions.brewage.vo.tpo.TaskDiscardDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDiscardDetailService
 * @description: 丢糟任务详情Service
 * @date 2025/7/18 16:10
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDiscardDetailService {
    
    @Resource
    private TaskDiscardDetailMapper taskDiscardDetailMapper;
    
    @Resource
    private TaskDiscardMapper taskDiscardMapper;

    @Resource
    private TaskDiscardService taskDiscardService;

    @Resource
    private BaseWrapper baseWrapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;

    @Resource
    private TaskCurrentCenterService taskCurrentCenterService;


    /**
     * 查询丢糟任务详情列表
     * @param queryDTO
     * @return
     */
    public Page<TaskDiscardDetailVO> findPageList(TaskDiscardDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskDiscardDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDiscardDetail::getDeleted, 0)
                .eq(TaskDiscardDetail::getTaskDiscardId, queryDTO.getTaskDiscardId());
        IPage<TaskDiscardDetail> page = taskDiscardDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, TaskDiscardDetailVO.class);
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskDiscardDetailAddDTO addDTO) {
        log.info("调用新增丢糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增丢糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskDiscardDetail taskDiscardDetail = DtoMapper.convert(addDTO, TaskDiscardDetail.class);

        //根据iot传的窖号查询连窖号
        String pitNo = taskSpreadingMapper.selectPitNo(addDTO.getPitNo());

        LambdaQueryWrapper<TaskDiscard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDiscard::getDeleted, 0)
                .eq(TaskDiscard::getPitNo, pitNo)
                .eq(TaskDiscard::getMaterialType, addDTO.getOutMaterialType())
                .orderByDesc(TaskDiscard::getCreateTime)
                .last("limit 1");

        //挂到对应的任务上
        TaskDiscard taskDiscard = taskDiscardMapper.selectOne(wrapper);
        if (null != taskDiscard) {
            taskDiscardDetail.setTaskDiscardId(taskDiscard.getId());

            //判断任务是否是待执行
            if (taskDiscard.getStatus().equals(TaskStatus.PENDING_EXE.getName())) {
                taskDiscard.setStatus(TaskStatus.IN_PROGRESS.getName());
                taskDiscard.setStartTime(new Date());
                taskDiscardMapper.updateById(taskDiscard);
            }


            //判断是否要更改上一个订单状态为已完成
            TaskCurrentCenterVO taskCurrentCenterVO = taskCurrentCenterService.verifyIsEqualOrder(taskDiscard.getCenterId(), taskDiscard.getLocationId(), taskDiscard.getPitOrder(), ExecutionStatus.DISTILLERS_GRAIN_OUT.getStatus());
            if (taskCurrentCenterVO.getIsChange()) {
                //关闭上一个丢糟任务
                taskDiscardService.endTask(taskCurrentCenterVO.getUpperPitOrder());
            }
        }

        taskDiscardDetail.setRecordTime(new Date());
        taskDiscardDetail.setDataSource(TaskBusiness.TASK_LOADING_DETAIL.getTaskType());
        taskDiscardDetail.setCreateTime(new Date());
        taskDiscardDetail.setCreatorId(userId);

        //查询任务信息是否已存在，存在则更新，不存在则添加
        LambdaQueryWrapper<TaskDiscardDetail> wrapperDetail = new LambdaQueryWrapper<>();
        wrapperDetail.eq(TaskDiscardDetail::getDeleted, 0)
                .eq(TaskDiscardDetail::getIotTaskNo, addDTO.getIotTaskNo());
        TaskDiscardDetail taskDiscardDetail1 = taskDiscardDetailMapper.selectOne(wrapperDetail);
        if(null==taskDiscardDetail1){
            taskDiscardDetailMapper.insert(taskDiscardDetail);

            //添加入窖甑口总数
            if(null!=taskDiscard){
                taskDiscard.setTotalBatches(taskDiscard.getTotalBatches()+1);
                taskDiscardMapper.updateById(taskDiscard);
            }
        }else {
            taskDiscardDetail.setId(taskDiscardDetail1.getId());
            taskDiscardDetailMapper.updateById(taskDiscardDetail);
        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 入窖任务更改窖池-改出
     *
     * @param iotTaskNo  iot任务号
     * @param outMaterialType 出窖糟源类别
     * @param sourcePitNo     出窖窖号
     * @return
     */
    public String updateTaskSpreadingDetailOut(String iotTaskNo, String outMaterialType, String sourcePitNo, String outPitOrder) {
        log.info("丢糟任务更改窖池-改出，刷新丢糟任务明细的数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("丢糟任务更改窖池-改出，刷新丢糟任务明细的数据，获取到当前登录用户，用户id：{}", userId);
        LambdaQueryWrapper<TaskDiscardDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDiscardDetail::getIotTaskNo, iotTaskNo)
                .eq(TaskDiscardDetail::getDeleted, 0);
        TaskDiscardDetail discardDetail = taskDiscardDetailMapper.selectOne(wrapper);
        if (null != discardDetail) {
            Integer taskDiscardId = discardDetail.getTaskDiscardId();

            //挂载到对应的任务上
            LambdaQueryWrapper<TaskDiscard> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(TaskDiscard::getPitOrder, outPitOrder)
                    .eq(TaskDiscard::getDeleted, 0);
            TaskDiscard taskDiscard = taskDiscardMapper.selectOne(wrapper1);
            if (null != taskDiscard) {
                discardDetail.setTaskDiscardId(taskDiscard.getId());
            } else {
                log.info("丢糟任务更改窖池-改出，刷新丢糟任务明细的数据-没有查询更改后的窖池订单任务数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
            }

            discardDetail.setOutMaterialType(outMaterialType);
            discardDetail.setUpdaterId(userId);
            discardDetail.setUpdateTime(new Date());
            taskDiscardDetailMapper.updateById(discardDetail);

            //查询原先的任务，修改甑口
            if(null!=taskDiscardId){
                log.info("丢糟任务更改窖池-改出，修改原任务的总甑口数，参数------》丢糟任务ID：{}", taskDiscardId);
                updateTaskDiscardTotalBatches(taskDiscardId);
            }

            //修改更改后的任务总甑口
            if(null!=taskDiscard){
                log.info("丢糟任务更改窖池-改出，修改新任务的总甑口数，参数------》丢糟任务ID：{}", taskDiscard.getId());
                updateTaskDiscardTotalBatches(taskDiscard.getId());
            }

        } else {
            log.info("丢糟任务更改窖池-改出，刷新丢糟任务明细的数据-没有查询到对应的明细数据，传入参数------》iot任务号：{}，出窖糟源类别：{}，出窖窖号：{},出窖窖池号：{}", iotTaskNo, outMaterialType, sourcePitNo, outPitOrder);
        }

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 修改任务总甑口数
     * @param taskDiscardId
     */
    private void updateTaskDiscardTotalBatches(Integer taskDiscardId){
        LambdaQueryWrapper<TaskDiscardDetail> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(TaskDiscardDetail::getTaskDiscardId, taskDiscardId)
                .eq(TaskDiscardDetail::getDeleted, 0);
        Integer count = taskDiscardDetailMapper.selectCount(wrapper2);
        LambdaUpdateWrapper<TaskDiscard> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskDiscard::getId,taskDiscardId)
                .eq(TaskDiscard::getDeleted,0)
                .set(TaskDiscard::getTotalBatches,count);
        taskDiscardMapper.update(null,updateWrapper);
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除丢糟任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除丢糟任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskDiscardDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }
}
