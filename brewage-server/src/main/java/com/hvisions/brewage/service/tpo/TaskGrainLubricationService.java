package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskGrainLubricationDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskGrainLubricationMapper;
import com.hvisions.brewage.dto.tpo.TaskAllQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationAddDTO;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationDetailAddDTO;
import com.hvisions.brewage.entity.tpo.TaskGrainLubrication;
import com.hvisions.brewage.entity.tpo.TaskGrainLubricationDetail;
import com.hvisions.brewage.entity.tpo.TaskPileUpDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderQueryDTO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderQueryVO;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.utils.CopyUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskGrainLubricationVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskGrainLubricationService
 * @description: 润粮任务Service
 * @date 2025/7/7 14:14
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskGrainLubricationService {

    @Resource
    private TaskGrainLubricationMapper taskGrainLubricationMapper;
    @Resource
    private TaskGrainLubricationDetailMapper taskGrainLubricationDetailMapper;
    @Resource
    private UserAuditorAware userAuditorAware;
    @Resource
    private LogService logService;
    @Resource
    private TaskGrainLubricationDetailService taskGrainLubricationDetailService;


    /**
     * 查询润粮任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskGrainLubricationVO> findPageList(TaskAllQueryDTO queryDTO) {
        Page<TaskGrainLubricationVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskGrainLubricationVO> taskGrainLubricationVOList = taskGrainLubricationMapper.selectPageList(page, queryDTO);
        taskGrainLubricationVOList.forEach(item->{
            LambdaQueryWrapper<TaskGrainLubricationDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskGrainLubricationDetail::getTaskGrainLubricationId, item.getId())
                    .eq(TaskGrainLubricationDetail::getDeleted,0);
            List<TaskGrainLubricationDetail> list = taskGrainLubricationDetailMapper.selectList(wrapper);
            if(list.size()>0){

                Double runliangWaterWeight = list.stream().mapToDouble(x-> Double.parseDouble(x.getRunliangWaterWeight())).sum();
                Double singleContainerSorghumActualValue = list.stream().mapToDouble(x-> Double.parseDouble(x.getSingleContainerSorghumActualValue())).sum();
                item.setRunliangWaterWeight(String.valueOf(runliangWaterWeight));
                item.setSingleContainerSorghumActualValue(String.valueOf(singleContainerSorghumActualValue));
            }
        });
        page.setRecords(taskGrainLubricationVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskGrainLubricationAddDTO addDTO) {
        log.info("调用新增润粮任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增润粮任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskGrainLubrication> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskGrainLubrication::getPitOrder,addDTO.getPitOrder())
                .eq(TaskGrainLubrication::getDeleted,0);
        Integer count = taskGrainLubricationMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskGrainLubrication taskGrainLubrication = DtoMapper.convert(addDTO, TaskGrainLubrication.class);

        //查询配置最大的流水号
        Integer maxNum = taskGrainLubricationMapper.selectMaxTaskNo(ProdConfigEnum.RL);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.RL, maxNum);
        taskGrainLubrication.setTaskNo(taskNo);

//        TaskDishMixing.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
        taskGrainLubrication.setTaskType(TaskBusiness.GRAINLUBRICATION.getTaskType());
        taskGrainLubrication.setState(TaskStatus.PENDING_EXE.getCode());
        taskGrainLubrication.setCellarStatus(TaskStatus.CELLAR_OUT_STATUS.getName());
        taskGrainLubrication.setCreateTime(new Date());
        taskGrainLubrication.setCreatorId(userId);

        taskGrainLubricationMapper.insert(taskGrainLubrication);
        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 批量新增
     * @param addDTOList
     * @return
     */
    public String addList(List<TaskGrainLubricationAddDTO> addDTOList) {
        log.info("调用批量新增润粮任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增润粮任务列表，获取到当前登录用户，用户id：{}", userId);

        addDTOList.forEach(this::add);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskGrainLubricationAddDTO updateDTO) {
        log.info("调用修改润粮任务列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改润粮任务列表，获取到当前登录用户，用户id：{}", userId);

        TaskGrainLubrication taskGrainLubrication = taskGrainLubricationMapper.selectById(updateDTO.getId());
        if (null == taskGrainLubrication || taskGrainLubrication.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }
        CopyUtil.copyProperties(updateDTO,taskGrainLubrication);
//        TaskDishMixing.setStartTime(updateDTO.getStartTime());
//        TaskDishMixing.setEndTime(updateDTO.getEndTime());
//        TaskDishMixing.setUpdaterId(userId);
//        TaskDishMixing.setUpdateTime(new Date());
        taskGrainLubricationMapper.updateById(taskGrainLubrication);
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除润粮任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除润粮任务列表，获取到当前登录用户，用户id：{}", userId);
        taskGrainLubricationMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    @Transactional(rollbackFor = Exception.class)
    public String syncIotToMes(List<TaskGrainLubricationDetailAddDTO> addDTOList) {

        log.info("调用批量新增润粮任务，传入参数-------》{}", JSONObject.toJSONString(addDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用批量新增润粮任务，获取到当前登录用户，用户id：{}", userId);

        //新增日志
        logService.addLogCapture(addDTOList, LogCaptureEnum.GRAINLUBRICATION_IOT_TO_MES);

        //新增数据
        addDTOList.forEach(addDTO->{

            //根据iot传的窖号查询连窖号
            String pitNo = taskGrainLubricationMapper.selectPitNo(addDTO.getPitNo());

            WorkshopPitOrderQueryDTO query = new WorkshopPitOrderQueryDTO();
            query.setVinasseName(addDTO.getOutMaterialType());
            query.setFullPitId(pitNo);
            List<WorkshopPitOrderQueryVO> list = taskGrainLubricationMapper.getWorkshopPitOrderQuery(query);
            if(list.size()>0){
                WorkshopPitOrderQueryVO vo = list.get(0);
                TaskGrainLubrication tgl = new TaskGrainLubrication();

                //查询任务是否已经创建过了
                LambdaQueryWrapper<TaskGrainLubrication> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TaskGrainLubrication::getPitOrder,vo.getOrderCode())
                        .eq(TaskGrainLubrication::getDeleted,0);
                List<TaskGrainLubrication> count = taskGrainLubricationMapper.selectList(wrapper);
                if(count.size()>0){
                    Integer taskGrainLubricationId = count.get(0).getId();
                    addDTO.setTaskGrainLubricationId(taskGrainLubricationId);
                }else{

                    //查询配置最大的流水号
                    Integer maxNum = taskGrainLubricationMapper.selectMaxTaskNo(ProdConfigEnum.RL);
                    String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.RL, maxNum);
                    tgl.setTaskNo(taskNo);

//        tgl.setTaskName(TaskBusiness.TASK_SPREADING.getTaskName());
                    tgl.setTaskType(TaskBusiness.GRAINLUBRICATION.getTaskType());
                    tgl.setState(TaskStatus.PENDING_EXE.getCode());
                    tgl.setCreateTime(new Date());
                    tgl.setCreatorId(userId);
                    tgl.setIotNo(addDTO.getIotTaskNo());
                    tgl.setCenterId(vo.getCenterId());
                    tgl.setLocationId(vo.getLocationId());
                    tgl.setPitOrder(vo.getOrderCode());
                    tgl.setPitNo(vo.getFullPitId());
                    tgl.setVinasseName(vo.getVinasseName());
                    tgl.setStartTime(new Date());
                    taskGrainLubricationMapper.insert(tgl);

                }

                taskGrainLubricationDetailService.add(addDTO);
            }

        });

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    public String endTaskGrainLubrication(String pitOrder) {
        log.info("调用结束润粮任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束润粮任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskGrainLubrication> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskGrainLubrication::getPitOrder,pitOrder)
                .ne(TaskGrainLubrication::getState,TaskStatus.COMPLETED.getName())
                .eq(TaskGrainLubrication::getDeleted,0);
        List<TaskGrainLubrication> list = taskGrainLubricationMapper.selectList(wrapper);
        list.forEach(v->{
            log.info("调用结束润粮任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setState(TaskStatus.COMPLETED.getCode());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskGrainLubricationMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束润粮任务成功");
    }
}
