package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.common.constant.MqQueueConstant;
import com.hvisions.brewage.common.entity.MessageConsume;
import com.hvisions.brewage.common.utils.AmqpTemplateUtil;
import com.hvisions.brewage.common.utils.SnowflakeIdWorker;
import com.hvisions.brewage.dao.tpo.TaskLoadingMapper;
import com.hvisions.brewage.dao.tpo.TaskSealingMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskLoading;
import com.hvisions.brewage.entity.tpo.TaskSealing;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.feign.message.MessageConsumeClient;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mq.dto.BaseOrderMessageSapDTO;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskLoadingVO;
import com.hvisions.brewage.vo.tpo.TaskSealingVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSealingService
 * @description: 封窖任务Service
 * @date 2025/7/10 14:36
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskSealingService {
    @Resource
    private TaskSealingMapper taskSealingMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TaskLoadingService taskLoadingService;

    @Resource
    private TaskLoadingMapper taskLoadingMapper;

    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    private TaskTemperatureService taskTemperatureService;

    @Resource
    private MessageConsumeClient messageConsumeClient;

    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    @Resource
    private WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;


    /**
     * 查询封窖任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskSealingVO> findPageList(TaskSealingQueryDTO queryDTO) {
        Page<TaskSealingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskSealingVO> spreadingVOList = taskSealingMapper.selectPageList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }


    /**
     * 查询封窖任务列表-PDA
     * @param queryDTO
     * @return
     */
    public Page<TaskSealingVO> findPDAPageList(TaskSealingPDAQueryDTO queryDTO) {
        Page<TaskSealingVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskSealingVO> spreadingVOList = taskSealingMapper.selectPagePDAList(page, queryDTO);

        page.setRecords(spreadingVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskSealingAddDTO addDTO) {
        log.info("调用新增封窖任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增封窖任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskSealing> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskSealing::getPitOrder,addDTO.getPitOrder())
                .eq(TaskSealing::getDeleted,0);
        Integer count = taskSealingMapper.selectCount(wrapper);
        if(count>0){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        //查询窖池订单层数
        LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TPoWorkshopPitOrder::getOrderCode,addDTO.getPitOrder())
                .eq(TPoWorkshopPitOrder::getIsDeleted,0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
        if(null==workshopPitOrder){
            FailureCode addFailed = FailureCode.ADD_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription("窖池订单，窖池订单号="+addDTO.getPitOrder()));
        }

        TaskSealing taskSealing = DtoMapper.convert(addDTO, TaskSealing.class);

        //查询配置最大的流水号
        Integer maxNum = taskSealingMapper.selectMaxTaskNo(ProdConfigEnum.FJ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.FJ, maxNum);
        taskSealing.setTaskNo(taskNo);

        taskSealing.setLayer(workshopPitOrder.getLayer());
        taskSealing.setTaskName(TaskBusiness.TASK_SEALING.getTaskName());
        taskSealing.setTaskType(TaskBusiness.TASK_SEALING.getTaskType());
        taskSealing.setStatus(TaskStatus.IN_PROGRESS.getName());
        taskSealing.setStartTime(new Date());
        taskSealing.setCreateTime(new Date());
        taskSealing.setCreatorId(userId);

        taskSealingMapper.insert(taskSealing);

        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(workshopPitOrder.getOrderCode(), ExecutionStatus.SEALED_CELLAR.getStatus(),1);

        //创建PDA个人任务执行进度
        taskProcessRecordsService.addTask(taskSealing.getTaskNo(),taskSealing.getPitNo(), ExecutionStatus.SEALED_CELLAR.getStatus(),taskSealing.getCenterId(),taskSealing.getLocationId());
        return OperationResult.ADD_SUCCESS.getDescription();
    }


    /**
     * 封窖确认
     * @param confirmDTO
     * @return
     */
    public String confirm(TaskSealingConfirmDTO confirmDTO) {
        log.info("调用封窖确认，传入参数-------》{}", JSONObject.toJSONString(confirmDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用封窖确认，获取到当前登录用户，用户id：{}", userId);

        TaskSealing taskSealing = taskSealingMapper.selectById(confirmDTO.getId());
        if (null == taskSealing || taskSealing.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(confirmDTO.getId()));
        }

        if(taskSealing.getStatus().equals(TaskStatus.COMPLETED.getName())){
            FailureCode updateFailed = FailureCode.OTHER_FAILED_NOT_FOUND_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("封窖确认","当前窖池订单已完成封窖确认"));
        }

        taskSealing.setClayUsage(confirmDTO.getClayUsage());
        taskSealing.setSealingTime(confirmDTO.getSealingTime());
        taskSealing.setEndTime(new Date());
        taskSealing.setStatus(TaskStatus.COMPLETED.getName());
        taskSealing.setUpdaterId(userId);
        taskSealing.setUpdateTime(new Date());
        taskSealingMapper.updateById(taskSealing);

        //关闭对应的入窖任务
        taskLoadingService.closeTask(taskSealing.getPitOrder());

        //修改窖池订单状态为1(发酵)
        log.info("修改窖池订单状态为1(发酵)");
        LambdaQueryWrapper<TPoWorkshopPitOrder> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TPoWorkshopPitOrder::getOrderCode,taskSealing.getPitOrder())
                .eq(TPoWorkshopPitOrder::getIsDeleted,0);
        TPoWorkshopPitOrder workshopPitOrder = workshopPitOrderMapper.selectOne(wrapper1);
        if(null==workshopPitOrder){
            FailureCode addFailed = FailureCode.OTHER_FAILED;
            throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription("封窖确认","窖池订单，窖池订单号="+taskSealing.getPitOrder()));
        }

        workshopPitOrder.setSealConfirmTime(confirmDTO.getSealingTime());
        workshopPitOrder.setOrderStatus(0);
        workshopPitOrder.setPitStatus(1);
        workshopPitOrder.setIsSealConfirmTag(1);
        workshopPitOrder.setSealConfirmUserId(userId);
        workshopPitOrder.setSealWaterTestTime(confirmDTO.getSealingTime());
        workshopPitOrderMapper.updateById(workshopPitOrder);


        //查询入窖温度
        LambdaQueryWrapper<TaskLoading> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskLoading::getPitOrder,taskSealing.getPitOrder())
                .eq(TaskLoading::getDeleted,0);
        TaskLoading taskLoading = taskLoadingMapper.selectOne(queryWrapper);

        //创建窖池升温任务
        addTaskTemperature(taskSealing.getCenterId(),taskSealing.getLocationId(),taskSealing.getPitNo(),taskSealing.getPitOrder(),taskSealing.getLayer(),new BigDecimal(taskLoading.getLoadingTemperature()));

        List<TPoWorkshopPitOrderSap> tPoWorkshopPitOrderSaps = workshopPitOrderSapMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderSap>().eq(TPoWorkshopPitOrderSap::getOrderCodeId, workshopPitOrder.getId()));
        for (TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap : tPoWorkshopPitOrderSaps) {
            //修改单窖的信息
            tPoWorkshopPitOrderSap.setSealConfirmTime(confirmDTO.getSealingTime());
            tPoWorkshopPitOrderSap.setOrderStatus(0);
            tPoWorkshopPitOrderSap.setPitStatus(1);
            workshopPitOrderSapMapper.updateById(tPoWorkshopPitOrderSap);


            //调用SAP【生产订单抬头更改】接口，传递封窖时间(SAP:基本开始)、糟排次(SAP:生产5、排次)----------林远志写
            log.info("封窖确认-调用SAP【生产订单抬头更改】接口，传递封窖时间(SAP:基本开始)、糟排次(SAP:生产5、排次)");
            BaseOrderMessageSapDTO messageSapDTO = new BaseOrderMessageSapDTO();
            messageSapDTO.setId(tPoWorkshopPitOrderSap.getId());
            messageSapDTO.setUserId(userId);
            messageSapDTO.setPstngDate(tPoWorkshopPitOrderSap.getSealConfirmTime());
            messageSapDTO.setSyncStage(1);
            messageSapDTO.setOrderCode(workshopPitOrder.getOrderCode());
            messageSapDTO.setSapOrderCode(tPoWorkshopPitOrderSap.getSapOrderCode());
            String headKey = MqQueueConstant.ORDER_RISE_UPDATE_SAP + "_" +MqQueueConstant.OPERATE_TYPE_SYNC + "_" +tPoWorkshopPitOrderSap.getId();
            //发送同步请求到mq
            MessageConsume messageConsume = AmqpTemplateUtil.init(MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, MqQueueConstant.ORDER_RISE_UPDATE_SAP, JSONObject.toJSONString(messageSapDTO), headKey, MqQueueConstant.OPERATE_TYPE_SYNC);
            messageConsume.setPackageId(SnowflakeIdWorker.getNextId());
            //消息投递
            messageConsumeClient.addMessage(messageConsume);
            AmqpTemplateUtil.sendMessageToMq(MqQueueConstant.ORDER_RISE_UPDATE_SAP, MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, JSONObject.toJSONString(messageConsume));
        }


        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(workshopPitOrder.getOrderCode(), ExecutionStatus.SEALED_CELLAR.getStatus(),9);

        //结束PDA个人任务执行进度
        taskProcessRecordsService.closeTask(taskSealing.getTaskNo(),ExecutionStatus.SEALED_CELLAR.getStatus());
        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 创建窖池升温任务
     * @param centerId 中心id
     * @param locationId 车间id
     * @param pitNo 窖池号
     * @param pitOrder 窖池订单号
     * @param layer 层数
     * @param loadingTemp 入窖温度
     */
    public void addTaskTemperature(Integer centerId, Integer locationId, String pitNo, String pitOrder, Integer layer,BigDecimal loadingTemp){
        TaskTemperatureAddDTO addDTO =  new TaskTemperatureAddDTO();
        addDTO.setCenterId(centerId);
        addDTO.setLocationId(locationId);
        addDTO.setPitNo(pitNo);
        addDTO.setLayer(layer);
        addDTO.setLoadingTemp(loadingTemp);
        addDTO.setLoadingTemp(loadingTemp);
        addDTO.setPitOrder(pitOrder);
        taskTemperatureService.add(addDTO);
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除封窖任务列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除封窖任务列表，获取到当前登录用户，用户id：{}", userId);

        List<TaskSealing> sealingList = taskSealingMapper.selectBatchIds(ids);
        sealingList.forEach(v->{
            //删除PDA个人任务执行进度
            taskProcessRecordsService.deleteTask(v.getTaskNo(),ExecutionStatus.SEALED_CELLAR.getStatus());
        });

        taskSealingMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    public String update(TaskSealingUpdateDTO updateDTO) {
        log.info("调用封窖任务-修改，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用封窖任务-修改，获取到当前登录用户，用户id：{}", userId);

        TaskSealing taskSealing = taskSealingMapper.selectById(updateDTO.getId());
        if (null == taskSealing || taskSealing.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_NOT_FOUND;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription(updateDTO.getId()));
        }

        taskSealing.setClayUsage(updateDTO.getClayUsage());
        taskSealing.setUpdaterId(userId);
        taskSealing.setUpdateTime(new Date());
        taskSealingMapper.updateById(taskSealing);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }
}
