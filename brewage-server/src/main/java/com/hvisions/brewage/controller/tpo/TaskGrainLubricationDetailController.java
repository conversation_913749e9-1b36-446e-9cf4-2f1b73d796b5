package com.hvisions.brewage.controller.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskGrainLubricationDetailQueryDTO;
import com.hvisions.brewage.service.tpo.TaskGrainLubricationDetailService;
import com.hvisions.brewage.vo.tpo.TaskGrainLubricationDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingDetailController
 * @description: 润粮任务详情Controller
 * @date 2025/7/7 14:12
 */
@RestController
@RequestMapping("/tpo/task/grainLubricationDetail")
@Api(value = "润粮任务详情相关接口", tags = "润粮任务详情相关接口")
public class TaskGrainLubricationDetailController {

    @Resource
    private TaskGrainLubricationDetailService taskGrainLubricationDetailService;

    @PostMapping("/findPageList")
    @ApiOperation(value = "查询润粮任务详情列表")
    public Page<TaskGrainLubricationDetailVO> findPageList(@RequestBody @Valid TaskGrainLubricationDetailQueryDTO queryDTO) {
        return taskGrainLubricationDetailService.findPageList(queryDTO);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    private String addTaskSpreading(@RequestBody @Valid TaskGrainLubricationDetailAddDTO addDTO) {
        return taskGrainLubricationDetailService.add(addDTO);
    }

    @PostMapping("/addList")
    @ApiOperation(value = "批量新增")
    private String addList(@RequestBody @Valid List<TaskGrainLubricationDetailAddDTO> addDTOList) {
        return taskGrainLubricationDetailService.addList(addDTOList);
    }

    @DeleteMapping("/deleteIds")
    @ApiOperation(value = "删除")
    private String deleteIds(@RequestBody List<Integer> ids) {
        return taskGrainLubricationDetailService.deleteIds(ids);
    }

    @PostMapping("/syncIotToMes")
    @ApiOperation(value = "IOT同步到MES")
    private String syncIotToMes(@RequestBody List<TaskGrainLubricationDetailAddDTO> addDTOList) {
        return taskGrainLubricationDetailService.addListMq(addDTOList);
    }

}
