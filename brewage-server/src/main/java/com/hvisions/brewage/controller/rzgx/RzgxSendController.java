package com.hvisions.brewage.controller.rzgx;

import com.hvisions.brewage.service.rzgx.RzgxSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: RzgxSendController
 * @description:
 * @date 2025/9/5 14:42
 */
@RestController
@RequestMapping("/rzgx/send")
@Api(value = "人资共享相关接口", tags = "人资共享相关接口")
public class RzgxSendController {
    @Resource
    private RzgxSendService rzgxSendService;

    @GetMapping("/mesToSyncRzgxScheduled")
    @ApiOperation(value = "MES拉取人资共享人员排班信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "centerId", value = "中心id", required = false),
            @ApiImplicitParam(name = "locationId", value = "车间id", required = false),
            @ApiImplicitParam(name = "startDate", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true)
    })
    public String mesToSyncRzgxScheduled(@RequestParam(value = "centerId",required = false) Integer centerId,
                                         @RequestParam(value = "locationId",required = false) Integer locationId,
                                         @RequestParam("startDate") String startDate,
                                         @RequestParam("endDate") String endDate) {
        return rzgxSendService.mesToSyncRzgxScheduled(centerId,locationId,startDate, endDate);
    }

    @GetMapping("/mesToSyncRzgxTeam")
    @ApiOperation(value = "MES拉取人资共享班组信息")
    public String mesToSyncRzgxTeam() {
        return rzgxSendService.mesToSyncRzgxTeam();
    }
}
