package com.hvisions.brewage.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.base.TeamQueryDTO;
import com.hvisions.brewage.dto.base.TeamUserQueryDTO;
import com.hvisions.brewage.entity.base.Team;
import com.hvisions.brewage.entity.base.TeamUser;
import com.hvisions.brewage.service.base.TeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamController
 * @description: 班组管理controller
 * @date 2025/9/15 16:25
 */
@RestController
@RequestMapping("/base/team")
@Api(value = "班组管理相关接口", tags = "班组管理相关接口")
public class TeamController {
    @Resource
    private TeamService teamService;

    @PostMapping("/findTeamPageList")
    @ApiOperation(value = "查询班组列表")
    public Page<Team> findTeamPageList(@RequestBody @Valid TeamQueryDTO queryDTO) {
        return teamService.findTeamPageList(queryDTO);
    }

    @PostMapping("/findTeamUserPageList")
    @ApiOperation(value = "查询班组员工列表")
    public Page<TeamUser> findTeamUserPageList(@RequestBody @Valid TeamUserQueryDTO queryDTO) {
        return teamService.findTeamUserPageList(queryDTO);
    }
}
