package com.hvisions.brewage.controller.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.TaskSpreadingDetailAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingDetailQueryDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingDetailUpdateDTO;
import com.hvisions.brewage.dto.tpo.TaskSpreadingHopperTempDTO;
import com.hvisions.brewage.service.tpo.TaskSpreadingDetailService;
import com.hvisions.brewage.vo.tpo.TaskSpreadingDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailDetailController
 * @description: 摊晾任务详情Controller
 * @date 2025/7/7 14:12
 */
@RestController
@RequestMapping("/tpo/task/spreadingDetail")
@Api(value = "摊晾任务详情相关接口", tags = "摊晾任务详情相关接口")
public class TaskSpreadingDetailController {
    @Resource
    private TaskSpreadingDetailService taskSpreadingDetailService;

    @PostMapping("/findPageList")
    @ApiOperation(value = "查询摊晾任务详情列表")
    public Page<TaskSpreadingDetailVO> findPageList(@RequestBody @Valid TaskSpreadingDetailQueryDTO queryDTO) {
        return taskSpreadingDetailService.findPageList(queryDTO);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    private String addTaskSpreadingDetail(@RequestBody @Valid TaskSpreadingDetailAddDTO addDTO) {
        return taskSpreadingDetailService.add(addDTO);
    }

    @PostMapping("/updateHopperTemp")
    @ApiOperation(value = "修改斗内糟醅温度")
    private String updateHopperTemp(@RequestBody TaskSpreadingHopperTempDTO tempDTO) {
        return taskSpreadingDetailService.updateHopperTemp(tempDTO);
    }

    @PostMapping("/addList")
    @ApiOperation(value = "批量新增")
    private String addList(@RequestBody @Valid List<TaskSpreadingDetailAddDTO> addDTOList) {
        return taskSpreadingDetailService.addList(addDTOList);
    }

    @DeleteMapping("/deleteIds")
    @ApiOperation(value = "删除")
    private String deleteIds(@RequestBody List<Integer> ids) {
        return taskSpreadingDetailService.deleteIds(ids);
    }

    @PostMapping("/syncIotToMes")
    @ApiOperation(value = "IOT同步到MES")
    private String syncIotToMes(@RequestBody List<TaskSpreadingDetailAddDTO> addDTOList) {
        return taskSpreadingDetailService.addListMq(addDTOList);
    }


    @GetMapping("/updateTaskSpreadingDetailIn")
    @ApiOperation(value = "入窖任务更改窖池-改入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "iotTaskNo", value = "工序任务号(IOT任务号)", required = true),
            @ApiImplicitParam(name = "enterMaterialType", value = "入窖糟源类别", required = true),
            @ApiImplicitParam(name = "targetPitNo", value = "入窖窖号", required = true)
    })
    private String updateTaskSpreadingDetailIn(@RequestParam("iotTaskNo") String iotTaskNo, @RequestParam("enterMaterialType") String enterMaterialType, @RequestParam("targetPitNo") String targetPitNo) {
        return taskSpreadingDetailService.updateTaskSpreadingDetailIn(iotTaskNo, enterMaterialType, targetPitNo);
    }

    @GetMapping("/updateTaskSpreadingDetailOut")
    @ApiOperation(value = "入窖任务更改窖池-改出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "steamingTaskNo", value = "甑口任务号", required = true),
            @ApiImplicitParam(name = "outMaterialType", value = "出窖糟源类别", required = true),
            @ApiImplicitParam(name = "sourcePitNo", value = "出窖窖号", required = true),
            @ApiImplicitParam(name = "outPitOrder", value = "出窖窖池订单号", required = true)
    })
    private String updateTaskSpreadingDetailOut(@RequestParam("steamingTaskNo") String steamingTaskNo, @RequestParam("outMaterialType") String outMaterialType, @RequestParam("sourcePitNo") String sourcePitNo, @RequestParam("outPitOrder") String outPitOrder) {
        return taskSpreadingDetailService.updateTaskSpreadingDetailOut(steamingTaskNo, outMaterialType, sourcePitNo, outPitOrder);
    }
}
