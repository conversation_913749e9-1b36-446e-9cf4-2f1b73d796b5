package com.hvisions.brewage.controller.tpo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.service.tpo.TaskLoadingService;
import com.hvisions.brewage.vo.tpo.IdentifyVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingSourcePitNoVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingTargetPitNoVO;
import com.hvisions.brewage.vo.tpo.TaskLoadingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingController
 * @description: 入窖任务Controller
 * @date 2025/7/8 16:48
 */
@RestController
@RequestMapping("/tpo/task/loading")
@Api(value = "入窖任务相关接口", tags = "入窖任务相关接口")
public class TaskLoadingController {

    @Resource
    private TaskLoadingService taskLoadingService;

    @PostMapping("/findPageList")
    @ApiOperation(value = "查询入窖任务列表-WEB")
    public Page<TaskLoadingVO> findPageList(@RequestBody @Valid TaskLoadingQueryDTO queryDTO) {
        return taskLoadingService.findPageList(queryDTO);
    }

    @PostMapping("/findPDAPageList")
    @ApiOperation(value = "查询入窖任务列表-PDA")
    public Page<TaskLoadingVO> findPDAPageList(@RequestBody @Valid TaskLoadingPDAQueryDTO queryDTO) {
        return taskLoadingService.findPDAPageList(queryDTO);
    }

    @GetMapping("/findTargetPitNoList")
    @ApiOperation(value = "查询入窖确认入窖窖池号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "materialType", value = "糟源类别", required = true)
    })
    public List<TaskLoadingTargetPitNoVO> findTargetPitNoList(@RequestParam("materialType") String materialType) {
        return taskLoadingService.findTargetPitNoList(materialType);
    }

    @GetMapping("/findSourcePitNoList")
    @ApiOperation(value = "查询来源窖号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "入窖任务id", required = true)
    })
    public List<TaskLoadingSourcePitNoVO> findSourcePitNoList(@RequestParam("id") Integer id) {
        return taskLoadingService.findSourcePitNoList(id);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增")
    private Integer addTaskLoading(@RequestBody @Valid TaskLoadingAddDTO addDTO) {
        return taskLoadingService.add(addDTO);
    }

    @PostMapping("/addList")
    @ApiOperation(value = "批量新增")
    private String addList(@RequestBody @Valid List<TaskLoadingAddDTO> addDTOList) {
        return taskLoadingService.addList(addDTOList);
    }

    @PostMapping("/update")
    @ApiOperation(value = "入窖确认")
    private String update(@RequestBody @Valid TaskLoadingUpdateDTO updateDTO) {
        return taskLoadingService.update(updateDTO);
    }

    @PostMapping("/updatePitNo")
    @ApiOperation(value = "更改窖池号")
    private String updatePitNo(@RequestBody @Valid TaskPitBatchesUpdateDTO updateDTO) {
        return taskLoadingService.updatePitNo(updateDTO);
    }

    @GetMapping("/closeTask")
    @ApiOperation(value = "结束任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pitOrder", value = "窖池订单", required = true)
    })
    private String closeTask(@RequestParam("pitOrder") String pitOrder) {
        return taskLoadingService.closeTask(pitOrder);
    }

    @PostMapping("/identify")
    @ApiOperation(value = "入窖鉴定")
    private String identify(@RequestBody @Valid TaskLoadingIdentifyDTO identifyDTO) {
        return taskLoadingService.identify(identifyDTO);
    }

    @PostMapping("/findIdentifyList")
    @ApiOperation(value = "待入窖鉴定列表")
    private Page<IdentifyVO> findPageIdentifyList(@RequestBody @Valid TaskLoadingIdentifyQueryDTO queryDTO) {
        return taskLoadingService.findPageIdentifyList(queryDTO);
    }


    @DeleteMapping("/deleteIds")
    @ApiOperation(value = "删除")
    private String deleteIds(@RequestBody List<Integer> ids) {
        return taskLoadingService.deleteIds(ids);
    }
}
