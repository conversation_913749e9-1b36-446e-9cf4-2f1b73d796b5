package com.hvisions.brewage.controller.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.dto.base.TeamScheduledOriginalQueryDTO;
import com.hvisions.brewage.dto.base.TeamScheduledQueryDTO;
import com.hvisions.brewage.entity.base.TeamScheduled;
import com.hvisions.brewage.service.base.TeamScheduledService;
import com.hvisions.brewage.vo.base.TeamScheduledVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledController
 * @description: 排班管理Controller
 * @date 2025/9/15 17:41
 */
@RestController
@RequestMapping("/base/teamScheduled")
@Api(value = "排班管理相关接口", tags = "排班管理相关接口")
public class TeamScheduledController {
    @Resource
    private TeamScheduledService teamScheduledService;

    @PostMapping("/findTeamScheduledList")
    @ApiOperation(value = "查询排班管理列表")
    public List<TeamScheduledVO> findTeamScheduledList(@RequestBody @Valid TeamScheduledQueryDTO queryDTO) {
        return teamScheduledService.findTeamScheduledList(queryDTO);
    }

    @PostMapping("/findPageTeamScheduledOriginalList")
    @ApiOperation(value = "分页查询排班管理列表-原始数据")
    public Page<TeamScheduled> findPageTeamScheduledOriginalList(@RequestBody @Valid TeamScheduledOriginalQueryDTO queryDTO) {
        return teamScheduledService.findPageTeamScheduledOriginalList(queryDTO);
    }
}
