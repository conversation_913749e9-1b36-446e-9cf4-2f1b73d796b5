package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入窖任务详情
 */
@ApiModel(value = "入窖任务详情")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_po_task_loading_detail")
public class TaskLoadingDetail extends SysBaseNew {
    /**
     * 入窖任务主键id
     */
    @TableField(value = "task_loading_id")
    @ApiModelProperty(value = "入窖任务主键id")
    private Integer taskLoadingId;

    /**
     * 窖池号
     */
    @TableField(value = "pit_no")
    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    /**
     * 出窖糟源类别
     */
    @TableField(value = "out_material_type")
    @ApiModelProperty(value = "出窖糟源类别")
    private String outMaterialType;

    /**
     * 入窖糟源类别
     */
    @TableField(value = "enter_material_type")
    @ApiModelProperty(value = "入窖糟源类别")
    private String enterMaterialType;

    /**
     * 糟源层次
     */
    @TableField(value = "material_layer")
    @ApiModelProperty(value = "糟源层次")
    private String materialLayer;

    /**
     * 投粮类型
     */
    @TableField(value = "grain_input_type")
    @ApiModelProperty(value = "投粮类型")
    private String grainInputType;

    /**
     * 甑口任务号
     */
    @TableField(value = "steaming_task_no")
    @ApiModelProperty(value = "甑口任务号")
    private String steamingTaskNo;

    /**
     * 工序任务号(IOT任务号)
     */
    @TableField(value = "iot_task_no")
    @ApiModelProperty(value = "工序任务号(IOT任务号)")
    private String iotTaskNo;

    /**
     * 任务状态
     */
    @TableField(value = "task_status")
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    /**
     * 任务创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "任务创建时间")
    private String createdAt;

    /**
     * 任务完成时间
     */
    @TableField(value = "completed_at")
    @ApiModelProperty(value = "任务完成时间")
    private String completedAt;

    /**
     * 起窖窖号
     */
    @TableField(value = "source_pit_no")
    @ApiModelProperty(value = "起窖窖号")
    private String sourcePitNo;

    /**
     * 入窖窖号
     */
    @TableField(value = "target_pit_no")
    @ApiModelProperty(value = "入窖窖号")
    private String targetPitNo;

    /**
     * 摊晾机编号
     */
    @TableField(value = "spreader_no")
    @ApiModelProperty(value = "摊晾机编号")
    private String spreaderNo;

    /**
     * 摊晾到入窖时长
     */
    @TableField(value = "spread_to_pit_duration")
    @ApiModelProperty(value = "摊晾到入窖时长")
    private String spreadToPitDuration;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    /**
     * 地温(℃)
     */
    @TableField(value = "ground_temp")
    @ApiModelProperty(value = "地温(℃)")
    private BigDecimal groundTemp;

    /**
     * 来源(IOT)
     */
    @TableField(value = "data_source")
    @ApiModelProperty(value = "来源(IOT)")
    private String dataSource;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注 ")
    private String remarks;

    /**
     * 是否入窖确认(0：否 1：是)
     */
    @TableField(value = "is_pit_confirmed")
    @ApiModelProperty(value = "是否入窖确认(0：否 1：是)")
    private Boolean isPitConfirmed;

    /**
     * 是否已鉴定(0:否 1：是)
     */
    @TableField(value = "is_identify")
    @ApiModelProperty(value = "是否已鉴定(0:否 1：是)")
    private Boolean isIdentify;

    /**
     * 确认人
     */
    @TableField(value = "confirmed_by_id")
    @ApiModelProperty(value = "确认人")
    private Integer confirmedById;

    /**
     * 确认时间
     */
    @TableField(value = "confirmation_time")
    @ApiModelProperty(value = "确认时间")
    private Date confirmationTime;
}