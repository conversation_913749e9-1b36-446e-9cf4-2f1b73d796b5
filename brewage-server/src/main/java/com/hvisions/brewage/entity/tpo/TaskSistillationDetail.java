package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.dto.tpo.TaskSistillationDetailParameterAddDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskSistillationDetail
*@Date：2025/7/9  10:27
*@Filename：TaskSistillationDetail
*/
/**
 * 蒸馏任务明细
 */
@ApiModel(description="蒸馏任务明细")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_sistillation_detail")
public class TaskSistillationDetail extends IotTaskDetail{

    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_sistillation_id")
    @ApiModelProperty(value="蒸馏任务id")
    private Integer taskSistillationId;

    @TableField(value = "steam_valve_opening")
    @ApiModelProperty(value="蒸汽阀门开度")
    private String steamValveOpening;

    @TableField(value = "total_steam_flow_rate")
    @ApiModelProperty(value="总蒸汽流量")
    private String totalSteamFlowRate;

    @TableField(value = "steam_flow_rate")
    @ApiModelProperty(value="单甑蒸汽流量")
    private String steamFlowRate;

    @TableField(value = "steam_pressure")
    @ApiModelProperty(value="单甑蒸汽压力")
    private String steamPressure;

    @TableField(value = "steam_consumption")
    @ApiModelProperty(value="单甑蒸汽用量")
    private String steamConsumption;

    @TableField(value = "circulating_water_usage")
    @ApiModelProperty(value="循环水单甑使用量")
    private String circulatingWaterUsage;

//    @TableField(value = "circulating_water_return_temperature")
//    @ApiModelProperty(value="循环水回水温度")
//    private String circulatingWaterReturnTemperature;
//
//    @TableField(value = "bottom_pot water_temperature")
//    @ApiModelProperty(value="底锅水温度")
//    private String bottomPotWaterTemperature;

    @TableField(value = "clear_water_setting_value")
    @ApiModelProperty(value="清水设定值")
    private String clearWaterSettingValue;

    @TableField(value = "ysage_of_circulating_water")
    @ApiModelProperty(value="清水单甑使用量")
    private String ysageOfCirculatingWater;

    @TableField(value = "yellow_water_set_value")
    @ApiModelProperty(value="黄水设定值")
    private String yellowWaterSetValue;

    @TableField(value = "yellow_water_usage")
    @ApiModelProperty(value="黄水单甑使用量")
    private String yellowWaterUsage;

    @TableField(value = "yellow_water_alcohol_content")
    @ApiModelProperty(value="黄水酒度")
    private String yellowWaterAlcoholContent;

    @TableField(value = "tail_wine_setting_value")
    @ApiModelProperty(value="尾酒设定值")
    private String tailWineSettingValue;

    @TableField(value = "tail_wine_usage")
    @ApiModelProperty(value="尾酒单甑使用量")
    private String tailWineUsage;

    @TableField(value = "tail_wine_alcohol_content")
    @ApiModelProperty(value="尾酒酒度")
    private String tailWineAlcoholContent;

    @TableField(value = "hj1_set_value")
    @ApiModelProperty(value="HJ1设定值")
    private String hj1SetValue;

    @TableField(value = "hj1_single_container_usage")
    @ApiModelProperty(value="HJ1单甑使用量")
    private String hj1SingleContainerUsage;

    @TableField(value = "hj1_alcohol_content")
    @ApiModelProperty(value="HJ1酒度")
    private String hj1AlcoholContent;

    @TableField(value = "hj1_single_cellar_usage")
    @ApiModelProperty(value="HJ1单窖使用量")
    private String hj1SingleCellarUsage;

    @TableField(value = "total_amount_of_bottom_pot_water")
    @ApiModelProperty(value="单甑底锅水总量")
    private String totalAmountOfBottomPotWater;

    @TableField(value = "bottom_pot_water_level")
    @ApiModelProperty(value="单甑底锅水液位（蒸馏前对应的值）")
    private String bottomPotWaterLevel;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "flowing_wine_start_time")
    @ApiModelProperty(value="流酒开始时间")
    private String flowingWineStartTime;

    @TableField(value = "opening_time")
    @ApiModelProperty(value="开盖时间")
    private String openingTime;

    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}