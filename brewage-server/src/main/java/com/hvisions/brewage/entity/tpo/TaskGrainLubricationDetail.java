package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskGrainLubricationDetail
*@Date：2025/7/9  10:25
*@Filename：TaskGrainLubricationDetail
*/
/**
 * 润粮任务明细
 */
@ApiModel(description="润粮任务明细")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_grain_lubrication_detail")
public class TaskGrainLubricationDetail extends IotTaskDetail{
    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_grain_lubrication_id")
    @ApiModelProperty(value="润粮任务id")
    private Integer taskGrainLubricationId;

    @TableField(value = "runliang_machine_no")
    @ApiModelProperty(value="润粮机编号")
    private String runliangMachineNo;

    @TableField(value = "runliang_water_temperature")
    @ApiModelProperty(value="润粮水温度")
    private String runliangWaterTemperature;

    @TableField(value = "runliang_water_weight")
    @ApiModelProperty(value="润粮水重量")
    private String runliangWaterWeight;

    @TableField(value = "runliang_duration")
    @ApiModelProperty(value="润粮时长（拌糟开始时间-搅拌开始时间）")
    private String runliangDuration;

    @TableField(value = "stir_duration")
    @ApiModelProperty(value="搅拌时长（拌料结束时间-搅拌开始时间）")
    private String stirDuration;

    @TableField(value = "waiting_materials_duration")
    @ApiModelProperty(value="待料时长（拌糟开始时间-润粮机出料结束时间）")
    private String waitingMaterialsDuration;

    @TableField(value = "sorghum_fragmentation_setting_value")
    @ApiModelProperty(value="高粱破碎度设定值")
    private String sorghumFragmentationSettingValue;

    @TableField(value = "sorghum_fragmentation_actual_value")
    @ApiModelProperty(value="高粱破碎度实际值")
    private String sorghumFragmentationActualValue;

    @TableField(value = "single_container_sorghum_setting_value")
    @ApiModelProperty(value="单甑高粱设定值")
    private String singleContainerSorghumSettingValue;

    @TableField(value = "single_container_sorghum_actual_value")
    @ApiModelProperty(value="单甑高粱实际值")
    private String singleContainerSorghumActualValue;

    @TableField(value = "runliang_circulating_water_consumption")
    @ApiModelProperty(value="润粮仓循环水用量")
    private String runliangCirculatingWaterConsumption;

    @TableField(value = "runliang_circulating_water_cooling_time")
    @ApiModelProperty(value="润粮仓循环水冷却时间")
    private String runliangCirculatingWaterCoolingTime;

    @TableField(value = "runliang_time")
    @ApiModelProperty(value="润粮时间")
    private String runliangTime;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "material_name")
    @ApiModelProperty(value="物料名称")
    private String materialName;

    @TableField(value = "zeng_no")
    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @TableField(value = "run_grain_start_time")
    @ApiModelProperty(value = "润粮开始时间")
    private String runGrainStartTime;

    @TableField(value = "run_grain_end_time")
    @ApiModelProperty(value = "润粮结束时间")
    private String runGrainEndTime;

    @TableField(value = "stir_start_time")
    @ApiModelProperty(value = "搅拌开始时间")
    private String stirStartTime;

    @TableField(value = "stir_end_time")
    @ApiModelProperty(value = "搅拌结束时间")
    private String stirEndTime;

    @TableField(value = "awaiting_materials_start_time")
    @ApiModelProperty(value = "待料开始时间")
    private String awaitingMaterialsStartTime;

    @TableField(value = "awaiting_materials_end_time")
    @ApiModelProperty(value = "待料结束时间")
    private String awaitingMaterialsEndTime;

    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @TableField(value = "material_code")
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @TableField(value = "batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

}