package com.hvisions.brewage.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 班组管理-员工
 */
@ApiModel(value = "班组管理-员工")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "base_team_user")
public class TeamUser extends SysBaseNew {
    /**
     * 班组id
     */
    @TableField(value = "team_id")
    @ApiModelProperty(value = "班组id")
    private Integer teamId;

    /**
     * 员工编码
     */
    @TableField(value = "staff_code")
    @ApiModelProperty(value = "员工编码")
    private String staffCode;

    /**
     * 员工名称
     */
    @TableField(value = "staff_name")
    @ApiModelProperty(value = "员工名称")
    private String staffName;
}