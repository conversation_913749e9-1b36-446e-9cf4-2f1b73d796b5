package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 窖池养护任务
 */
@ApiModel(value = "窖池养护任务")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_po_task_maintain")
public class TaskMaintain extends SysBaseNew {
    /**
     * 任务号
     */
    @TableField(value = "task_no")
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 中心id
     */
    @TableField(value = "center_id")
    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    /**
     * 车间id
     */
    @TableField(value = "location_id")
    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    /**
     * 窖池号
     */
    @TableField(value = "pit_no")
    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    /**
     * 窖池订单
     */
    @TableField(value = "pit_order")
    @ApiModelProperty(value = "窖池订单")
    private String pitOrder;

    /**
     * 空窖时间
     */
    @TableField(value = "empty_time")
    @ApiModelProperty(value = "空窖时间")
    private Date emptyTime;

    /**
     * 养护预计开始时间
     */
    @TableField(value = "maintain_expect_start_time")
    @ApiModelProperty(value = "养护预计开始时间")
    private Date maintainExpectStartTime;

    /**
     * 养护开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "养护开始时间")
    private Date startTime;

    /**
     * 养护结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "养护结束时间")
    private Date endTime;

    /**
     * 状态(待执行、执行中、已完成)
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态(待执行、执行中、已完成)")
    private String status;

    /**
     * 子任务状态(未开始、执行中)
     */
    @TableField(value = "subtask_status")
    @ApiModelProperty(value = "子任务状态(未开始、执行中)")
    private String subtaskStatus;
}