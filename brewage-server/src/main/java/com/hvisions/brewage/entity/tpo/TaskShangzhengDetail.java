package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskShangzhengDetail
*@Date：2025/7/9  10:26
*@Filename：TaskShangzhengDetail
*/
/**
 * 上甑任务明细
 */
@ApiModel(description="上甑任务明细")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_shangzheng_detail")
public class TaskShangzhengDetail extends IotTaskDetail{
    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_shangzheng_id")
    @ApiModelProperty(value="上甑任务id")
    private Integer taskShangzhengId;

    @TableField(value = "zeng_no")
    @ApiModelProperty(value="甑号")
    private String zengNo;

    @TableField(value = "preparation_time")
    @ApiModelProperty(value="糟醅备料时长")
    private String preparationTime;

    @TableField(value = "moisture_content")
    @ApiModelProperty(value="上甑糟水分含量")
    private String moistureContent;

    @TableField(value = "duration_of_steaming")
    @ApiModelProperty(value="上甑时长（盒盖时间-上甑喂料机启动下料时间）")
    private String durationOfSteaming;

    @TableField(value = "steam_pressure_depth")
    @ApiModelProperty(value="压汽深度")
    private String steamPressureDepth;

    @TableField(value = "volume_of_fermented_grains")
    @ApiModelProperty(value="糟醅体积")
    private String volumeOfFermentedGrains;

    @TableField(value = "shangzheng_layer")
    @ApiModelProperty(value="上甑层数")
    private String shangzhengLayer;

    @TableField(value = "raise_the_height")
    @ApiModelProperty(value="每层提高高度")
    private Double raiseTheHeight;

    @TableField(value = "robot_rotation_speed")
    @ApiModelProperty(value="上甑机器人转速")
    private Double robotRotationSpeed;

    @TableField(value = "delivery_duration")
    @ApiModelProperty(value="上甑前输送时长（上甑喂料机接料时间-二维拌合机下料时间）")
    private String deliveryDuration;

    @TableField(value = "feeding_machine_duration")
    @ApiModelProperty(value="自动行车转运拌合后糟醅到上甑喂料机时长")
    private String feedingMachineDuration;
    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "take_it_start_time")
    @ApiModelProperty(value="接糟开始时间")
    private String takeItStartTime;

    @TableField(value = "shangzheng_start_time")
    @ApiModelProperty(value = "上甑开始时间")
    private String shangzhengStartTime;

    @TableField(value = "shangzheng_end_time")
    @ApiModelProperty(value = "上甑结束时间")
    private String shangzhengEndTime;

    @TableField(value = "steam_valve_opening")
    @ApiModelProperty(value="蒸汽阀门开度")
    private String steamValveOpening;

    @TableField(value = "box_cover_time")
    @ApiModelProperty(value = "盒盖时间")
    private String boxCoverTime;

    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}