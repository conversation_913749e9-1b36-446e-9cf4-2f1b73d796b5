package com.hvisions.brewage.entity.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 排班管理
 */
@ApiModel(value = "排班管理")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "base_team_scheduled")
public class TeamScheduled extends SysBaseNew {
    /**
     * 中心编码
     */
    @TableField(value = "center_code")
    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    /**
     * 中心名称
     */
    @TableField(value = "center_name")
    @ApiModelProperty(value = "中心名称")
    private String centerName;

    /**
     * 车间编码
     */
    @TableField(value = "workshop_code")
    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    /**
     * 车间名称
     */
    @TableField(value = "workshop_name")
    @ApiModelProperty(value = "车间名称")
    private String workshopName;

    /**
     * 班组编码
     */
    @TableField(value = "group_code")
    @ApiModelProperty(value = "班组编码")
    private String groupCode;

    /**
     * 班组名称
     */
    @TableField(value = "group_name")
    @ApiModelProperty(value = "班组名称")
    private String groupName;

    /**
     * 班次编码
     */
    @TableField(value = "shift_id")
    @ApiModelProperty(value = "班次编码")
    private String shiftId;

    /**
     * 班次名称
     */
    @TableField(value = "shift_name")
    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    /**
     * 班次日期
     */
    @TableField(value = "calendar_date")
    @ApiModelProperty(value = "班次日期")
    private Date calendarDate;

    /**
     * 班次开始时间
     */
    @TableField(value = "shiftStart_time")
    @ApiModelProperty(value = "班次开始时间")
    @JSONField(format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date shiftstartTime;

    /**
     * 班次结束时间
     */
    @TableField(value = "shiftEnd_time")
    @ApiModelProperty(value = "班次结束时间")
    @JSONField(format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date shiftendTime;

    /**
     * 员工编码
     */
    @TableField(value = "staff_code")
    @ApiModelProperty(value = "员工编码")
    private String staffCode;

    /**
     * 员工名称
     */
    @TableField(value = "staff_name")
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 应出勤时长(H)
     */
    @TableField(value = "scheduled_work_hours")
    @ApiModelProperty(value = "应出勤时长(H)")
    private BigDecimal scheduledWorkHours;

    /**
     * 实际出勤时长(H)
     */
    @TableField(value = "actual_work_hours")
    @ApiModelProperty(value = "实际出勤时长(H)")
    private BigDecimal actualWorkHours;
}