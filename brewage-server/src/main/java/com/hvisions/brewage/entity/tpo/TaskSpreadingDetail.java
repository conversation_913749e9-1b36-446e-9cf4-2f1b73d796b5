package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 摊晾任务详情
 */
@ApiModel(value = "摊晾任务详情")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_po_task_spreading_detail")
public class TaskSpreadingDetail extends SysBaseNew {
    /**
     * 摊晾任务主键id
     */
    @TableField(value = "task_spreading_id")
    @ApiModelProperty(value = "摊晾任务主键id")
    private Integer taskSpreadingId;

    /**
     * 窖池号
     */
    @TableField(value = "pit_no")
    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    /**
     * 出窖糟源类别
     */
    @TableField(value = "out_material_type")
    @ApiModelProperty(value = "出窖糟源类别")
    private String outMaterialType;

    /**
     * 入窖糟源类别
     */
    @TableField(value = "enter_material_type")
    @ApiModelProperty(value = "入窖糟源类别")
    private String enterMaterialType;

    /**
     * 糟源层次
     */
    @TableField(value = "material_layer")
    @ApiModelProperty(value = "糟源层次")
    private String materialLayer;

    /**
     * 投粮类型
     */
    @TableField(value = "grain_input_type")
    @ApiModelProperty(value = "投粮类型")
    private String grainInputType;

    /**
     * 甑口任务号
     */
    @TableField(value = "steaming_task_no")
    @ApiModelProperty(value = "甑口任务号")
    private String steamingTaskNo;

    /**
     * 工序任务号(IOT任务号)
     */
    @TableField(value = "iot_task_no")
    @ApiModelProperty(value = "工序任务号(IOT任务号)")
    private String iotTaskNo;

    /**
     * 任务状态
     */
    @TableField(value = "task_status")
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    /**
     * 任务创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "任务创建时间")
    private String createdAt;

    /**
     * 任务完成时间
     */
    @TableField(value = "completed_at")
    @ApiModelProperty(value = "任务完成时间")
    private String completedAt;

    /**
     * 起窖窖号
     */
    @TableField(value = "source_pit_no")
    @ApiModelProperty(value = "起窖窖号")
    private String sourcePitNo;

    /**
     * 入窖窖号
     */
    @TableField(value = "target_pit_no")
    @ApiModelProperty(value = "入窖窖号")
    private String targetPitNo;

    /**
     * 摊晾机编号
     */
    @TableField(value = "spreader_no")
    @ApiModelProperty(value = "摊晾机编号")
    private String spreaderNo;

    /**
     * 地温
     */
    @TableField(value = "floor_temp")
    @ApiModelProperty(value = "地温")
    private String floorTemp;

    /**
     * 湿度
     */
    @TableField(value = "humidity")
    @ApiModelProperty(value = "湿度")
    private String humidity;

    /**
     * 摊晾开始时间
     */
    @TableField(value = "spreading_start_time")
    @ApiModelProperty(value = "摊晾开始时间")
    private String spreadingStartTime;

    /**
     * 摊晾结束时间
     */
    @TableField(value = "spreading_end_time")
    @ApiModelProperty(value = "摊晾结束时间")
    private String spreadingEndTime;

    /**
     * 单甑加曲开始时间
     */
    @TableField(value = "koji_addition_start_time")
    @ApiModelProperty(value = "单甑加曲开始时间")
    private String kojiAdditionStartTime;

    /**
     * 单甑加曲结束时间
     */
    @TableField(value = "koji_addition_end_time")
    @ApiModelProperty(value = "单甑加曲结束时间")
    private String kojiAdditionEndTime;

    /**
     * 摊晾斗内糟醅温度
     */
    @TableField(value = "hopper_temp")
    @ApiModelProperty(value = "摊晾斗内糟醅温度")
    private String hopperTemp;

    /**
     * 摊晾斗内糟醅温度填写时间
     */
    @TableField(value = "hopper_temp_time")
    @ApiModelProperty(value = "摊晾斗内糟醅温度填写时间")
    private String hopperTempTime;

    /**
     * 摊晾斗内糟醅温度填写记录人id
     */
    @TableField(value = "hopper_temp_user_id")
    @ApiModelProperty(value = "摊晾斗内糟醅温度填写记录人id")
    private Integer hopperTempUserId;

    /**
     * 摊晾斗内糟醅温度填写记录人
     */
    @TableField(value = "hopper_temp_user")
    @ApiModelProperty(value = "摊晾斗内糟醅温度填写记录人")
    private String hopperTempUser;

    /**
     * 摊晾喂料机转速
     */
    @TableField(value = "feeder_speed")
    @ApiModelProperty(value = "摊晾喂料机转速")
    private String feederSpeed;

    /**
     * 摊晾机输送链板转速
     */
    @TableField(value = "conveyor_speed")
    @ApiModelProperty(value = "摊晾机输送链板转速")
    private String conveyorSpeed;

    /**
     * 风机使用数量
     */
    @TableField(value = "fan_quantity")
    @ApiModelProperty(value = "风机使用数量")
    private String fanQuantity;

    /**
     * 加曲机转速
     */
    @TableField(value = "yeast_feeder_speed")
    @ApiModelProperty(value = "加曲机转速")
    private String yeastFeederSpeed;

    /**
     * 加曲前重量
     */
    @TableField(value = "weight_before_koji")
    @ApiModelProperty(value = "加曲前重量")
    private String weightBeforeKoji;

    /**
     * 加曲后重量
     */
    @TableField(value = "weight_after_koji")
    @ApiModelProperty(value = "加曲后重量")
    private String weightAfterKoji;

    /**
     * 大曲破碎度
     */
    @TableField(value = "yeast_fineness")
    @ApiModelProperty(value = "大曲破碎度")
    private String yeastFineness;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    /**
     * 来源(IOT)
     */
    @TableField(value = "data_source")
    @ApiModelProperty(value = "来源(IOT)")
    private String dataSource;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注 ")
    private String remarks;

    /**
     * 摊晾时长
     */
    @TableField(value = "duration")
    @ApiModelProperty(value = "摊晾时长")
    private String duration;

    /**
     * 单甑加曲时长
     */
    @TableField(value = "yeast_adding_time")
    @ApiModelProperty(value = "单甑加曲时长")
    private String yeastAddingTime;

    /**
     * 单甑加曲量
     */
    @TableField(value = "yeast_amount")
    @ApiModelProperty(value = "单甑加曲量")
    private String yeastAmount;

    /**
     * 出入窖状态
     */
    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}