package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskWinePicking
*@Date：2025/7/9  10:28
*@Filename：TaskWinePicking
*/
/**
 * 摘酒任务
 */
@ApiModel(description="摘酒任务")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_wine_picking")
public class TaskWinePicking extends SysBaseNew {
    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_no")
    @ApiModelProperty(value="摘酒任务号")
    private String taskNo;

    @TableField(value = "iot_no")
    @ApiModelProperty(value="IOT任务号")
    private String iotNo;

    @TableField(value = "center_id")
    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @TableField(value = "location_id")
    @ApiModelProperty(value="车间id")
    private Integer locationId;

    @TableField(value = "pit_no")
    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @TableField(value = "pit_order")
    @ApiModelProperty(value="窖池订单")
    private String pitOrder;

    @TableField(value = "state")
    @ApiModelProperty(value="任务状态：1、待执行、2、执行中、3、已完成")
    private Integer state;

    @TableField(value = "vinasse_name")
    @ApiModelProperty(value="糟源类型")
    private String vinasseName;
    /**
     * 任务类型(自动任务)
     */
    @TableField(value = "task_type")
    @ApiModelProperty(value="任务类型(自动任务)")
    private String taskType;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value="开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value="结束时间")
    private Date endTime;

    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}