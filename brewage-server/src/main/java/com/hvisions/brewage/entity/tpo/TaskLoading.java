package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 入窖任务
 */
@ApiModel(value = "入窖任务")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_po_task_loading")
public class TaskLoading extends SysBaseNew {
    /**
     * 任务号
     */
    @TableField(value = "task_no")
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 中心id
     */
    @TableField(value = "center_id")
    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    /**
     * 车间id
     */
    @TableField(value = "location_id")
    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务类型(自动任务)
     */
    @TableField(value = "task_type")
    @ApiModelProperty(value = "任务类型(自动任务)")
    private String taskType;

    /**
     * 任务状态(待执行、执行中、已完成、已终止)
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "任务状态(待执行、执行中、已完成、已终止)")
    private String status;

    /**
     * 窖池号
     */
    @TableField(value = "pit_no")
    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    /**
     * 糟源类别
     */
    @TableField(value = "material_type")
    @ApiModelProperty(value = "糟源类别")
    private String materialType;

    /**
     * 窖池订单号
     */
    @TableField(value = "pit_order")
    @ApiModelProperty(value = "窖池订单号")
    private String pitOrder;

    /**
     * 空窖时长(H)
     */
    @TableField(value = "empty_duration")
    @ApiModelProperty(value = "空窖时长(H)")
    private BigDecimal emptyDuration;

    /**
     * 地温(℃)
     */
    @TableField(value = "ground_temp")
    @ApiModelProperty(value = "地温(℃)")
    private BigDecimal groundTemp;

    /**
     * 入窖温度
     */
    @TableField(value = "loading_temperature")
    @ApiModelProperty(value = "入窖温度")
    private String loadingTemperature;

    /**
     * 稀踩情况
     */
    @TableField(value = "loose_packing_status")
    @ApiModelProperty(value = "稀踩情况")
    private String loosePackingStatus;

    /**
     * 密踩情况
     */
    @TableField(value = "dense_packing_status")
    @ApiModelProperty(value = "密踩情况")
    private String densePackingStatus;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}