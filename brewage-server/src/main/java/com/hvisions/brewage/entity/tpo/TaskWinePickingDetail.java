package com.hvisions.brewage.entity.tpo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
*@Author：JIA WANG
*@Package：com.hvisions.brewage.entity.tpo
*@Project：酿酒二期
*@name：TaskWinePickingDetail
*@Date：2025/7/9  10:28
*@Filename：TaskWinePickingDetail
*/
/**
 * 摘酒任务明细
 */
@ApiModel(description="摘酒任务明细")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "t_po_task_wine_picking_detail")
public class TaskWinePickingDetail extends IotTaskDetail{
    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_wine_picking_id")
    @ApiModelProperty(value="摘酒任务id")
    private Integer taskWinePickingId;

    @TableField(value = "weight_flowing_wine_one")
    @ApiModelProperty(value="一段流酒重量")
    private String weightFlowingWineOne;

    @TableField(value = "weight_flowing_wine_two")
    @ApiModelProperty(value="二段流酒重量")
    private String weightFlowingWineTwo;

    @TableField(value = "weight_flowing_wine_three")
    @ApiModelProperty(value="三段流酒重量")
    private String weightFlowingWineThree;

    @TableField(value = "weight_flowing_wine_four")
    @ApiModelProperty(value="四段流酒重量")
    private String weightFlowingWineFour;

    @TableField(value = "alcohol_content_one")
    @ApiModelProperty(value="一段酒精度")
    private String alcoholContentOne;

    @TableField(value = "alcohol_content_two")
    @ApiModelProperty(value="二段酒精度")
    private String alcoholContentTwo;

    @TableField(value = "alcohol_content_three")
    @ApiModelProperty(value="三段酒精度")
    private String alcoholContentThree;

    @TableField(value = "alcohol_content_four")
    @ApiModelProperty(value="四段酒精度")
    private String alcoholContentFour;

    @TableField(value = "proportion_flowing_alcohol_one")
    @ApiModelProperty(value="一段流酒占比")
    private String proportionFlowingAlcoholOne;

    @TableField(value = "proportion_flowing_alcohol_two")
    @ApiModelProperty(value="二段流酒占比")
    private String proportionFlowingAlcoholTwo;

    @TableField(value = "proportion_flowing_alcohol_three")
    @ApiModelProperty(value="三段流酒占比")
    private String proportionFlowingAlcoholThree;

    @TableField(value = "proportion_flowing_alcohol_four")
    @ApiModelProperty(value="四段流酒占比")
    private String proportionFlowingAlcoholFour;

    @TableField(value = "flowing_wine_duration")
    @ApiModelProperty(value="流酒时长")
    private String flowingWineDuration;

    @TableField(value = "duration_one")
    @ApiModelProperty(value="一段时长")
    private String durationOne;

    @TableField(value = "duration_two")
    @ApiModelProperty(value="二段时长")
    private String durationTwo;

    @TableField(value = "duration_three")
    @ApiModelProperty(value="三段时长")
    private String durationThree;

    @TableField(value = "duration_four")
    @ApiModelProperty(value="四段时长")
    private String durationFour;

    @TableField(value = "flowing_wine_temperature")
    @ApiModelProperty(value="流酒温度")
    private String flowingWineTemperature;

    @TableField(value = "flowing_wine_speed")
    @ApiModelProperty(value="流酒速度")
    private String flowingWineSpeed;

    @TableField(value = "cover_plate_flowing_wine_duration")
    @ApiModelProperty(value="盖盘到流酒时长")
    private String coverPlateFlowingWineDuration;
    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "cover_time")
    @ApiModelProperty(value="盖盘时间")
    private String coverTime;

    @TableField(value = "one_stage_wine_start_time")
    @ApiModelProperty(value="一段酒开始时间")
    private String oneStageWineStartTime;

    @TableField(value = "two_stage_wine_start_time")
    @ApiModelProperty(value="二段酒开始时间")
    private String twoStageWineStartTime;

    @TableField(value = "three_stage_wine_start_time")
    @ApiModelProperty(value="三段酒开始时间")
    private String threeStageWineStartTime;

    @TableField(value = "cocktail_start_time")
    @ApiModelProperty(value="尾酒开始时间")
    private String cocktailStartTime;

    @TableField(value = "cocktail_end_time")
    @ApiModelProperty(value = "尾酒结束时间")
    private String cocktailEndTime;

    @TableField(value = "cellar_status")
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}