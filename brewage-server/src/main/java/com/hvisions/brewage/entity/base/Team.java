package com.hvisions.brewage.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 班组管理
    */
@ApiModel(value="班组管理")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "base_team")
public class Team extends SysBaseNew {
    /**
     * 班组编码
     */
    @TableField(value = "group_code")
    @ApiModelProperty(value="班组编码")
    private String groupCode;

    /**
     * 班组名称
     */
    @TableField(value = "group_name")
    @ApiModelProperty(value="班组名称")
    private String groupName;

    /**
     * 开始时间
     */
    @TableField(value = "start_date")
    @ApiModelProperty(value="开始时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField(value = "end_date")
    @ApiModelProperty(value="结束时间")
    private Date endDate;
}