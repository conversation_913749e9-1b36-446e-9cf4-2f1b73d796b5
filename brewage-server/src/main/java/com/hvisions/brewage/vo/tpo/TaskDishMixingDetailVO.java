package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.entity.tpo.IotTaskDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingDetailVO
 * @description: 拌糟任务详情查询返回VO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="拌糟任务详情查询返回VO")
@Data
public class TaskDishMixingDetailVO extends IotTaskDetail {

    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_dish_mixing_id")
    @ApiModelProperty(value="拌糟任务id")
    private Integer taskDishMixingId;

    @TableField(value = "single_container_rice_husk_setting_value")
    @ApiModelProperty(value="单甑稻壳设定值")
    private String singleContainerRiceHuskSettingValue;

    @TableField(value = "single_container_rice_husk_actual_value")
    @ApiModelProperty(value="单甑稻壳实际值")
    private String singleContainerRiceHuskActualValue;

    @TableField(value = "transport_bran_start_time")
    @ApiModelProperty(value="输糠绞龙开始时间")
    private Date transportBranStartTime;

    @TableField(value = "transport_bran_end_time")
    @ApiModelProperty(value="输糠绞龙结束时间")
    private Date transportBranEndTime;

    @TableField(value = "mixing_duration")
    @ApiModelProperty(value="拌合时长（二维拌合机下料时间-料斗开门机构打开时间）")
    private String mixingDuration;

    @TableField(value = "tilt_angle_of_mixer")
    @ApiModelProperty(value="拌合机倾斜角度")
    private String tiltAngleOfMixer;

    @TableField(value = "mixing_speed")
    @ApiModelProperty(value="拌合转速")
    private Double mixingSpeed;

    @TableField(value = "grain_addition_duration")
    @ApiModelProperty(value="加粮时长")
    private String grainAdditionDuration;

    @TableField(value = "duration_of_adding_dregs")
    @ApiModelProperty(value="加糟时长")
    private String durationOfAddingDregs;

    @TableField(value = "duration_of_adding_cooked_rice_husks")
    @ApiModelProperty(value="加熟稻壳时长")
    private String durationOfAddingCookedRiceHusks;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "zeng_no")
    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @TableField(value = "add_grain_start_time")
    @ApiModelProperty(value = "加粮开始时间")
    private String addGrainStartTime;

    @TableField(value = "add_grain_end_time")
    @ApiModelProperty(value = "加粮结束时间")
    private String addGrainEndTime;

    @TableField(value = "add_dregs_start_time")
    @ApiModelProperty(value = "加糟开始时间")
    private String addDregsStartTime;

    @TableField(value = "add_dregs_end_time")
    @ApiModelProperty(value = "加糟结束时间")
    private String addDregsEndTime;

    @TableField(value = "add_cooked_rice_husks_start_time")
    @ApiModelProperty(value = "加熟稻壳开始时间")
    private String addCookedRiceHusksStartTime;

    @TableField(value = "add_cooked_rice_husks_end_time")
    @ApiModelProperty(value = "加熟稻壳结束时间")
    private String addCookedRiceHusksEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

}
