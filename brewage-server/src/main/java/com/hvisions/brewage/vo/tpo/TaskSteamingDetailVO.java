package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.dto.tpo.TaskSistillationDetailParameterAddDTO;
import com.hvisions.brewage.dto.tpo.TaskSteamingDetailParameterAddDTO;
import com.hvisions.brewage.entity.tpo.IotTaskDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSteamingDetailVO
 * @description: 出甑任务详情查询返回VO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="出甑任务详情查询返回VO")
@Data
public class TaskSteamingDetailVO extends IotTaskDetail {

    @TableField(value = "task_steaming_id")
    @ApiModelProperty(value="出甑任务id")
    private Integer taskSteamingId;

    @TableField(value = "steaming_time")
    @ApiModelProperty(value="蒸粮时长")
    private String steamingTime;

    @TableField(value = "steam_usage_duration")
    @ApiModelProperty(value="用汽时长")
    private String steamUsageDuration;

    @TableField(value = "measuring_water_usage")
    @ApiModelProperty(value="量水单甑使用量")
    private String measuringWaterUsage;

    @TableField(value = "one_measure_water_weight")
    @ApiModelProperty(value="第一次量水重量")
    private String oneMeasureWaterWeight;

    @TableField(value = "two_measure_water_weight")
    @ApiModelProperty(value="第二次量水重量")
    private String twoMeasureWaterWeight;

    @TableField(value = "one_measure_water_temperature")
    @ApiModelProperty(value="第一次量水温度")
    private String oneMeasureWaterTemperature;

    @TableField(value = "two_measure_water_temperature")
    @ApiModelProperty(value="第二次量水温度")
    private String twoMeasureWaterTemperature;

//    @TableField(value = "temperature_measuring_bucket")
//    @ApiModelProperty(value="量水桶量水温度")
//    private String temperatureMeasuringBucket;

    @TableField(value = "measure_water_duration")
    @ApiModelProperty(value="打量水时长")
    private String measureWaterDuration;

    @TableField(value = "measure_water_interval_duration")
    @ApiModelProperty(value="打量水间隔时长")
    private String measureWaterIntervalDuration;

    @TableField(value = "bottom_pot_water_emission_number")
    @ApiModelProperty(value="底锅水排放次数")
    private String bottomPotWaterEmissionNumber;

    @TableField(value = "bottom_pot_water_discharge")
    @ApiModelProperty(value="底锅水单甑排放量")
    private String bottomPotWaterDischarge;

    @TableField(value = "bottom_pot_water_total_discharge")
    @ApiModelProperty(value="底锅水总排放量")
    private String bottomPotWaterTotalDischarge;

    @TableField(value = "bottom_pot_water_level")
    @ApiModelProperty(value="底锅水液位")
    private String bottomPotWaterLevel;

    @TableField(value = "huizheng_run_duration")
    @ApiModelProperty(value="自动行车从出甑到回甑运行时长")
    private String huizhengRunDuration;

    @TableField(value = "extraction_time")
    @ApiModelProperty(value="出甑时间")
    private String extractionTime;

    @TableField(value = "one_measure_water_start_time")
    @ApiModelProperty(value="量水1开始时间")
    private String oneMeasureWaterStartTime;

    @TableField(value = "one_measure_water_end_time")
    @ApiModelProperty(value="量水1结束时间")
    private String oneMeasureWaterEndTime;

    @TableField(value = "two_measure_water_start_time")
    @ApiModelProperty(value="量水2开始时间")
    private String twoMeasureWaterStartTime;

    @TableField(value = "two_measure_water_end_time")
    @ApiModelProperty(value = "量水2结束时间")
    private String twoMeasureWaterEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value = "量水桶量水温度")
    private List<TaskSteamingDetailParameterAddDTO> temperatureMeasuringBucketTemp;

}
