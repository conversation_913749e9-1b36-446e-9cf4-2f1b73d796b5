package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSistillationVO
 * @description: 蒸馏任务查询返回VO
 * @date 2025/7/7 14:15
 */
@ApiModel(value="蒸馏任务查询返回VO")
@Data
public class TaskSistillationVO {

    @ApiModelProperty(value="中心")
    private String centerName;

    @ApiModelProperty(value="车间")
    private String locationName;

    @TableField(value = "task_no")
    @ApiModelProperty(value="蒸馏任务号")
    private String taskNo;

    @TableField(value = "iot_no")
    @ApiModelProperty(value="IOT任务号")
    private String iotNo;

    @TableField(value = "center_id")
    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @TableField(value = "location_id")
    @ApiModelProperty(value="车间id")
    private Integer locationId;

    @TableField(value = "hj1_single_container_usage")
    @ApiModelProperty(value="HJ1（kg）")
    private String hj1SingleContainerUsage;

    @TableField(value = "yellow_water_usage")
    @ApiModelProperty(value="黄水用量（kg）")
    private String yellowWaterUsage;

    @TableField(value = "tail_wine_usage")
    @ApiModelProperty(value="尾酒用量（kg）")
    private String tailWineUsage;

    @TableField(value = "ysage_of_circulating_water")
    @ApiModelProperty(value="清水用量（kg）")
    private String ysageOfCirculatingWater;

    /**
     * 任务类型(自动任务)
     */
    @TableField(value = "task_type")
    @ApiModelProperty(value="任务类型(自动任务)")
    private String taskType;

    @TableField(value = "pit_no")
    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @TableField(value = "pit_order")
    @ApiModelProperty(value="窖池订单")
    private String pitOrder;

    @TableField(value = "state")
    @ApiModelProperty(value="任务状态(4：待执行:7：执行中、5：已完成)")
    private Integer state;

    @ApiModelProperty(value="任务状态(4：待执行:7：执行中、5：已完成)")
    private Integer status;

    @TableField(value = "vinasse_name")
    @ApiModelProperty(value="糟源类型")
    private String vinasseName;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value="开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value="结束时间")
    private Date endTime;

    @ApiModelProperty(value="主键id")
    private Integer id;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value="创建人id")
    private Integer creatorId;

    @ApiModelProperty(value="创建人")
    private String creatorName;

    @ApiModelProperty(value="修改人id")
    private Integer updaterId;

    @ApiModelProperty(value="修改人")
    private String updaterName;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}
