package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.entity.tpo.IotTaskDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPileUpDetailVO
 * @description: 下糟任务详情查询返回VO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="下糟任务详情查询返回VO")
@Data
public class TaskPileUpDetailVO extends IotTaskDetail {

    @TableField(value = "site_num")
    @ApiModelProperty(value="租户")
    private String siteNum;

    @TableField(value = "task_pile_up_id")
    @ApiModelProperty(value="下糟任务id")
    private Integer taskPileUpId;

    @TableField(value = "total_duration")
    @ApiModelProperty(value="下糟总时长(s)")
    private String totalDuration;

    @TableField(value = "duration_steaming")
    @ApiModelProperty(value="出甑时长（单甑糟醅下糟铺料机运行时长）")
    private String durationSteaming;

    @TableField(value = "total_weight")
    @ApiModelProperty(value="单甑糟醅重量(kg)")
    private String totalWeight;

    @TableField(value = "fermentative_material_volume")
    @ApiModelProperty(value="单甑糟醅体积")
    private String fermentativeMaterialVolume;

    @TableField(value = "six_cubic_bucket_duration")
    @ApiModelProperty(value="更换六立方斗时长")
    private String sixCubicBucketDuration;

    @ApiModelProperty(value="六立方暂存斗ID")
    private String sixCubicBucketId;

    @ApiModelProperty(value="下糟开始时间")
    private String startTime;

    @ApiModelProperty(value="下糟结束时间")
    private String endTime;
    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @TableField(value = "zeng_no")
    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @TableField(value = "change_bucket_start_time")
    @ApiModelProperty(value = "换斗开始时间")
    private String changeBucketStartTime;

    @TableField(value = "change_bucket_end_time")
    @ApiModelProperty(value = "换斗结束时间")
    private String changeBucketEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}
