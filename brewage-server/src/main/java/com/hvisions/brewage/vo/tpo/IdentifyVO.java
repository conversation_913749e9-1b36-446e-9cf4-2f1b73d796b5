package com.hvisions.brewage.vo.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: IdentifyVO
 * @description: 入窖鉴定列表查询返回VO
 * @date 2025/9/16 16:12
 */
@ApiModel(value="待入窖鉴定查询返回VO")
@Data
public class IdentifyVO {
    @ApiModelProperty(value = "出窖糟源类别")
    private String outMaterialType;

    @ApiModelProperty(value = "入窖糟源类别")
    private String enterMaterialType;

    @ApiModelProperty(value = "甑口任务号")
    private String steamingTaskNo;

    @ApiModelProperty(value = "工序任务号(IOT任务号)")
    private String iotTaskNo;

    @ApiModelProperty(value = "起窖窖号")
    private String sourcePitNo;

    @ApiModelProperty(value = "入窖窖号")
    private String targetPitNo;

    @ApiModelProperty(value = "摊晾机编号")
    private String spreaderNo;

    @ApiModelProperty(value = "摊晾到入窖时长")
    private String spreadToPitDuration;

    @ApiModelProperty(value = "采集时间")
    private Date recordTime;
}
