package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPileUpVO
 * @description: 下糟任务查询返回VO
 * @date 2025/7/7 14:15
 */
@ApiModel(value="下糟任务查询返回VO")
@Data
public class TaskPileUpVO {

    @ApiModelProperty(value="下糟任务号")
    private String taskNo;

    @ApiModelProperty(value="IOT任务号")
    private String iotNo;

    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @ApiModelProperty(value="中心")
    private String centerName;

    @ApiModelProperty(value="车间id")
    private Integer locationId;

    @ApiModelProperty(value="车间")
    private String locationName;
    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value="开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value="结束时间")
    private Date endTime;
    /**
     * 任务类型(自动任务)
     */
    @ApiModelProperty(value="任务类型(自动任务)")
    private String taskType;

    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @ApiModelProperty(value="窖池订单")
    private String pitOrder;

    @ApiModelProperty(value="糟源类型")
    private String vinasseName;

    @ApiModelProperty(value="下糟总时长(s)")
    private String totalDuration;

    @ApiModelProperty(value="糟醅重量(kg)")
    private String fermentedGrainsWeight;

    @ApiModelProperty(value="糟醅体积(m³)")
    private String fermentedGrainsVolume;

    @ApiModelProperty(value="出糟甑口（甑）")
    private String zengkouNumber;

    @ApiModelProperty(value="智能行车更换六立方斗时长(s)")
    private String sixCubicBucketDuration;

    @ApiModelProperty(value="任务状态(4：待执行:7：执行中、5：已完成)")
    private Integer state;

    @ApiModelProperty(value="任务状态(4：待执行:7：执行中、5：已完成)")
    private Integer status;

    @ApiModelProperty(value="单窖涨幅")
    private String singleCellarIncreaseRate;

    @ApiModelProperty(value="主键id")
    private Integer id;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value="创建人id")
    private Integer creatorId;

    @ApiModelProperty(value="创建人")
    private String creatorName;

    @ApiModelProperty(value="修改人id")
    private Integer updaterId;

    @ApiModelProperty(value="修改人")
    private String updaterName;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}
