package com.hvisions.brewage.vo.tpo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingVO
 * @description: 摊晾任务查询返回VO
 * @date 2025/7/7 14:15
 */
@ApiModel(value="摊晾任务查询返回VO")
@Data
public class TaskSpreadingVO {
    @ApiModelProperty(value="任务号")
    private String taskNo;

    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @ApiModelProperty(value="车间id")
    private Integer locationId;

    @ApiModelProperty(value="任务名称")
    private String taskName;

    @ApiModelProperty(value="任务类型(自动任务)")
    private String taskType;

    @ApiModelProperty(value="任务状态(待执行、执行中、已完成、已终止)")
    private String status;

    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @ApiModelProperty(value="糟源类别")
    private String materialType;

    @ApiModelProperty(value="糟源")
    private String vinasseName;

    @ApiModelProperty(value="窖池订单号")
    private String pitOrder;

    @ApiModelProperty(value="大曲用量")
    private String yeastAmountTotal;

    @ApiModelProperty(value="开始时间")
    private Date startTime;

    @ApiModelProperty(value="结束时间")
    private Date endTime;

    @ApiModelProperty(value="主键id")
    private Integer id;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value="创建人id")
    private Integer creatorId;

    @ApiModelProperty(value="创建人")
    private String creatorName;

    @ApiModelProperty(value="修改人id")
    private Integer updaterId;

    @ApiModelProperty(value="修改人")
    private String updaterName;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}
