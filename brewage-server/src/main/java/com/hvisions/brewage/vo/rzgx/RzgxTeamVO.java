package com.hvisions.brewage.vo.rzgx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: RzgxTeamVO
 * @description:
 * @date 2025/9/15 16:07
 */
@ApiModel(value="SZGX班组管理查询返回VO")
@Data
public class RzgxTeamVO {
    @ApiModelProperty(value="班组编码")
    private String groupCode;

    @ApiModelProperty(value="班组名称")
    private String groupName;

    @ApiModelProperty(value="开始时间")
    private Date startDate;

    @ApiModelProperty(value="结束时间")
    private Date endDate;

    @ApiModelProperty(value="班组管理-员工")
    private List<RzgxTeamUserVO> members;
}
