package com.hvisions.brewage.vo.rzgx;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: RzgxTeamUserVO
 * @description:
 * @date 2025/9/15 16:11
 */
@ApiModel(value="SZGX班组管理-员工查询返回VO")
@Data
public class RzgxTeamUserVO {
    @ApiModelProperty(value = "员工编码")
    private String staffCode;

    @ApiModelProperty(value = "员工名称")
    private String staffName;
}
