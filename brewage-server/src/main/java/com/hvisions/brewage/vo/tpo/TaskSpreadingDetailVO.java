package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.dto.tpo.TaskSpreadingDetailParameterAddDTO;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailVO
 * @description: 摊晾任务详情查询返回VO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="摊晾任务详情查询返回VO")
@Data
public class TaskSpreadingDetailVO extends IotSendTaskDetailVO {
    @ApiModelProperty(value="摊晾任务主键id")
    private Integer taskSpreadingId;

    @ApiModelProperty(value="甑口任务号")
    private String steamingTaskNo;

    @ApiModelProperty(value="摊晾机编号")
    private String spreaderNo;

    @ApiModelProperty(value="地温")
    private String floorTemp;

    @ApiModelProperty(value="湿度")
    private String humidity;

    @ApiModelProperty(value = "摊晾开始时间")
    private String spreadingStartTime;

    @ApiModelProperty(value = "摊晾结束时间")
    private String spreadingEndTime;

    @ApiModelProperty(value="摊晾时长")
    private String duration;

    @ApiModelProperty(value="单甑加曲时长")
    private String yeastAddingTime;

    @ApiModelProperty(value = "单甑加曲开始时间")
    private String kojiAdditionStartTime;

    @ApiModelProperty(value = "单甑加曲结束时间")
    private String kojiAdditionEndTime;

    @ApiModelProperty(value="摊晾斗内糟醅温度")
    private String hopperTemp;

    @ApiModelProperty(value = "摊晾斗内糟醅温度填写时间")
    private String hopperTempTime;

    @ApiModelProperty(value = "摊晾机输送链板左侧糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorLeftGrainTemp;

    @ApiModelProperty(value = "摊晾机输送链板右侧糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorRightGrainTemp;

    @ApiModelProperty(value="摊晾机输送链板前端糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorFrontTemp;

    @ApiModelProperty(value="摊晾温度1")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorMidTemp;

    @ApiModelProperty(value="摊晾温度2")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorRearTemp;

    @ApiModelProperty(value="摊晾喂料机转速")
    private String feederSpeed;

    @ApiModelProperty(value="摊晾机输送链板转速")
    private String conveyorSpeed;

    @ApiModelProperty(value="每台风机运行时长")
    private List<TaskSpreadingDetailParameterAddDTO> fanRuntimePerUnit;

    @ApiModelProperty(value="风机使用数量")
    private String fanQuantity;

    @ApiModelProperty(value="风机运行转速")
    private List<TaskSpreadingDetailParameterAddDTO> fanSpeed;

    @ApiModelProperty(value="加曲机转速")
    private String yeastFeederSpeed;

    @ApiModelProperty(value="单甑加曲量")
    private String yeastAmount;

    @ApiModelProperty(value = "加曲前重量")
    private String weightBeforeKoji;

    @ApiModelProperty(value = "加曲后重量")
    private String weightAfterKoji;

    @ApiModelProperty(value="大曲破碎度")
    private String yeastFineness;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @ApiModelProperty(value = "摊晾斗内糟醅温度填写记录人id")
    private Integer hopperTempUserId;

    @ApiModelProperty(value = "摊晾斗内糟醅温度填写记录人")
    private String hopperTempUser;
}
