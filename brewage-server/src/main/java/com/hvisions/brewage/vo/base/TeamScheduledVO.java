package com.hvisions.brewage.vo.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledVO
 * @description:
 * @date 2025/9/15 18:22
 */
@ApiModel(value = "排班管理列表查询返回VO")
@Data
public class TeamScheduledVO {
    @ApiModelProperty(value = "班次日期")
    private Date calendarDate;

    @ApiModelProperty(value = "班次信息")
    private List<TeamScheduledShiftVO> shiftVOList;
}
