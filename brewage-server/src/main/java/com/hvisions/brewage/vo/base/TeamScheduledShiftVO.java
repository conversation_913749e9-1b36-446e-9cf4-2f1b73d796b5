package com.hvisions.brewage.vo.base;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledShiftVO
 * @description:
 * @date 2025/9/15 18:23
 */
@ApiModel(value = "排班管理列表-班次查询返回VO")
@Data
public class TeamScheduledShiftVO {
    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "中心名称")
    private String centerName;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "车间名称")
    private String workshopName;

    @ApiModelProperty(value = "班组编码")
    private String groupCode;

    @ApiModelProperty(value = "班组名称")
    private String groupName;

    @ApiModelProperty(value = "班次编码")
    private String shiftId;

    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    @ApiModelProperty(value = "班次日期")
    private Date calendarDate;

    @ApiModelProperty(value = "班次开始时间")
    @JSONField(format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date shiftstartTime;

    @ApiModelProperty(value = "班次结束时间")
    @JSONField(format = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date shiftendTime;

    @ApiModelProperty(value = "应出勤时长(H)")
    private BigDecimal scheduledWorkHours;

    @ApiModelProperty(value = "排班管理列表-班次员工")
    private List<TeamScheduledShiftUserVO> shiftUserVOList;
}
