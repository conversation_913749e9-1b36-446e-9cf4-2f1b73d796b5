package com.hvisions.brewage.vo.base;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledShiftUserVO
 * @description:
 * @date 2025/9/15 18:26
 */
@ApiModel(value = "排班管理列表-班次-员工查询返回VO")
@Data
public class TeamScheduledShiftUserVO {
    @ApiModelProperty(value = "员工编码")
    private String staffCode;

    @ApiModelProperty(value = "员工名称")
    private String staffName;

    @ApiModelProperty(value = "实际出勤时长(H)")
    private BigDecimal actualWorkHours;
}
