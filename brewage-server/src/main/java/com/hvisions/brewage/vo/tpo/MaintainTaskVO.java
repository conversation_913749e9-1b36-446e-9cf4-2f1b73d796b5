package com.hvisions.brewage.vo.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.brewage.entity.SysBaseNew;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MaintainTaskVO
 * @description: 窖池养护任查询返回VO
 * @date 2025/6/30 11:10
 */
@ApiModel(value="窖池养护任查询返回VO")
@Data
public class MaintainTaskVO extends SysBaseNew {
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "任务类型(自动任务)")
    private String taskType="自动任务";

    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    @ApiModelProperty(value = "窖池订单")
    private String pitOrder;

    @ApiModelProperty(value = "空窖时间")
    private Date emptyTime;

    @ApiModelProperty(value = "养护开始时间")
    private Date startTime;

    @ApiModelProperty(value = "养护结束时间")
    private Date endTime;

    @ApiModelProperty(value = "养护次数")
    private Integer maintenanceCount;

    @ApiModelProperty(value = "大曲使用量(kg)")
    private BigDecimal starterCultureUsage;

    @ApiModelProperty(value = "状态(待执行、执行中、已完成)")
    private String status;

    @ApiModelProperty(value = "养护预计开始时间")
    private Date maintainExpectStartTime;

    @ApiModelProperty(value = "子任务状态(未开始、执行中)")
    private String subtaskStatus;

}
