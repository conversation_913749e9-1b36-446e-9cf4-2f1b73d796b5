package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskGrainLubricationDetailAddDTO
 * @description: 润粮任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="润粮任务详情新增DTO")
@Data
public class TaskGrainLubricationDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="润粮任务id")
    private Integer taskGrainLubricationId;

    @ApiModelProperty(value="润粮机编号")
    private String runliangMachineNo;

    @ApiModelProperty(value="润粮水温度")
    private String runliangWaterTemperature;

    @ApiModelProperty(value="润粮水重量")
    private String runliangWaterWeight;

    @ApiModelProperty(value="润粮时长（拌糟开始时间-搅拌开始时间）")
    private String runliangDuration;

    @ApiModelProperty(value="搅拌时长（拌料结束时间-搅拌开始时间）")
    private String stirDuration;

    @ApiModelProperty(value="待料时长（拌糟开始时间-润粮机出料结束时间）")
    private String waitingMaterialsDuration;

    @ApiModelProperty(value="高粱破碎度设定值")
    private String sorghumFragmentationSettingValue;

    @ApiModelProperty(value="高粱破碎度实际值")
    private String sorghumFragmentationActualValue;

    @ApiModelProperty(value="单甑高粱设定值")
    private String singleContainerSorghumSettingValue;

    @ApiModelProperty(value="单甑高粱实际值")
    private String singleContainerSorghumActualValue;

    @ApiModelProperty(value="润粮仓循环水用量")
    private String runliangCirculatingWaterConsumption;

    @ApiModelProperty(value="润粮仓循环水冷却时间")
    private String runliangCirculatingWaterCoolingTime;

    @ApiModelProperty(value="润粮时间")
    private String runliangTime;

    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value="物料名称")
    private String materialName;

    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @ApiModelProperty(value = "润粮开始时间")
    private String runGrainStartTime;

    @ApiModelProperty(value = "润粮结束时间")
    private String runGrainEndTime;

    @ApiModelProperty(value = "搅拌开始时间")
    private String stirStartTime;

    @ApiModelProperty(value = "搅拌结束时间")
    private String stirEndTime;

    @ApiModelProperty(value = "待料开始时间")
    private String awaitingMaterialsStartTime;

    @ApiModelProperty(value = "待料结束时间")
    private String awaitingMaterialsEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "批次号")
    private String batchNo;
}
