package com.hvisions.brewage.dto.tpo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingIdentifyDTO
 * @description: 入窖鉴定DTO
 * @date 2025/7/10 17:27
 */
@ApiModel(value="入窖鉴定DTO")
@Data
public class TaskLoadingIdentifyDTO {
    @ApiModelProperty(value="入窖任务主键id",required = true)
    @NotNull(message = "id不能为空")
    private Integer id;

    @ApiModelProperty(value="入窖任务明细id",required = true)
    @NotNull(message = "入窖任务明细id不能为空")
    private List<Integer> detailsId;

    @ApiModelProperty(value = "入窖鉴定时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inAppraiseTime;

    @ApiModelProperty(value = "中心id", example = "1",required = true)
    @NotNull(message = "中心id不能为空")
    private Integer centerId;

    @ApiModelProperty(value = "车间id", example = "1",required = true)
    @NotNull(message = "车间id不能为空")
    private Integer locationId;

    @ApiModelProperty(value = "入窖窖池id(连窖的窖池id)", example = "1",required = true)
    @NotNull(message = "入窖窖池id不能为空")
    private Integer pitId;

    @ApiModelProperty(value = "窖池订单id",required = true)
    @NotNull(message = "窖池订单id不能为空")
    private String orderId;

    @ApiModelProperty(value = "来源窖号id(连窖的窖池id)", example = "1",required = true)
    @NotNull(message = "来源窖号id不能为空")
    private Integer fromPitId;

    @ApiModelProperty(value = "感官形态")
    private String[]  senseForm;

    @ApiModelProperty(value = "感官糠(稻壳)水状况")
    private String senseFermentMoist;

    @ApiModelProperty(value = "入窖温度(min)",required = true)
    @NotNull(message = "入窖温度(min)不能为空")
    private BigDecimal minLoadingTemp;

    @ApiModelProperty(value = "入窖温度(max)",required = true)
    @NotNull(message = "入窖温度(max)不能为空")
    private BigDecimal maxLoadingTemp;

    @ApiModelProperty(value = "稻壳(%)",required = true)
    @NotNull(message = "稻壳(%)不能为空")
    private BigDecimal riceHullPercent;

    @ApiModelProperty(value = "量水(%)",required = true)
    @NotNull(message = "量水(%)不能为空")
    private BigDecimal waterAdditionPercent;

    @ApiModelProperty(value = "糟醅取样数量")
    private Integer fermentedGrainSamples;
}
