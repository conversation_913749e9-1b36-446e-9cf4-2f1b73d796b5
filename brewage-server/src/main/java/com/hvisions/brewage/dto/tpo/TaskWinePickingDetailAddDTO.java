package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskWinePickingDetailAddDTO
 * @description: 摘酒任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="摘酒任务详情新增DTO")
@Data
public class TaskWinePickingDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="摘酒任务id")
    private Integer taskWinePickingId;

    @ApiModelProperty(value="一段流酒重量")
    private String weightFlowingWineOne;

    @ApiModelProperty(value="二段流酒重量")
    private String weightFlowingWineTwo;

    @ApiModelProperty(value="三段流酒重量")
    private String weightFlowingWineThree;

    @ApiModelProperty(value="四段流酒重量")
    private String weightFlowingWineFour;

    @ApiModelProperty(value="一段酒精度")
    private String alcoholContentOne;

    @ApiModelProperty(value="二段酒精度")
    private String alcoholContentTwo;

    @ApiModelProperty(value="三段酒精度")
    private String alcoholContentThree;

    @ApiModelProperty(value="四段酒精度")
    private String alcoholContentFour;

    @ApiModelProperty(value="一段流酒占比")
    private String proportionFlowingAlcoholOne;

    @ApiModelProperty(value="二段流酒占比")
    private String proportionFlowingAlcoholTwo;

    @ApiModelProperty(value="三段流酒占比")
    private String proportionFlowingAlcoholThree;

    @ApiModelProperty(value="四段流酒占比")
    private String proportionFlowingAlcoholFour;

    @ApiModelProperty(value="流酒时长")
    private String flowingWineDuration;

    @ApiModelProperty(value="一段时长")
    private String durationOne;

    @ApiModelProperty(value="二段时长")
    private String durationTwo;

    @ApiModelProperty(value="三段时长")
    private String durationThree;

    @ApiModelProperty(value="四段时长")
    private String durationFour;

//    @TableField(value = "flowing_wine_temperature")
//    @ApiModelProperty(value="流酒温度")
//    private String flowingWineTemperature;
//
//    @TableField(value = "flowing_wine_speed")
//    @ApiModelProperty(value="流酒速度")
//    private String flowingWineSpeed;

    @ApiModelProperty(value="盖盘到流酒时长")
    private String coverPlateFlowingWineDuration;

    @ApiModelProperty(value="盖盘时间")
    private String coverTime;

    @ApiModelProperty(value="一段酒开始时间")
    private String oneStageWineStartTime;

    @ApiModelProperty(value="二段酒开始时间")
    private String twoStageWineStartTime;

    @ApiModelProperty(value="三段酒开始时间")
    private String threeStageWineStartTime;

    @ApiModelProperty(value="尾酒开始时间")
    private String cocktailStartTime;

    @ApiModelProperty(value = "尾酒结束时间")
    private String cocktailEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value = "流酒温度")
    private List<TaskWinePickingDetailParameterAddDTO> flowingWineTemperatureTemp;

    @ApiModelProperty(value = "流酒速度")
    private List<TaskWinePickingDetailParameterAddDTO> flowingWineSpeedTemp;

}
