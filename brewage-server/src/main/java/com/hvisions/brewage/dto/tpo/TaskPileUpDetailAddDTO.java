package com.hvisions.brewage.dto.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskPileUpDetailAddDTO
 * @description: 下糟任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="下糟任务详情新增DTO")
@Data
public class TaskPileUpDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="下糟任务id")
    private Integer taskPileUpId;

    @ApiModelProperty(value="下糟总时长(s)")
    private String totalDuration;

    @ApiModelProperty(value="出甑时长（单甑糟醅下糟铺料机运行时长）")
    private String durationSteaming;

    @ApiModelProperty(value="单甑糟醅重量(kg)")
    private String totalWeight;

    @ApiModelProperty(value="单甑糟醅体积")
    private String fermentativeMaterialVolume;

    @ApiModelProperty(value="更换六立方斗时长")
    private String sixCubicBucketDuration;

    @ApiModelProperty(value="六立方暂存斗ID")
    private String sixCubicBucketId;

    @ApiModelProperty(value="下糟开始时间")
    private String startTime;

    @ApiModelProperty(value="下糟结束时间")
    private String endTime;

    /**
     * 采集时间
     */
    @TableField(value = "record_time")
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @ApiModelProperty(value = "换斗开始时间")
    private String changeBucketStartTime;

    @ApiModelProperty(value = "换斗结束时间")
    private String changeBucketEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}
