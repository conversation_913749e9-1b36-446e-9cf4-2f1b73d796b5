package com.hvisions.brewage.dto.base;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledQueryDTO
 * @description:
 * @date 2025/9/15 17:44
 */
@ApiModel(value="排班管理查询DTO")
@Data
public class TeamScheduledQueryDTO{
    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "班次日期(年-月)")
    private String calendarDate;
}
