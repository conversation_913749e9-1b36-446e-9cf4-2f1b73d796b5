package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskShangzhengDetailAddDTO
 * @description: 上甑任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="上甑任务详情新增DTO")
@Data
public class TaskShangzhengDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="上甑任务id")
    private Integer taskShangzhengId;

    @ApiModelProperty(value="甑号")
    private String zengNo;

    @ApiModelProperty(value="糟醅备料时长")
    private String preparationTime;

    @ApiModelProperty(value="上甑糟水分含量")
    private String moistureContent;

    @ApiModelProperty(value="上甑时长（盒盖时间-上甑喂料机启动下料时间）")
    private String durationOfSteaming;

    @ApiModelProperty(value="压汽深度")
    private String steamPressureDepth;

    @ApiModelProperty(value="糟醅体积")
    private String volumeOfFermentedGrains;

    @ApiModelProperty(value="上甑层数")
    private String shangzhengLayer;

    @ApiModelProperty(value="每层提高高度")
    private Double raiseTheHeight;

    @ApiModelProperty(value="上甑机器人转速")
    private Double robotRotationSpeed;

    @ApiModelProperty(value="上甑前输送时长（上甑喂料机接料时间-二维拌合机下料时间）")
    private String deliveryDuration;

    @ApiModelProperty(value="自动行车转运拌合后糟醅到上甑喂料机时长")
    private String feedingMachineDuration;
    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value="接糟开始时间")
    private String takeItStartTime;

    @ApiModelProperty(value = "上甑开始时间")
    private String shangzhengStartTime;

    @ApiModelProperty(value = "上甑结束时间")
    private String shangzhengEndTime;

    @ApiModelProperty(value="蒸汽阀门开度")
    private String steamValveOpening;

    @ApiModelProperty(value = "盒盖时间")
    private String boxCoverTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

}
