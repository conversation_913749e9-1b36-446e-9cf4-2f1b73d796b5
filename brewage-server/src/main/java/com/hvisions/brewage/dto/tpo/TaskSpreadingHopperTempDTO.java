package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingHopperTempDTO
 * @description:
 * @date 2025/9/16 15:24
 */
@ApiModel(value="修改斗内糟醅温度DTO")
@Data
public class TaskSpreadingHopperTempDTO {
    @ApiModelProperty(value = "摊晾任务详情主键id",required = true)
    @NotNull(message = "摊晾任务详情主键id不能为空")
    private List<Integer> ids;

    @ApiModelProperty(value = "摊晾斗内糟醅温度",required = true)
    @NotEmpty(message = "摊晾斗内糟醅温度不能为空")
    private String hopperTemp;
}
