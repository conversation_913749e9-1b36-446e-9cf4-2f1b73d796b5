package com.hvisions.brewage.dto.rzgx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamScheduledQueryDTO
 * @description:
 * @date 2025/9/15 13:55
 */
@ApiModel(value="人资共享人员排班查询DTO")
@Data
public class RzgxTeamScheduledQueryDTO {

    @ApiModelProperty(value = "组织编码",example ="1100")
    private String orgCode="1100";

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "中心编码")
    private String centerCode;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;
}
