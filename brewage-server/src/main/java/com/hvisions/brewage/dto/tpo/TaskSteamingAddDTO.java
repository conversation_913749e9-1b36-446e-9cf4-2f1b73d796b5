package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSteamingAddDTO
 * @description: 出甑任务新增DTO
 * @date 2025/7/7 14:16
 */
@ApiModel(value="出甑任务新增DTO")
@Data
public class TaskSteamingAddDTO {

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="出甑任务号")
    private String taskNo;

    @ApiModelProperty(value="IOT任务号")
    private String iotNo;

    @ApiModelProperty(value="中心id")
    private Integer centerId;

    @ApiModelProperty(value="车间id")
    private Integer locationId;

    /**
     * 任务类型(自动任务)
     */
    @ApiModelProperty(value="任务类型(自动任务)")
    private String taskType;

    @ApiModelProperty(value="窖池号")
    private String pitNo;

    @ApiModelProperty(value="窖池订单")
    private String pitOrder;

    @ApiModelProperty(value="任务状态(4：待执行:7：执行中、5：已完成)")
    private Integer state;

    @ApiModelProperty(value="糟源类型")
    private String vinasseName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value="开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value="结束时间")
    private Date endTime;
    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;
}
