package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSistillationDetailAddDTO
 * @description: 蒸馏任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="蒸馏任务详情新增DTO")
@Data
public class TaskSistillationDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="蒸馏任务id")
    private Integer taskSistillationId;

    @ApiModelProperty(value="蒸汽阀门开度")
    private String steamValveOpening;

    @ApiModelProperty(value="总蒸汽流量")
    private String totalSteamFlowRate;

    @ApiModelProperty(value="单甑蒸汽流量")
    private String steamFlowRate;

    @ApiModelProperty(value="单甑蒸汽压力")
    private String steamPressure;

    @ApiModelProperty(value="单甑蒸汽用量")
    private String steamConsumption;

    @ApiModelProperty(value="循环水单甑使用量")
    private String circulatingWaterUsage;

//    @TableField(value = "circulating_water_return_temperature")
//    @ApiModelProperty(value="循环水回水温度")
//    private String circulatingWaterReturnTemperature;
//
//    @TableField(value = "bottom_pot water_temperature")
//    @ApiModelProperty(value="底锅水温度")
//    private String bottomPotWaterTemperature;

    @ApiModelProperty(value="清水设定值")
    private String clearWaterSettingValue;

    @ApiModelProperty(value="清水单甑使用量")
    private String ysageOfCirculatingWater;

    @ApiModelProperty(value="黄水设定值")
    private String yellowWaterSetValue;

    @ApiModelProperty(value="黄水单甑使用量")
    private String yellowWaterUsage;

    @ApiModelProperty(value="黄水酒度")
    private String yellowWaterAlcoholContent;

    @ApiModelProperty(value="尾酒设定值")
    private String tailWineSettingValue;

    @ApiModelProperty(value="尾酒单甑使用量")
    private String tailWineUsage;

    @ApiModelProperty(value="尾酒酒度")
    private String tailWineAlcoholContent;

    @ApiModelProperty(value="HJ1设定值")
    private String hj1SetValue;

    @ApiModelProperty(value="HJ1单甑使用量")
    private String hj1SingleContainerUsage;

    @ApiModelProperty(value="HJ1酒度")
    private String hj1AlcoholContent;

    @ApiModelProperty(value="HJ1单窖使用量")
    private String hj1SingleCellarUsage;

    @ApiModelProperty(value="单甑底锅水总量")
    private String totalAmountOfBottomPotWater;

    @ApiModelProperty(value="单甑底锅水液位（蒸馏前对应的值）")
    private String bottomPotWaterLevel;

    @ApiModelProperty(value="流酒开始时间")
    private String flowingWineStartTime;

    @ApiModelProperty(value="开盖时间")
    private String openingTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value = "循环水回水温度")
    private List<TaskSistillationDetailParameterAddDTO> circulatingWaterReturnTemperatureTemp;

    @ApiModelProperty(value = "底锅水温度")
    private List<TaskSistillationDetailParameterAddDTO> bottomPotWaterTemperatureTemp;

}
