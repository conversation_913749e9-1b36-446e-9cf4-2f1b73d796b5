package com.hvisions.brewage.dto.base;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TeamUserQueryDTO
 * @description:
 * @date 2025/9/15 16:28
 */
@ApiModel(value="班组管理-员工查询DTO")
@Data
public class TeamUserQueryDTO extends PageInfo {

    @ApiModelProperty(value="班组Id",required = true)
    @NotNull(message = "班组Id不能为空")
    private Integer teamId;

    @ApiModelProperty(value="员工名称")
    private String staffName;
}
