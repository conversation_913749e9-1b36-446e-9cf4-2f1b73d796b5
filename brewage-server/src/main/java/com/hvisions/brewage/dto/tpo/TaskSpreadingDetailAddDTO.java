package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailAddDTO
 * @description: 摊晾任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="摊晾任务详情新增DTO")
@Data
public class TaskSpreadingDetailAddDTO extends IotSendTaskDetailDTO{
    @ApiModelProperty(value = "起窖窖号",required = true)
    @NotEmpty(message = "起窖窖号不能为空")
    private String sourcePitNo;

    @ApiModelProperty(value = "入窖窖号",required = true)
    @NotEmpty(message = "入窖窖号不能为空")
    private String targetPitNo;

    @ApiModelProperty(value="摊晾机编号")
    private String spreaderNo;

    @ApiModelProperty(value="摊晾时长")
    private String duration;

    @ApiModelProperty(value="单甑加曲时长")
    private String yeastAddingTime;

    @ApiModelProperty(value="摊晾斗内糟醅温度")
    private String hopperTemp;

    @ApiModelProperty(value="摊晾开始时间")
    private String spreadingStartTime;

    @ApiModelProperty(value="摊晾结束时间")
    private String spreadingEndTime;

    @ApiModelProperty(value="单甑加曲开始时间")
    private String kojiAdditionStartTime;

    @ApiModelProperty(value="单甑加曲结束时间")
    private String kojiAdditionEndTime;

    @ApiModelProperty(value = "摊晾机输送链板左侧糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorLeftGrainTemp;

    @ApiModelProperty(value = "摊晾机输送链板右侧糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorRightGrainTemp;

    @ApiModelProperty(value="摊晾机输送链板前端糟醅温度")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorFrontTemp;

    @ApiModelProperty(value="摊晾温度1")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorMidTemp;

    @ApiModelProperty(value="摊晾温度2")
    private List<TaskSpreadingDetailParameterAddDTO> conveyorRearTemp;

    @ApiModelProperty(value="摊晾喂料机转速")
    private String feederSpeed;

    @ApiModelProperty(value="摊晾机输送链板转速")
    private String conveyorSpeed;

    @ApiModelProperty(value="每台风机运行时长")
    private List<TaskSpreadingDetailParameterAddDTO> fanRuntimePerUnit;

    @ApiModelProperty(value="风机使用数量")
    private String fanQuantity;

    @ApiModelProperty(value="风机运行转速")
    private List<TaskSpreadingDetailParameterAddDTO> fanSpeed;

    @ApiModelProperty(value="加曲机转速")
    private String yeastFeederSpeed;

    @ApiModelProperty(value="单甑加曲量")
    private String yeastAmount;

    @ApiModelProperty(value="加曲前重量")
    private String weightBeforeKoji;

    @ApiModelProperty(value="加曲后重量")
    private String weightAfterKoji;

    @ApiModelProperty(value="大曲破碎度")
    private String yeastFineness;

    @ApiModelProperty(value="备注 ")
    private String remarks;
}
