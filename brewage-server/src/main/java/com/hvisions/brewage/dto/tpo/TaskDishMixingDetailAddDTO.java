package com.hvisions.brewage.dto.tpo;

import com.alibaba.fastjson.annotation.JSONType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDishMixingDetailAddDTO
 * @description: 拌糟任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="拌糟任务详情新增DTO")
@Data
@JSONType(asm = false)
public class TaskDishMixingDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="单甑稻壳设定值")
    private String singleContainerRiceHuskSettingValue;

    @ApiModelProperty(value="单甑稻壳实际值")
    private String singleContainerRiceHuskActualValue;

    @ApiModelProperty(value="输糠绞龙开始时间")
    private String transportBranStartTime;

    @ApiModelProperty(value="输糠绞龙结束时间")
    private String transportBranEndTime;

    @ApiModelProperty(value="拌合时长（二维拌合机下料时间-料斗开门机构打开时间）")
    private String mixingDuration;

    @ApiModelProperty(value="拌合机倾斜角度")
    private String tiltAngleOfMixer;

    @ApiModelProperty(value="拌合转速")
    private Double mixingSpeed;

    @ApiModelProperty(value="加粮时长")
    private String grainAdditionDuration;

    @ApiModelProperty(value="加糟时长")
    private String durationOfAddingDregs;

    @ApiModelProperty(value="加熟稻壳时长")
    private String durationOfAddingCookedRiceHusks;

    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value = "甑号")
    private String zengNo;

    @ApiModelProperty(value = "加粮开始时间")
    private String addGrainStartTime;

    @ApiModelProperty(value = "加粮结束时间")
    private String addGrainEndTime;

    @ApiModelProperty(value = "加糟开始时间")
    private String addDregsStartTime;

    @ApiModelProperty(value = "加糟结束时间")
    private String addDregsEndTime;

    @ApiModelProperty(value = "加熟稻壳开始时间")
    private String addCookedRiceHusksStartTime;

    @ApiModelProperty(value = "加熟稻壳结束时间")
    private String addCookedRiceHusksEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "批次号")
    private String batchNo;
}
