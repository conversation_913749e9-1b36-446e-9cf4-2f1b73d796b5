package com.hvisions.brewage.dto.tpo;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskLoadingIdentifyQueryDTO
 * @description:
 * @date 2025/9/16 16:15
 */
@ApiModel(value="待入窖鉴定列表查询DTO")
@Data
public class TaskLoadingIdentifyQueryDTO extends PageInfo {
    @ApiModelProperty(value = "入窖任务id")
    @NotNull(message = "入窖任务id不能为空")
    private Integer id;
}
