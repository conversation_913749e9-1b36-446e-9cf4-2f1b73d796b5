package com.hvisions.brewage.dto.tpo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskSpreadingDetailAddDTO
 * @description: 出甑任务详情新增DTO
 * @date 2025/7/7 16:41
 */
@ApiModel(value="出甑任务详情新增DTO")
@Data
public class TaskSteamingDetailAddDTO extends IotSendTaskDetailDTO{

    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="出甑任务id")
    private Integer taskSteamingId;

    @ApiModelProperty(value="蒸粮时长")
    private String steamingTime;

    @ApiModelProperty(value="用汽时长")
    private String steamUsageDuration;

    @ApiModelProperty(value="量水单甑使用量")
    private String flowMeasurementUsage;

    @ApiModelProperty(value="第一次量水重量")
    private String oneMeasureWaterWeight;

    @ApiModelProperty(value="第二次量水重量")
    private String twoMeasureWaterWeight;

    @ApiModelProperty(value="第一次量水温度")
    private String oneMeasureWaterTemperature;

    @ApiModelProperty(value="第二次量水温度")
    private String twoMeasureWaterTemperature;

//    @TableField(value = "temperature_measuring_bucket")
//    @ApiModelProperty(value="量水桶量水温度")
//    private String temperatureMeasuringBucket;

    @ApiModelProperty(value="打量水时长")
    private String measureWaterDuration;

    @ApiModelProperty(value="打量水间隔时长")
    private String measureWaterIntervalDuration;

    @ApiModelProperty(value="底锅水排放次数")
    private String bottomPotWaterEmissionNumber;

    @ApiModelProperty(value="底锅水单甑排放量")
    private String bottomPotWaterDischarge;

    @ApiModelProperty(value="底锅水总排放量")
    private String bottomPotWaterTotalDischarge;

    @ApiModelProperty(value="底锅水液位")
    private String bottomPotWaterLevel;

    @ApiModelProperty(value="自动行车从出甑到回甑运行时长")
    private String huizhengRunDuration;
    /**
     * 采集时间
     */
    @ApiModelProperty(value = "采集时间")
    private Date recordTime;

    @ApiModelProperty(value="出甑时间")
    private String extractionTime;

    @ApiModelProperty(value="量水1开始时间")
    private String oneMeasureWaterStartTime;

    @ApiModelProperty(value="量水1结束时间")
    private String oneMeasureWaterEndTime;

    @ApiModelProperty(value="量水2开始时间")
    private String twoMeasureWaterStartTime;

    @ApiModelProperty(value = "量水2结束时间")
    private String twoMeasureWaterEndTime;

    @ApiModelProperty(value = "出入窖状态")
    private String cellarStatus;

    @ApiModelProperty(value = "量水桶量水温度")
    private List<TaskSteamingDetailParameterAddDTO> temperatureMeasuringBucketTemp;

}
