package com.hvisions.brewage.dto.tpo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MaintainTaskQueryDTO
 * @description: 窖池养护任查询DTO
 * @date 2025/6/30 11:10
 */
@ApiModel(value="窖池养护任查询DTO")
@Data
public class MaintainTaskQueryDTO extends PageInfo {
    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "窖池号")
    private String pitNo;

    @ApiModelProperty(value = "任务号")
    private String taskNo;

    @ApiModelProperty(value = "窖池订单")
    private String pitOrder;

    @ApiModelProperty(value = "状态(待执行、执行中、已完成)")
    private String status;

    @ApiModelProperty(value = "创建开始时间")
    private Date createStartTime;

    @ApiModelProperty(value = "创建结束时间")
    private Date createEndTime;

}
