package com.hvisions.brewage.bw.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @classname TaskDetailQueryReq
 * @description 回酒需求任务单详情数据传输查询参数实体类
 * @date 2022-04-13
 */
@Data
@ApiModel("TaskDetail数据传输查询参数实体类")
public class TaskDetailQueryReq {

    @ApiModelProperty("回酒类型ID")
    private Integer materialId;

    @ApiModelProperty("中心id")
    private Integer centerId;

    @ApiModelProperty("车间id")
    private Integer locationId;

    @ApiModelProperty("开始发放日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginDate;

    @ApiModelProperty("结束发放日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @ApiModelProperty("提报开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitBegin;

    @ApiModelProperty("提报结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitEnd;

    @ApiModelProperty("批次信息")
    private String batch;

    @ApiModelProperty("批次id")
    private Integer batchId;
}