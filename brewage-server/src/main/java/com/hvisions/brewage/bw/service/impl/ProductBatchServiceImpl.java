package com.hvisions.brewage.bw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.brewage.bw.dao.BatchMapper;
import com.hvisions.brewage.bw.dto.BatchNodeQueryReq;
import com.hvisions.brewage.bw.dto.ForwardBatchQueryReq;
import com.hvisions.brewage.bw.service.IProductBatchService;
import com.hvisions.brewage.bw.vo.*;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopHandinTaskMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.QueryHandIn3DTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderPageQueryDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskQueryDTO;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopHandinTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.service.TPoWorkshopHandinTaskService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderPotTaskService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mkwine.vo.GetDataByPageVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderPageVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskQueryPageVO;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.common.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批次追踪
 */
@Slf4j
@Service
public class ProductBatchServiceImpl implements IProductBatchService {

    @Resource
    BaseWrapper baseWrapper;

    @Resource
    WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;

    @Resource
    BatchMapper batchMapper;

    @Resource
    TPoWorkshopHandinTaskMapper handinTaskMapper;

    @Resource
    public WorkshopPitOrderPotTaskService workshopPitOrderPotTaskService;

    @Resource
    public WorkshopPitOrderService workshopPitOrderService;

    @Resource
    TPoWorkshopHandinTaskService handinTaskService;


    @Override
    public Page<ForwardBatchVO> getForwardBatchPage(ForwardBatchQueryReq queryReq) {
        return PageHelperUtil.getPage(workshopPitOrderPotTaskMapper::getForwardBatchPage, queryReq, ForwardBatchVO.class);
    }

    /**
     * 查询甑口任务节点数据
     * @param queryReq
     * @return
     */
    @Override
    public WorkshopPitOrderPotTaskQueryPageVO getPotTaskPageByBatch(BatchNodeQueryReq queryReq) {
        WorkshopPitOrderPotTaskQueryDTO queryDTO = new WorkshopPitOrderPotTaskQueryDTO();
        queryDTO.setPage(queryReq.getPage());
        queryDTO.setPageSize(queryReq.getPageSize());
        if (StringUtils.isNotBlank(queryReq.getHandinTaskNo())) {
            queryDTO.setOrderCodes(getOrderCodesByTaskNo(queryReq.getHandinTaskNo()));
        } else {
            queryDTO.setQuLotId(queryReq.getQuLotId());
            queryDTO.setBackAlcoholicLotId(queryReq.getBackAlcoholicLotId());
            queryDTO.setCrushedGrainsLotId(queryReq.getCrushedGrainsLotId());
            queryDTO.setRicehullLotId(queryReq.getRicehullLotId());
        }
        return workshopPitOrderPotTaskService.workshopPitOrderPotTaskQueryPage(queryDTO);
    }

    /**
     * 查询窖池订单节点数据
     * @param queryReq
     * @return
     */
    @Override
    public Page<WorkshopPitOrderPageVO> getPitOrderPageByBatch(BatchNodeQueryReq queryReq) {
        WorkshopPitOrderPageQueryDTO pageQueryDTO = new WorkshopPitOrderPageQueryDTO();
        pageQueryDTO.setPage(queryReq.getPage());
        pageQueryDTO.setPageSize(queryReq.getPageSize());
        if (StringUtils.isNotBlank(queryReq.getHandinTaskNo())) {
            pageQueryDTO.setOrderCodes(getOrderCodesByTaskNo(queryReq.getHandinTaskNo()));
        } else {
            pageQueryDTO.setOrderCodes(getOrderCodes(queryReq));
        }
        return workshopPitOrderService.getWorkshopPitOrderPage(pageQueryDTO);
    }

    /**
     * 根据交酒任务获取订单号列表
     * @param handinTaskNo
     * @return
     */
    private List<String> getOrderCodesByTaskNo(String handinTaskNo) {
        return handinTaskMapper.selectOrderCodesByTaskNo(handinTaskNo);
    }

    /**
     * 获取订单号
     * @param queryReq
     * @return
     */
    private List<String> getOrderCodes(BatchNodeQueryReq queryReq) {
        List<String> orderCodes = null;
        if (StringUtils.isNotBlank(queryReq.getCrushedGrainsLotId())) {
            orderCodes = workshopPitOrderPotTaskMapper.selectOrderNoByBatch(queryReq.getCrushedGrainsLotId(), "高粱");
        } else if (StringUtils.isNotBlank(queryReq.getBackAlcoholicLotId())) {
            orderCodes = workshopPitOrderPotTaskMapper.selectOrderNoByBatch(queryReq.getBackAlcoholicLotId(), "回酒");
        } else if (StringUtils.isNotBlank(queryReq.getRicehullLotId())) {
            orderCodes = workshopPitOrderPotTaskMapper.selectOrderNoByBatch(queryReq.getRicehullLotId(), "稻壳");
        } else if (StringUtils.isNotBlank(queryReq.getQuLotId())) {
            orderCodes = workshopPitOrderPotTaskMapper.selectOrderNoByBatch(queryReq.getQuLotId(), "曲粉");
        }
        if (CollectionUtils.isEmpty(orderCodes)) {
            throw new IllegalArgumentException("订单数据信息不存在");
        }
        return orderCodes;
    }

    /**
     * 查询交酒任务节点数据
     * @param queryReq
     * @return
     */
    @Override
    public GetDataByPageVO<TPoWorkshopHandinTask> getHandinTaskPageByBatch(BatchNodeQueryReq queryReq) {
        QueryHandIn3DTO queryHandIn3DTO = new QueryHandIn3DTO();
        queryHandIn3DTO.setPage(queryReq.getPage());
        queryHandIn3DTO.setPageSize(queryReq.getPageSize());
        if (StringUtils.isNotBlank(queryReq.getHandinTaskNo())) {
            //反向批次查询的情况
            queryHandIn3DTO.setTaskCode(queryReq.getHandinTaskNo());
        } else {
            queryHandIn3DTO.setOrderCodes(getOrderCodes(queryReq));
        }
        return handinTaskService.selectAllData(queryHandIn3DTO);
    }

    /**
     * 根据批次查询批次图渲染数据--稻壳
     * @param batch
     * @return
     */
    @Override
    public ForwardBatchDetailVO getForwardBatchDetailByDK(String batch) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        vo.setBatch(batch);
        // 送货批次列表
        vo.setDkDeliveryBatchList(batchMapper.selectDeliveryBatchList(batch, null));
        // 稻壳转运
        vo.setDkWarehouse2BatchList(batchMapper.selectRiceTransferOrderBatch(batch, null));
        // 稻壳发放
        vo.setDkWarehouse1BatchList(batchMapper.selectRiceDispenseOrderBatch(batch, null));
        //蒸糠生产
        vo.setDkWarehouseTemporaryBatchList(batchMapper.selectBranProductionOrderBatch(batch, null));
        //熟糠传输
        vo.setDkWarehouseSmashBatchList(batchMapper.selectBranTransferOrderBatch(batch, null));
        //熟糠发放
        vo.setDkIssueBatchList(batchMapper.selectBranIssueOrderBatch(batch, null));
        putOrderData(batch, "稻壳", vo);
        return vo;
    }

    /**
     *
     * @param batch
     * @param type 高粱、稻壳、大曲、回酒
     * @param vo
     */
    private void putOrderData(String batch, String type, ForwardBatchDetailVO vo) {
        //甑口数据
        LambdaQueryWrapper<TPoWorkshopPitOrderPotTask> potTaskQuery = new QueryWrapper<TPoWorkshopPitOrderPotTask>()
                .select("id", "pot_serial_number").lambda()
                .eq(TPoWorkshopPitOrderPotTask::getIsDeleted, false)
                .isNotNull(TPoWorkshopPitOrderPotTask::getPotSerialNumber);
        if ("高粱".equals(type)) {
            potTaskQuery.eq(TPoWorkshopPitOrderPotTask::getCrushedGrainsQuantity, batch);
        } else if ("稻壳".equals(type)) {
            potTaskQuery.eq(TPoWorkshopPitOrderPotTask::getRicehullQuantity, batch);
        } else if ("回酒".equals(type)) {
            potTaskQuery.eq(TPoWorkshopPitOrderPotTask::getBackAlcoholicLotId, batch);
        } else if ("曲粉".equals(type)) {
            potTaskQuery.eq(TPoWorkshopPitOrderPotTask::getQuLotId, batch);
        }
        List<TPoWorkshopPitOrderPotTask> potTaskList = workshopPitOrderPotTaskMapper.selectList(potTaskQuery);
        vo.setPotTaskList(potTaskList.stream().map(TPoWorkshopPitOrderPotTask::getPotSerialNumber).filter(StringUtils::isNotBlank)
                .map(serialNumber -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(serialNumber);
                    return legendVO;
                })
                .collect(Collectors.toList()));
        //订单数据
        vo.setOrderList(workshopPitOrderPotTaskMapper.selectOrderNoByBatch(batch, type).stream()
                .filter(StringUtils::isNotBlank)
                .map(o -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(o);  // 设置legend字段
                    return legendVO;
                })
                .collect(Collectors.toList()));
        //交酒任务数据
        vo.setHandinTaskList(handinTaskMapper.selectTaskNoByBtach(batch, type));
    }

    /**
     * 根据批次查询批次图渲染数据--高粱
     * @param batch
     * @return
     */
    @Override
    public ForwardBatchDetailVO getForwardBatchDetailByGL(String batch) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        vo.setBatch(batch);
        // 送货批次列表
        vo.setGlDeliveryBatchList(batchMapper.selectDeliveryBatchList(batch, null));
        // 高粱转运
        vo.setGlWarehouse2BatchList(batchMapper.selectSorghumTransferOrderBatch(batch, null));
        // 高粱发放
        vo.setGlWarehouse1BatchList(batchMapper.selectSorghumShipmentOrderBatch(batch, null));
        // 高粱粉生产
        vo.setGlWarehouseTemporaryBatchList(batchMapper.selectSorghumProductionOrderBatch(batch, null));
        // 高粱粉传输
        vo.setGlWarehouseSmashBatchList(batchMapper.selectFlourTransferOrderBatch(batch, null));
        // 高粱粉发放
        vo.setGlIssueBatchList(batchMapper.selectSorghumDispenseOrderBatch(batch, null));
        putOrderData(batch, "高粱", vo);
        return vo;
    }

    /**
     * 根据批次查询批次图渲染数据--反向追溯
     * @param batch
     * @return
     */
    @Override
    public ForwardBatchDetailVO getReverseBatchDetail(String batch) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        //订单批次信息
        List<ReverseBatchOrderVO> reverseBatchDetailList = workshopPitOrderPotTaskMapper.selectPitOrderByHandinTask(batch);
        //订单信息
        List<String> orderCodeList = reverseBatchDetailList.stream().map(ReverseBatchOrderVO::getOrderCode).distinct().collect(Collectors.toList());
        vo.setOrderList(orderCodeList.stream()
                .filter(StringUtils::isNotBlank)
                .map(o -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(o);  // 设置legend字段
                    legendVO.setBatch(batch);
                    return legendVO;
                })
                .collect(Collectors.toList()));
        //交酒批次
        List<ForwardBatchDetailLegendVO> handinTaskList = new ArrayList<>();
        ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
        legendVO.setLegend(batch);
        legendVO.setBatch(batch);
        handinTaskList.add(legendVO);
        vo.setHandinTaskList(handinTaskList);
        //甑口批次
        vo.setPotTaskList(workshopPitOrderPotTaskMapper.selectSerialNumberByHandinTask(batch));
        putBatchData(vo, reverseBatchDetailList);
        return vo;
    }

    /**
     * 根据订单查询批次图渲染数据--用于节点查询
     * @param orderCode
     * @return
     */
    @Override
    public ForwardBatchDetailVO getForwardBatchDetailByOrder(String orderCode) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        //订单批次信息
        List<ReverseBatchOrderVO> batchDetailList = workshopPitOrderPotTaskMapper.selectPitOrderByOrderNo(orderCode);
        //订单信息
        List<String> orderCodeList = batchDetailList.stream().map(ReverseBatchOrderVO::getOrderCode).distinct().collect(Collectors.toList());
        vo.setOrderList(orderCodeList.stream()
                .filter(StringUtils::isNotBlank)
                .map(o -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(o);  // 设置legend字段
                    return legendVO;
                })
                .collect(Collectors.toList()));
        //交酒批次
        vo.setHandinTaskList(handinTaskMapper.getHandingTaskByOrder(orderCode));
        //甑口批次
        vo.setPotTaskList(workshopPitOrderPotTaskMapper.selectSerialNumberByOrder(orderCode));
        putBatchData(vo, batchDetailList);
        return vo;
    }

    /**
     * 处理原辅料批次数据
     * @param vo
     * @param batchDetailList
     */
    private void putBatchData(ForwardBatchDetailVO vo, List<ReverseBatchOrderVO> batchDetailList) {
        //回酒/曲粉批次
        vo.setHjBatchList(batchDetailList.stream()
                .map(ReverseBatchOrderVO::getBackAlcoholicLotId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .map(lotId -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(lotId);  // 根据实际需求设置legend字段
                    legendVO.setBatch(lotId);
                    return legendVO;
                })
                .collect(Collectors.toList()));
        vo.setDqBatchList(batchDetailList.stream()
                .map(ReverseBatchOrderVO::getQuLotId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .map(lotId -> {
                    ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
                    legendVO.setLegend(lotId);  // 根据实际需求设置legend字段
                    legendVO.setBatch(lotId);
                    return legendVO;
                })
                .collect(Collectors.toList()));
        //最终的高粱稻壳发放批次信息集合
        List<String> gl = batchDetailList.stream().map(ReverseBatchOrderVO::getCrushedGrainsLotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> dk = batchDetailList.stream().map(ReverseBatchOrderVO::getRicehullLotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gl)) {
            // 高粱送货车辆
            vo.setGlDeliveryBatchList(batchMapper.selectDeliveryBatchList(null, gl));
            // 高粱转运
            vo.setGlWarehouse2BatchList(batchMapper.selectSorghumTransferOrderBatch(null, gl));
            // 高粱发放
            vo.setGlWarehouse1BatchList(batchMapper.selectSorghumShipmentOrderBatch(null, gl));
            // 高粱粉生产
            vo.setGlWarehouseTemporaryBatchList(batchMapper.selectSorghumProductionOrderBatch(null, gl));
            // 高粱粉传输
            vo.setGlWarehouseSmashBatchList(batchMapper.selectFlourTransferOrderBatch(null, gl));
            // 高粱粉发放
            vo.setGlIssueBatchList(batchMapper.selectSorghumDispenseOrderBatch(null, gl));
        }

        if (CollectionUtils.isNotEmpty(dk)) {
            // 稻壳送货车辆
            vo.setDkDeliveryBatchList(batchMapper.selectDeliveryBatchList(null, dk));
            // 稻壳转运
            vo.setDkWarehouse2BatchList(batchMapper.selectRiceTransferOrderBatch(null, dk));
            // 稻壳发放
            vo.setDkWarehouse1BatchList(batchMapper.selectRiceDispenseOrderBatch(null, dk));
            // 蒸糠生产
            vo.setDkWarehouseTemporaryBatchList(batchMapper.selectBranProductionOrderBatch(null, dk));
            // 熟糠传输
            vo.setDkWarehouseSmashBatchList(batchMapper.selectBranTransferOrderBatch(null, dk));
            // 熟糠发放
            vo.setDkIssueBatchList(batchMapper.selectBranIssueOrderBatch(null, dk));
        }
    }

    /**
     * 查询回酒批次
     * @param batch
     * @return
     */
    @Override
    public ForwardBatchDetailVO getForwardBatchDetailByHj(String batch) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        vo.setBatch(batch);
        List<ForwardBatchDetailLegendVO> hjBatchList = new ArrayList<>();
        ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
        legendVO.setLegend(batch);
        hjBatchList.add(legendVO);
        vo.setHjBatchList(hjBatchList);
        putOrderData(batch, "回酒", vo);
        return vo;
    }

    /**
     * 查询曲粉批次
     * @param batch
     * @return
     */
    @Override
    public ForwardBatchDetailVO getForwardBatchDetailByQf(String batch) {
        ForwardBatchDetailVO vo = new ForwardBatchDetailVO();
        vo.setBatch(batch);
        List<ForwardBatchDetailLegendVO> dqBatchList = new ArrayList<>();
        ForwardBatchDetailLegendVO legendVO = new ForwardBatchDetailLegendVO();
        legendVO.setLegend(batch);
        dqBatchList.add(legendVO);
        vo.setDqBatchList(dqBatchList);
        putOrderData(batch, "曲粉", vo);
        return vo;
    }

    /**
     * 根据送货车次查询节点详情--车辆信息详情
     * @param deliveryNumber
     * @return
     */
    @Override
    public DeliveryNodeVO getNodeDetailByDelivery(String deliveryNumber) {
        DeliveryNodeVO deliveryNodeVO = batchMapper.selectNodeDetailByDelivery(deliveryNumber);
        //查询检验数据详情
        deliveryNodeVO.setInspectionDataDetailDTOList(batchMapper.selectInspectionDataByInspectionId(deliveryNodeVO.getInspectionId()));
        return deliveryNodeVO;
    }
}
