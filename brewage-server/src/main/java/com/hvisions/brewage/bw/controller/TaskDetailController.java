package com.hvisions.brewage.bw.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.bw.dao.BatchMapper;
import com.hvisions.brewage.bw.dao.TaskMapper;
import com.hvisions.brewage.bw.dto.IssueReportQueryReq;
import com.hvisions.brewage.bw.dto.TaskDatailReviewQueryReq;
import com.hvisions.brewage.bw.dto.TaskDetailQueryReq;
import com.hvisions.brewage.bw.entity.Batch;
import com.hvisions.brewage.bw.entity.Task;
import com.hvisions.brewage.bw.entity.TaskDetail;
import com.hvisions.brewage.bw.service.ITaskDetailService;
import com.hvisions.brewage.bw.vo.IssueReportVO;
import com.hvisions.brewage.bw.vo.TaskDetailReviewVO;
import com.hvisions.brewage.bw.vo.TaskDetailVO;
import com.hvisions.brewage.consts.CommonConsts;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.powder.client.SapPostClient;
import com.hvisions.powder.dto.qudou.SapPostQueryDTO;
import com.hvisions.powder.dto.qudou.SapPostVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 回酒需求任务单详情管理相关接口
 *
 * <AUTHOR>
 * 2022-04-25
 */
@RestController
@RequestMapping("/system/TaskDetail")
@Api(value = "回酒需求任务单详情管理相关接口", tags = "回酒需求任务单详情管理相关接口")
public class TaskDetailController {

    @Autowired
    ITaskDetailService baseService;
    @Autowired
    BaseWrapper baseWrapper;

    @Resource
    private SapPostClient sapPostClient;

    @Autowired
    private TaskMapper taskMapper;

    @Resource
    private BatchMapper batchMapper;

    /**
     * 查询分页回酒需求任务单详情
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页回酒需求任务单详情")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<TaskDetailVO> queryPagedTaskDetail(TaskDetailQueryReq req, PageInfo pageInfo) {

        boolean dateBoolean = Objects.nonNull(req.getBeginDate()) && Objects.nonNull(req.getEndDate());
        boolean subBoolean = Objects.nonNull(req.getSubmitBegin()) && Objects.nonNull(req.getSubmitEnd());

        LocalDateTime issueBeginTime = null;
        LocalDateTime issueEndTime = null;
        if (req.getBeginDate() != null && req.getEndDate() != null) {
            issueBeginTime = com.hvisions.brewage.utils.DateUtil.getBeginLocalDate(req.getBeginDate());
            issueEndTime = com.hvisions.brewage.utils.DateUtil.getEndLocalDate(req.getEndDate());
        }


        LocalDateTime submissionBeginTime = null;
        LocalDateTime submissionEndTime = null;

        if (req.getSubmitBegin() != null && req.getSubmitEnd() != null) {
            submissionBeginTime = com.hvisions.brewage.utils.DateUtil.getBeginLocalDate(req.getSubmitBegin());
            submissionEndTime = com.hvisions.brewage.utils.DateUtil.getEndLocalDate(req.getSubmitEnd());
        }
        if (StringUtils.isNotBlank(req.getBatch())) {
            Batch batch = batchMapper.selectOne(new LambdaUpdateWrapper<Batch>().eq(Batch::getBatch, req.getBatch()).eq(Batch::getDeleted, false));
            if (batch != null) {
                req.setBatchId(batch.getId());
            }
        }

        LocalDateTime finalIssueBeginTime = issueBeginTime;
        LocalDateTime finalIssueEndTime = issueEndTime;
        LocalDateTime finalSubmissionBeginTime = submissionBeginTime;
        LocalDateTime finalSubmissionEndTime = submissionEndTime;
        LambdaQueryWrapper<TaskDetail> wrapper = new LambdaQueryWrapper<TaskDetail>()
                .eq(Objects.nonNull(req.getMaterialId()), TaskDetail::getMaterialId, req.getMaterialId())
                .eq(Objects.nonNull(req.getCenterId()), TaskDetail::getCenterId, req.getCenterId())
                .eq(Objects.nonNull(req.getLocationId()), TaskDetail::getLocationId, req.getLocationId())
                .eq(Objects.nonNull(req.getBatchId()), TaskDetail::getBatchId, req.getBatchId())
                .and(dateBoolean, it -> it
                                .between(dateBoolean, TaskDetail::getIssuedBeginTime, finalIssueBeginTime, finalIssueEndTime)
                        // 2022 10 19 BUG #3426 经jk确认，仅需开始时间在区间内即可
                        //                     .or()
                        //                    .between(dateBoolean, TaskDetail::getIssuedEndTime, req.getBeginDate(), req.getEndDate())
                )
                // 2022 10 19 BUG #3426 增加提报时间
                .and(subBoolean, it -> it
                        .between(subBoolean, TaskDetail::getSubmissionTime, finalSubmissionBeginTime, finalSubmissionEndTime)
                )
                //.orderByAsc(TaskDetail::getIssuedEndTime);
                //2022 10 18 禅道3407 排序根据创建时间由近到远排序  2022 10 19 BUG #3426 页面根据提报时间倒序排序
                .orderByDesc(TaskDetail::getCreateTime, TaskDetail::getIssuedBeginTime);
        IPage<TaskDetail> page = baseService.page(new Page<>(pageInfo.getPage(), pageInfo.getPageSize()), wrapper);

        List<TaskDetail> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Integer> sapPostIdList = records.stream().map(TaskDetail::getSapPostId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sapPostIdList)) {
                SapPostQueryDTO sapPostQueryDTO = new SapPostQueryDTO();
                sapPostQueryDTO.setIds(sapPostIdList);
                List<SapPostVO> sapPostVOList = sapPostClient.getSapPostList(sapPostQueryDTO).getData();
                Map<Integer, SapPostVO> mapById = sapPostVOList.stream()
                        .collect(Collectors.toMap(SapPostVO::getId, v -> v));
                records.forEach(it -> {
                    if (Objects.isNull(it.getSapPostId())) {
                        return;
                    }
                    Optional.ofNullable(mapById.get(it.getSapPostId()))
                            .ifPresent(sp -> {
                                it.setTransferVoucherNumber(sp.getCertificateNumber());
                                it.setSapState(sp.getState());
                            });
                });
                page.setRecords(records);
            }
        }
        Page<TaskDetailVO> taskDetailVOS = baseWrapper.convertToPage(page, TaskDetailVO.class);
        List<TaskDetailVO> list = taskDetailVOS.getRecords();
        list.forEach(f -> {
            Task task = taskMapper.selectById(f.getTaskId());
            if (task != null) {
                f.setTaskOrder(task.getTaskOrder());
                f.setPlanTime(task.getPlanTime());
            }
        });
        taskDetailVOS.setRecords(list);
        return taskDetailVOS;
    }

    /**
     * 根据批次查询数据
     */
    @GetMapping("/getByBatch/{batch}")
    @ApiOperation(value = "根据批次查询数据")
    public List<TaskDetailVO> getByBatch(@ApiParam(value = "batch") @PathVariable String batch) {

        Batch batchData = batchMapper.selectOne(new LambdaUpdateWrapper<Batch>().eq(Batch::getBatch, batch).eq(Batch::getDeleted, false));
        if (batchData == null) {
            throw new BaseKnownException(10000, "批次信息不存在");
        }
        List<TaskDetail> records = baseService.list(new LambdaUpdateWrapper<TaskDetail>()
                .eq(TaskDetail::getBatchId, batchData.getId())
                .orderByDesc(TaskDetail::getCreateTime, TaskDetail::getIssuedBeginTime));
        List<TaskDetailVO> taskDetailVOS = baseWrapper.convertToList(records, TaskDetailVO.class);
        taskDetailVOS.forEach(f -> {
            Task task = taskMapper.selectById(f.getTaskId());
            if (task != null) {
                f.setTaskOrder(task.getTaskOrder());
                f.setPlanTime(task.getPlanTime());
            }
        });
        return taskDetailVOS;
    }

    /**
     * 回酒发放报表
     *
     * @param req
     * @return java.util.List<com.hvisions.brewage.bw.vo.IssueReportVO>
     * <AUTHOR>
     * @date 2022/8/5 15:53
     */
    @GetMapping("/issueReport")
    @ApiOperation(value = "回酒发放报表")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public List<IssueReportVO> queryIssueReport(IssueReportQueryReq req) {
        return baseService.queryIssueReport(req);
    }

    /**
     * 回酒发放报表
     *
     * @return java.util.List<com.hvisions.brewage.bw.vo.IssueReportVO>
     * <AUTHOR>
     * @date 2022/8/5 15:53
     */
    @PostMapping("/taskDetailReview")
    @ApiOperation(value = "回酒追溯")
    public List<TaskDetailReviewVO> getTaskDetailByQuery(@RequestBody TaskDatailReviewQueryReq req) {
        return baseService.getTaskDetailByQuery(req.getLocationId(), req.getBegin(), req.getEnd());
    }

    /**
     * 撤销
     *
     * @param sapPostId
     * @return void
     * <AUTHOR>
     * @date 2022/8/24 13:48
     */
    @PostMapping("/revoke/{sapPostId}")
    @ApiOperation("撤销sap")
    public void revoke(@PathVariable Integer sapPostId) {
        sapPostClient.revokeSapPost(sapPostId);
    }
}