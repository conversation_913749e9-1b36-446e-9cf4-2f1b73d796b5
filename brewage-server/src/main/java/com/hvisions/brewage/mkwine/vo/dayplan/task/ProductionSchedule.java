package com.hvisions.brewage.mkwine.vo.dayplan.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("日计划排产订单查询")
@Data
public class ProductionSchedule {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "封窖完成时间-发酵开始时间")
    private Date sealConfirmTime;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "层次")
    private Integer layer;

    @ApiModelProperty(value = "窖池id")
    private Integer pitId;

    @ApiModelProperty(value = "窖池号-连窖")
    private String fullPitId;

    @ApiModelProperty(value = "糟源名称")
    private String vinasseName;

    @ApiModelProperty(value = "入窖甑口")
    private Integer inPitNum;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "糟源类别")
    private Integer categoryId;

    @ApiModelProperty(value = "糟源id")
    private Integer vinasseId;

    @ApiModelProperty(value = "剩余甑口数量")
    private Integer remainNum;

    @ApiModelProperty(value = "是否是丢糟订单")
    private boolean isDiscarded;
}
