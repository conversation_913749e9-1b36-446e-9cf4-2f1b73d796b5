package com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ExecuteDayPlanTaskDTO {

    @ApiModelProperty(value = "车间")
    private Integer locationId;

    @ApiModelProperty(value = "中心")
    private Integer centerId;

    @ApiModelProperty(value = "生成的日期")
    private String planDate;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;
}
