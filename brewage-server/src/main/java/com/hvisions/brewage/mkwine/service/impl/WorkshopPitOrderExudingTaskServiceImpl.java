package com.hvisions.brewage.mkwine.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dao.base.ProdConfigItemMapper;
import com.hvisions.brewage.dto.mkwine.vo.PageVO;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoWorkshopPitOrderExudingRecommendMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoWorkshopPitOrderExudingTaskDetailMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.WorkshopPitOrderExudingTaskMapper;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.ExecuteDayPlanTaskDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.ExudingQueryOrderDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.ExudingTaskExecuteDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskPageDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingTask;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingTaskDetail;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderExudingTaskService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.ExudingTaskByOrderVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.OrderExudingVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import com.hvisions.brewage.service.tpo.TaskProcessRecordsService;
import com.hvisions.brewage.utils.CollectionListUtils;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: brewage
 * @description: 滴窖任务
 * @author: DengWeiTao
 **/
@Slf4j
@Service
public class WorkshopPitOrderExudingTaskServiceImpl implements WorkshopPitOrderExudingTaskService {

    @Autowired
    private WorkshopPitOrderExudingTaskMapper workshopPitOrderExudingTaskMapper;

    @Resource
    private TPoWorkshopPitOrderExudingTaskDetailMapper workshopPitOrderExudingTaskDetailMapper;

    @Resource
    private WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;

    @Resource
    private TPoWorkshopPitOrderExudingRecommendMapper exudingRecommendMapper;

    @Resource
    private DayPlanTaskService dayPlanTaskService;

    @Resource
    ProdConfigItemMapper prodConfigItemMapper;


    /**
     *获取全部滴窖任务
     */
    @Override
    public List<WorkshopPitOrderExudingTaskVO> getAllWorkshopPitOrderExudingTask() {
        return workshopPitOrderExudingTaskMapper.getAllWorkshopPitOrderExudingTask();
    }

    /**
     *分页获取滴窖任务
     */
    @Override
    public PageVO<WorkshopPitOrderExudingTaskVO> getWorkshopPitOrderExudingTaskByPage(
            WorkshopPitOrderExudingTaskPageDTO workshopPitOrderExudingTaskPageDTO) {
        workshopPitOrderExudingTaskPageDTO.setPage(
                (workshopPitOrderExudingTaskPageDTO.getPage() - 1) * workshopPitOrderExudingTaskPageDTO.getPageSize());

        PageVO<WorkshopPitOrderExudingTaskVO> pageVO = new PageVO<>();

        pageVO.setData(
                workshopPitOrderExudingTaskMapper.getWorkshopPitOrderExudingTaskByPage(workshopPitOrderExudingTaskPageDTO));

        pageVO.setCount(workshopPitOrderExudingTaskMapper.getCount(workshopPitOrderExudingTaskPageDTO));
        return pageVO;
    }

    /**
     *窖池订单查询接口
     */
    @Override
    public PageVO<OrderExudingVO> getOrder(ExudingQueryOrderDTO exudingQueryOrderDTO) {
        if (exudingQueryOrderDTO.getCenterId() == null || exudingQueryOrderDTO.getLocationId() == null) {
            throw new RuntimeException("中心车间不能为空");
        }
        // 查询出关联的窖池订单id
        int[] orderIds = workshopPitOrderExudingTaskMapper.getOrderIds(exudingQueryOrderDTO.getCenterId(), exudingQueryOrderDTO.getLocationId());
        exudingQueryOrderDTO.setPage((exudingQueryOrderDTO.getPage() - 1) * exudingQueryOrderDTO.getPageSize());
        PageVO<OrderExudingVO> pageVO = new PageVO<>();
        // 剔除掉推荐的订单
        List<OrderExudingVO> orderRecommend = getOrderRecommend(exudingQueryOrderDTO);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderRecommend)) {
            exudingQueryOrderDTO.setNotInList(orderRecommend.stream().map(OrderExudingVO::getId).collect(Collectors.toList()));
        }
        // 筛选没有跟滴窖任务关联的窖池订单
        pageVO.setData(workshopPitOrderExudingTaskMapper.getOrder(exudingQueryOrderDTO, orderIds));
        pageVO.setCount(workshopPitOrderExudingTaskMapper.getCountOrder(exudingQueryOrderDTO, orderIds));
        return pageVO;
    }

    /**
     *删除滴窖任务
     */
    @Override
    public ResultVO deleteWorkshopPitOrderExudingTaskById(Integer id) {
        TPoWorkshopPitOrderExudingTask exudingTask = workshopPitOrderExudingTaskMapper.getById(id);
        if(null==exudingTask){
            return ResultVO.error("删除滴窖任务失败");
        }

        //删除PDA个人任务执行进度
        taskProcessRecordsService.deleteTask(exudingTask.getExudingTaskCode(), ExecutionStatus.CELLAR_DRAIN.getStatus());

        if (workshopPitOrderExudingTaskMapper.deleteWorkshopPitOrderExudingTaskById(id))
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        return ResultVO.error("删除滴窖任务失败");
    }

    /**
     *添加滴窖任务
     */
    @Override
    public synchronized ResultVO addWorkshopPitOrderExudingTask(List<WorkshopPitOrderExudingTaskDTO> workshopPitOrderExudingTaskDTOS) {
        int j = workshopPitOrderExudingTaskDTOS.size();
        for (WorkshopPitOrderExudingTaskDTO workshopPitOrderExudingTaskDTO : workshopPitOrderExudingTaskDTOS) {
            j--;
            if (workshopPitOrderExudingTaskDTO.getCenterId() == null || workshopPitOrderExudingTaskDTO.getLocationId() == null ||
                    workshopPitOrderExudingTaskDTO.getId() == null) {
                throw new RuntimeException("中心、车间、窖池订单id不能为空");
            }

            // 根据窖池订单id查询窖池订单
            WorkshopPitOrderExudingTaskVO vo = workshopPitOrderExudingTaskMapper.getOrderById(workshopPitOrderExudingTaskDTO.getId());

            if (vo == null) {
                throw new RuntimeException("id为 " + workshopPitOrderExudingTaskDTO.getId() + " 的窖池订单不存在");
            }

            // 新增校验
            Integer nums = workshopPitOrderExudingTaskMapper.insertCheck(workshopPitOrderExudingTaskDTO.getCenterId(), workshopPitOrderExudingTaskDTO.getLocationId(), workshopPitOrderExudingTaskDTO.getId());
            if (nums != null && nums > 0) {
                throw new RuntimeException("已经存在跟订单 " + vo.getOrderCode() + " 关联的滴窖任务，不能重复关联");
            }

            Integer maxId = workshopPitOrderExudingTaskMapper.getMaxId();
            if (maxId == null) maxId = 1;
            else maxId = maxId + 1;

            // 按规则生成滴窖任务号，滴窖+创建年月日+四位流水码，比如 DJ202007210001
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            StringBuilder exudingTaskCode = new StringBuilder("DJ" + format.format(new Date()));
            // 自动补 0
            if (maxId < 1000) {
                for (int i = 0; i < 4 - (maxId + "").length(); i++) {
                    exudingTaskCode.append(0);
                }
            }
            exudingTaskCode.append(maxId);

            workshopPitOrderExudingTaskDTO.setExudingTaskCode(exudingTaskCode.toString());
            workshopPitOrderExudingTaskDTO.setStatus(workshopPitOrderExudingTaskDTO.getStatus() == null ? 1 : workshopPitOrderExudingTaskDTO.getStatus());
            workshopPitOrderExudingTaskDTO.setId(maxId);
            workshopPitOrderExudingTaskDTO.setCreateTime(new Date());
            workshopPitOrderExudingTaskDTO.setLastExecTime(new Date());
            workshopPitOrderExudingTaskDTO.setIsRemind("0");
            workshopPitOrderExudingTaskDTO.setPitId(vo.getPitId());
            workshopPitOrderExudingTaskDTO.setPitCode(vo.getPitCode());
            workshopPitOrderExudingTaskDTO.setOrderCode(vo.getOrderCode());
            workshopPitOrderExudingTaskDTO.setInPitDate(vo.getInPitDate());
            if (workshopPitOrderExudingTaskMapper.addWorkshopPitOrderExudingTask(workshopPitOrderExudingTaskDTO)) {
                //新增窖池订单执行状态--发酵结束
                workshopPitOrderService.putExecutionStatus(workshopPitOrderExudingTaskDTO.getOrderCode(), ExecutionStatus.FERMENTATION.getStatus(),9);
                workshopPitOrderService.putExecutionStatus(workshopPitOrderExudingTaskDTO.getOrderCode(), ExecutionStatus.CELLAR_DRAIN.getStatus(),1);

                //创建PDA个人任务执行进度
                taskProcessRecordsService.addTask(exudingTaskCode.toString(),workshopPitOrderExudingTaskDTO.getPitCode(),ExecutionStatus.CELLAR_DRAIN.getStatus(),workshopPitOrderExudingTaskDTO.getCenterId(),workshopPitOrderExudingTaskDTO.getLocationId());

                if (workshopPitOrderExudingTaskDTO.getStatus() == 2) {
                    // 滴窖中，更新执行开始时间
                    workshopPitOrderExudingTaskMapper.updateExecTime(workshopPitOrderExudingTaskDTO.getId(), new Date());
                }
                if (workshopPitOrderExudingTaskDTO.getStatus() == 3) {
                    // 查询这条数据是否存在执行开始时间，没有的话给它添加上去
                    Date execStartTime = workshopPitOrderExudingTaskMapper.getExudingTaskById(workshopPitOrderExudingTaskDTO.getId());
                    if (execStartTime == null) {
                        // 更新执行开始时间
                        workshopPitOrderExudingTaskMapper.updateExecTime(workshopPitOrderExudingTaskDTO.getId(), new Date());
                    }
                    workshopPitOrderExudingTaskMapper.updateCompleteTime(workshopPitOrderExudingTaskDTO.getId(), new Date());
                }
                if (j == 0) {
                    return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                }
            }

       }
        return ResultVO.error("添加滴窖任务失败");
    }

    /**
     *修改滴窖任务
     */
    @Override
    public ResultVO updateWorkshopPitOrderExudingTaskById(WorkshopPitOrderExudingTaskDTO workshopPitOrderExudingTaskDTO) {
        if (workshopPitOrderExudingTaskDTO.getStatus() == 2) {
            // 滴窖中
            workshopPitOrderExudingTaskDTO.setExecStartTime(new Date());
        }
        if (workshopPitOrderExudingTaskDTO.getStatus() == 3) {
            // 根据id获取执行开始时间
            Date execStartTime = workshopPitOrderExudingTaskMapper.getExecStartTime(workshopPitOrderExudingTaskDTO.getId());
            if (execStartTime == null) workshopPitOrderExudingTaskDTO.setExecStartTime(new Date());
            // 已完成
            workshopPitOrderExudingTaskDTO.setCompleteTime(new Date());
        }
        if (workshopPitOrderExudingTaskMapper.updateWorkshopPitOrderExudingTaskById(workshopPitOrderExudingTaskDTO))
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        return ResultVO.error("修改滴窖任务失败");
    }

    /**
     * 更新滴窖任务状态
     * @param id
     * @param status
     * @return
     */
    @Override
    public ResultVO updateExudingTaskStatus(Integer id, Integer status) {
        if (workshopPitOrderExudingTaskMapper.updateExudingTaskStatus(id, status)) {
            if (status == 2) {
                // 滴窖中，更新执行开始时间
                workshopPitOrderExudingTaskMapper.updateExecTime(id, new Date());
            }
            if (status == 3) {
                workshopPitOrderExudingTaskMapper.updateCompleteTime(id, new Date());
            }
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("更新滴窖任务状态失败");
    }

    /**
     * 滴窖执行（开始/结束滴窖）
     * @param exudingTaskExecuteDTO
     * @return
     */
    @Override
    @Transactional
    public ResultVO exudingTaskExecute(ExudingTaskExecuteDTO exudingTaskExecuteDTO) {
        Date endTime = new Date();
        for (Integer id : exudingTaskExecuteDTO.getIds()) {
            if ("1".equals(exudingTaskExecuteDTO.getType())) {
                //开始滴窖
                workshopPitOrderExudingTaskMapper.updateExecTime(id, new Date());
                workshopPitOrderExudingTaskMapper.updateExudingTaskStatus(id, 2);
            } else {
                //结束滴窖更改数据
                workshopPitOrderExudingTaskMapper.updateExudingTaskExecuteEnd(id, endTime);
                //更改状态为待执行-等待下次滴窖
                workshopPitOrderExudingTaskMapper.updateExudingTaskStatus(id, 9);
                Date startTime = workshopPitOrderExudingTaskMapper.getExudingTaskById(id);
                //结束滴窖 生成操作记录详情
                TPoWorkshopPitOrderExudingTaskDetail detail = new TPoWorkshopPitOrderExudingTaskDetail();
                detail.setExudingTaskId(id);
                detail.setStartTime(startTime);
                detail.setEndTime(endTime);
                workshopPitOrderExudingTaskDetailMapper.insert(detail);
            }
        }
        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
    }

    /**
     * 获取滴窖记录详情列表
     * @param id
     * @return
     */
    @Override
    public ResultVO getExudingTaskDetailList(Integer id) {
        return ResultVO.success(workshopPitOrderExudingTaskDetailMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderExudingTaskDetail>().eq(TPoWorkshopPitOrderExudingTaskDetail::getExudingTaskId, id)));
    }

    /**
     * 查询订单滴窖任务信息
     * @param orderCode
     * @return
     */
    @Override
    public ExudingTaskByOrderVO getExudingTaskByOrder(String orderCode) {
        ExudingTaskByOrderVO vo = new ExudingTaskByOrderVO();
        TPoWorkshopPitOrderExudingTask task = workshopPitOrderExudingTaskMapper.selectByOrderCode(orderCode);
        if (task == null) {
            return vo;
        }
        List<TPoWorkshopPitOrderExudingTaskDetail> details = workshopPitOrderExudingTaskDetailMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderExudingTaskDetail>()
                .eq(TPoWorkshopPitOrderExudingTaskDetail::getExudingTaskId, task.getId()));
        vo.setPitCode(task.getPitCode());
        if (CollectionUtils.isNotEmpty(details)) {
            vo.setNum(details.size());
            vo.setStartTime(details.stream()
                    .map(TPoWorkshopPitOrderExudingTaskDetail::getStartTime)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .min(Comparator.naturalOrder())
                    .orElse(null));
            vo.setEndTime(details.stream()
                    .map(TPoWorkshopPitOrderExudingTaskDetail::getEndTime)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .min(Comparator.naturalOrder())
                    .orElse(null));
        } else {
            vo.setNum(0);
        }
        return vo;
    }

    /**
     *
     * @param exudingQueryOrderDTO
     * @return
     */
    @Override
    public List<OrderExudingVO> getOrderRecommend(ExudingQueryOrderDTO exudingQueryOrderDTO) {
        //查询接下来7天的预排数据
        ExecuteDayPlanTaskDTO executeDayPlanTaskDTO = new ExecuteDayPlanTaskDTO();
        executeDayPlanTaskDTO.setLocationId(exudingQueryOrderDTO.getLocationId());
        executeDayPlanTaskDTO.setCenterId(exudingQueryOrderDTO.getCenterId());
        //查询滴窖推荐配置
        ProdConfigItem configItem = prodConfigItemMapper.selectOne(new LambdaUpdateWrapper<ProdConfigItem>()
                .eq(ProdConfigItem::getWorkshop, exudingQueryOrderDTO.getLocationId())
                .eq(ProdConfigItem::getDeleted, false)
                .like(ProdConfigItem::getItemName, "滴窖推荐周期")
                .orderByDesc(ProdConfigItem::getId).last("limit 1"));
        if (configItem == null) {
            throw new BaseKnownException(10000, "未设置 滴窖推荐周期 ,请先设置参数");
        }
        //查询发酵周期配置
        List<ProdConfigItem> fermentList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>()
                .eq(ProdConfigItem::getWorkshop, exudingQueryOrderDTO.getLocationId())
                .eq(ProdConfigItem::getDeleted, false)
                .like(ProdConfigItem::getItemName, "发酵周期"));
        if (CollectionUtils.isEmpty(fermentList)) {
            throw new BaseKnownException(10000, "未设置 发酵周期 ,请先设置参数");
        }
        //根据推荐周期来获取预排的日期范围
        Integer day = Integer.valueOf(configItem.getItemValue());
        List<String> weekDateList = DateUtil.getWeekDateList(LocalDate.now(), day, true);
        executeDayPlanTaskDTO.setStartDate(weekDateList.get(0));
        executeDayPlanTaskDTO.setEndDate(weekDateList.get(weekDateList.size() -1));
        List<WorkshopPitDayPlanDTO> workshopPitDayPlanDTOS = dayPlanTaskService.executeCycleDayPlanTask(executeDayPlanTaskDTO);
        List<Integer> orderIds = workshopPitDayPlanDTOS.stream().map(WorkshopPitDayPlanDTO::getOrderId).distinct().collect(Collectors.toList());
        //获取已经在滴窖的任务
        List<ProductionSchedule> exudingTaskOrderList = exudingRecommendMapper.selectExudingTaskOrder(exudingQueryOrderDTO);
        //订单in查询--已经滴窖的任务的订单不再推荐，预排的订单要推荐
        exudingQueryOrderDTO.setNotInList(exudingTaskOrderList.stream().map(ProductionSchedule::getId).collect(Collectors.toList()));
        exudingQueryOrderDTO.setInList(orderIds);
        List<OrderExudingVO> orderRecommend = exudingRecommendMapper.getOrderRecommend(exudingQueryOrderDTO);
        for (OrderExudingVO orderExudingVO : orderRecommend) {
            //设置开窖周期
            WorkshopPitDayPlanDTO first = workshopPitDayPlanDTOS.stream().filter(p -> Objects.equals(p.getOrderId(), orderExudingVO.getId())).findFirst().orElse(null);
            orderExudingVO.setPlanDate(first != null ? first.getPlanDate():null);
            //发酵周期
            ProdConfigItem ferment = fermentList.stream().filter(f -> f.getItemName().contains(orderExudingVO.getVinasseCode())).findFirst().orElse(null);
            orderExudingVO.setFermentationCycle(ferment != null ? Integer.valueOf(ferment.getItemValue()):null);
        }
        orderRecommend.sort(Comparator.comparing(OrderExudingVO::getPlanDate));
        return orderRecommend;
    }

    @Override
    public void endTaskByOrder(String pitOrder) {
        TPoWorkshopPitOrderExudingTask task = workshopPitOrderExudingTaskMapper.selectByOrderCode(pitOrder);
        if (task != null) {
            updateExudingTaskStatus(task.getId(), 3);
        }
    }
}
