package com.hvisions.brewage.mkwine.entity.dayplan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 滴窖推荐
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_po_workshop_pit_order_exuding_recommend")
@ApiModel(value="TPoWorkshopPitOrderExudingRecommend对象", description="滴窖推荐")
public class TPoWorkshopPitOrderExudingRecommend implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "创建人")
    private Integer creatorId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private Integer updaterId;

    @ApiModelProperty(value = "逻辑删除标记")
    private Boolean deleted;

    @ApiModelProperty(value = "窖池ID")
    private Integer pitId;

    @ApiModelProperty(value = "计划剩余甑口（计划出窖 - 已出窖）")
    private Integer balancePotCount;

    @ApiModelProperty(value = "预计出窖甑口（根据涨幅计算获得）")
    private Integer calcOutPotCount;

    @ApiModelProperty(value = "糟源类别")
    private Integer categoryId;

    @ApiModelProperty(value = "是否开窖")
    private Boolean isOpened;

    @ApiModelProperty(value = "窖池订单ID")
    private Integer orderId;

    @ApiModelProperty(value = "计划时间")
    private LocalDate planDate;

    @ApiModelProperty(value = "计划出窖甑口（默认等于预计出窖甑口，可以手工修改）")
    private Integer planOutPotCount;

    @ApiModelProperty(value = "计划总甑口")
    private Integer potCount;

    @ApiModelProperty(value = "顺序")
    private Integer sortNo;

    @ApiModelProperty(value = "中心id")
    private Integer workshopCenterId;

    @ApiModelProperty(value = "车间id")
    private Integer workshopId;

    @ApiModelProperty(value = "基础糟源比例")
    private Integer baseVinasseId;

    @ApiModelProperty(value = "订单剩余数量--如果没有排完则剩下的数量")
    private Integer orderRemainderCount;

    @ApiModelProperty(value = "糟源类型")
    private String vinasseCode;

    @ApiModelProperty(value = "排产类型 1.开工排产；2.连续排产；3.停工前排产")
    private String scheduleType;


}
