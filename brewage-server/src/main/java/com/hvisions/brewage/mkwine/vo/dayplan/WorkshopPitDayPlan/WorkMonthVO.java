package com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan;

import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitDayPlan;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: brewage
 * @description: 自然月单个车间剩余计划生产甑口数据返回VO
 * @author: DengWeiTao
 **/
@ApiModel("自然月单个车间剩余计划生产甑口数据返回VO")
@Data
public class WorkMonthVO {

    @ApiModelProperty(value = "年份")
    private Integer cycleYear;

    @ApiModelProperty(value = "月份")
    private Integer cycleMonth;

    @ApiModelProperty(value = "工作天数")
    private Integer workDay;

    @ApiModelProperty("月度开始时间")
    private LocalDate monthBeginDate;

    @ApiModelProperty("月度结束时间")
    private LocalDate monthEndDate;

    @ApiModelProperty("计划时间")
    private String planDate;

    @ApiModelProperty("预计空窖数量")
    private Integer planEmptyNum = 0;

    @ApiModelProperty("回酒计划使用量")
    private BigDecimal hjPlanQuality;

    @ApiModelProperty(value = "糟源甑口详细列表")
    private List<WorkMonthDetailVO> workMonthDetailVOList;

    @ApiModelProperty(value = "糟源甑口Map key:糟源; value:剩余生产的数量")
    private Map<String, Integer> detailMap;

    @ApiModelProperty(value = "物料数据详细列表")
    private List<WorkMonthMaterialVO> workMonthMaterialVOList;

    @ApiModelProperty(value = "未完成的日计划 -- 还需要执行的日计划")
    private List<TPoWorkshopPitDayPlan> continueDayPlans;

    @ApiModelProperty(value = "中心id")
    private Integer centerId;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

    @ApiModelProperty(value = "剩余甑口数量--上一个窖池投粮生产还差多少个甑口")
    private Integer remainPotNum = 0;

    @ApiModelProperty(value = "预备订单--未生产完的订单生成，用于下一天进行排产")
    private ProductionSchedule preparationSchedule;

    @ApiModelProperty(value = "已经排产的订单")
    private List<Integer> orderIds = new ArrayList<>();


    @ApiModelProperty(value = "入窖甑口数量--根据配置获取")
    private Integer inputNum;
    @ApiModelProperty(value = "空窖数量--根据配置获取")
    private Integer emptyPitNum;

    @ApiModelProperty(value = "涨幅--根据涨幅周期获取")
    private BigDecimal increaseAmplitude;
}
