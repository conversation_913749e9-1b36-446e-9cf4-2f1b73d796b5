package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.enums.ProdConfigEnum;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderOutAppraiseMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.WorkshopPitOrderExudingTaskMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.*;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshoppitSapApprove.WorkshoppitSapApproveInsertDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingTask;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.enums.OrderStatus;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderExudingTaskService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderOutAppraiseService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderService;
import com.hvisions.brewage.mkwine.service.WorkshoppitSapApproveService;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.URVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraisePageVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO;
import com.hvisions.brewage.service.tpo.*;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.SapSyncConfigVO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @program: brewage
 * @description: 开窖鉴定
 * @author: DengWeiTao
 **/
@Slf4j
@Service
public class WorkshopPitOrderOutAppraiseServiceImpl implements WorkshopPitOrderOutAppraiseService {

    @Autowired
    private WorkshopPitOrderOutAppraiseMapper workshopPitOrderOutAppraiseMapper;
    @Autowired
    private WorkshoppitSapApproveService sapApproveService;
    @Autowired
    private WorkshopPitOrderOutAppraiseService outAppraiseService;
    @Resource
    private WorkshopPitOrderMapper workshopPitOrderMapper;
    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;
    @Resource
    private TaskCellarLiftingService taskCellarLiftingService;
    @Resource
    private TaskPileUpService taskPileUpService;
    @Resource
    private TaskDishMixingService taskDishMixingService;
    @Resource
    private TaskShangzhengService taskShangzhengService;
    @Resource
    private TaskSistillationService taskSistillationService;
    @Resource
    private TaskWinePickingService taskWinePickingService;
    @Resource
    private TaskSteamingService taskSteamingService;
    @Resource
    private TaskDiscardService taskDiscardService;
    @Resource
    private WorkshopPitOrderService workshopPitOrderService;
    @Resource
    private TaskGrainLubricationService taskGrainLubricationService;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;

    @Resource
    private WorkshopFullPitMapper fullPitMapper;

    @Resource
    private WorkshopPitOrderExudingTaskMapper workshopPitOrderExudingTaskMapper;

    @Resource
    private WorkshopPitOrderExudingTaskService workshopPitOrderExudingTaskService;

    /**
     * 获取全部开窖鉴定，后面需要将开窖鉴定表中的order_id重新关联到 SAP 窖池订单同步表进行关联查询，现在没有这个表所以先查询窖池订单的
     *
     * @return
     */
    @Override
    public List<WorkshopPitOrderOutAppraiseVO> getAllWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO dto) {
        return workshopPitOrderOutAppraiseMapper.getAllWorkshopPitOrderOutAppraise(dto);
    }

    /**
     * 分页获取开窖鉴定，问题同上
     */
    @Override
    public WorkshopPitOrderOutAppraisePageVO getWorkshopPitOrderOutAppraiseByPage(
            WorkshopPitOrderOutAppraisePageDTO workshopPitOrderOutAppraisePageDTO) {
        // 查询
        workshopPitOrderOutAppraisePageDTO.setPage((workshopPitOrderOutAppraisePageDTO.getPage() - 1) * workshopPitOrderOutAppraisePageDTO.getPageSize());
        WorkshopPitOrderOutAppraisePageVO workshopPitOrderOutAppraisePageVO = new WorkshopPitOrderOutAppraisePageVO();
        workshopPitOrderOutAppraisePageVO.setData(workshopPitOrderOutAppraiseMapper.getWorkshopPitOrderOutAppraiseByPage(workshopPitOrderOutAppraisePageDTO));
        workshopPitOrderOutAppraisePageVO.setCount(workshopPitOrderOutAppraiseMapper.getCount(workshopPitOrderOutAppraisePageDTO));
        return workshopPitOrderOutAppraisePageVO;
    }

    /**
     * 更新执行状态
     */
    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    public ResultVO updateStatus(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        if (workshopPitOrderOutAppraiseMapper.updateStatus(workshopPitOrderOutAppraiseDTO)) {
            if (workshopPitOrderOutAppraiseDTO.getStatus() == 2) {
                updateCellarLiftingCompletedTime(workshopPitOrderOutAppraiseDTO);
                updateExuding(workshopPitOrderOutAppraiseDTO.getOrderId(), workshopPitOrderOutAppraiseDTO.getOutAppraiseTime());
            }
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        } else {
            return ResultVO.error("更新执行状态失败");
        }
    }

    /**
     * 日计划下发新增开窖鉴定
     */
    @Override
    public ResultVO DayPlanAddOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        // 新增校验
        Integer nums = workshopPitOrderOutAppraiseMapper.checkOrderId(workshopPitOrderOutAppraiseDTO.getOrderId());

        if (workshopPitOrderOutAppraiseDTO.getOutAppraiseTime() != null) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String date = format.format(workshopPitOrderOutAppraiseDTO.getOutAppraiseTime());
            workshopPitOrderOutAppraiseDTO.setOutAppraiseTime(DateUtil.toDateDay(date));
        }
        if (nums != null && nums > 0) {
            log.info("日计划下发新增开窖池鉴定失败, 已存在开窖鉴定, 订单id:" + workshopPitOrderOutAppraiseDTO.getOrderId());
            String data = null;
            try {
                data = JSONObject.toJSONString(workshopPitOrderOutAppraiseDTO);
            } catch (Exception e) {
                data = "转JSON数据格式失败";
            }
            workshopPitOrderOutAppraiseDTO.setNotes("该窖池订单已经进行开窖鉴定");
            workshopPitOrderOutAppraiseDTO.setUserName(data);
            workshopPitOrderOutAppraiseMapper.insertOutLog(workshopPitOrderOutAppraiseDTO);
            return ResultVO.error("该窖池订单已经进行开窖鉴定");
        }
        TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectById(workshopPitOrderOutAppraiseDTO.getOrderId());
        //新增窖池订单执行状态
        workshopPitOrderService.putExecutionStatus(tPoWorkshopPitOrder.getOrderCode(), ExecutionStatus.CELLAR_OPEN.getStatus(),1);
        //查询配置最大的流水号
        Integer maxNum = workshopPitOrderOutAppraiseMapper.selectMaxTaskNo(ProdConfigEnum.KQ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.KQ, maxNum);
        workshopPitOrderOutAppraiseDTO.setTaskNo(taskNo);
        workshopPitOrderOutAppraiseDTO.setTaskType("自动任务");
        if (workshopPitOrderOutAppraiseMapper.insertWorkshopPitOrderOutAppraise(workshopPitOrderOutAppraiseDTO)) {
            //查询连窖号
            TPoWorkshopFullPit tPoWorkshopFullPit = fullPitMapper.selectById(workshopPitOrderOutAppraiseDTO.getPitId());
            //创建PDA个人任务执行进度
            taskProcessRecordsService.addTask(workshopPitOrderOutAppraiseDTO.getTaskNo(),tPoWorkshopFullPit.getFullPitId(),ExecutionStatus.CELLAR_OPEN.getStatus(),workshopPitOrderOutAppraiseDTO.getCenterId(),workshopPitOrderOutAppraiseDTO.getLocationId());

            if (workshopPitOrderOutAppraiseDTO.getStatus() == 2) {
                updateCellarLiftingCompletedTime(workshopPitOrderOutAppraiseDTO);
                updateExuding(workshopPitOrderOutAppraiseDTO.getOrderId(), workshopPitOrderOutAppraiseDTO.getOutAppraiseTime());
            }

            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        } else {
            String data = null;
            try {
                data = JSONObject.toJSONString(workshopPitOrderOutAppraiseDTO);
            } catch (Exception e) {
                data = "转JSON数据格式失败";
            }
            workshopPitOrderOutAppraiseDTO.setNotes("新增开窖鉴定失败");
            workshopPitOrderOutAppraiseDTO.setUserName(data);
            workshopPitOrderOutAppraiseMapper.insertOutLog(workshopPitOrderOutAppraiseDTO);
            return ResultVO.error("新增开窖鉴定失败");
        }
    }

    /**
     * 新增开窖鉴定
     */
    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    public synchronized ResultVO insertWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {

        if (workshopPitOrderOutAppraiseDTO.getOrderId() == null) {
            return ResultVO.error("窖池订单不能为空");
        }

        if (workshopPitOrderOutAppraiseDTO.getStatus() == 2) {
            if (workshopPitOrderOutAppraiseDTO.getOrderId() == null) {
                throw new RuntimeException("开窖鉴定更新执行状态，窖池订单 id 不能为空");
            }
            // 查询窖池订单的滴窖任务
            List<WorkshopPitOrderExudingTaskVO> exudingTaskVO = workshopPitOrderOutAppraiseMapper.getExudingTask(workshopPitOrderOutAppraiseDTO.getOrderId());
            if (exudingTaskVO.size() > 1) {
                throw new RuntimeException("该窖池订单存在多个滴窖任务，请联系管理员进行排查");
            }

            if (exudingTaskVO.size() == 0 || exudingTaskVO.get(0).getStatus() == 1) {
                String orderCode = workshopPitOrderOutAppraiseMapper.getOrderCode(workshopPitOrderOutAppraiseDTO.getOrderId());
                throw new RuntimeException(orderCode + " 该订单对应的窖池还未滴窖，请优先执行滴窖！");
            }
        }

        // 该窖池订单不存在开窖鉴定，在判断日计划中是否存在该窖池订单，如果日计划中存在该窖池订单则不用走审批，直接进行开窖鉴定的新增操作
        Integer RJHNums = workshopPitOrderOutAppraiseMapper.judgmentRJH(workshopPitOrderOutAppraiseDTO.getOrderId());
        if (RJHNums != null && RJHNums > 0) {
            workshopPitOrderOutAppraiseDTO.setApproveStatus(2);
            // 日志
            workshopPitOrderOutAppraiseMapper.insertOutLog(workshopPitOrderOutAppraiseDTO);
            return outAppraiseService.DayPlanAddOutAppraise(workshopPitOrderOutAppraiseDTO);
        }

        // 新增校验
        Integer nums = workshopPitOrderOutAppraiseMapper.checkOrderId(workshopPitOrderOutAppraiseDTO.getOrderId());
        if (nums != null && nums > 0) {
            throw new RuntimeException("该窖池订单已经进行开窖鉴定，或者该窖池订单正在进行审批中，请勿重复申请");
        }

        // 获取一级审批人信息
        URVO urvo1 = workshopPitOrderOutAppraiseMapper.getRUInfo(workshopPitOrderOutAppraiseDTO.getFirstLevelApproveId());
        if (urvo1 == null) {
            throw new RuntimeException("一级审批人id不存在或者没有进行角色关联");
        }
        // 获取二级审批人信息
        URVO urvo2 = workshopPitOrderOutAppraiseMapper.getRUInfo(workshopPitOrderOutAppraiseDTO.getSecondLevelApproveId());
        if (urvo2 == null) {
            throw new RuntimeException("二级审批人id不存在或者没有进行角色关联");
        }
        workshopPitOrderOutAppraiseDTO.setFirstLevelApproveName(urvo1.getUserName());
        workshopPitOrderOutAppraiseDTO.setSecondLevelApproveName(urvo2.getUserName());

        //查询配置最大的流水号
        Integer maxNum = workshopPitOrderOutAppraiseMapper.selectMaxTaskNo(ProdConfigEnum.KQ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.KQ, maxNum);
        workshopPitOrderOutAppraiseDTO.setTaskNo(taskNo);
        workshopPitOrderOutAppraiseDTO.setTaskType("手动任务");
        workshopPitOrderOutAppraiseDTO.setStatus(2);
        if (workshopPitOrderOutAppraiseMapper.insertWorkshopPitOrderOutAppraise(workshopPitOrderOutAppraiseDTO)) {

            /* 新增开窖鉴定成功，往订单审批记录中新增一条审批记录 */
            // 获取申请人信息
            URVO ruInfo = workshopPitOrderOutAppraiseMapper.getRUInfo(workshopPitOrderOutAppraiseDTO.getUserId());
            // 获取当前新增的开窖鉴定id
            Integer maxId = workshopPitOrderOutAppraiseMapper.getMaxId();
            if (ruInfo == null) {
                throw new RuntimeException("申请人id不存在或者没有进行角色关联");
            }
            // 根据窖池订单id获取窖池订单编号
            String orderCode = workshopPitOrderOutAppraiseMapper.getOrderCode(workshopPitOrderOutAppraiseDTO.getOrderId());
            WorkshoppitSapApproveInsertDTO approveDTO = getApproveDTO(
                    maxId,
                    workshopPitOrderOutAppraiseDTO.getOrderId(),
                    workshopPitOrderOutAppraiseDTO.getNotes(),
                    workshopPitOrderOutAppraiseDTO.getUserId(),
                    ruInfo.getUserName(),
                    urvo1.getRoleId(),
                    urvo1.getRoleName(),
                    urvo1.getUserId(),
                    urvo1.getUserName(),
                    urvo2.getRoleId(),
                    urvo2.getRoleName(),
                    urvo2.getUserId(),
                    urvo2.getUserName(),
                    orderCode,
                    workshopPitOrderOutAppraiseDTO.getStatus());
            ResultVO resultVO = sapApproveService.insertApproveEvenCellar(approveDTO);
            if (resultVO.getCode() != 200) {
                throw new RuntimeException("该订单没有SAP订单号，请完成SAP同步后再进行新增！");
            }
            //自动开窖鉴定
            workshopPitOrderOutAppraiseDTO.setType(1);
            openCellarAppraisal(workshopPitOrderOutAppraiseDTO);
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        } else {
            return ResultVO.error("新增开窖鉴定失败");
        }
    }

    /**
     * 修改开窖鉴定
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public ResultVO updateWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        if (workshopPitOrderOutAppraiseMapper.updateWorkshopPitOrderOutAppraise(workshopPitOrderOutAppraiseDTO)) {
            if (workshopPitOrderOutAppraiseDTO.getStatus() == 2) {
                openCellarAppraisal(workshopPitOrderOutAppraiseDTO);
            }

            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        } else {
            return ResultVO.error("修改开窖鉴定失败");
        }
    }

    /**
     * 开窖鉴定
     * @param workshopPitOrderOutAppraiseDTO
     * @return
     */
    private void openCellarAppraisal(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO){
        updateCellarLiftingCompletedTime(workshopPitOrderOutAppraiseDTO);
        updateExuding(workshopPitOrderOutAppraiseDTO.getOrderId(), workshopPitOrderOutAppraiseDTO.getOutAppraiseTime());
        if(workshopPitOrderOutAppraiseDTO.getType() == 1){
            //追加窖池订单任务完成状态
            //开窖结束
            workshopPitOrderService.putExecutionStatus(workshopPitOrderOutAppraiseDTO.getPitOrder(), ExecutionStatus.CELLAR_OPEN.getStatus(),9);

            //结束PDA个人任务执行进度-开窖结束
            taskProcessRecordsService.closeTask(workshopPitOrderOutAppraiseDTO.getTaskNo(),ExecutionStatus.CELLAR_OPEN.getStatus());

            //起窖开始
            workshopPitOrderService.putExecutionStatus(workshopPitOrderOutAppraiseDTO.getPitOrder(),ExecutionStatus.CELLAR_OUT.getStatus(),1);

            TPoWorkshopPitOrderExudingTask task = workshopPitOrderExudingTaskMapper.selectByOrderCode(workshopPitOrderOutAppraiseDTO.getPitOrder());

            //结束滴窖任务
            workshopPitOrderExudingTaskService.endTaskByOrder(workshopPitOrderOutAppraiseDTO.getPitOrder());
            //新增窖池订单执行状态
            workshopPitOrderService.putExecutionStatus(task.getOrderCode(), ExecutionStatus.CELLAR_DRAIN.getStatus(),9);
            //结束PDA个人任务执行进度
            taskProcessRecordsService.closeTask(task.getExudingTaskCode(), ExecutionStatus.CELLAR_DRAIN.getStatus());

            //创建起窖任务-王佳
            TaskCellarLiftingAddDTO taskCellarLiftingAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskCellarLiftingAddDTO.class);
            taskCellarLiftingService.add(taskCellarLiftingAddDTO);
            //创建下糟任务
            TaskPileUpAddDTO taskPileUpAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskPileUpAddDTO.class);
            taskPileUpService.add(taskPileUpAddDTO);
            //创建润粮任务
            TaskGrainLubricationAddDTO taskGrainLubricationAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskGrainLubricationAddDTO.class);
            taskGrainLubricationService.add(taskGrainLubricationAddDTO);
            //创建拌糟任务
            TaskDishMixingAddDTO taskDishMixingAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskDishMixingAddDTO.class);
            taskDishMixingService.add(taskDishMixingAddDTO);
            //创建上甑任务
            TaskShangzhengAddDTO taskShangzhengAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskShangzhengAddDTO.class);
            taskShangzhengService.add(taskShangzhengAddDTO);
            //创建蒸馏任务
            TaskSistillationAddDTO taskSistillationAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskSistillationAddDTO.class);
            taskSistillationService.add(taskSistillationAddDTO);
            //创建摘酒任务
            TaskWinePickingAddDTO taskWinePickingAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskWinePickingAddDTO.class);
            taskWinePickingService.add(taskWinePickingAddDTO);
            //创建出甑任务
            TaskSteamingAddDTO taskSteamingAddDTO = DtoMapper.convert(workshopPitOrderOutAppraiseDTO, TaskSteamingAddDTO.class);
            taskSteamingService.add(taskSteamingAddDTO);

        }

    }

    /**
     * 根据中心车间获取状态未关闭的窖池订单
     *
     * @param centerId
     * @param locationId
     * @return
     */
    @Override
    public List<WorkshopPitOrderDTO> getOrder(Integer centerId, Integer locationId) {

        if (centerId == null || locationId == null) {
            throw new RuntimeException("中心跟车间不能为空");
        }
        return workshopPitOrderOutAppraiseMapper.getOrder(centerId, locationId);
    }

    /**
     * 删除开窖鉴定
     *
     * @param id
     * @return 1、限制：若需要删的开窖鉴定记录状态是【已完成】状态，关联的窖池订单有空窖起始时间，
     * 且该空窖确认生成的新窖池订单(DD+空窖起始时间的年份-月份-日期_窖号 来获取)中有执行状态为非【待执行】时，不可删除，
     * 给予提示：该窖池本排新订单XXX不是待执行状态，不可删除上排窖池订单的开窖鉴定记录！
     * 反之可以正常删除，且需要将该空窖订单删除，将该开窖鉴定的关联订单状态改为【入窖完成】【发酵】，清空空窖起始时间、起窖完成时间；
     * 2、若需要删的开窖鉴定记录状态是【已完成】状态，关联的窖池订单没有空窖起始时间，则将该开窖鉴定的关联订单状态改为【入窖完成】【发酵】，清空起窖完成时间；
     * 3、若需要删的开窖鉴定记录状态是非【已完成】状态，则正常删除，对订单不产生影响。
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public ResultVO DeleteWorkshopPitOrderOutAppraise(Integer id) {
        WorkshopPitOrderOutAppraiseVO vo = workshopPitOrderOutAppraiseMapper.getOutById(id);

        String taskNo=workshopPitOrderOutAppraiseMapper.selectTaskNoById(id);
        if(StringUtils.isNotBlank(taskNo)){
            //删除PDA个人任务执行进度
            taskProcessRecordsService.deleteTask(taskNo, ExecutionStatus.CELLAR_OPEN.getStatus());
        }

        if (vo == null) {
            // 根据id找不到开窖鉴定，可能是跟开窖鉴定关联的窖池订单已经删除了，这里直接把开窖鉴定删掉
            // 删除开窖鉴定
            boolean deleteOutAppraise = workshopPitOrderOutAppraiseMapper.deleteOutAppraise(id);
            if (deleteOutAppraise) {
                return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
            } else {
                throw new RuntimeException("删除开窖鉴定失败");
            }
        }

        if (vo.getStatus() != null && vo.getStatus() == 2) {

            // 已完成状态
            if (vo.getEmptyStartTime() != null) {
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                String orderCode = "DD" + format.format(vo.getEmptyStartTime()).substring(2) + "_" + vo.getFullPitId() + "_" + vo.getLayer();
                Integer orderStatus = workshopPitOrderOutAppraiseMapper.getOrderStatusByOrderCode(orderCode);
                if (orderStatus != null && orderStatus != OrderStatus.PERFORM) {
                    // 待执行状态
                    return ResultVO.error("该窖池本排新订单" + orderCode + "不是待执行状态，不可删除上排窖池订单的开窖鉴定记录！");
                } else {
                    // 删除空窖确认生成的窖池订单
                    boolean deleteOrder = workshopPitOrderOutAppraiseMapper.deleteOrder(orderCode);
                    // 删除开窖鉴定
                    boolean deleteOutAppraise = workshopPitOrderOutAppraiseMapper.deleteOutAppraise(id);
                    // 将该开窖鉴定的关联订单状态改为【入窖完成】【发酵】，清空空窖起始时间、起窖完成时间；
                    boolean updateOrder = workshopPitOrderOutAppraiseMapper.updateOrder(vo.getOrderId());
                    // 同时更新跟窖池订单关联的sap
                    boolean updateOrderSap = workshopPitOrderOutAppraiseMapper.updateOrderSap(vo.getOrderId());
                    if (deleteOutAppraise && updateOrder && updateOrderSap) {
                        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                    } else {
                        throw new RuntimeException("删除失败");
                    }
                }
            } else {
                // 将该开窖鉴定的关联订单状态改为【入窖完成】【发酵】，清空空窖起始时间、起窖完成时间；
                boolean updateOrder = workshopPitOrderOutAppraiseMapper.updateOrder(vo.getOrderId());
                // 同时更新跟窖池订单关联的sap
                boolean updateOrderSap = workshopPitOrderOutAppraiseMapper.updateOrderSap(vo.getOrderId());
                // 删除开窖鉴定
                boolean deleteOutAppraise = workshopPitOrderOutAppraiseMapper.deleteOutAppraise(id);
                if (updateOrder && deleteOutAppraise && updateOrderSap) {
                    return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                } else {
                    throw new RuntimeException("删除失败");
                }
            }
        } else {
            // 删除开窖鉴定
            boolean deleteOutAppraise = workshopPitOrderOutAppraiseMapper.deleteOutAppraise(id);
            if (deleteOutAppraise) {
                return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
            } else {
                throw new RuntimeException("删除失败");
            }
        }
    }

    @Override
    public DigitalTwinDTO getOpenCellarInformation(OpenCellarInformationDTO openCellarInformationDTO) {
        DigitalTwinDTO digitalTwinDTO = new DigitalTwinDTO();
        List<DigitalTwinOpenCellarInformationDTO> list = new ArrayList<DigitalTwinOpenCellarInformationDTO>();

        //所有有数据的中心
        List<OpenCellarInformationDTO> allList = workshopPitOrderOutAppraiseMapper.getDigitalTwinOpenCellarInformation(null,null,null);
        if(allList.size()>0){
            allList.forEach(item->{
                DigitalTwinOpenCellarInformationDTO dto = new DigitalTwinOpenCellarInformationDTO();
                dto.setCenterCode(item.getCenterCode());
                dto.setCenterName(item.getCenterName());
                list.add(dto);
            });
        }
        // 获取当前日期并设置为当天的开始时间（00:00:00）
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        // 获取当前日期并设置为当天的结束时间（23:59:59）
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);

        // 如果需要将LocalDateTime转换为java.util.Date，可以使用以下方式：
        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

        System.out.println("今日开始时间：" + startDate);
        System.out.println("今日结束时间：" + endDate);
        //今日
        List<OpenCellarInformationDTO> todayList = workshopPitOrderOutAppraiseMapper.getDigitalTwinOpenCellarInformation(openCellarInformationDTO.getCenterCode(),startDate,endDate);
        if(todayList.size()>0){
            if(list.size()>0){
                todayList.forEach(item22->{
                    list.forEach(item2->{
                        if(item22.getCenterCode().equals(item2.getCenterCode())){
                            item2.setDayPlanNum(item22.getPlanNum());
                            item2.setDayProdNum(item22.getProdNum());
                        }
                    });
                });
            }
        }
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取当前月份的第一天
        LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
        // 获取当前月份的最后一天
        LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        System.out.println("当前月份的第一天: " + firstDayOfMonth.atStartOfDay());
        System.out.println("当前月份的最后一天: " + lastDayOfMonth.atTime(LocalTime.MAX));
        Date startDate2 = Date.from(firstDayOfMonth.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        Date endDate2 = Date.from(lastDayOfMonth.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());

        System.out.println("当前月份开始时间：" + startDate2);
        System.out.println("当前月份结束时间：" + endDate2);

        //本月
        List<OpenCellarInformationDTO> monthList = workshopPitOrderOutAppraiseMapper.getDigitalTwinOpenCellarInformation(openCellarInformationDTO.getCenterCode(),startDate2,endDate2);
        if(monthList.size()>0){
            if(list.size()>0){
                monthList.forEach(item33->{
                    list.forEach(item3->{
                        if(item33.getCenterCode().equals(item3.getCenterCode())){
                            item3.setMouthPlanNum(item33.getPlanNum());
                            item3.setMouthProdNum(item33.getProdNum());
                        }
                    });
                });
            }
        }
        digitalTwinDTO.setCellarOpeningInfoList(list);
        return digitalTwinDTO;
    }

    @Override
    public List<WorkshopPitOrderDTO> openCellarInvestmentNotSynchronized(Integer centerId,Integer locationId) {
        Calendar calendar = Calendar.getInstance();

        // 获取当前年
        int year = calendar.get(Calendar.YEAR);

        int month = calendar.get(Calendar.MONTH) + 1;
        SapSyncConfigVO vo = workshopPitOrderOutAppraiseMapper.findSapSyncConfig(year,month);
        if(vo != null){
            return workshopPitOrderOutAppraiseMapper.openCellarInvestmentNotSynchronized(vo.getStartTime(),vo.getEndTime(),centerId,locationId);
        }
        //return workshopPitOrderOutAppraiseMapper.openCellarInvestmentNotSynchronized(vo.getStartTime(),vo.getEndTime());
        return null;
    }

    private WorkshoppitSapApproveInsertDTO getApproveDTO(
            Integer outAppraiseId,
            Integer orderId,
            String notes,
            Integer userId,
            String userName,
            Integer approve1RoleId,
            String approve1RoleName,
            Integer approve1UserId,
            String approve1UserName,
            Integer approve2RoleId,
            String approve2RoleName,
            Integer approve2UserId,
            String approve2UserName,
            String orderCode,
            Integer outStatus) {
        WorkshoppitSapApproveInsertDTO dto = new WorkshoppitSapApproveInsertDTO();
        dto.setOutAppraiseId(outAppraiseId);
        dto.setId(orderId);
        dto.setApproveStatus(0);
        dto.setApproveType("窖池生产订单");
        dto.setApproveTitle("非计划窖池订单开窖申请");
        dto.setIsOpenInput(false);
        dto.setIsOpenTurnoverPit(false);
        dto.setIsOpenWorking(false);
        dto.setIsOpenWorkhour(false);
        dto.setIsOpenOutput(false);
        dto.setNotes(notes);
        dto.setUserId(userId);
        dto.setUserName(userName);
        dto.setUserTime(new Date());
        dto.setIsAgree(0);
        dto.setApprove1RoleId(approve1RoleId);
        dto.setApprove1RoleName(approve1RoleName);
        dto.setApprove1UserId(approve1UserId);
        dto.setApprove1UserName(approve1UserName);
        dto.setApprove1IsAgree(0);
        dto.setApprove2RoleId(approve2RoleId);
        dto.setApprove2RoleName(approve2RoleName);
        dto.setApprove2UserId(approve2UserId);
        dto.setApprove2UserName(approve2UserName);
        dto.setApprove2IsAgree(0);
        dto.setOrderCode(orderCode);
        dto.setOutStatus(outStatus);
        dto.setOrderId(orderId);
        return dto;
    }

    /**
     * 修改窖池订单的起窖完成时间
     */
    private void updateCellarLiftingCompletedTime(WorkshopPitOrderOutAppraiseDTO dto) {
        // 修改开窖鉴定状态为 已完成 状态，更新起窖完成时间
        if (dto.getOrderId() == null) {
            throw new RuntimeException("开窖鉴定更新执行状态，窖池订单 id 不能为空");
        }
        workshopPitOrderOutAppraiseMapper.updateOutPitFinishTime(dto.getOutAppraiseTime(), dto.getOrderId());
        workshopPitOrderOutAppraiseMapper.updateOutPitFinishTimeSap(dto.getOutAppraiseTime(), dto.getOrderId());
        // 更新开窖完成时间
        workshopPitOrderMapper.update(null,new LambdaUpdateWrapper<TPoWorkshopPitOrder>()
                .eq(TPoWorkshopPitOrder::getId,dto.getOrderId())
                .set(TPoWorkshopPitOrder::getOpenPitFinishTime,dto.getOutAppraiseTime()));
        workshopPitOrderSapMapper.update(null,new LambdaUpdateWrapper<TPoWorkshopPitOrderSap>()
                .eq(TPoWorkshopPitOrderSap::getOrderCodeId,dto.getOrderId())
                .set(TPoWorkshopPitOrderSap::getOpenPitFinishTime,dto.getOutAppraiseTime()));


        // 查询窖池订单状态 窖池状态（枚举0入窖 1 发酵 2开窖 3 空窖 4 异常）pit_status
        Integer pitStatus = workshopPitOrderOutAppraiseMapper.getPitStatus(dto.getOrderId());
        if (pitStatus != null && pitStatus == 1) {
            // 当该窖池订单的窖池状态为发酵时，自动修改为【开窖】
            workshopPitOrderOutAppraiseMapper.updatePitStatus(2, dto.getOrderId());
            workshopPitOrderOutAppraiseMapper.updateSapPitStatus(2, dto.getOrderId());
        }

        // 窖池订单执行状态 工单状态 枚举 0 入窖完成 1翻窖完成 2起窖完成 3开窖完成 4交酒完成 5待执行 6 异常 OrderStatus
        Integer orderStatus = workshopPitOrderOutAppraiseMapper.getOrderStatus(dto.getOrderId());
        if (orderStatus != null && (orderStatus == 0 || orderStatus == 1)) {
            workshopPitOrderOutAppraiseMapper.updateOrderStatus(2, dto.getOrderId());
            workshopPitOrderOutAppraiseMapper.updateSapOrderStatus(2, dto.getOrderId());
        }
    }

    /**
     * "滴窖任务的完成状态与开窖鉴定做联动，当同个订单的开窖鉴定完成时(选择状态为完成，点击保存时)，自动检测该订单的滴窖是否完成：
     * 1、若状态为滴窖中，则更新该条滴窖任务，状态改为已完成，滴窖完成时间为开窖鉴定完成时间；
     * 2、若状态为已完成，则对于滴窖任务不需任何操作；
     * 3、若无该滴窖任务或者状态为创建，则无法点击保存按钮，且则给予提示：DD220704_11975-11976_1 该订单对应的窖池还未滴窖，请优先执行滴窖！"
     */
    private void updateExuding(Integer orderId, Date outAppraiseTime) {
        if (orderId == null) {
            throw new RuntimeException("开窖鉴定更新执行状态，窖池订单 id 不能为空");
        }
        // 查询窖池订单的滴窖任务
        List<WorkshopPitOrderExudingTaskVO> exudingTaskVO = workshopPitOrderOutAppraiseMapper.getExudingTask(orderId);
        if (exudingTaskVO.size() > 1) {
            throw new RuntimeException("该窖池订单存在多个滴窖任务，请联系管理员进行排查");
        }

        if (exudingTaskVO.size() == 0 || exudingTaskVO.get(0).getStatus() == 1) {
            String orderCode = workshopPitOrderOutAppraiseMapper.getOrderCode(orderId);
            throw new RuntimeException(orderCode + " 该订单对应的窖池还未滴窖，请优先执行滴窖！");
        }

        if (exudingTaskVO.get(0).getStatus() == 2) {
            workshopPitOrderOutAppraiseMapper.updateExudingStatus(outAppraiseTime, exudingTaskVO.get(0).getId());
        }
    }
}
