package com.hvisions.brewage.mkwine.entity.ProductionProcess;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: 窖池订单_甑口任务管理
 * @projectName brewage
 * @description: 窖池订单_甑口任务管理
 * @date 2022/4/29 14:24:41
 */
@Data
@Entity
@ApiModel(value = "窖池订单_甑口任务实体类")
@Table(name = "t_po_workshop_pit_order_pot_task")
public class TPoWorkshopPitOrderPotTask {

    @Id
    @TableField("id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "甑口任务id", example = "1")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @TableField("controller_id")
    @ApiModelProperty(value = "中控数据id", example = "1")
    private Integer controllerId;

    @TableField("line_id")
    @ApiModelProperty(value = "中控传过来的产线")
    private Integer lineId;

    @TableField("pot_serial_number")
    @ApiModelProperty(value = "甑口任务编号(中控传递过来的，每条产线一个流水号)  ")
    private String potSerialNumber;

    @TableField("pottask_starttime")
    @ApiModelProperty(value = "甑口任务开始时间（出糟时间）")
    private Date pottaskStarttime;

    @TableField("pottask_endtime")
    @ApiModelProperty(value = "甑口任务结束时间（ZL糟（丢糟）是出甑时间，其它CJ CZ是摊晾结束时间）")
    private Date pottaskEndtime;

    @TableField("out_time")
    @ApiModelProperty(value = "出糟时间")
    private Date outTime;

    @TableField("vinasse_id")
    @ApiModelProperty(value = "糟源类型(中控传递过来)")
    private Integer vinasseId;

    @TableField("vinasse")
    @ApiModelProperty(value = "糟源类型名称")
    private String vinasse;

    @TableField("fermentative_material_quantity")
    @ApiModelProperty(value = "糟醅重量")
    private BigDecimal fermentativeMaterialQuantity;

    @TableField("crushed_grains_quantity")
    @ApiModelProperty(value = "粉粮（高粱）重量")
    private BigDecimal crushedGrainsQuantity;

    @TableField("crushed_grains_quantity_back")
    @ApiModelProperty(value = "粉粮（高粱）重量(备份)")
    private BigDecimal crushedGrainsQuantityBack;

    @TableField("ricehull_quantity")
    @ApiModelProperty(value = "稻壳重量")
    private BigDecimal ricehullQuantity;

    @TableField("ricehull_quantity_back")
    @ApiModelProperty(value = "稻壳重量(备份)")
    private BigDecimal ricehullQuantityBack;

    @TableField("fermentative_material_volume")
    @ApiModelProperty(value = "糟醅体积")
    private BigDecimal fermentativeMaterialVolume;

    @TableField("out_pit_code")
    @ApiModelProperty(value = "中控输入的起窖号（单窖）")
    private Integer outPitCode;

    @TableField("in_pit_code")
    @ApiModelProperty(value = "中控输入的入窖号（单窖）")
    private Integer inPitCode;

    @TableField("before_mixing_transmit_starttime")
    @ApiModelProperty(value = "拌合前输送开始时间")
    private Date beforeMixingTransmitStarttime;

    @TableField("before_mixing_transmit_endtime")
    @ApiModelProperty(value = "拌合前输送结束时间")
    private Date beforeMixingTransmitEndtime;

    @TableField("before_mixing_transmit_timespan")
    @ApiModelProperty(value = "拌和前输送时长")
    private BigDecimal beforeMixingTransmitTimespan;

    @TableField("mixing_starttime")
    @ApiModelProperty(value = "开始拌合时间")
    private Date mixingStarttime;

    @TableField("mixing_endtime")
    @ApiModelProperty(value = "拌合结束时间")
    private Date mixingEndtime;

    @TableField("mixing_timespan")
    @ApiModelProperty(value = "拌和时长")
    private BigDecimal mixingTimespan;

    @TableField("moisten_grain_starttime")
    @ApiModelProperty(value = "润粮开始时间")
    private LocalDateTime moistenGrainStartTime;

    @TableField("moisten_grain_endtime")
    @ApiModelProperty(value = "润粮结束时间")
    private LocalDateTime moistenGrainEndTime;

    @TableField("moisten_grain_timespan")
    @ApiModelProperty(value = "润粮时长")
    private BigDecimal moistenGrainTimespan;

    @TableField("moisten_grain_water_temperature")
    @ApiModelProperty(value = "润粮水温度")
    private String moistenGrainWaterTemperature;

    @TableField("moisten_grain_water_weight")
    @ApiModelProperty(value = "润粮水重量")
    private BigDecimal moistenGrainWaterWeight;

    @TableField("before_loading_transmit_starttime")
    @ApiModelProperty(value = "上甑前输送开始时间")
    private Date beforeLoadingTransmitStarttime;

    @TableField("before_loading_transmit_endtime")
    @ApiModelProperty(value = "上甑前输送结束时间")
    private Date beforeLoadingTransmitEndtime;

    @TableField("before_loading_transmit_timespan")
    @ApiModelProperty(value = "上甑前输送时长")
    private BigDecimal beforeLoadingTransmitTimespan;

    @TableField("feed_machine_code")
    @ApiModelProperty(value = "喂料机编号")
    private Integer feedMachineCode;

    @TableField("pot_num")
    @ApiModelProperty(value = "甑号（一条产线6口甑）")
    private String potNum;

    @TableField("loading_starttime")
    @ApiModelProperty(value = "开始上甑时间")
    private Date loadingStarttime;

    @TableField("loading_endtime")
    @ApiModelProperty(value = "上甑结束时间")
    private Date loadingEndtime;

    @TableField("loading_timespan")
    @ApiModelProperty(value = "上甑时长")
    private BigDecimal loadingTimespan;

    @TableField("close_lid_time")
    @ApiModelProperty(value = "合盖时间")
    private Date closeLidTime;

    @TableField("open_lid_time")
    @ApiModelProperty(value = "开盖时间")
    private Date openLidTime;

    @TableField("distillate_starttime")
    @ApiModelProperty(value = "蒸馏开始时间")
    private Date distillateStarttime;

    @TableField("distillate_endtime")
    @ApiModelProperty(value = "蒸馏结束时间")
    private Date distillateEndtime;

    @TableField("distillate_timespan")
    @ApiModelProperty(value = "蒸馏时长")
    private BigDecimal distillateTimespan;

    @TableField("steaming_starttime")
    @ApiModelProperty(value = "蒸粮开始时间")
    private Date steamingStarttime;

    @TableField("steaming_endtime")
    @ApiModelProperty(value = "蒸粮结束时间")
    private Date steamingEndtime;

    @TableField("steaming_timespan")
    @ApiModelProperty(value = "蒸粮时长")
    private BigDecimal steamingTimespan;

    @TableField("steam_valve_starttime")
    @ApiModelProperty(value = "蒸汽阀门打开时间")
    private Date steamValveStarttime;

    @TableField("steam_valve_endtime")
    @ApiModelProperty(value = "蒸汽阀门关闭时间")
    private Date steamValveEndtime;

    @TableField("outflow_starttime")
    @ApiModelProperty(value = "开始流酒时间（出酒）")
    private Date outflowStarttime;

    @TableField("distillate_first_starttime")
    @ApiModelProperty(value = "头酒开始时间")
    private Date distillateFirstStarttime;

    @TableField("distillate2_starttime")
    @ApiModelProperty(value = "二段酒开始时间")
    private Date distillate2Starttime;

    @TableField("distillate3_starttime")
    @ApiModelProperty(value = "三段酒开始时间")
    private Date distillate3Starttime;

    @TableField("distillate4_starttime")
    @ApiModelProperty(value = "四段酒开始时间")
    private Date distillate4Starttime;

    @TableField("distillate_lastwater_starttime")
    @ApiModelProperty(value = "尾水酒开始时间")
    private Date distillateLastwaterStarttime;

    @TableField("distillate_first_tank_id")
    @ApiModelProperty(value = "头酒暂存罐（可能不单独存头酒，混在其它段次罐中）")
    private Integer distillateFirstTankId;

    @TableField("distillate2_tank_id")
    @ApiModelProperty(value = "二段酒暂存罐")
    private Integer distillate2_TankId;

    @TableField("distillate3_tank_id")
    @ApiModelProperty(value = "三段酒暂存罐")
    private Integer distillate3_TankId;

    @TableField("distillate4_tank_id")
    @ApiModelProperty(value = "四段酒暂存罐")
    private Integer distillate4_TankId;

    @TableField("distillate_lastwater_tank_id")
    @ApiModelProperty(value = "尾水酒暂存罐")
    private Integer distillateLastwaterTankId;

    @TableField("outflow_endtime")
    @ApiModelProperty(value = "流酒完成时间")
    private Date outflowEndtime;

    @TableField("outflow_timespan")
    @ApiModelProperty(value = "流酒时长（馏酒完成时间-馏酒开始时间）馏酒开始时间 也是 酒头开始时间")
    private BigDecimal outflowTimespan;

    @TableField("distillate_flowspeed")
    @ApiModelProperty(value = "流酒速度（kg/min）")
    private BigDecimal distillateFlowspeed;

    @TableField("distillate_temperature")
    @ApiModelProperty(value = "流酒温度")
    private String distillateTemperature;

    @TableField("distillate_notes")
    @ApiModelProperty(value = "摘酒备注")
    private String distillateNotes;

    @TableField("out_pot_time")
    @ApiModelProperty(value = "出甑时间")
    private Date outPotTime;

    @TableField("turn_pot_time")
    @ApiModelProperty(value = "翻甑时间")
    private Date turnPotTime;

    @TableField("bottom_pot_level")
    @ApiModelProperty(value = "锅底液位")
    private String bottomPotLevel;

    @TableField("bottom_pot_temperature")
    @ApiModelProperty(value = "锅底温度")
    private String bottomPotTemperature;

    @TableField("liquor_tailing_quantity")
    @ApiModelProperty(value = "加尾酒量")
    private BigDecimal liquorTailingQuantity;

    @TableField("water_quantity")
    @ApiModelProperty(value = "加清水量")
    private BigDecimal waterQuantity;

    @TableField("yellow_water_quantity")
    @ApiModelProperty(value = "加黄水量")
    private BigDecimal yellowWaterQuantity;

    @TableField("water_proportioning_starttime")
    @ApiModelProperty(value = "打量水开始时间")
    private Date waterProportioningStarttime;

    @TableField("water_proportioning_endtime")
    @ApiModelProperty(value = "打量水结束时间")
    private Date waterProportioningEndtime;

    @TableField("water_proportioning_timespan")
    @ApiModelProperty(value = "打量水时长")
    private BigDecimal waterProportioningTimespan;

    @TableField("water_proportioning_temperature")
    @ApiModelProperty(value = "量水温度")
    private String waterProportioningTemperature;

    @TableField("water_proportioning_quantity")
    @ApiModelProperty(value = "量水重量")
    private BigDecimal waterProportioningQuantity;

    @TableField("colling_feed_machine_code")
    @ApiModelProperty(value = "摊晾喂料机编号")
    private Integer collingFeedMachineCode;

    @TableField("colling_starttime")
    @ApiModelProperty(value = "开始摊晾时间")
    private Date collingStarttime;

    @TableField("colling_endtime")
    @ApiModelProperty(value = "摊晾结束时间")
    private Date collingEndtime;

    @TableField("colling_timespan")
    @ApiModelProperty(value = "摊晾时长（摊晾完成时间-摊晾开始时间）")
    private BigDecimal collingTimespan;

    @TableField("colling_notes")
    @ApiModelProperty(value = "摊晾备注")
    private String collingNotes;

    @TableField("qu_hopper_code")
    @ApiModelProperty(value = "曲粉斗")
    private String quHopperCode;

    @TableField("colling_machine_code")
    @ApiModelProperty(value = "摊晾机编号")
    private Integer collingMachineCode;

    @TableField("colling_machine_id")
    @ApiModelProperty(value = "摊凉机MES中的id")
    private Integer colling_machineId;

    @TableField("colling_temperature")
    @ApiModelProperty(value = "摊晾温度")
    private String collingTemperature;

    @TableField("qu_changed_weight")
    @ApiModelProperty(value = "加曲机重量变化")
    private BigDecimal quChangedWeight;

    @TableField("transport_starttime")
    @ApiModelProperty(value = "转运开始时间")
    private Date transportStarttime;

    @TableField("arrive_lifting_area_time")
    @ApiModelProperty(value = "到达吊装区时间")
    private Date arriveLiftingAreaTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField("out_layer")
    @ApiModelProperty(value = "出窖层级0不分层1上层2下层")
    private Integer outLayer;

    @TableField("out_doublepit_code")
    @ApiModelProperty(value = "出窖连窖窖池编号")
    private String outDoublepitCode;

    @TableField("out_order_code")
    @ApiModelProperty(value = "出窖窖池订单编号")
    private String outOrderCode;

    @TableField("out_pot_Index")
    @ApiModelProperty(value = "出窖甑口顺序")
    private Integer outPotIndex;

    @TableField("out_shift_id")
    @ApiModelProperty(value = "出窖班次id")
    private Integer outShiftId;

    @TableField("in_layer")
    @ApiModelProperty(value = "入窖层级0不分层1上层2下层")
    private Integer inLayer;

    @TableField("in_doublepit_code")
    @ApiModelProperty(value = "入窖连窖窖池编号")
    private String inDoublepitCode;

    @TableField("in_order_code")
    @ApiModelProperty(value = "入窖窖池订单编号")
    private String inOrderCode;

    @TableField("in_pot_index")
    @ApiModelProperty(value = "入窖甑口顺序")
    private Integer inPotIndex;

    @TableField("in_shift_id")
    @ApiModelProperty(value = "入窖班次id")
    private Integer inShiftId;

    @TableField("fermentative_material_lot_id")
    @ApiModelProperty("糟醅批次")
    private String fermentativeMaterialLotId;

    @TableField("crushed_grains_lot_id")
    @ApiModelProperty(value = "粉粮批次")
    private String crushedGrainsLotId;

    @TableField("ricehull_lot_id")
    @ApiModelProperty(value = "稻壳批次")
    private String ricehullLotId;

    @TableField("back_alcoholic_lot_id")
    @ApiModelProperty(value = "回酒批次")
    private String backAlcoholicLotId;

    @TableField("back_alcoholic_quantity")
    @ApiModelProperty(value = "回酒重量")
    private BigDecimal backAlcoholicQuantity;

    @TableField("back_alcoholic_quantity_revise")
    @ApiModelProperty(value = "回酒重量修正")
    private BigDecimal backAlcoholicQuantityRevise;

    @TableField("qu_lot_id")
    @ApiModelProperty(value = "曲粉批次")
    private String quLotId;

    @TableField("add_qu_quyantity")
    @ApiModelProperty(value = "添加曲粉重量")
    private BigDecimal addQuQuyantity;

    @TableField("qu_quantity")
    @ApiModelProperty(value = "曲粉重量(加曲机重量变化+添加重量)")
    private BigDecimal quQuantity;

    @TableField("in_pit_temperature")
    @ApiModelProperty(value = "入窖温度(预留)")
    private String inPitTemperature;

    @TableField("ground_temperature")
    @ApiModelProperty("地温")
    private String groundTemperature;

    @TableField("is_check")
    @ApiModelProperty(value = "核对确认")
    private Boolean isCheck;

    @TableField("check_user_id")
    @ApiModelProperty(value = "确认人id")
    private Integer checkUserId;

    @TableField("check_time")
    @ApiModelProperty(value = "确认时间")
    private Date checkTime;

    @TableField("order_status")
    @ApiModelProperty(value = "任务状态（0待执行  1执行中 2已完成）")
    private Integer orderStatus;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("modify_time")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @TableField("change_shift_log_id")
    @ApiModelProperty(value = "交接班考勤id")
    private Integer changeShiftLogId;

    @TableField("is_deleted")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @TableField("serial_number")
    @ApiModelProperty(value = "中控推送的potSerialNumber")
    private String serialNumber;

    //-----------------------------------------------------------------------分割线-------------------------------------------------------------------------------------------


    @ApiModelProperty(value = "出糟开始时间")
    @TableField("out_start_time")
    private LocalDateTime outStartTime;


    //新增字段，需求重新采集中控数据
    @ApiModelProperty(value = "糟醅层次")
    @TableField("fermentative_material_levels")
    private String fermentativeMaterialLevels;

    @ApiModelProperty(value = "出糟开始时间")
    @TableField("out_dregs_start_time")
    private LocalDateTime outDregsStartTime;

    @ApiModelProperty(value = "出糟结束时间")
    @TableField("out_dregs_end_time")
    private LocalDateTime outDregsEndTime;

    @ApiModelProperty(value = "糟醅出料时长")
    @TableField("out_dregs_timespan")
    private BigDecimal outDregsTimespan;

    @ApiModelProperty(value = "输糠绞龙开始运行时间")
    @TableField("auger_start_time")
    private LocalDateTime augerStartTime;

    @ApiModelProperty(value = "输糠绞龙结束运行时间")
    @TableField("auger_end_time")
    private LocalDateTime augerEndTime;

    @ApiModelProperty(value = "润粮机编号")
    @TableField("moisten_grain_no")
    private String moistenGrainNo;

    @ApiModelProperty(value = "润粮水重量")
    @TableField("moisten_grain_weight")
    private BigDecimal moistenGrainWeight;

    @ApiModelProperty(value = "润粮水桶液位差")
    @TableField("moisten_grain_liquid_level")
    private String moistenGrainLiquidLevel;

    @ApiModelProperty(value = "润粮水桶开始液位")
    @TableField("moisten_grain_start_level")
    private String moistenGrainStartLevel;

    @ApiModelProperty(value = "润粮水桶终止液位")
    @TableField("moisten_grain_end_level")
    private String moistenGrainEndLevel;

    @ApiModelProperty(value = "润粮水泵运行时长")
    @TableField("moisten_grain_pump_timespan")
    private BigDecimal moistenGrainPumpTimespan;

    @ApiModelProperty(value = "搅拌开始时间")
    @TableField("stir_start_time")
    private LocalDateTime stirStartTime;

    @ApiModelProperty(value = "搅拌结束时间")
    @TableField("stir_end_time")
    private LocalDateTime stirEndTime;

    @ApiModelProperty(value = "合计重量=HJ")
    @TableField("hj_weight")
    private BigDecimal hjWeight;

    @ApiModelProperty(value = "尾酒泵起始时间")
    @TableField("wj_pump_start_time")
    private LocalDateTime wjPumpStartTime;

    @ApiModelProperty(value = "尾酒泵停泵时间")
    @TableField("wj_pump_end_time")
    private LocalDateTime wjPumpEndTime;

    @ApiModelProperty(value = "尾酒泵运行时长")
    @TableField("wj_pump_timespan")
    private BigDecimal wjPumpTimespan;

    @ApiModelProperty(value = "单甑时长")
    @TableField("pot_timespan")
    private BigDecimal potTimespan;

    @ApiModelProperty(value = "上甑喂料机启动时间")
    @TableField("feed_machin_starttime")
    private LocalDateTime feedMachinStartTime;

    @ApiModelProperty(value = "ZT底锅流量计起始读数")
    @TableField("zt_bottom_pot_flow_rate_start_reading")
    private String ztBottomPotFlowRateStartReading;

    @ApiModelProperty(value = "ZT底锅流量计终止读数")
    @TableField("zt_bottom_pot_flow_rate_end_reading")
    private String ztBottomPotFlowRateEndReading;

    @ApiModelProperty(value = "HS底锅流量计起始读数")
    @TableField("hs_bottom_pot_flow_rate_start_reading")
    private String hsBottomPotFlowRateStartReading;

    @ApiModelProperty(value = "HS底锅流量计终止读数")
    @TableField("hs_bottom_pot_flow_rate_end_reading")
    private String hsBottomPotFlowRateEndReading;

    @ApiModelProperty(value = "QS底锅流量计起始读数")
    @TableField("qs_bottom_pot_flow_rate_start_reading")
    private String qsBottomPotFlowRateStartReading;

    @ApiModelProperty(value = "QS底锅流量计终止读数")
    @TableField("qs_bottom_pot_flow_rate_end_reading")
    private String qsBottomPotFlowRateEndReading;

    @ApiModelProperty(value = "WJ底锅流量计起始读数")
    @TableField("wj_bottom_pot_flow_rate_start_reading")
    private String wjBottomPotFlowRateStartReading;

    @ApiModelProperty(value = "WJ底锅流量计终止读数")
    @TableField("wj_bottom_pot_flow_rate_end_reading")
    private String wjBottomPotFlowRateEndReading;

    @ApiModelProperty(value = "出甑开盖时间")
    @TableField("out_pot_starttime")
    private LocalDateTime outPotStartTime;

    @ApiModelProperty(value = "料斗开门机构打开时间")
    @TableField("material_lssuance_starttime")
    private LocalDateTime materialLssuanceStartTime;

    @ApiModelProperty(value = "二段时长")
    @TableField("distillate2_timespan")
    private BigDecimal distillate2Timespan;

    @ApiModelProperty(value = "三段时长")
    @TableField("distillate3_timespan")
    private BigDecimal distillate3Timespan;

    @ApiModelProperty(value = "四段时长")
    @TableField("distillate4_timespan")
    private BigDecimal distillate4Timespan;

    @ApiModelProperty(value = "第一次量水")
    @TableField("water_proportioning_1")
    private BigDecimal waterProportioning1;

    @ApiModelProperty(value = "第一次量水温度")
    @TableField("water_proportioning_1_temperature")
    private String waterProportioning1Temperature;

    @ApiModelProperty(value = "第二次量水")
    @TableField("water_proportioning_2")
    private BigDecimal waterProportioning2;

    @ApiModelProperty(value = "第二次量水温度")
    @TableField("water_proportioning_2_temperature")
    private String waterProportioning2Temperature;

    @ApiModelProperty(value = "加曲机开始重量")
    @TableField("in_yeast_start_weight")
    private BigDecimal inYeastStartWeight;

    @ApiModelProperty(value = "加曲机结束重量")
    @TableField("in_yeast_end_weight")
    private BigDecimal inYeastEndWeight;

    @ApiModelProperty(value = "加曲时长")
    @TableField("in_yeast_temperature")
    private BigDecimal inYeastTemperature;

    @ApiModelProperty(value = "加曲开始时间")
    @TableField("in_yeast_starttime")
    private LocalDateTime inYeastStartTime;

    @ApiModelProperty(value = "加曲结束时间")
    @TableField("in_yeast_endtime")
    private LocalDateTime inYeastEndTime;

    @ApiModelProperty(value = "风机运行数量")
    @TableField("fan_running_quantity")
    private BigDecimal fanRunningQuantity;

    @ApiModelProperty(value = "风机运行时长")
    @TableField("fan_running_timespan")
    private BigDecimal fanRunningTimespan;

    @ApiModelProperty(value = "风机启动时间")
    @TableField("fan_starttime")
    private LocalDateTime fanStartTime;

    @ApiModelProperty(value = "风机关闭时间")
    @TableField("fan_endtime")
    private LocalDateTime fanEndTime;

    @ApiModelProperty(value = "W1重量")
    @TableField("w1quality")
    private BigDecimal w1Quality;

    @ApiModelProperty(value = "dz2时长")
    @TableField(value = "dz2_timespan")
    private BigDecimal dz2Timespan;

    @ApiModelProperty(value = "dz2开始时间")
    @TableField(value = "dz2_starttime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dz2StartTime;

    @ApiModelProperty(value = "曲斗id")
    private Integer quDouId;

    @ApiModelProperty(value = "曲粉批次")
    private String quBatch;

    /**
     * IOT任务号
     */
    @TableField(value = "iot_task_no")
    @ApiModelProperty(value = "IOT任务号")
    private String iotTaskNo;

    @TableField(value = "is_callback")
    @ApiModelProperty(value = "总包数据是否已回传")
    private Boolean isCallback;
}
