package com.hvisions.brewage.mkwine.config;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dao.base.ProdConfigItemMapper;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoIncreaseAmplitudeDetailMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoIncreaseAmplitudeMapper;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.AmplitudeDetailQueryDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.IncreaseAmplitudeDetailDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoIncreaseAmplitude;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoIncreaseAmplitudeDetail;
import com.hvisions.brewage.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 近期涨幅定时任务
 */
@Slf4j
@Component
@EnableScheduling
public class IncreaseAmplitudeTask {

    @Resource
    TPoIncreaseAmplitudeDetailMapper increaseAmplitudeDetailMapper;

    @Resource
    TPoIncreaseAmplitudeMapper increaseAmplitudeMapper;

    @Resource
    ProdConfigItemMapper prodConfigItemMapper;

    /**
     * 近期涨幅定时任务
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @SchedulerLock(name = "increaseAmplitude")
    public void increaseAmplitude() {
        log.info("开始生成近期涨幅数据");
        //获取配置项
        List<ProdConfigItem> configTime = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemName, "涨幅定时时间").eq(ProdConfigItem::getDeleted, false));
        List<ProdConfigItem> configCycle = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemName, "涨幅计算周期").eq(ProdConfigItem::getDeleted, false));
        LocalTime now = LocalTime.now();
        for (ProdConfigItem prodConfigItem : configTime) {
            Integer locationId = Integer.valueOf(prodConfigItem.getWorkshop());
            if(!DateUtil.checkTime(prodConfigItem.getItemValue(), now, 5)){
                continue;
            }
            //遍历是否有周期配置
            if (configCycle.stream().noneMatch(item -> item.getWorkshop().equals(prodConfigItem.getWorkshop()))) {
                log.info("不存在涨幅计算周期配置,涨幅不生成,车间id:" + locationId);
            }
            log.info("开始生成涨幅数据,车间:" + locationId);
            ProdConfigItem cycle = configCycle.stream().filter(item -> item.getWorkshop().equals(prodConfigItem.getWorkshop())).collect(Collectors.toList()).get(0);
            //获取 配置项（天） 前的数据
            Date date = DateUtil.addDay(new Date(), Math.negateExact(Integer.parseInt(cycle.getItemValue())));
            AmplitudeDetailQueryDTO queryDTO = new AmplitudeDetailQueryDTO();
            queryDTO.setInitiationTime(date);
            queryDTO.setLocationId(locationId);
            List<IncreaseAmplitudeDetailDTO> list = increaseAmplitudeDetailMapper.getIncreaseAmplitudeDetailPage(queryDTO);
            //按照车间分组
            if (CollectionUtils.isNotEmpty(list)) {
                TPoIncreaseAmplitude amplitude = new TPoIncreaseAmplitude();
                BeanUtils.copyProperties(list.get(0), amplitude);
                amplitude.setStartTime(list.stream().filter(Objects::nonNull).map(IncreaseAmplitudeDetailDTO::getOpenPitFinishTime).filter(Objects::nonNull).min(Date::compareTo).orElse(null));
                amplitude.setEndTime(list.stream().filter(Objects::nonNull).map(IncreaseAmplitudeDetailDTO::getOpenPitFinishTime).filter(Objects::nonNull).max(Date::compareTo).orElse(null));
                amplitude.setInPutNum(list.stream().mapToInt(IncreaseAmplitudeDetailDTO::getInPitNum).sum());
                amplitude.setOutPutNum(list.stream().mapToInt(IncreaseAmplitudeDetailDTO::getOutPitNum).sum());
                //涨幅日期
                amplitude.setIncreaseDate(DateUtil.getTodayStartDate());
                amplitude.setOrderNum(list.size());
                //设置涨幅 近期涨幅=（蒸馏总甑口-在制总甑口）/在制总甑口*100%
                BigDecimal increaseAmplitude = new BigDecimal(amplitude.getOutPutNum()).subtract(new BigDecimal(amplitude.getInPutNum())).divide(new BigDecimal(amplitude.getInPutNum()), 2, RoundingMode.HALF_UP);
                amplitude.setIncreaseAmplitude(increaseAmplitude);
                increaseAmplitudeMapper.insert(amplitude);
                //生成明细数据
                for (IncreaseAmplitudeDetailDTO increaseAmplitudeDetailDTO : list) {
                    TPoIncreaseAmplitudeDetail detail = new TPoIncreaseAmplitudeDetail();
                    BeanUtils.copyProperties(increaseAmplitudeDetailDTO, detail);
                    detail.setIncreaseAmplitudeId(amplitude.getId());
                    increaseAmplitudeDetailMapper.insert(detail);
                }
            }
        }
        log.info("生成近期涨幅数据执行结束");
    }
}
