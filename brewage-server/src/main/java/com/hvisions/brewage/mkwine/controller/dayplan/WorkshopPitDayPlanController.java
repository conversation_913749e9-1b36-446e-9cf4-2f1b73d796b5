package com.hvisions.brewage.mkwine.controller.dayplan;

import com.hvisions.brewage.dto.mkwine.vo.ParamVO;
import com.hvisions.brewage.mkwine.config.DayPlanTask;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.*;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoFirstInFirstOut;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.service.WorkshopPitDayPlanService;
import com.hvisions.brewage.mkwine.service.impl.DayPlanTaskService;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.*;
import com.hvisions.brewage.mkwine.vo.productiondisposition.WorkshopPitOrder.WorkshopPitOrderVO;
import com.hvisions.brewage.operlog.annotation.Log;
import com.hvisions.brewage.operlog.enums.BusinessType;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: brewage
 * @description: 日计划
 * @author: DengWeiTao
 **/
@RestController
@RequestMapping("/WorkshopPitDayPlan")
@Api(description = "日计划")
public class WorkshopPitDayPlanController {

    @Autowired
    private WorkshopPitDayPlanService workshopPitDayPlanService;

    @Autowired
    private DayPlanTaskService dayPlanTaskService;


    @ApiOperation(value = "获取全部日计划")
    @GetMapping("/getAllWorkshopPitDayPlan")
    public List<WorkshopPitDayPlanVO> getAllWorkshopPitDayPlan() {
        return workshopPitDayPlanService.getAllWorkshopPitDayPlan();
    }

    @ApiOperation(value = "分页获取日计划")
    @PostMapping("/getWorkshopPitDayPlanPage")
    public WorkshopPitDayPlanPageVO getWorkshopPitDayPlanPage(@RequestBody WorkshopPitDayPlanPageDTO workshopPitDayPlanPageDTO) {
        return workshopPitDayPlanService.getWorkshopPitDayPlanPage(workshopPitDayPlanPageDTO);
    }

    @ApiOperation(value = "根据日计划号获取日计划")
    @PostMapping("/getWorkshopPitDayPlanByDayPlayId/{dayPlayId}")
    public List<WorkshopPitDayPlanVO> getWorkshopPitDayPlanByDayPlayId(
            @ApiParam(value = "id", example = "1") @PathVariable String dayPlayId) {
        return workshopPitDayPlanService.getPitDayPlanByDayPlayId(dayPlayId);
    }

    @ApiOperation(value = "删除日计划")
    @DeleteMapping("/deleteWorkshopPitDayPlan/{dayPlayCode}")
    public ResultVO deleteWorkshopPitDayPlan(@ApiParam(value = "id", example = "1") @PathVariable String dayPlayCode) {
        return workshopPitDayPlanService.deleteWorkshopPitDayPlan(dayPlayCode);
    }

    @Log(title = "添加日计划", businessType = BusinessType.INSERT)
    @ApiOperation(value = "添加日计划")
    @PostMapping("/addWorkshopPitDayPlan")
    ResultVO addWorkshopPitDayPlan(@RequestBody List<WorkshopPitDayPlanDTO> workshopPitDayPlanDTOS) {
        return workshopPitDayPlanService.addWorkshopPitDayPlan(workshopPitDayPlanDTOS);
    }

    @ApiOperation(value = "修改日计划")
    @PostMapping("/updateWorkshopPitDayPlan")
    ResultVO updateWorkshopPitDayPlan(@RequestBody List<WorkshopPitDayPlanDTO> workshopPitDayPlanDTOS) {
        return workshopPitDayPlanService.updateWorkshopPitDayPlan(workshopPitDayPlanDTOS);
    }

    /**
     * 某个班组的甑口生产数量后面还得关联窖池订单来查询出来
     *
     * @param executionDetails
     * @return
     */
    @ApiOperation(value = "根据日计划编号，获取某条日计划执行详情")
    @PostMapping("/getWorkshopPitDayPlanExecutionDetail")
    public List<ExecutionDetail> getWorkshopPitDayPlanExecutionDetail(
            @RequestBody List<ExecutionDetail> executionDetails) {
        return workshopPitDayPlanService.getWorkshopPitDayPlanExecutionDetail(executionDetails);
    }

    /**
     * 自然月单个车间剩余计划生产甑口
     */
    @ApiOperation(value = "自然月单个车间剩余计划生产甑口")
    @PostMapping("/getMonthlyPlanRemainingProductionRetort")
    public WorkMonthVO getMonthlyPlanRemainingProductionRetort(@RequestBody WorkMonthPlanDTO workMonthPlanDTO) {
        return workshopPitDayPlanService.getMonthlyPlanRemainingProductionRetort(workMonthPlanDTO);
    }

    /**
     * 自然月单个车间计划和生产甑口
     */
    @ApiOperation(value = "自然月单个车间剩余计划生产甑口")
    @PostMapping("/getMonthlyPlanAndProductionRetort")
    public WorkMonthVinasseVO getMonthlyPlanAndProductionRetort(@RequestBody WorkMonthPlanDTO workMonthPlanDTO) {
        return workshopPitDayPlanService.getMonthlyPlanAndProductionRetort(workMonthPlanDTO);
    }

    /**
     * 日计划待选窖池工单查询接口
     */
    @ApiOperation(value = "日计划待选窖池工单查询接口")
    @PostMapping("/cellarOrderQuery")
    public List<WorkshopPitOrderVO> cellarOrderQuery(@RequestBody WorkshopPitDayPlanToOrderDTO workshopPitDayPlanToOrderDTO) {
        return workshopPitDayPlanService.cellarOrderQuery(workshopPitDayPlanToOrderDTO);
    }

    @ApiOperation(value = "周期查询接口")
    @GetMapping("/getCycleById/{param_id}")
    public ParamVO getCycleById(@ApiParam(value = "参数的paramId") @PathVariable String param_id) {
        return workshopPitDayPlanService.getCycleById(param_id);
    }

    @ApiOperation(value = "周期修改接口")
    @PostMapping("/updateCycle")
    public ResultVO updateCycle(@RequestBody ParamDTO paramDTO) {
        if (workshopPitDayPlanService.updateCycle(paramDTO)) {
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("修改周期失败");
    }

    @ApiOperation(value = "周期删除接口")
    @DeleteMapping("/deleteCycle/{param_id}")
    public ResultVO deleteCycle(@ApiParam(value = "id", example = "1") @PathVariable String param_id) {
        if (workshopPitDayPlanService.deleteCycle(param_id)) {
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("删除周期失败，大概率是因为已经有相同的 param_id 存在数据表中了");
    }

    @ApiOperation(value = "周期添加接口")
    @PostMapping("/insertCycle")
    public ResultVO insertCycle(@RequestBody ParamDTO paramDTO) {
        if (workshopPitDayPlanService.insertCycle(paramDTO)) {
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("添加周期失败");
    }

    @ApiOperation(value = "用来提供给物料需求请求数据的接口")
    @PostMapping("/getDayPlanToMaterielRequest")
    public List<WorkshopPitDayPlanVO> getDayPlanToMaterielRequest(
            @RequestBody WorkshopPitDayPlanDTO workshopPitDayPlanDTO) {
        return workshopPitDayPlanService.getDayPlanToMaterielRequest(workshopPitDayPlanDTO);
    }

    @ApiOperation(value = "日计划下发")
    @PostMapping("/workshopPitDayPlanIssued")
    public ResultVO workshopPitDayPlanIssued(
            @RequestBody WorkshopPitDayPlanDTO workshopPitDayPlanDTO) {
        return workshopPitDayPlanService.workshopPitDayPlanIssued(workshopPitDayPlanDTO);
    }

    @ApiOperation(value = "甑口任务详情")
    @PostMapping("/potTaskDetail/{orderCode}")
    public List<PotTaskVO> potTaskDetail(
            @ApiParam(value = "orderCode", example = "DD220519_2111-2112_1") @PathVariable String orderCode) {
        return workshopPitDayPlanService.potTaskDetail(orderCode);
    }

    @ApiOperation(value = "根据中心车间连窖号判断是否存在日计划")
    @PostMapping("/existDayPlans")
    public List<String> existDayPlans(@RequestBody WorkshopPitDayPlanExist exist) {
        return workshopPitDayPlanService.existDayPlans(exist);
    }

    @ApiOperation(value = "根据中心车间当天日期查询日计划总甑口")
    @PostMapping("/existDayPlans/{centerId}&{locationId}")
    public Integer[] getPotCount(
            @ApiParam(value = "centerId", example = "4") @PathVariable Integer centerId,
            @ApiParam(value = "locationId", example = "5") @PathVariable Integer locationId) {
        return workshopPitDayPlanService.getPotCount(centerId, locationId);
    }

    @ApiOperation(value = "先入先出查询接口")
    @PostMapping("/getFirstInFirstOut")
    public Boolean getFirstInFirstOut(@RequestBody TPoFirstInFirstOut out) {
        return workshopPitDayPlanService.getFirstInFirstOut(out.getCenterId(), out.getLocationId());
    }

    @ApiOperation(value = "先入先出修改接口")
    @PostMapping("/updateFirstInFirstOut")
    public ResultVO updateFirstInFirstOut(@RequestBody TPoFirstInFirstOut out) {
        return workshopPitDayPlanService.updateFirstInFirstOut(out);
    }

    @ApiOperation(value = "日计划新增页面的计划生产甑口跟已生产甑口")
    @PostMapping("/getPot/{centerId}&{locationId}")
    public PitAndPotVO getPot(
            @ApiParam(value = "centerId", example = "2") @PathVariable String centerId,
            @ApiParam(value = "locationId", example = "5") @PathVariable Integer locationId) {
        return workshopPitDayPlanService.getPot(centerId, locationId);
    }

    /**
     * @Author: LiuYu
     * @Description: 物料需求回退下发的日计划
     * @Date: 2022/9/27 13:51
     **/
    @ApiOperation(value = "物料需求回退下发的日计划")
    @PutMapping("rollbackDayPlan/{dayPlanId}")
    public ResultVO<?> rollbackDayPlan(@ApiParam(value = "dayPlanId") @PathVariable String dayPlanId) {
        return workshopPitDayPlanService.rollbackDayPlan(dayPlanId);
    }

    @ApiOperation(value = "执行日计划生成")
    @PostMapping("/executeDayPlanTask")
    public void executeDayPlanTask(@RequestBody ExecuteDayPlanTaskDTO executeDayPlanTaskDTO) {
        dayPlanTaskService.executeDayPlanTask(executeDayPlanTaskDTO);
    }

    @ApiOperation(value = "执行周期内的日计划生成")
    @PostMapping("/executeCycleDayPlanTask")
    public List<WorkshopPitDayPlanDTO> executeCycleDayPlanTask(@RequestBody ExecuteDayPlanTaskDTO executeDayPlanTaskDTO) {
        return dayPlanTaskService.executeCycleDayPlanTask(executeDayPlanTaskDTO);
    }
}
