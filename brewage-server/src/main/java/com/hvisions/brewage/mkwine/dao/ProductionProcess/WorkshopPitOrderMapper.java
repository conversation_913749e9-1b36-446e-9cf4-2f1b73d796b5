package com.hvisions.brewage.mkwine.dao.ProductionProcess;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.dto.mkwine.vo.WorkshopPitOrderQmVO;
import com.hvisions.brewage.dto.tpo.MaintainTaskAddDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrder.*;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.InPitAssociatedOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.OutPitAssociatedOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderSap.OrderSapPageQueryDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderStatus;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.*;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.WorkshopPitOrderSapPageListVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.FormulaUsedVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.WorkShopPitOrderFormulaVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: 窖池订单Mapper
 * @projectName brewage
 * @description:
 * @date 2022/4/18 10:20:58
 */
@Mapper
@Repository
public interface WorkshopPitOrderMapper extends BaseMapper<TPoWorkshopPitOrder> {
    public List<WorkshopPitOrderVO> getAllWorkshopPitOrder();

    /**
     * <AUTHOR>
     * @Des 新增窖池订单
     * @Date 2022/4/18 10:51:58
     * @Param
     * @Return
     */
    public Integer addWorkshopPitOrder(WorkshopPitOrderDTO workshopPitOrderDTO);

    /**
     * <AUTHOR>
     * @Des 分页查询窖池订单管理
     * @Date 2022/4/18 15:14:38
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderQueryVO> getWorkshopPitOrderQueryPage(WorkshopPitOrderQueryDTO workshopPitOrderQueryDTO);

    /**
     * <AUTHOR>
     * @Des 修改窖池订单
     * @Date 2022/4/20 10:25:59
     * @Param
     * @Return
     */
    public Boolean updateWorkshopPitOrder(WorkshopPitOrderUpdateDTO workshopPitOrderUpdateDTO);

    /**
     * <AUTHOR>
     * @Des 删除窖池订单
     * @Date 2022/4/20 16:41:24
     * @Param
     * @Return
     */
    public Boolean deleteWorkshopPitOrder(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id查找窖池订单
     * @Date 2022/4/20 16:45:13
     * @Param
     * @Return
     */
    public WorkshopPitOrderVO findWorkshopPitOrderById(Integer id);

    /**
     * <AUTHOR>
     * @Des 获取分页总条数
     * @Date 2022/4/21 9:58:36
     * @Param
     * @Return
     */
    public Integer getCount(WorkshopPitOrderQueryDTO workshopPitOrderQueryDTO);

    /**
     * <AUTHOR>
     * @Des 获取封窖确认数据查询
     * @Date 2022/4/21 14:30:38
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderSealConfirmQueryVO> getWorkshopPitOrderSealConfirmQuery(@Param("locationId") Integer locationId, @Param("fullPitId") String fullPitId);

    /**
     * @Des 获取封窖确认数据查询(单窖)
     * <AUTHOR>
     * @Date 2022/8/5 10:32:56
     * @Param
     * @Return
     */
    List<WorkshopPitOrderSapSealConfirmQueryVO> getWorkshopPitOrderSapSealConfirm(@Param("locationId") Integer locationId, @Param("pitCode") String pitCode);

    /**
     * <AUTHOR>
     * @Des 窖池订单封窖确认
     * @Date 2022/4/21 16:41:15
     * @Param
     * @Return
     */
    public Boolean WorkshopPitOrderSealConfirm(WorkshopPitOrderSealConfirmDTO workshopPitOrderSealConfirmDTOList);

    /**
     * <AUTHOR>
     * @Des 获取空窖确认数据查询
     * @Date 2022/4/22 11:31:36
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderEmptyPitQueryVO> getWorkshopPitOrderEmptyPitQuery(@Param("locationId") Integer locationId, @Param("fullPitId") String fullPitId, @Param("layer") String layer);

    /**
     * @Des 获取空窖确认数据查询(单窖)
     * <AUTHOR>
     * @Date 2022/8/5 16:06:45
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderSapEmptyPitQueryVO> getWorkshopPitOrderSapEmptyPitQuery(@Param("locationId") Integer locationId, @Param("pitCode") String pitCode, @Param("layer") String layer);

    /**
     * <AUTHOR>
     * @Des 窖池订单空窖确认
     * @Date 2022/4/22 14:14:21
     * @Param
     * @Return
     */
    public Boolean WorkshopPitOrderEmptyPit(WorkshopPitOrderEmptyPitDTO workshopPitOrderEmptyPitDTO);

    /**
     * <AUTHOR>
     * @Des 用于校验窖池是否重复或不可插入
     * @Date 2022/4/22 15:44:00
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderVO> getCheckWorkshopPitOrder(WorkshopPitOrderDTO workshopPitOrderDTO);

    public Integer getMaxId();

    /**
     * <AUTHOR>
     * @Des 入窖报检
     * @Date 2022/4/27 14:18:38
     * @Param id:窖池订单id inPitQmId:检验任务id
     * @Return
     */
    public Boolean WorkshopPitOrderInPitQm(@Param("id") Integer id, @Param("inPitQmId") Integer inPitQmId);


    /**
     * <AUTHOR>
     * @Des 出窖报检
     * @Date 2022/4/27 14:27:04
     * @Param id:窖池订单id outPitQmId:检验任务id
     * @Return
     */
    public Boolean WorkshopPitOrderOutPitQm(@Param("id") Integer id, @Param("outPitQmId") Integer outPitQmId);

    /**
     * <AUTHOR>
     * @Des 优先级数据查询，传递-1查询全部(起窖完成(即非发酵状态)的窖池订单不再显示在该列表中)
     * @Date 2022/4/27 17:55:26
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderPriorityQueryVO> getWorkshopPitOrderPriorityQuery(String fullPitId);

    /**
     * <AUTHOR>
     * @Des 优先级设置提交
     * @Date 2022/4/28 13:51:59
     * @Param
     * @Return
     */
    public Boolean WorkshopPitOrderPriority(WorkshopPitOrderPriorityDTO workshopPitOrderPriorityDTO);

    /**
     * <AUTHOR>
     * @Des 通过单窖编号和车间id查询对应在制的窖池订单
     * @Date 2022/5/11 14:08:19
     * @Param
     * @Return
     */
    public List<Map<String, String>> findOrderCodeByPitName(@Param("locationId") Integer locationId, @Param("pitName") String pitName);

    /**
     * @Des 修改窖池订单状态
     * <AUTHOR>
     * @Date 2022/5/25 9:59:22
     * @Param
     * @Return
     */
    public Integer WorkshopPitOrderUpdateStatus(WorkshopPitOrderStatusDTO workshopPitOrderStatusDTO);

    /**
     * @Des 通过物料名称获取物料id
     * <AUTHOR>
     * @Date 2022/5/31 14:51:35
     * @Param
     * @Return
     */
    public Integer getMaterialIdByName(String name);

    /**
     * @Des 通过物料名称获取物料数据
     * <AUTHOR>
     * @Date 2022/6/1 15:44:50
     * @Param
     * @Return
     */
    public List<Map<String, Object>> getMaterialByName(String name);

    /**
     * @Des 通过车间id获取车间数据
     * <AUTHOR>
     * @Date 2022/6/1 17:14:22
     * @Param
     * @Return
     */
    public List<Map<String, Object>> getLocationById(Integer id);

    /**
     * @Des 获取查询页面的子项数据
     * <AUTHOR>
     * @Date 2022/6/13 15:06:40
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderQuerySapVO> getQueryChild(Integer id);

    /**
     * @Des 通过窖池编号查找窖池数据
     * <AUTHOR>
     * @Date 2022/6/14 17:05:29
     * @Param
     * @Return
     */
    public List<Map<String, Object>> getPitByCode(@Param("locationId") Integer locationId, @Param("pitCode") String pitCode);

    /**
     * @Des 质检：通过窖池订单获取窖池糟源数据
     * <AUTHOR>
     * @Date 2022/6/15 14:42:35
     * @Param
     * @Return
     */
    public WorkshopPitOrderQmVO getOrderQmInfo(@Param("orderCode") String orderCode);

    /**
     * @Des 通过车间id获取对应的连窖号
     * <AUTHOR>
     * @Date 2022/6/17 10:35:57
     * @Param
     * @Return
     */
    public List<TPoWorkshopFullPit> getFullPitByLocation(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId);

    /**
     * @Des 通过连窖订单id和物料名称获取物料数据
     * <AUTHOR>
     * @Date 2022/6/20 17:07:19
     * @Param
     * @Return
     */
    public Map<String, Object> getMaterialDataByOrderIdAndName(@Param("orderCodeId") Integer orderCodeId, @Param("materialName") String materialName);

    /**
     * @Des 通过连窖池订单id，获取最晚的甑口任务出甑时间
     * <AUTHOR>
     * @Date 2022/7/11 15:01:57
     * @Param
     * @Return
     */
    public Date getOpenPitFinishTimeById(@Param("id") Integer id);

    /**
     * @Des 通过单窖池订单id，获取最晚的甑口任务出甑时间
     * <AUTHOR>
     * @Date 2022/8/8 16:21:25
     * @Param
     * @Return
     */
    public Date getOpenPitFinishTimeBySapId(@Param("id") Integer id);

    /**
     * 批量更新窖池订单
     *
     * @param list
     * @return
     */
    public int updateBatchSelective(@Param("list") List<TPoWorkshopPitOrder> list);

    /**
     * @Des 优先级分页查询
     * <AUTHOR>
     * @Date 2022/9/5 10:30:10
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderPriorityQueryVO> getWorkshopPitOrderPriorityQueryPage(WorkshopPitOrderPriorityPageDTO workshopPitOrderPriorityPageDTO);

    /**
     * @Des 优先级分页查询条数
     * <AUTHOR>
     * @Date 2022/9/5 10:35:33
     * @Param
     * @Return
     */
    public Integer getWorkshopPitOrderPriorityQueryCount(WorkshopPitOrderPriorityPageDTO workshopPitOrderPriorityPageDTO);

    /**
     * @Des 通过糟源类型代码获取订单数量
     * <AUTHOR>
     * @Date 2022/9/6 18:01:04
     * @Param
     * @Return
     */
    public Integer getOrderByVinasse(@Param("vinasse") String vinasse);

    /**
     * @Des 通过连窖订单id，获取出窖甑口数
     * <AUTHOR>
     * @Date 2022/10/10 12:56:27
     * @Param
     * @Return
     */
    public Integer getOutPitNumByOrderId(@Param("orderId") Integer orderId);

    /**
     * @Des 订单导出
     * <AUTHOR>
     * @Date 2022/10/21 10:50:59
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderQueryVO> exportOrderData(WorkshopPitOrderQueryDTO workshopPitOrderQueryDTO);

    // 根据中心车间获取 酿酒中心车间班长、酿酒中心车间主任、酿酒中心生产主管
    String[] roleTelephone(Integer centerId, Integer locationId);

    // 通过配方id获取物料使用量
    Map<String, Object> getFormulaDetailByFormulaId(Integer id);


    List<FormulaUsedVO> getFormulaUsed();

    WorkShopPitOrderFormulaVO selectByOrderId(Integer id);

    void updateFormulaByVO(WorkShopPitOrderFormulaVO pitOrder);

    List<PitConfirmDatVO> selectPitConfirmData(WorkshopPitOrderQueryDTO queryDTO);

    MaintainTaskAddDTO selectWorkshopPitOrder(Integer workshopPitOrderId);

    List<ProductionSchedule> selectOrderEmptyProductionSchedule(@Param("centerId")Integer centerId, @Param("locationId")Integer locationId);

    List<ProductionSchedule> selectOrderProductionSchedule(@Param("centerId")Integer centerId, @Param("locationId")Integer locationId, @Param("dayPlanVinasseList")List<String> dayPlanVinasseList, @Param("continueIds")List<Integer> continueIds);

    /**
     * 分页查询窖池订单-新版
     * @param pageQueryDTO
     * @return
     */
    List<WorkshopPitOrderPageVO> selectWorkshopPitOrderPage(WorkshopPitOrderPageQueryDTO pageQueryDTO);

    /**
     * 分页查询窖池订单同步数据--新的
     * @param queryDTO
     * @return
     */
    List<WorkshopPitOrderSapPageListVO> getWorkshopPitOrderSapPage(OrderSapPageQueryDTO queryDTO);

    /**
     * 订单数据汇总数据查询
     * @param ids
     * @return
     */
    List<WorkshopPitOrderCollectVO> selectOrderCollect(@Param("ids") List<Integer> ids);

    /**
     * 查询订单详情
     * @param orderCodeId
     * @return
     */
    WorkshopPitOrderDetailVO selectWorkshopPitOrderById(Integer orderCodeId);

    /**
     * 订单产出详情
     * @param orderCodeId
     * @return
     */
    List<WorkShopPitOrderOutputVO> selectOrderOutputList(Integer orderCodeId);

    /**
     * 订单质量详情
     * @param orderCodeId
     * @return
     */
    List<WorkShopPitOrderQualityVO> selectOrderQualityList(Integer orderCodeId);

    /**
     * 设置订单入窖甑口调整信息查询
     * @param inPitOrder
     * @return
     */
    InPitAssociatedOrderDTO selectPotTaskAdjustInDataByOrder(String inPitOrder);

    /**
     * 设置订单出窖甑口调整信息查询
     * @param outPitOrder
     * @return
     */
    OutPitAssociatedOrderDTO selectPotTaskAdjustOutDataByOrder(String outPitOrder);

    /**
     * 根据IOT窖号去查询窖池订单信息--正在生产的订单只有一条 糟源+窖号
     * @param pitCode
     * @param vinasse
     * @return
     */
    TPoWorkshopPitOrder selectOrderByIotPitCode(@Param("pitCode") String pitCode, @Param("vinasse")String vinasse);

    /**
     * 获取订单糟源
     * @param id
     * @return
     */
    Map<String, String> getSapVinasse(Integer id);

    /**
     *
     * @param centerId
     * @param locationId
     * @param fullPit
     * @return
     */
    List<ProductionSchedule> selectTopFloorOrderSchedule(@Param("centerId") int centerId, @Param("locationId") int locationId, @Param("fullPit") List<String> fullPit);
}
