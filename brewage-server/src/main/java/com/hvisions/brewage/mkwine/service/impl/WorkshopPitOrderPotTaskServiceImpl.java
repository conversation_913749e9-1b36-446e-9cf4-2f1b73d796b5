package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.hvisions.brewage.BrewageApplication;
import com.hvisions.brewage.dao.tpo.TaskOrderBatchMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskOrderBatch;
import com.hvisions.brewage.enums.LogCaptureEnum;
import com.hvisions.brewage.feign.plan.BrewagePlanClient;
import com.hvisions.brewage.mkwine.RequestZk;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopHandinTaskMapper;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TPoWorkshopWinePotTaskMapper;
import com.hvisions.brewage.mkwine.dao.LiquorConnectManage.TmpTankMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskTemperatureMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dao.WorkshopPitMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.TechnologicalRequirementsMapper;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.HandInTaskQueryPotTaskDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.InPitAssociatedOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.OutPitAssociatedOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.PitAssociatedOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotTask.*;
import com.hvisions.brewage.mkwine.dto.Zk.ZkBaseListDTO;
import com.hvisions.brewage.mkwine.dto.Zk.ZkBaseResponseDTO;
import com.hvisions.brewage.mkwine.dto.Zk.ZkBaseResponseDataDTO;
import com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoOpctagValue;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopHandinTask;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopTmpTank;
import com.hvisions.brewage.mkwine.entity.LiquorConnectManage.TPoWorkshopWinePotTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTaskTemperature;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopPit;
import com.hvisions.brewage.mkwine.enums.CentralSecondVinasse;
import com.hvisions.brewage.mkwine.enums.CentralVinasseEnum;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderPotAssociatedService;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderPotTaskService;
import com.hvisions.brewage.mkwine.vo.LiquorConnectManage.HandTaskGetPitOrderPotTaskVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.*;
import com.hvisions.brewage.mkwine.vo.productiondisposition.TechnologicalRequirements.TechnologicalRequirementsVO;
import com.hvisions.brewage.plan.entity.VinasseSource;
import com.hvisions.brewage.plan.vo.FormulaAllInfo;
import com.hvisions.brewage.plan.vo.FormulaDetailVO;
import com.hvisions.brewage.service.base.LogService;
import com.hvisions.brewage.service.tpo.*;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.PageUtils;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.powder.client.IssueOrderDetailClient;
import com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueOrderDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: 甑口任务管理接口
 * @projectName brewage
 * @description:
 * @date 2022/5/5 11:13:14
 */
@Service
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = BrewageApplication.class)
public class WorkshopPitOrderPotTaskServiceImpl implements WorkshopPitOrderPotTaskService {
    @Resource
    BrewagePlanClient brewagePlanClient;
    @Resource
    public RequestZk requestZk;
    @Resource
    WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;
    @Resource
    WorkshopPitOrderMapper workshopPitOrderMapper;
    @Resource
    private LogCaptureClient logCaptureClient;
    @Resource
    WorkshopPitMapper workshopPitMapper;
    @Resource
    LocationExtendClient locationExtendClient;
    @Resource
    TechnologicalRequirementsMapper technologicalRequirementsMapper;
    @Resource
    TmpTankMapper tmpTankMapper;
    @Resource
    TPoWorkshopWinePotTaskMapper tPoWorkshopWinePotTaskMapper;
    @Resource
    TPoWorkshopHandinTaskMapper tPoWorkshopHandinTaskMapper;
    @Resource
    WorkshopPitOrderPotTaskTemperatureMapper workshopPitOrderPotTaskTemperatureMapper;

    @Resource
    private LogService logService;

    @Resource
    IssueOrderDetailClient issueOrderDetailClient;

    @Resource
    private TaskDishMixingDetailService taskDishMixingDetailService;

    @Resource
    private TaskSpreadingDetailService taskSpreadingDetailService;

    @Resource
    private TaskPileUpDetailService taskPileUpDetailService;

    @Resource
    private TaskGrainLubricationDetailService taskGrainLubricationDetailService;

    @Resource
    private TaskShangzhengDetailService taskShangzhengDetailService;

    @Resource
    private TaskSistillationDetailService taskSistillationDetailService;

    @Resource
    private TaskWinePickingDetailService taskWinePickingDetailService;

    @Resource
    private TaskSteamingDetailService taskSteamingDetailService;

    @Resource
    private WorkshopPitOrderPotAssociatedService workshopPitOrderPotAssociatedService;

    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    @Resource
    private TaskOrderBatchMapper taskOrderBatchMapper;


    @Value("${material.type.da-qu}")
    String DaQu;

    @Override
    public ExcelExportDto exportPotTaskData(WorkshopPitOrderPotTaskQueryDTO workshopPitOrderPotTaskQueryDTO) throws IOException {
        workshopPitOrderPotTaskQueryDTO.setPageSize(Integer.MAX_VALUE);
        workshopPitOrderPotTaskQueryDTO.setExport("export");
        List<WorkshopPitOrderPotTaskQueryVO> list = workshopPitOrderPotTaskQueryPage(workshopPitOrderPotTaskQueryDTO).getData();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
//                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
//                .registerWriteHandler(new StatisticalCellWriteHandler())
                .build();

        List<List<String>> head = head();
        // 动态添加表头，适用一些表头动态变化的场景
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetName("甑口任务");
        sheet1.setSheetNo(0);
        // 创建一个表格，用于 Sheet 中使用
        WriteTable table = new WriteTable();
        table.setTableNo(0);
        table.setHead(head);

        writer.write(contentData(list), sheet1, table);
//        writer.write(null, sheet1, table);
        writer.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("甑口任务.xlsx");
        exportDto.setBody(outputStream.toByteArray());

        return exportDto;
    }

    private List<List<Object>> contentData(List<WorkshopPitOrderPotTaskQueryVO> list) {
        List<List<Object>> contentList = Lists.newArrayList();
        for (WorkshopPitOrderPotTaskQueryVO workshopPitOrderPotTaskQueryVO : list) {
            List<Object> objects = Lists.newArrayList();
            // 甑口任务号
            objects.add(workshopPitOrderPotTaskQueryVO.getPotSerialNumber());
            // 糟源类型
            objects.add(workshopPitOrderPotTaskQueryVO.getVinasse());
            // 起窖号
            objects.add(workshopPitOrderPotTaskQueryVO.getOutPitCode());
            // 出糟时间
            objects.add(workshopPitOrderPotTaskQueryVO.getOutTime() == null ? "" : DateUtil.format(workshopPitOrderPotTaskQueryVO.getOutTime(), "yyyy-MM-dd HH:mm:ss"));
            // 糟重
            objects.add(workshopPitOrderPotTaskQueryVO.getFermentativeMaterialQuantity());
            // 粮重
            objects.add(workshopPitOrderPotTaskQueryVO.getCrushedGrainsQuantity());
            // 糠重
            objects.add(workshopPitOrderPotTaskQueryVO.getRicehullQuantity());
            // 回酒
            objects.add(workshopPitOrderPotTaskQueryVO.getBackAlcoholicQuantity());
            // 甑号
            objects.add(workshopPitOrderPotTaskQueryVO.getPotNum());
            // 出甑时间
            objects.add(workshopPitOrderPotTaskQueryVO.getOutPotTime() == null ? "" : DateUtil.format(workshopPitOrderPotTaskQueryVO.getOutPotTime(), "yyyy-MM-dd HH:mm:ss"));
            // 摊晾机编号
            objects.add(workshopPitOrderPotTaskQueryVO.getCollingMachineCode());
            // 加曲重量
            objects.add(workshopPitOrderPotTaskQueryVO.getQuChangedWeight());
            // 曲粉斗
            objects.add(workshopPitOrderPotTaskQueryVO.getQuHopperCode());
            // 加曲重量确认
            objects.add(workshopPitOrderPotTaskQueryVO.getQuQuantity());
            // 核对完成时间
            objects.add(workshopPitOrderPotTaskQueryVO.getCheckTime() == null ? "" : DateUtil.format(workshopPitOrderPotTaskQueryVO.getCheckTime(), "yyyy-MM-dd HH:mm:ss"));
            // 核对人
            objects.add(workshopPitOrderPotTaskQueryVO.getCheckName());

            contentList.add(objects);
        }
        return contentList;
    }

    private List<List<String>> head() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("甑口任务号"));
        headTitles.add(Lists.newArrayList("糟源类型"));
        headTitles.add(Lists.newArrayList("起窖号"));
        headTitles.add(Lists.newArrayList("出糟时间"));
        headTitles.add(Lists.newArrayList("糟重"));
        headTitles.add(Lists.newArrayList("粮重"));
        headTitles.add(Lists.newArrayList("糠重"));
        headTitles.add(Lists.newArrayList("回酒"));
        headTitles.add(Lists.newArrayList("甑号"));
        headTitles.add(Lists.newArrayList("出甑时间"));
        headTitles.add(Lists.newArrayList("摊晾机编号"));
        headTitles.add(Lists.newArrayList("加曲重量"));
        headTitles.add(Lists.newArrayList("曲粉斗"));
        headTitles.add(Lists.newArrayList("加曲重量确认(kg)"));
        headTitles.add(Lists.newArrayList("核对完成时间"));
        headTitles.add(Lists.newArrayList("核对人"));

        return headTitles;
    }

    /**
     * <AUTHOR>
     * @Des 获取所有甑口任务管理
     * @Date 2022/5/5 11:17:20
     * @Param
     * @Return
     */
    @Override
    public List<WorkshopPitOrderPotTaskVO> getAllWorkshopPitOrderPotTask() {
        return workshopPitOrderPotTaskMapper.getAllWorkshopPitOrderPotTask();
    }

    /**
     * <AUTHOR>
     * @Des 新增甑口任务
     * @Date 2022/5/5 16:29:05
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO addWorkshopPitOrderPotTask(WorkshopPitOrderPotTaskAddDTO workshopPitOrderPotTaskAddDTO) {
        // 查找对应的考勤数据
        Integer ShiftLogId = workshopPitOrderPotTaskMapper.getNowChangeShiftLogAttendanceId(workshopPitOrderPotTaskAddDTO.getOutTime(), workshopPitOrderPotTaskAddDTO.getOutPitCode().toString());
        workshopPitOrderPotTaskAddDTO.setChangeShiftLogId(ShiftLogId);
        // 通过糟源类型名称获取对应的糟源工艺
        TechnologicalRequirementsVO technologicalRequirementsByName = technologicalRequirementsMapper.findTechnologicalRequirementsByName(workshopPitOrderPotTaskAddDTO.getVinasse());

        if (technologicalRequirementsByName == null)
            throw new BaseKnownException(10000, "当前糟源类别不存在");
        // 查看工艺是否丢糟
        if (technologicalRequirementsByName.getIsSpentGrains()) {
            // 如果是丢糟，则设置出甑时间为任务结束时间
            if (workshopPitOrderPotTaskAddDTO.getOutPotTime() != null) {
                workshopPitOrderPotTaskAddDTO.setPottaskEndtime(workshopPitOrderPotTaskAddDTO.getOutPotTime());
                // 设置任务状态为已完成
                workshopPitOrderPotTaskAddDTO.setOrderStatus(2);
            }
        } else {
            // 如果不是丢糟，则设置摊晾结束时间为任务结束
            if (workshopPitOrderPotTaskAddDTO.getCollingEndtime() != null) {
                workshopPitOrderPotTaskAddDTO.setPottaskEndtime(workshopPitOrderPotTaskAddDTO.getCollingEndtime());
                // 设置任务状态为已完成
                workshopPitOrderPotTaskAddDTO.setOrderStatus(2);
            }
        }

        final TPoWorkshopPitOrderPotTask pojo = DtoMapper.convert(workshopPitOrderPotTaskAddDTO, TPoWorkshopPitOrderPotTask.class);
        if (pojo.getOutTime() != null) {
            pojo.setOutDregsStartTime(DateUtil.convertToLocalTime(pojo.getOutTime()));
            pojo.setPottaskStarttime(pojo.getOutTime());
        }
        if (pojo.getOutPotTime() != null) {
            pojo.setOutPotStartTime(DateUtil.convertToLocalTime(pojo.getOutPotTime()));
        }

        pojo.setBackAlcoholicQuantity(pojo.getBackAlcoholicQuantityRevise());
        pojo.setCreateTime(new Date());
        pojo.setIsDeleted(false);
        final int insert = workshopPitOrderPotTaskMapper.insert(pojo);
        TPoWorkshopPit tPoWorkshopPit = workshopPitMapper.selectOne(new LambdaUpdateWrapper<TPoWorkshopPit>()
                .eq(TPoWorkshopPit::getIsDeleted, false)
                .eq(TPoWorkshopPit::getPitCode, workshopPitOrderPotTaskAddDTO.getOutPitCode()));
        if (tPoWorkshopPit == null)
            throw new BaseKnownException(10000, "该出窖号不存在");
        String location = null;
        // 为了匹配中控的controllerId，处理中心车间的字符
        Map<String, Object> map = workshopPitOrderMapper.getLocationById(tPoWorkshopPit.getWorkshopId()).get(0);
        String code = map.get("code").toString();
        location = code.substring(0, code.length() - 2) + code.substring(code.length() - 1);
        workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                .set(TPoWorkshopPitOrderPotTask::getControllerId, location)
                .eq(TPoWorkshopPitOrderPotTask::getId, pojo.getId()));

//        if (insert < 1) {
//            throw new BaseKnownException(10000, "新增甑口任务失败");
//        }
        if (workshopPitOrderPotTaskAddDTO.getInOrderCode() != null) {
            // 修改入窖甑口数
            updateInPotNumBySapOrderCode(workshopPitOrderPotTaskAddDTO.getInOrderCode());
        }
        return ResultVO.success("Success");

    }

    /**
     * @Des 新增甑口任务(批量)
     * <AUTHOR>
     * @Date 2022/8/15 17:37:08
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO addWorkshopPitOrderPotTaskBatch(List<WorkshopPitOrderPotTaskAddDTO> workshopPitOrderPotTaskAddDTOList) {
        for (WorkshopPitOrderPotTaskAddDTO workshopPitOrderPotTaskAddDTO : workshopPitOrderPotTaskAddDTOList) {
            addWorkshopPitOrderPotTask(workshopPitOrderPotTaskAddDTO);
        }
        return ResultVO.success("Success");
    }

    /**
     * <AUTHOR>
     * @Des 甑口任务查询
     * @Date 2022/5/6 17:57:03
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskQueryPageVO workshopPitOrderPotTaskQueryPage(WorkshopPitOrderPotTaskQueryDTO workshopPitOrderPotTaskQueryDTO) {
        String location = null;
        // 为了匹配中控的controllerId，处理中心车间的字符
        if (workshopPitOrderPotTaskQueryDTO.getLocationId() != null) {
            Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskQueryDTO.getLocationId()).get(0);
            String code = map.get("code").toString();
            location = code.substring(0, code.length() - 2) + code.substring(code.length() - 1);
        } else if (workshopPitOrderPotTaskQueryDTO.getCenterId() != null) {
            Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskQueryDTO.getCenterId()).get(0);
            String code = map.get("code").toString();
            location = code;
        }
        workshopPitOrderPotTaskQueryDTO.setControllerId(location);

        WorkshopPitOrderPotTaskQueryPageVO workshopPitOrderPotTaskQueryPageVO = new WorkshopPitOrderPotTaskQueryPageVO();

        workshopPitOrderPotTaskQueryDTO.setPage((workshopPitOrderPotTaskQueryDTO.getPage() - 1) * workshopPitOrderPotTaskQueryDTO.getPageSize());
        //设置表格数据
        workshopPitOrderPotTaskQueryPageVO.setData(workshopPitOrderPotTaskMapper.workshopPitOrderPotTaskQueryPage(workshopPitOrderPotTaskQueryDTO));
        //设置条数
        workshopPitOrderPotTaskQueryPageVO.setCount(workshopPitOrderPotTaskMapper.getCount(workshopPitOrderPotTaskQueryDTO));
        return workshopPitOrderPotTaskQueryPageVO;
    }

    private int validTaskIsEnd(Integer controllerId, String vinasse, Object collingEndtime, Object outPotStartTime,
                               Object outPotTime) {
//        709/713/718  CJ，HZ，CZ 通过有摊晾结束时间 修改状态为完成状态；
//        其他糟源类型  709/718(出甑开盖时间/出糟时间 只要其中一个有数据就认为完成)    713以(出甑开盖时间) 作为结束状态。
        final List<String> vinasses = Lists.newArrayList("CJ", "HZ", "CZ");
        if (vinasses.contains(vinasse)) {
            return collingEndtime == null ? 1 : 2;
        } else {
            if (Pattern.matches("^713.*", String.valueOf(controllerId))) {
                return outPotStartTime == null ? 1 : 2;
            }
            return outPotStartTime == null && outPotTime == null ? 1 : 2;
        }
    }

    /**
     * <AUTHOR>
     * @Des 甑口任务_中控接口
     * @Date 2022/5/9 17:55:40
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO WorkshopPitOrderPotTaskCentral(WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO, String body) {
        final LogDto log = getBaseLog("中控数据推送", "", "");
        String centralVinasse;
        String centralVinasseLevel;
        try {
            // 把中控的糟源类型编号转换成糟源类型名称
            // 增加解析逻辑，兼容二期内容
            Integer vinasseId = workshopPitOrderPotTaskCentralDTO.getVinasseId();
            int lastDigit = vinasseId % 10;
            if (lastDigit == 1) {
                //最后一位是1的情况 是二期新逻辑
                //倒数第二位是层级
                int secondLastDigit = (vinasseId / 10) % 10;
                //余下的是糟源类型
                String s = "" + secondLastDigit + lastDigit;
                String[] split = String.valueOf(vinasseId).split(s);
                centralVinasse = CentralSecondVinasse.getVinasseName(split[0]);
                centralVinasseLevel = CentralSecondVinasse.getVinasseLevel("" + secondLastDigit);
                workshopPitOrderPotTaskCentralDTO.setVinasse(centralVinasse);
                workshopPitOrderPotTaskCentralDTO.setFermentativeMaterialLevels(centralVinasseLevel);
            } else {
                centralVinasse = CentralVinasseEnum.FindVinasseName(workshopPitOrderPotTaskCentralDTO.getVinasseId());
                centralVinasseLevel = CentralVinasseEnum.FindVinasseLevel(workshopPitOrderPotTaskCentralDTO.getVinasseId());
                workshopPitOrderPotTaskCentralDTO.setVinasse(centralVinasse);
                workshopPitOrderPotTaskCentralDTO.setFermentativeMaterialLevels(centralVinasseLevel);
            }
            final String vinasse = centralVinasse;
            //判断任务是否结束
//            workshopPitOrderPotTaskCentralDTO.setOrderStatus(
//                    validTaskIsEnd(workshopPitOrderPotTaskCentralDTO.getControllerId(), vinasse,
//                            workshopPitOrderPotTaskCentralDTO.getCollingEndtime(),
//                            workshopPitOrderPotTaskCentralDTO.getOutPotStartTime(),
//                            workshopPitOrderPotTaskCentralDTO.getOutPotTime())
//            );


            if (workshopPitOrderPotTaskCentralDTO.getOutDregsStartTime() != null) {
                workshopPitOrderPotTaskCentralDTO.setOutTime(Date.from(workshopPitOrderPotTaskCentralDTO.getOutDregsStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            }
            // 开盖时间时间等于出甑时间
            if (workshopPitOrderPotTaskCentralDTO.getTurnPotTime() != null) {
                workshopPitOrderPotTaskCentralDTO.setOutPotTime(workshopPitOrderPotTaskCentralDTO.getTurnPotTime());
            }
            if (workshopPitOrderPotTaskCentralDTO.getOutPotStartTime() != null) {
                workshopPitOrderPotTaskCentralDTO.setOutPotTime(
                        DateUtil.convertToDate(workshopPitOrderPotTaskCentralDTO.getOutPotStartTime()));
            }
            if (workshopPitOrderPotTaskCentralDTO.getOutflowEndtime() != null) {
                workshopPitOrderPotTaskCentralDTO.setDistillateEndtime(workshopPitOrderPotTaskCentralDTO.getOutflowEndtime());
            }

            workshopPitOrderPotTaskCentralDTO.setOrderStatus(workshopPitOrderPotTaskCentralDTO.getOutPotTime() == null ? 1 : 2);

            // 先查看当前数据中有没有相同的中控id
            LambdaQueryWrapper<TPoWorkshopPitOrderPotTask> tPoWorkshopPitOrderPotTaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tPoWorkshopPitOrderPotTaskLambdaQueryWrapper
                    .eq(TPoWorkshopPitOrderPotTask::getIsDeleted, false)
                    .eq(TPoWorkshopPitOrderPotTask::getControllerId, workshopPitOrderPotTaskCentralDTO.getControllerId())
                    .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, workshopPitOrderPotTaskCentralDTO.getId());
            List<TPoWorkshopPitOrderPotTask> tPoWorkshopPitOrderPotTasks = workshopPitOrderPotTaskMapper.selectList(tPoWorkshopPitOrderPotTaskLambdaQueryWrapper);

            if (tPoWorkshopPitOrderPotTasks.size() <= 0) {// 插入数据
                // 设置任务状态为待执行
//                workshopPitOrderPotTaskCentralDTO.setOrderStatus(1);
                // 设置 出糟时间 为 甑口任务开始时间
                if (workshopPitOrderPotTaskCentralDTO.getOutTime() != null) {
                    workshopPitOrderPotTaskCentralDTO.setPottaskStarttime(workshopPitOrderPotTaskCentralDTO.getOutTime());
                }
                // 获取考勤的数据id，用于记录班组人数
                Integer shiftId = workshopPitOrderPotTaskMapper.getNowChangeShiftLogAttendanceId(workshopPitOrderPotTaskCentralDTO.getOutTime(), workshopPitOrderPotTaskCentralDTO.getOutPitCode() == null ? null : workshopPitOrderPotTaskCentralDTO.getOutPitCode().toString());
                workshopPitOrderPotTaskCentralDTO.setChangeShiftLogId(shiftId);

                // 通过糟源类型名称获取对应的糟源工艺
                TechnologicalRequirementsVO technologicalRequirementsByName = technologicalRequirementsMapper.findTechnologicalRequirementsByName(vinasse);

                if (technologicalRequirementsByName == null) {
                    throw new BaseKnownException(10000, "当前糟源类别不存在");
                }

                final TPoWorkshopPitOrderPotTask dto = DtoMapper.convert(workshopPitOrderPotTaskCentralDTO,
                        TPoWorkshopPitOrderPotTask.class, "id");
                dto.setCrushedGrainsQuantityBack(dto.getCrushedGrainsQuantity());
                dto.setRicehullQuantityBack(dto.getRicehullQuantity());
                dto.setBackAlcoholicQuantityRevise(dto.getBackAlcoholicQuantity());
                dto.setQuQuantity(dto.getQuChangedWeight());
                dto.setCreateTime(new Date());
                dto.setSerialNumber(workshopPitOrderPotTaskCentralDTO.getPotSerialNumber());
                dto.setPotSerialNumber(String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()));
                dto.setIsDeleted(false);

                final int count = workshopPitOrderPotTaskMapper.insert(dto);

                if (count < 1) {
                    throw new BaseKnownException(10000, "中控添加数据失败,新增条数为0");
                }
                // 找出时间最近的阀门打开信号
                // 找出糟源信息
                VinasseSource vinasseSource = brewagePlanClient.selectVinasseSourceOne(workshopPitOrderPotTaskCentralDTO.getVinasse()).getData();
                // 找出车间信息
                StringBuilder sb = new StringBuilder(workshopPitOrderPotTaskCentralDTO.getControllerId().toString());
                sb.insert(3, "0");
                String location = sb.toString();
                LocationDTO locationData = locationExtendClient.getLocationByCode(location).getData();
                // 匹配第一段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillateFirstStarttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillateFirstStarttime(), 1, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber,
                                    String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()))
                            .set(TPoWorkshopPitOrderPotTask::getDistillateFirstTankId, tmpTankId));
                }
                // 匹配第二段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate2Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate2Starttime(), 2, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()))
                            .set(TPoWorkshopPitOrderPotTask::getDistillate2_TankId, tmpTankId));
                }
                // 匹配第三段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate3Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate3Starttime(), 3, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()))
                            .set(TPoWorkshopPitOrderPotTask::getDistillate3_TankId, tmpTankId));
                }
                // 匹配第四段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate4Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate4Starttime(), 4, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()))
                            .set(TPoWorkshopPitOrderPotTask::getDistillate4_TankId, tmpTankId));
                }
                // 匹配第五段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillateLastwaterStarttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillateLastwaterStarttime(), 5, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, String.valueOf(workshopPitOrderPotTaskCentralDTO.getId()))
                            .set(TPoWorkshopPitOrderPotTask::getDistillateLastwaterTankId, tmpTankId));
                }

            } else {// 修改数据
                // 通过糟源类型名称获取对应的糟源工艺
                TechnologicalRequirementsVO technologicalRequirementsByName = technologicalRequirementsMapper.findTechnologicalRequirementsByName(vinasse);

                if (technologicalRequirementsByName == null) {
                    throw new BaseKnownException(10000, "当前糟源类别不存在");
                }

                // 如果入窖号 或出窖号不为0，则清空推送过来的窖号
                TPoWorkshopPitOrderPotTask potTask = tPoWorkshopPitOrderPotTasks.get(0);
                if (potTask.getInPitCode() != null && potTask.getInPitCode() != 0) {
                    workshopPitOrderPotTaskCentralDTO.setInPitCode(null);
                }
                if (potTask.getOutPitCode() != null && potTask.getOutPitCode() != 0) {
                    workshopPitOrderPotTaskCentralDTO.setOutPitCode(null);
                }

                Integer integer = workshopPitOrderPotTaskMapper.updateWorkshopPitOrderPotTaskCentral(workshopPitOrderPotTaskCentralDTO);
                if (integer < 1)
                    throw new BaseKnownException(10000, "中控更新数据失败,作用条数为0");

                // 找出时间最近的阀门打开信号
                // 找出糟源信息
                VinasseSource vinasseSource = brewagePlanClient.selectVinasseSourceOne(workshopPitOrderPotTaskCentralDTO.getVinasse()).getData();
                // 找出车间信息
                StringBuilder sb = new StringBuilder(workshopPitOrderPotTaskCentralDTO.getControllerId().toString());
                sb.insert(3, "0");
                String location = sb.toString();
                LocationDTO locationData = locationExtendClient.getLocationByCode(location).getData();
                // 匹配第一段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillateFirstStarttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillateFirstStarttime(), 1, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, potTask.getId())
                            .set(TPoWorkshopPitOrderPotTask::getDistillateFirstTankId, tmpTankId));
                }
                // 匹配第二段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate2Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate2Starttime(), 2, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, potTask.getId())
                            .set(TPoWorkshopPitOrderPotTask::getDistillate2_TankId, tmpTankId));
                }
                // 匹配第三段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate3Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate3Starttime(), 3, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, potTask.getId())
                            .set(TPoWorkshopPitOrderPotTask::getDistillate3_TankId, tmpTankId));
                }
                // 匹配第四段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillate4Starttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillate4Starttime(), 4, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, potTask.getId())
                            .set(TPoWorkshopPitOrderPotTask::getDistillate4_TankId, tmpTankId));
                }
                // 匹配第五段酒的暂存罐
                if (workshopPitOrderPotTaskCentralDTO.getDistillateLastwaterStarttime() != null) {
                    Integer tmpTankId = null;// 暂存罐id
                    String opcValueByStageTime = workshopPitOrderPotTaskMapper.getOpcValueByStageTime(workshopPitOrderPotTaskCentralDTO.getDistillateLastwaterStarttime(), 5, locationData.getId(), vinasseSource.getId());
                    if (opcValueByStageTime != null) {
                        TPoWorkshopTmpTank tPoWorkshopTmpTank = tmpTankMapper.selectOne(new LambdaQueryWrapper<TPoWorkshopTmpTank>().eq(TPoWorkshopTmpTank::getInputValveTagId, opcValueByStageTime));
                        tmpTankId = tPoWorkshopTmpTank.getId();
                    }
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, potTask.getId())
                            .set(TPoWorkshopPitOrderPotTask::getDistillateLastwaterTankId, tmpTankId));
                }
            }
            log.setLogParameter(StringUtil.isNotEmpty(body) ? body :
                    JSONObject.toJSONString(workshopPitOrderPotTaskCentralDTO));
            log.setLogType(1);
            logCaptureClient.logRecord(log);
            return ResultVO.success("Success");
        } catch (Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(StringUtil.isNotEmpty(body) ? body :
                    JSONObject.toJSONString(workshopPitOrderPotTaskCentralDTO));
            log.setLogType(2);
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "中控推送失败，原因：" + e.getMessage());
        }

    }

    /**
     * @Des 甑口任务中控温度接口
     * <AUTHOR>
     * @Date 2022/12/28 9:41:35
     * @Param
     * @Return
     */
    @Override
    public ResultVO temperatureReceive(WorkshopPitOrderPotTemperatureDTO dto) {
        final LogDto log = getBaseLog("中控温度数据推送", "", "");
        try {
            TPoWorkshopPitOrderPotTaskTemperature tPoWorkshopPitOrderPotTaskTemperature = new TPoWorkshopPitOrderPotTaskTemperature();
            tPoWorkshopPitOrderPotTaskTemperature.setControllerId(dto.getControllerId());
            tPoWorkshopPitOrderPotTaskTemperature.setPotSerialNumber(dto.getPotSerialNumber());
            tPoWorkshopPitOrderPotTaskTemperature.setType(dto.getType());
            tPoWorkshopPitOrderPotTaskTemperature.setUpdateTime(dto.getUpdateTime());
            tPoWorkshopPitOrderPotTaskTemperature.setValue(dto.getValue());

            workshopPitOrderPotTaskTemperatureMapper.insert(tPoWorkshopPitOrderPotTaskTemperature);
            log.setLogParameter(JSONObject.toJSONString(dto));
            log.setLogType(1);
            logCaptureClient.logRecord(log);
        } catch (final Exception e) {
            log.setLogExceptionMessage(e.getMessage());
            log.setLogParameter(JSONObject.toJSONString(dto));
            log.setLogType(2);
            logCaptureClient.logRecord(log);
            throw new BaseKnownException(10000, "中控推送失败，原因：" + e.getMessage());
        }

        return ResultVO.success("Success");
    }


    /**
     * <AUTHOR>
     * @Des 甑口任务摘酒记录提交
     * @Date 2022/5/9 17:54:10
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO workshopPitOrderPotTaskPickWine(WorkshopPitOrderPotTaskPickWineDTO workshopPitOrderPotTaskPickWineDTO) {
        if (workshopPitOrderPotTaskMapper.workshopPitOrderPotTaskPickWine(workshopPitOrderPotTaskPickWineDTO)) {
            return ResultVO.success("Success");
        } else {
            return ResultVO.error("摘酒记录提交失败");
        }
    }

    /**
     * <AUTHOR>
     * @Des 摊晾记录提交
     * @Date 2022/5/10 14:04:12
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO workshopPitOrderPotTaskColling(WorkshopPitOrderPotTaskCollingDTO workshopPitOrderPotTaskCollingDTO) {
        if (workshopPitOrderPotTaskMapper.workshopPitOrderPotTaskColling(workshopPitOrderPotTaskCollingDTO)) {
            TPoWorkshopPitOrderPotTask task = workshopPitOrderPotTaskMapper.selectById(workshopPitOrderPotTaskCollingDTO.getId());
            // 通过糟源类型名称获取对应的糟源工艺
            TechnologicalRequirementsVO technologicalRequirementsByName = technologicalRequirementsMapper.findTechnologicalRequirementsByName(task.getVinasse());
            if (technologicalRequirementsByName == null)
                throw new BaseKnownException(10000, "当前糟源类别不存在,不能判断是否丢糟");
            // 如果不是丢糟，则设置摊晾结束时间为任务结束
            if (!technologicalRequirementsByName.getIsSpentGrains()) {
                workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                        .eq(TPoWorkshopPitOrderPotTask::getId, workshopPitOrderPotTaskCollingDTO.getId())
                        .set(TPoWorkshopPitOrderPotTask::getPottaskEndtime, workshopPitOrderPotTaskCollingDTO.getCollingEndtime())
                        .set(TPoWorkshopPitOrderPotTask::getOrderStatus, 2)
                );
            }

            return ResultVO.success("Success");
        } else {
            return ResultVO.error("摊晾记录提交失败");
        }
    }

    /**
     * <AUTHOR>
     * @Des 获取摘酒记录数据
     * @Date 2022/5/10 14:45:21
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskPickWineVO getWorkshopPitOrderPotTaskPickWine(Integer id) {
        return workshopPitOrderPotTaskMapper.getWorkshopPitOrderPotTaskPickWine(id);
    }

    /**
     * <AUTHOR>
     * @Des 获取摊晾记录数据
     * @Date 2022/5/10 15:07:08
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskCollingVO getWorkshopPitOrderPotTaskColling(Integer id) {
        // 获取摊晾记录数据
        WorkshopPitOrderPotTaskCollingVO workshopPitOrderPotTaskColling = workshopPitOrderPotTaskMapper.getWorkshopPitOrderPotTaskColling(id);
        // 获取所有配方数据
        List<FormulaAllInfo> formulaAllInfos = brewagePlanClient.listAllFormulaInfo().getData();
        // 找出对应糟源名称和状态值为生效的 配方数据集合
        List<FormulaAllInfo> formulaCollect = formulaAllInfos.stream().filter(t -> t.getState().equals("1")).filter(t -> t.getSourceCode().equals(workshopPitOrderPotTaskColling.getVinasse())).collect(Collectors.toList());
        // 如果找出对应的数据，找出物料类型为大曲的数据
        if (formulaCollect.size() > 0) {
            List<FormulaDetailVO> formulaDetail = formulaCollect.get(0).getFormulaDetailVOList().stream().filter(t -> DaQu.equals(t.getMaterialTypeName())).collect(Collectors.toList());
            // 如果找出对应的数据，找出他的对应标准加曲量
            if (formulaDetail.size() > 0) {
                BigDecimal quality = formulaDetail.get(0).getQuality();

                // 先判断 加曲机重量变化是否为空（确定该任务是否人工生产）
                // 如果为空，人工提醒就是 标准加曲量~标准加曲量
                if (workshopPitOrderPotTaskColling.getQuChangedWeight() == null) {
                    // 设置人工加曲量提示
                    String format = MessageFormat.format("{0}~{1}", quality.setScale(2, RoundingMode.FLOOR).toString(), quality.setScale(2, RoundingMode.FLOOR).toString());
                    workshopPitOrderPotTaskColling.setManualFlexingHint(format);
                    return workshopPitOrderPotTaskColling;
                }
                // 如果 标准加曲量 -  加曲机重量变化 >  标准加曲量 * 允许浮动最大百分比 则 min = 标准加曲量 * (1- 允许浮动最大百分比)，max = 标准加曲量 * (1+ 允许浮动最大百分比)
                if (quality.subtract(BigDecimal.valueOf(workshopPitOrderPotTaskColling.getQuChangedWeight())).compareTo(quality.multiply(new BigDecimal("0.1"))) > 0) {
                    // 保留两位小数不四舍五入
                    BigDecimal min = quality.multiply(BigDecimal.valueOf((1 - 0.1))).setScale(2, RoundingMode.FLOOR);
                    BigDecimal max = quality.multiply(BigDecimal.valueOf((1 + 0.1))).setScale(2, RoundingMode.FLOOR);
                    // 设置人工加曲量提示
                    String format = MessageFormat.format("{0}~{1}", min.toString(), max.toString());
                    workshopPitOrderPotTaskColling.setManualFlexingHint(format);
                }
            }
        }
        return workshopPitOrderPotTaskColling;
    }

    /**
     * <AUTHOR>
     * @Des 获取修改的数据
     * @Date 2022/5/10 16:48:26
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskUpdateVO getWorkshopPitOrderPotTaskUpdate(Integer id) {
        return workshopPitOrderPotTaskMapper.getWorkshopPitOrderPotTaskUpdate(id);
    }

    /**
     * <AUTHOR>
     * @Des 甑口任务修改提交
     * @Date 2022/5/10 18:02:14
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO workshopPitOrderPotTaskUpdate(WorkshopPitOrderPotTaskUpdateDTO workshopPitOrderPotTaskUpdateDTO) {
        // 获取入窖订单的连窖id
        TPoWorkshopPitOrderPotTask tPoWorkshopPitOrderPotTask = workshopPitOrderPotTaskMapper.selectById(workshopPitOrderPotTaskUpdateDTO.getId());
        Integer orderIdBySap = workshopPitOrderPotTaskMapper.getOrderIdBySap(tPoWorkshopPitOrderPotTask.getInOrderCode());

        if (workshopPitOrderPotTaskMapper.workshopPitOrderPotTaskUpdate(workshopPitOrderPotTaskUpdateDTO)) {
            // 更新开始上甑时间，和结束上甑时间
            if (workshopPitOrderPotTaskUpdateDTO.getLoadingEndtime() != null && workshopPitOrderPotTaskUpdateDTO.getLoadingStarttime() != null) { // 计算上甑时长
                Date loadingStarttime = workshopPitOrderPotTaskUpdateDTO.getLoadingStarttime();
                Date loadingEndtime = workshopPitOrderPotTaskUpdateDTO.getLoadingEndtime();
                if (loadingEndtime.compareTo(loadingStarttime) < 0)
                    throw new BaseKnownException(10000, "开始上甑时间不能大于结束上甑时间");

                long timeSpan = loadingEndtime.getTime() - loadingStarttime.getTime();
                workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                        .eq(TPoWorkshopPitOrderPotTask::getId, workshopPitOrderPotTaskUpdateDTO.getId())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingStarttime, workshopPitOrderPotTaskUpdateDTO.getLoadingStarttime())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingEndtime, workshopPitOrderPotTaskUpdateDTO.getLoadingEndtime())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingTimespan, timeSpan)
                );
            } else {
                workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                        .eq(TPoWorkshopPitOrderPotTask::getId, workshopPitOrderPotTaskUpdateDTO.getId())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingStarttime, workshopPitOrderPotTaskUpdateDTO.getLoadingStarttime())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingEndtime, workshopPitOrderPotTaskUpdateDTO.getLoadingEndtime())
                        .set(TPoWorkshopPitOrderPotTask::getLoadingTimespan, null)
                );
            }

            if (orderIdBySap != null) {
                // 修改旧订单的入窖池甑口数
                Integer potByOrderId1 = workshopPitOrderPotTaskMapper.getPotByOrderId(orderIdBySap);
                LambdaUpdateWrapper<TPoWorkshopPitOrder> tPoWorkshopPitOrderLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
                tPoWorkshopPitOrderLambdaUpdateWrapper2.eq(TPoWorkshopPitOrder::getId, orderIdBySap)
                        .set(TPoWorkshopPitOrder::getInPitNum, potByOrderId1);
                workshopPitOrderMapper.update(null, tPoWorkshopPitOrderLambdaUpdateWrapper2);
                // 修改新的入窖甑口数
                updateInPotNumBySapOrderCode(workshopPitOrderPotTaskUpdateDTO.getInOrderCode());

                // 通过糟源类型名称获取对应的糟源工艺
                TechnologicalRequirementsVO technologicalRequirementsByName = technologicalRequirementsMapper.findTechnologicalRequirementsByName(tPoWorkshopPitOrderPotTask.getVinasse());
                if (technologicalRequirementsByName == null)
                    throw new BaseKnownException(10000, "当前糟源类别不存在,无法判断是否丢糟");
                if (technologicalRequirementsByName.getIsSpentGrains()) {
                    // 如果是丢糟，则设置出甑时间为任务结束时间
                    workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                            .eq(TPoWorkshopPitOrderPotTask::getId, tPoWorkshopPitOrderPotTask.getId())
                            .eq(TPoWorkshopPitOrderPotTask::getPottaskEndtime, workshopPitOrderPotTaskUpdateDTO.getOutPotTime())
                            .set(TPoWorkshopPitOrderPotTask::getOrderStatus, 2)
                    );
                }

            }
            // 修改曲斗对应的数据
//            if (workshopPitOrderPotTaskUpdateDTO.getQuDouId() != null) {
//                ResultVO<IssueOrderDetailDTO> resultVO = issueOrderDetailClient.getLastIssueOrderDetailByParameterId(workshopPitOrderPotTaskUpdateDTO.getQuDouId());
//
//                if (resultVO.getCode() != 200 || resultVO.getData() == null) {
//                    throw new BaseKnownException(10000, "当前曲斗没有曲粉发放记录");
//                }
//                workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
//                        .eq(TPoWorkshopPitOrderPotTask::getId, tPoWorkshopPitOrderPotTask.getId())
//                        .set(TPoWorkshopPitOrderPotTask::getQuDouId, workshopPitOrderPotTaskUpdateDTO.getQuDouId())
//                        .set(TPoWorkshopPitOrderPotTask::getQuBatch, resultVO.getData().getBatch())
//                );
//            } else {
//                // 清空曲斗数据
//                workshopPitOrderPotTaskMapper.update(null, new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
//                        .eq(TPoWorkshopPitOrderPotTask::getId, tPoWorkshopPitOrderPotTask.getId())
//                        .eq(TPoWorkshopPitOrderPotTask::getQuDouId, null)
//                        .set(TPoWorkshopPitOrderPotTask::getQuBatch, null)
//                );
//            }
            return ResultVO.success("Success");
        } else {
            return ResultVO.error("甑口任务修改失败");
        }
    }

    /**
     * <AUTHOR>
     * @Des 删除甑口任务
     * @Date 2022/5/11 10:24:59
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO deleteWorkshopPitOrderPotTask(Integer id) {
        // 通过获取甑口任务数据
        TPoWorkshopPitOrderPotTask tPoWorkshopPitOrderPotTask = workshopPitOrderPotTaskMapper.selectById(id);
        // 手动添加的数据没有甑口任务编号,因此,如果不为空则不可删除
//        if (tPoWorkshopPitOrderPotTask.getPotSerialNumber() != null) {
//            throw new BaseKnownException(10000, "甑口任务来自中控，任务不可删除");
//        }

        if (workshopPitOrderPotTaskMapper.deleteWorkshopPitOrderPotTask(id)) {
            if (tPoWorkshopPitOrderPotTask.getInOrderCode() != null) {
                // 修改入窖甑口数
                updateInPotNumBySapOrderCode(tPoWorkshopPitOrderPotTask.getInOrderCode());
            }
            return ResultVO.success("Success");
        } else {
            throw new BaseKnownException(10000, "甑口任务删除失败");
        }
    }

    /**
     * <AUTHOR>
     * @Des 通过id获取详情数据
     * @Date 2022/5/12 10:28:51
     * @Param
     * @Return
     */
    // 中控数据新增，接口调整
    @Override
    public WorkshopPitOrderPotTaskDetailsVO getWorkshopPitOrderPotTaskDetailsById(Integer id) {
        WorkshopPitOrderPotTaskDetailsVO workshopPitOrderPotTaskDetailsVO = new WorkshopPitOrderPotTaskDetailsVO();
        // 获取表格数据
        TPoWorkshopPitOrderPotTask task = workshopPitOrderPotTaskMapper.selectById(id);
        workshopPitOrderPotTaskDetailsVO.setWorkshopPitOrderPotTaskDetailsTableVO(DtoMapper.convert(task,
                WorkshopPitOrderPotTaskDetailsTableVO.class));

        // 获取润粮拌和数据
        workshopPitOrderPotTaskDetailsVO.setMgMix(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.MgMix.class));
        // 备料环节
        workshopPitOrderPotTaskDetailsVO.setPrepareMaterials(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.PrepareMaterials.class));
        // 润粮环节
        workshopPitOrderPotTaskDetailsVO.setMgMix(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.MgMix.class));
        // 投料环节
        workshopPitOrderPotTaskDetailsVO.setInput(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.Input.class));
        // 生产节奏
        workshopPitOrderPotTaskDetailsVO.setProductionRhythm(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.ProductionRhythm.class));
        // 蒸馏摘酒
        workshopPitOrderPotTaskDetailsVO.setDistillationPick(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.DistillationPick.class));
        // 量水出甑
        workshopPitOrderPotTaskDetailsVO.setLiangWaterPot(DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.LiangWaterPot.class));

        // 摊晾下曲
        final WorkshopPitOrderPotTaskDetailsVO.StandQu qu = DtoMapper.convert(task, WorkshopPitOrderPotTaskDetailsVO.StandQu.class);
        if (qu.getCollingFeedMachineCode() == null) {
            qu.setCollingFeedMachineCode(qu.getCollingMachineCode());
        }
        if (qu.getQuQuantity() == null) {
            qu.setQuQuantity(task.getQuChangedWeight());
        }
        workshopPitOrderPotTaskDetailsVO.setStandQu(qu);
        return workshopPitOrderPotTaskDetailsVO;
    }


    /**
     * <AUTHOR>
     * @Des 通过班次数据，获取当前班次甑口任务情况，当班生产进度
     * @Date 2022/5/17 9:16:28
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskScheduleVO findWorkshopPitOrderPotTaskSchedule(WorkshopPitOrderPotTaskScheduleDTO workshopPitOrderPotTaskScheduleDTO) {
        WorkshopPitOrderPotTaskScheduleVO workshopPitOrderPotTaskScheduleVO = new WorkshopPitOrderPotTaskScheduleVO();
        // 获取班次时间内完成甑口数
        workshopPitOrderPotTaskScheduleDTO.setOrderStatus(2);// 设置已完成状态
        Integer workshopPitOrderPotTaskScheduleFinish = workshopPitOrderPotTaskMapper.findWorkshopPitOrderPotTaskScheduleFinish(workshopPitOrderPotTaskScheduleDTO);
        workshopPitOrderPotTaskScheduleVO.setFinishedPots(workshopPitOrderPotTaskScheduleFinish);

        return workshopPitOrderPotTaskScheduleVO;
    }

    /**
     * <AUTHOR>
     * @Des 通过中心id和车间id，获取今天日计划的计划总甑口数
     * @Date 2022/5/19 13:30:27
     * @Param
     * @Return
     */
    @Override
    public Integer findDayPlanPotCount(Integer centerId, Integer locationId, String outTime) {
        return workshopPitOrderPotTaskMapper.findDayPlanPotCount(centerId, locationId, outTime);
    }

    /**
     * @Des 获取甑口任务批量同步数据
     * <AUTHOR>
     * @Date 2022/9/6 15:25:24
     * @Param
     * @Return
     */
    @Override
    public WorkshopPitOrderPotTaskSyncPageVO getPotTaskSyncData(WorkshopPitOrderPotTaskSyncDTO workshopPitOrderPotTaskSyncDTO) {
        WorkshopPitOrderPotTaskSyncPageVO workshopPitOrderPotTaskSyncPageVO = new WorkshopPitOrderPotTaskSyncPageVO();
        // 数据类型是否同步
        if (workshopPitOrderPotTaskSyncDTO.getIsSync()) {
            String location = null;
            // 为了匹配中控的controllerId，处理中心车间的字符
            if (workshopPitOrderPotTaskSyncDTO.getLocationId() != null) {
                Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskSyncDTO.getLocationId()).get(0);
                String code = map.get("code").toString();
                location = code.substring(0, code.length() - 2) + code.substring(code.length() - 1);
            } else if (workshopPitOrderPotTaskSyncDTO.getCenterId() != null) {
                Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskSyncDTO.getCenterId()).get(0);
                String code = map.get("code").toString();
                location = code;
            }
            workshopPitOrderPotTaskSyncDTO.setControllerId(location);
            Integer startPage = PageUtils.getStartPage(workshopPitOrderPotTaskSyncDTO.getPage(), workshopPitOrderPotTaskSyncDTO.getPageSize());
            workshopPitOrderPotTaskSyncDTO.setPage(startPage);

            List<WorkshopPitOrderPotTaskCentralDTO> potTaskSyncDataPage = workshopPitOrderPotTaskMapper.getPotTaskSyncDataPage(workshopPitOrderPotTaskSyncDTO);
            Integer potTaskSyncDataCount = workshopPitOrderPotTaskMapper.getPotTaskSyncDataCount(workshopPitOrderPotTaskSyncDTO);
            workshopPitOrderPotTaskSyncPageVO.setData(potTaskSyncDataPage);
            workshopPitOrderPotTaskSyncPageVO.setCount(potTaskSyncDataCount);
        } else {
            ZkBaseListDTO zkBaseListDTO = new ZkBaseListDTO();
            // 设置时间
            String time = DateUtil.dateFormat(workshopPitOrderPotTaskSyncDTO.getOutTime(), "yyyy-MM-dd");
            zkBaseListDTO.setStartTime(time + " 00:00:00");
            zkBaseListDTO.setEndTime(time + " 23:59:59.9999999");
            // 设置产线
            if (workshopPitOrderPotTaskSyncDTO.getLocationId() == null) {
                throw new BaseKnownException(10000, "请先选择车间");
            }
            String location = null;
            // 为了匹配中控的controllerId，处理中心车间的字符
            if (workshopPitOrderPotTaskSyncDTO.getLocationId() != null) {
                Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskSyncDTO.getLocationId()).get(0);
                String code = map.get("code").toString();
                location = code.substring(0, code.length() - 2) + code.substring(code.length() - 1);
            } else if (workshopPitOrderPotTaskSyncDTO.getCenterId() != null) {
                Map<String, Object> map = workshopPitOrderMapper.getLocationById(workshopPitOrderPotTaskSyncDTO.getCenterId()).get(0);
                String code = map.get("code").toString();
                location = code;
            }
            zkBaseListDTO.setLine(new ArrayList<Integer>(Collections.singletonList(Integer.valueOf(location))));
            ZkBaseResponseDTO zkBaseResponseDTO = requestZk.dockingAvs(zkBaseListDTO);
            List<ZkBaseResponseDataDTO> zkBaseResponseDataDTO = zkBaseResponseDTO.getData().getData();
            ArrayList<WorkshopPitOrderPotTaskCentralDTO> workshopPitOrderPotTaskCentralDTOS = new ArrayList<>();

            for (ZkBaseResponseDataDTO baseResponseDataDTO : zkBaseResponseDataDTO) {
                // 复制实体类
                final WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO = DtoMapper.convert(baseResponseDataDTO, WorkshopPitOrderPotTaskCentralDTO.class);
                List<TPoWorkshopPitOrderPotTask> tPoWorkshopPitOrderPotTasks = workshopPitOrderPotTaskMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                                .eq(TPoWorkshopPitOrderPotTask::getIsDeleted, false)
                                .eq(TPoWorkshopPitOrderPotTask::getPotSerialNumber, workshopPitOrderPotTaskCentralDTO.getId())
                                .eq(TPoWorkshopPitOrderPotTask::getControllerId, workshopPitOrderPotTaskCentralDTO.getControllerId())
//                        .eq(TPoWorkshopPitOrderPotTask::getPotNum, workshopPitOrderPotTaskCentralDTO.getPotNum())
                );
                if (tPoWorkshopPitOrderPotTasks.size() <= 0) {
                    String s = CentralVinasseEnum.FindVinasseName(workshopPitOrderPotTaskCentralDTO.getVinasseId());
                    workshopPitOrderPotTaskCentralDTO.setVinasse(s);
                    if (workshopPitOrderPotTaskCentralDTO.getOutDregsStartTime() != null) {
                        workshopPitOrderPotTaskCentralDTO.setOutTime(Date.from(workshopPitOrderPotTaskCentralDTO.getOutDregsStartTime().atZone(ZoneId.systemDefault()).toInstant()));
                    }
                    workshopPitOrderPotTaskCentralDTOS.add(workshopPitOrderPotTaskCentralDTO);
                }
            }
            workshopPitOrderPotTaskSyncPageVO.setData(workshopPitOrderPotTaskCentralDTOS);
            workshopPitOrderPotTaskSyncPageVO.setCount(workshopPitOrderPotTaskCentralDTOS.size());
        }

        return workshopPitOrderPotTaskSyncPageVO;
    }

    /**
     * @Des 甑口任务批量同步提交
     * <AUTHOR>
     * @Date 2022/9/7 10:01:54
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO PotTaskBatchSync(WorkshopPitOrderPotTaskSyncPutDTO dto) {

        // 先处理已同步的数据
        if (dto.getIsSync()) {
            List<WorkshopPitOrderPotTaskCentralDTO> datas = dto.getData();
            if (datas.size() <= 0)
                throw new BaseKnownException(10000, "请先选择需要同步的甑口任务");
            // 获取中控的数据
            Integer line = datas.get(0).getControllerId();
            ZkBaseListDTO zkBaseListDTO = new ZkBaseListDTO();
            zkBaseListDTO.setLine(new ArrayList<Integer>(Collections.singletonList(line)));
            zkBaseListDTO.setStartTime(DateUtil.dateFormat(dto.getOutTime(), "yyyy-MM-dd") + " 00:00:00");
            zkBaseListDTO.setEndTime(DateUtil.dateFormat(dto.getOutTime(), "yyyy-MM-dd") + " 23:59:59.9999999");
            ZkBaseResponseDTO zkBaseResponseDTO = requestZk.dockingAvs(zkBaseListDTO);
            List<ZkBaseResponseDataDTO> responseData = zkBaseResponseDTO.getData().getData();

            // 循环查找对应的甑口任务
            for (WorkshopPitOrderPotTaskCentralDTO data : datas) {
                // 查找请求回来的数据
                List<ZkBaseResponseDataDTO> collect = responseData.stream().filter(t ->
                        t.getControllerId().equals(data.getControllerId()) &&
                                t.getId().toString().equals(data.getPotSerialNumber()))
                        .collect(Collectors.toList());
                if (collect.size() != 1) {
                    throw new BaseKnownException(10000, "同步数据有误存在多条或0条数据 产线：" + data.getControllerId() + " 甑号：" + data.getPotNum() + "甑口任务号：" + data.getPotSerialNumber());
                } else {
                    WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO = new WorkshopPitOrderPotTaskCentralDTO();
                    BeanUtils.copyProperties(collect.get(0), workshopPitOrderPotTaskCentralDTO);
                    this.WorkshopPitOrderPotTaskCentral(workshopPitOrderPotTaskCentralDTO, null);
                }
            }
        } else {
            List<WorkshopPitOrderPotTaskCentralDTO> datas = dto.getData();
            if (datas.size() <= 0) {
                throw new BaseKnownException(10000, "请先选择需要同步的甑口任务");
            }
            for (WorkshopPitOrderPotTaskCentralDTO data : datas) {
                this.WorkshopPitOrderPotTaskCentral(data, null);
            }
        }
        return ResultVO.success("Success");
    }

    @Override
    @Transactional
    public ResultVO PotTaskSyncOne(Integer id) {
        TPoWorkshopPitOrderPotTask potTask = workshopPitOrderPotTaskMapper.selectById(id);
        if (potTask == null) {
            throw new BaseKnownException(10000, "不存在改甑口任务");
        }
        // 获取中控的数据
        ZkBaseListDTO zkBaseListDTO = new ZkBaseListDTO();
        zkBaseListDTO.setLine(new ArrayList<Integer>(Collections.singletonList(potTask.getControllerId())));
        zkBaseListDTO.setCauldronNumber(new ArrayList<String>(Collections.singletonList(potTask.getPotNum())));
        zkBaseListDTO.setStartTime(DateUtil.dateFormat(potTask.getOutTime(), "yyyy-MM-dd") + " 00:00:00");
        zkBaseListDTO.setEndTime(DateUtil.dateFormat(potTask.getOutTime(), "yyyy-MM-dd") + " 23:59:59");
        ZkBaseResponseDTO zkBaseResponseDTO = requestZk.dockingAvs(zkBaseListDTO);
        List<ZkBaseResponseDataDTO> responseData = zkBaseResponseDTO.getData().getData();
        List<ZkBaseResponseDataDTO> collect = responseData.stream().filter(t ->
                t.getControllerId().equals(potTask.getControllerId()) &&
                        t.getId().toString().equals(potTask.getPotSerialNumber())).collect(Collectors.toList());
        if (collect.size() != 1) {
            throw new BaseKnownException(10000, "同步数据有误存在多条或0条数据 产线：" + potTask.getControllerId() + " 甑号：" + potTask.getPotNum() + "甑口任务号：" + potTask.getPotSerialNumber());
        } else {
            WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO = new WorkshopPitOrderPotTaskCentralDTO();
            BeanUtils.copyProperties(collect.get(0), workshopPitOrderPotTaskCentralDTO);
            this.WorkshopPitOrderPotTaskCentral(workshopPitOrderPotTaskCentralDTO, null);
        }

        return ResultVO.success("Success");
    }

    @Override
    public List<HandTaskGetPitOrderPotTaskVO> getPotTaskByHandInTaskTime(HandInTaskQueryPotTaskDTO dto) {
        ArrayList<HandTaskGetPitOrderPotTaskVO> handTaskGetPitOrderPotTaskVOS = new ArrayList<>();
        // 获取车间
        LocationDTO locationById = locationExtendClient.getLocationById(dto.getLocationId()).getData();
        String location = locationById.getCode().substring(0, locationById.getCode().length() - 2) + locationById.getCode().substring(locationById.getCode().length() - 1);
        // 获取时间范围内的点位信息
        List<TPoOpctagValue> opcDataByHandIn = workshopPitOrderPotTaskMapper.getOpcDataByHandIn(dto);
        // 校验点位数据中是否都有1和0 其中一个不存在，则抛出错误
        List<String> tagValues = opcDataByHandIn.stream().map(TPoOpctagValue::getTagValue).collect(Collectors.toList());
        if (!(tagValues.contains("1") && tagValues.contains("0"))) {
            // 如果找不到点位信息，则把dto的开始时间结束时间作为时间段
            handTaskGetPitOrderPotTaskVOS.addAll(workshopPitOrderPotTaskMapper.getPotTaskByStageNumTime(DateUtil.convertToLocalTime(dto.getStartTime()),
                    DateUtil.convertToLocalTime(dto.getEndTime()),
                    dto.getStageNum(),
                    location,
                    dto.getVinasseId()));
        }
        // 组合时间段 1 0 组成一组时间，必须1在上
        ArrayList<List<LocalDateTime>> lists = new ArrayList<>();
        for (int i = 0; i < opcDataByHandIn.size(); i++) {
            TPoOpctagValue tPoOpctagValue = opcDataByHandIn.get(i);
            if ("0".equals(tPoOpctagValue.getTagValue()) && i != 0) {
                ArrayList<LocalDateTime> localDateTimes = new ArrayList<>();
                localDateTimes.add(opcDataByHandIn.get(i - 1).getReadTime());
                localDateTimes.add(tPoOpctagValue.getReadTime());
                lists.add(localDateTimes);
            }
        }
        if (lists.size() == 0) {
            // 如果找不到点位组不成时间段，则把dto的开始时间结束时间作为时间段
            if (handTaskGetPitOrderPotTaskVOS.size() == 0) {
                handTaskGetPitOrderPotTaskVOS.addAll(workshopPitOrderPotTaskMapper.getPotTaskByStageNumTime(DateUtil.convertToLocalTime(dto.getStartTime()),
                        DateUtil.convertToLocalTime(dto.getEndTime()),
                        dto.getStageNum(),
                        location,
                        dto.getVinasseId()));
            }
        }

        // 通过时间段找甑口
        for (List<LocalDateTime> list : lists) {
            List<HandTaskGetPitOrderPotTaskVO> potTaskByStageNumTime = workshopPitOrderPotTaskMapper.getPotTaskByStageNumTime(list.get(0),
                    list.get(1),
                    dto.getStageNum(),
                    location,
                    dto.getVinasseId());
            handTaskGetPitOrderPotTaskVOS.addAll(potTaskByStageNumTime);
        }

        return handTaskGetPitOrderPotTaskVOS;
    }

    /**
     * @Des 获取甑口任务的交酒数据
     * <AUTHOR>
     * @Date 2022/11/20 19:04:26
     * @Param
     * @Return
     */
    @Override
    public List<TPoWorkshopWinePotTask> getHandinData(Integer id) {
        List<TPoWorkshopWinePotTask> tPoWorkshopWinePotTasks = tPoWorkshopWinePotTaskMapper.getDataByPotTaskId(id);
        for (TPoWorkshopWinePotTask tPoWorkshopWinePotTask : tPoWorkshopWinePotTasks) {
            TPoWorkshopHandinTask tPoWorkshopHandinTask = tPoWorkshopHandinTaskMapper.selectById(tPoWorkshopWinePotTask.getHandinTaskId());
            tPoWorkshopWinePotTask.setModifyStandardVol(tPoWorkshopHandinTask.getModifyStandardVol());
        }
        return tPoWorkshopWinePotTasks;
    }

    /**
     * @Des 批量修改甑口任务
     * <AUTHOR>
     * @Date 2022/11/22 14:45:26
     * @Param
     * @Return
     */
    @Override
    @Transactional
    public ResultVO BatchUpdate(WorkshopPitOrderPotTaskBatchUpdateDTO dto) {
        if (dto.getGroundTemperature() == null && dto.getInPitTemperature() == null && dto.getQuQuantity() == null && dto.getBackAlcoholicQuantity() == null)
            throw new BaseKnownException(10000, "修改数据不能全为空");
        if (dto.getIds() == null || dto.getIds().size() <= 0)
            throw new BaseKnownException(10000, "未选择甑口任务");
        LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask> update = new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>();
        update.set(Objects.nonNull(dto.getQuQuantity()), TPoWorkshopPitOrderPotTask::getQuQuantity, dto.getQuQuantity())
                .set(Objects.nonNull(dto.getQuQuantity()), TPoWorkshopPitOrderPotTask::getQuChangedWeight, dto.getQuQuantity())
                .set(Objects.nonNull(dto.getInPitTemperature()), TPoWorkshopPitOrderPotTask::getInPitTemperature, dto.getInPitTemperature())
                .set(Objects.nonNull(dto.getGroundTemperature()), TPoWorkshopPitOrderPotTask::getGroundTemperature, dto.getGroundTemperature())
                .set(Objects.nonNull(dto.getBackAlcoholicQuantity()), TPoWorkshopPitOrderPotTask::getBackAlcoholicQuantityRevise, dto.getBackAlcoholicQuantity())
                .set(Objects.nonNull(dto.getBackAlcoholicQuantity()), TPoWorkshopPitOrderPotTask::getBackAlcoholicQuantity, dto.getBackAlcoholicQuantity())
                .in(TPoWorkshopPitOrderPotTask::getId, dto.getIds());

        workshopPitOrderPotTaskMapper.update(null, update);
        return ResultVO.success("Success");
    }

    /**
     * @Des 通过单窖订单号获取对应的连窖入窖甑口数
     * <AUTHOR>
     * @Date 2022/6/21 15:16:30
     * @Param
     * @Return
     */
    public void updateInPotNumBySapOrderCode(String sapOrderCode) {
        Integer orderIdBySap = workshopPitOrderPotTaskMapper.getOrderIdBySap(sapOrderCode);
        Integer potByOrderId1 = workshopPitOrderPotTaskMapper.getPotByOrderId(orderIdBySap);
        LambdaUpdateWrapper<TPoWorkshopPitOrder> tPoWorkshopPitOrderLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
        tPoWorkshopPitOrderLambdaUpdateWrapper2.eq(TPoWorkshopPitOrder::getId, orderIdBySap)
                .set(TPoWorkshopPitOrder::getInPitNum, potByOrderId1);
        workshopPitOrderMapper.update(null, tPoWorkshopPitOrderLambdaUpdateWrapper2);
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("中控推送");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }

    @Override
    public List<String> getPotTaskTemperatureByCenterIdAndPotSerialNumber(Integer centerId, String potSerialNumber,
                                                                          Integer type) {
        List<String> collect = new ArrayList<>();
        LambdaQueryWrapper<TPoWorkshopPitOrderPotTaskTemperature> temperatureLambdaQueryWrapper = new LambdaQueryWrapper<>();
        temperatureLambdaQueryWrapper
                .eq(TPoWorkshopPitOrderPotTaskTemperature::getIsDeleted, false)
                .eq(TPoWorkshopPitOrderPotTaskTemperature::getControllerId, centerId)
                .eq(TPoWorkshopPitOrderPotTaskTemperature::getPotSerialNumber, potSerialNumber);
        List<TPoWorkshopPitOrderPotTaskTemperature> tPoWorkshopPitOrderPotTaskTemperatures = workshopPitOrderPotTaskTemperatureMapper.selectList(temperatureLambdaQueryWrapper);
        if (!tPoWorkshopPitOrderPotTaskTemperatures.isEmpty()) {
            collect = tPoWorkshopPitOrderPotTaskTemperatures.stream()
                    .filter(o -> o.getValue() != null && o.getType().equals(type))
                    .map(map -> String.valueOf(map.getValue()))
                    .collect(Collectors.toList());

        }
        return collect;
    }

    /**
     * 甑口任务_接受IOT回传数据
     * @param potTaskSyncByIotDTO
     * @return
     */
    @Override
    @Transactional
    public String potTaskSyncByIot(PotTaskSyncByIotDTO potTaskSyncByIotDTO) {
        TaskPitBatchesUpdateDTO batchesUpdateDTO = new TaskPitBatchesUpdateDTO();
        //新增日志
        logService.addLogCapture(potTaskSyncByIotDTO, LogCaptureEnum.POT_TASK_IOT_TO_MES);
        TPoWorkshopPitOrderPotTask save = new TPoWorkshopPitOrderPotTask();
        if(!CollectionUtils.isEmpty(potTaskSyncByIotDTO.getSpreadingDetailList())){
            save.setIsCallback(true);
        }

        save.setInPitCode(Integer.valueOf(potTaskSyncByIotDTO.getInPitCode()));
        save.setOutPitCode(Integer.valueOf(potTaskSyncByIotDTO.getOutPitCode()));
        //根据IOT窖号去查询窖池订单信息--正在生产的订单只有一条 糟源+窖号
//        if (StringUtils.isNotBlank(potTaskSyncByIotDTO.getInPitCode())) {
//            TPoWorkshopPitOrder inPitOrder = workshopPitOrderMapper.selectOrderByIotPitCode(potTaskSyncByIotDTO.getInPitCode(), potTaskSyncByIotDTO.getInPitVinasse());
//            List<TPoWorkshopPitOrderSap> inWorkshopPitOrderSaps = workshopPitOrderSapMapper.selectList(new LambdaQueryWrapper<TPoWorkshopPitOrderSap>().eq(TPoWorkshopPitOrderSap::getOrderCodeId, inPitOrder.getId()));
//            save.setInOrderCode(inWorkshopPitOrderSaps.get(0).getOrderCode());
//            save.setInDoublepitCode(inPitOrder.getOrderCode());
//            batchesUpdateDTO.setInPitOrder(inPitOrder.getOrderCode());
//        }
        //根据IOT窖号去查询窖池订单信息--正在生产的订单只有一条 糟源+窖号
        TPoWorkshopPitOrder outPitOrder = workshopPitOrderMapper.selectOrderByIotPitCode(potTaskSyncByIotDTO.getOutPitCode(), potTaskSyncByIotDTO.getOutPitVinasse());
        List<TPoWorkshopPitOrderSap> outWorkshopPitOrderSaps = workshopPitOrderSapMapper.selectList(new LambdaQueryWrapper<TPoWorkshopPitOrderSap>().eq(TPoWorkshopPitOrderSap::getOrderCodeId, outPitOrder.getId()));
        save.setInOrderCode(outWorkshopPitOrderSaps.get(0).getOrderCode());
        save.setOutDoublepitCode(outPitOrder.getOrderCode());
        batchesUpdateDTO.setOutPitOrder(outPitOrder.getOrderCode());
        save.setVinasse(potTaskSyncByIotDTO.getOutPitVinasse());
        save.setPottaskEndtime(potTaskSyncByIotDTO.getEndTime());
        save.setPotSerialNumber(potTaskSyncByIotDTO.getPotSerialNumber());
        save.setPottaskStarttime(potTaskSyncByIotDTO.getStartTime());
        Map<String, Object> map = workshopPitOrderMapper.getLocationById(outPitOrder.getLocationId()).get(0);
        String code = map.get("code").toString();
        String locationId = code.substring(0, code.length() - 2) + code.substring(code.length() - 1);
        save.setControllerId(Integer.parseInt(locationId));
        save.setCreateTime(new Date());
        save.setIotTaskNo(potTaskSyncByIotDTO.getIotTaskNo());
        save.setIsDeleted(false);
        //解析出甑时间
        List<TaskSteamingDetailAddDTO> steamingDetailList = potTaskSyncByIotDTO.getSteamingDetailList();
        if (!CollectionUtils.isEmpty(steamingDetailList)) {
            save.setOutPotTime(steamingDetailList.get(0).getRecordTime());
        }
        //解析出糟时间
        List<TaskPileUpDetailAddDTO> pileUpDetailList = potTaskSyncByIotDTO.getPileUpDetailList();
        if (!CollectionUtils.isEmpty(pileUpDetailList)) {
            if (StringUtils.isNotBlank(pileUpDetailList.get(0).getEndTime())) {
                save.setOutTime(DateUtil.parse(pileUpDetailList.get(0).getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                save.setOutDregsEndTime(save.getOutTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if (StringUtils.isNotBlank(pileUpDetailList.get(0).getStartTime())) {
                save.setPottaskStarttime(DateUtil.parse(pileUpDetailList.get(0).getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                save.setOutStartTime(save.getPottaskStarttime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
                save.setOutDregsStartTime(save.getOutStartTime());
            }
        }
        // 获取考勤的数据id，用于记录班组人数
//        Integer shiftId = workshopPitOrderPotTaskMapper.getNowChangeShiftLogAttendanceId(potTaskSyncByIotDTO.getStartTime(), potTaskSyncByIotDTO.getOutPitCode() == null ? null : potTaskSyncByIotDTO.getOutPitCode());
//        save.setChangeShiftLogId(shiftId);
        //甑口用量 -- 用于扎帐的数据
        putMaterialUse(potTaskSyncByIotDTO, save);
        // 先查看当前数据中有没有相同的数据
        LambdaQueryWrapper<TPoWorkshopPitOrderPotTask> tPoWorkshopPitOrderPotTaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tPoWorkshopPitOrderPotTaskLambdaQueryWrapper
                .eq(TPoWorkshopPitOrderPotTask::getIsDeleted, false)
                .eq(TPoWorkshopPitOrderPotTask::getIotTaskNo, potTaskSyncByIotDTO.getIotTaskNo())
                .orderByDesc(TPoWorkshopPitOrderPotTask::getId).last("limit 1");
        TPoWorkshopPitOrderPotTask query = workshopPitOrderPotTaskMapper.selectOne(tPoWorkshopPitOrderPotTaskLambdaQueryWrapper);
        if (query == null) {
            if (workshopPitOrderPotTaskMapper.insert(save) < 1) {
                throw new BaseKnownException(10000, "IOT添加数据失败,新增条数为0");
            }
        } else {
            save.setId(query.getId());
            save.setUpdateTime(new Date());
            if (workshopPitOrderPotTaskMapper.updateById(save) < 1) {
                throw new BaseKnownException(10000, "IOT更新数据失败,作用条数为0");
            }
        }
        //更新物料数据，设置甑口任务id
        taskOrderBatchMapper.update(null, new LambdaUpdateWrapper<TaskOrderBatch>()
                .eq(TaskOrderBatch::getTaskNo, potTaskSyncByIotDTO.getIotTaskNo())
                .eq(TaskOrderBatch::getDeleted, 0)
                .set(TaskOrderBatch::getTaskId, save.getId()));
        //甑口平衡
//        List<String> iotTaskNo = new ArrayList<>();
//        iotTaskNo.add(potTaskSyncByIotDTO.getIotTaskNo());
//        batchesUpdateDTO.setIotTaskNo(iotTaskNo);
//        updatePitNo(batchesUpdateDTO);
        //更新任务工序步奏数据
        taskGrainLubricationDetailService.addList(potTaskSyncByIotDTO.getGrainLubricationDetailList());
        taskDishMixingDetailService.addList(potTaskSyncByIotDTO.getDishMixingDetailList());
        taskShangzhengDetailService.addList(potTaskSyncByIotDTO.getShangzhengDetailList());
        taskSistillationDetailService.addList(potTaskSyncByIotDTO.getSistillationDetailList());
        taskSpreadingDetailService.addList(potTaskSyncByIotDTO.getSpreadingDetailList());
        taskWinePickingDetailService.addList(potTaskSyncByIotDTO.getWinePickingDetailList());
        taskPileUpDetailService.addList(potTaskSyncByIotDTO.getPileUpDetailList());
        taskSteamingDetailService.addList(potTaskSyncByIotDTO.getSteamingDetailList());
        return "Success";
    }

    /**
     * 修改甑口的订单信息
     * @param updateDTO
     */
    @Override
    public void updatePitNo(TaskPitBatchesUpdateDTO updateDTO) {
        PitAssociatedOrderDTO pitAssociatedOrderDTO = new PitAssociatedOrderDTO();
        List<Integer> ids = workshopPitOrderPotTaskMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitOrderPotTask>()
                        .in(TPoWorkshopPitOrderPotTask::getIotTaskNo, updateDTO.getIotTaskNo())
                        .eq(TPoWorkshopPitOrderPotTask::getIsDeleted, false))
                .stream().map(TPoWorkshopPitOrderPotTask::getId).collect(Collectors.toList());
        pitAssociatedOrderDTO.setIds(ids);
        pitAssociatedOrderDTO.setIsPotCountUpdate(true);
        if (StringUtils.isNotBlank(updateDTO.getInPitOrder())) {
            List<InPitAssociatedOrderDTO> inPitData = new ArrayList<>();
            InPitAssociatedOrderDTO inPitAssociatedOrderDTO = workshopPitOrderMapper.selectPotTaskAdjustInDataByOrder(updateDTO.getInPitOrder());
            inPitData.add(inPitAssociatedOrderDTO);
            pitAssociatedOrderDTO.setInPitData(inPitData);
            //入窖窖池有数据的情况需要将数据的物料信息同步更新到指定订单数据
            taskOrderBatchMapper.update(null, new LambdaUpdateWrapper<TaskOrderBatch>()
                    .in(TaskOrderBatch::getTaskNo, updateDTO.getIotTaskNo()).eq(TaskOrderBatch::getDeleted, false)
                    .set(TaskOrderBatch::getPitOrder, inPitAssociatedOrderDTO.getInOrderCode()));
        }

        if (StringUtils.isNotBlank(updateDTO.getOutPitOrder())) {
            List<OutPitAssociatedOrderDTO> outPitData = new ArrayList<>();
            OutPitAssociatedOrderDTO inPitAssociatedOrderDTO = workshopPitOrderMapper.selectPotTaskAdjustOutDataByOrder(updateDTO.getOutPitOrder());
            outPitData.add(inPitAssociatedOrderDTO);
            pitAssociatedOrderDTO.setOutPitData(outPitData);
        }
        log.info("甑口任务重新关联，更改甑口关联状态和订单甑口，参数:" + JSONObject.toJSONString(pitAssociatedOrderDTO));
        workshopPitOrderPotAssociatedService.PitAssociatedOrder(pitAssociatedOrderDTO);
    }

    /**
     * 甑口物料使用情况
     * @param potTaskSyncByIotDTO
     * @param add
     */
    private static void putMaterialUse(PotTaskSyncByIotDTO potTaskSyncByIotDTO, TPoWorkshopPitOrderPotTask add) {
        //高粱数据
        if(!CollectionUtils.isEmpty(potTaskSyncByIotDTO.getGrainLubricationDetailList())){
            List<TaskGrainLubricationDetailAddDTO> grainLubricationDetailList = potTaskSyncByIotDTO.getGrainLubricationDetailList();
            double doubleValue = Double.parseDouble(grainLubricationDetailList.get(0).getSingleContainerSorghumActualValue());
            add.setCrushedGrainsQuantityBack(BigDecimal.valueOf(doubleValue));
        }
        //稻壳数据
        if(!CollectionUtils.isEmpty(potTaskSyncByIotDTO.getDishMixingDetailList())){
            List<TaskDishMixingDetailAddDTO> dishMixingDetailList = potTaskSyncByIotDTO.getDishMixingDetailList();
            double doubleValue = Double.parseDouble(dishMixingDetailList.get(0).getSingleContainerRiceHuskActualValue());
            add.setRicehullQuantityBack(BigDecimal.valueOf(doubleValue));
        }
        //回酒数据
        if(!CollectionUtils.isEmpty(potTaskSyncByIotDTO.getSistillationDetailList())){
            List<TaskSistillationDetailAddDTO> dishMixingDetailList = potTaskSyncByIotDTO.getSistillationDetailList();
            double doubleValue = Double.parseDouble(dishMixingDetailList.get(0).getHj1SingleContainerUsage());
            add.setBackAlcoholicQuantityRevise(BigDecimal.valueOf(doubleValue));
        }
        //大曲数据
        if(!CollectionUtils.isEmpty(potTaskSyncByIotDTO.getSpreadingDetailList())){
            List<TaskSpreadingDetailAddDTO> dishMixingDetailList = potTaskSyncByIotDTO.getSpreadingDetailList();
            double doubleValue = Double.parseDouble(dishMixingDetailList.get(0).getYeastAmount());
            add.setQuQuantity(BigDecimal.valueOf(doubleValue));
        }
    }


    @Test
    public void test() {
        HandInTaskQueryPotTaskDTO handInTaskQueryPotTaskDTO = new HandInTaskQueryPotTaskDTO();
        handInTaskQueryPotTaskDTO.setStageNum("3");
        handInTaskQueryPotTaskDTO.setInputValveTagId("HYJK-13_HV01B11_OPENOK");
        handInTaskQueryPotTaskDTO.setVinasseId(10);
        handInTaskQueryPotTaskDTO.setLocationId(11);
        handInTaskQueryPotTaskDTO.setEndTime(DateUtil.parse("2022-11-23 09:46:00", DateUtils.DATE_FORMAT_19));
        handInTaskQueryPotTaskDTO.setStartTime(DateUtil.parse("2022-11-21 10:42:01", DateUtils.DATE_FORMAT_19));
        List<HandTaskGetPitOrderPotTaskVO> potTaskByHandInTaskTime = getPotTaskByHandInTaskTime(handInTaskQueryPotTaskDTO);
        System.out.println("");
    }
}
