package com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @program: brewage
 * @description: 酿酒生产日计划DTO
 * @author: DengWeiTao
 **/
@Data
@ApiModel(value = "酿酒生产日计划DTO")
public class WorkshopPitDayPlanDTO {
    @ApiModelProperty(value = "id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "日计划编号ID", hidden = true)
    private String dayPlayId;

    @ApiModelProperty(value = "窖池ID", example = "1")
    private Integer pitId;

    @ApiModelProperty(value = "中心ID", example = "1")
    private Integer workshopCenterId;

    @ApiModelProperty(value = "车间ID", example = "1")
    private Integer workshopId;

    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8") //返回时间类型
    @DateTimeFormat(pattern="yyyy-MM-dd") //接收时间类型
    @ApiModelProperty(value = "日期", example = "2022-04-27")
    private Date planDate;

    @ApiModelProperty(value = "计划总甑口", example = "20")
    private Integer potCount;

    @ApiModelProperty(value = "计划CJ甑口", example = "10")
    private Integer potCjCount;

    @ApiModelProperty(value = "计划CZ甑口", example = "5")
    private Integer potCzCount;

    @ApiModelProperty(value = "计划ZL甑口", example = "5")
    private Integer potZlCount;

    @ApiModelProperty(value = "计划状态", example = "1")
    private Integer planStatus;

    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    @ApiModelProperty(value = "顺序", example = "1")
    private Integer sortNo;

    @ApiModelProperty(value = "窖池订单ID", example = "1")
    private Integer orderId;

    @ApiModelProperty(value = "糟源类别", example = "1")
    private Integer categoryId;

    @ApiModelProperty(value = "预计出窖甑口（根据涨幅计算获得）", example = "23")
    private Integer calcOutPotCount;

    @ApiModelProperty(value = "日计划中的计划出窖甑口（默认等于预计出窖甑口，可以手工修改）", example = "20")
    private Integer planOutPotCount;

    @ApiModelProperty(value = "已出窖甑口", example = "10")
    private Integer outPotCount;

    @ApiModelProperty(value = "计划剩余甑口（计划出窖 - 已出窖）", example = "10")
    private Integer balancePotCount;

    @ApiModelProperty(value = "是否开窖", example = "true")
    private Boolean isOpened;

    @ApiModelProperty(value = "制定人", example = "1")
    private Integer creator;

    @ApiModelProperty(value = "基础糟源比例", example = "1")
    private Integer baseVinasseId;

    @ApiModelProperty(value = "订单剩余数量--如果没有排完则剩下的数量", example = "1")
    private Integer orderRemainderCount;

    @TableField("vinasse_code")
    @ApiModelProperty(value = "糟源类型")
    private String vinasseCode;

    @TableField("schedule_type")
    @ApiModelProperty(value = "排产类型 1.开工排产；2.连续排产；3.停工前排产")
    private String scheduleType;
}
