package com.hvisions.brewage.mkwine.dao.ProductionProcess;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.bw.dto.ForwardBatchQueryReq;
import com.hvisions.brewage.bw.dto.HandinTaskByBatchQueryReq;
import com.hvisions.brewage.bw.dto.BatchNodeQueryReq;
import com.hvisions.brewage.bw.dto.PotTaskByBatchQueryReq;
import com.hvisions.brewage.bw.vo.*;
import com.hvisions.brewage.dto.mkwine.dto.PutGrainDTO;
import com.hvisions.brewage.mkwine.dto.LiquorConnectManage.HandInTaskQueryPotTaskDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.*;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotTask.*;
import com.hvisions.brewage.mkwine.dto.kanban.location.LocationKanBanDTO;
import com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoOpctagValue;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.vo.LiquorConnectManage.HandTaskGetPitOrderPotTaskVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.*;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.*;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.OrderPotTaskVO;
import com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.ChoosePitOrderVO;
import com.hvisions.brewage.mkwine.vo.productiondisposition.ProductionPlanningBoard.TodayOutPotDataVO;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: 甑口任务管理接口Mapper
 * @projectName brewage
 * @description:
 * @date 2022/5/5 11:16:29
 */
@Mapper
@Repository
public interface WorkshopPitOrderPotTaskMapper extends BaseMapper<TPoWorkshopPitOrderPotTask> {

    /**
     * 批量修改多个物料
     * @param list
     */
    public void updateBatchForMaterials(@Param("list") List<TPoWorkshopPitOrderPotTask> list);

    /**
     * <AUTHOR>
     * @Des 获取所有甑口任务管理
     * @Date 2022/5/5 11:17:49
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderPotTaskVO> getAllWorkshopPitOrderPotTask();

    /**
     * <AUTHOR>
     * @Des 新增甑口任务
     * @Date 2022/5/5 16:29:22
     * @Param
     * @Return
     */
    public Integer addWorkshopPitOrderPotTask(WorkshopPitOrderPotTaskAddDTO workshopPitOrderPotTaskAddDTO);

    /**
     * <AUTHOR>
     * @Des 甑口任务查询
     * @Date 2022/5/6 17:57:46
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderPotTaskQueryVO> workshopPitOrderPotTaskQueryPage(WorkshopPitOrderPotTaskQueryDTO workshopPitOrderPotTaskQueryDTO);

    /**
     * <AUTHOR>
     * @Des 获取查询条数
     * @Date 2022/5/9 9:16:47
     * @Param
     * @Return
     */
    public Integer getCount(WorkshopPitOrderPotTaskQueryDTO workshopPitOrderPotTaskQueryDTO);

    /**
     * <AUTHOR>
     * @Des 甑口任务_中控添加接口
     * @Date 2022/5/9 13:34:38
     * @Param
     * @Return
     */
    public Integer addWorkshopPitOrderPotTaskCentral(WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO);

    /**
     * <AUTHOR>
     * @Des 甑口任务_中控修改接口
     * @Date 2022/5/16 9:08:00
     * @Param
     * @Return
     */
    public Integer updateWorkshopPitOrderPotTaskCentral(WorkshopPitOrderPotTaskCentralDTO workshopPitOrderPotTaskCentralDTO);

    /**
     * @Des 通过段次时间过去最近的阀门流入时间
     * <AUTHOR>
     * @Date 2022/11/9 16:16:43
     * @Param
     * @Return
     */
    public String getOpcValueByStageTime(@Param("stageTime") Date stageTime, @Param("stageNum") Integer stageNum, @Param("locationId") Integer locationId, @Param("vinasse_id") Integer vinasse_id);

    /**
     * <AUTHOR>
     * @Des 甑口任务摘酒记录提交
     * @Date 2022/5/9 17:55:56
     * @Param
     * @Return
     */
    public Boolean workshopPitOrderPotTaskPickWine(WorkshopPitOrderPotTaskPickWineDTO workshopPitOrderPotTaskPickWineDTO);

    /**
     * <AUTHOR>
     * @Des 摊晾记录提交
     * @Date 2022/5/10 14:20:23
     * @Param
     * @Return
     */
    public Boolean workshopPitOrderPotTaskColling(WorkshopPitOrderPotTaskCollingDTO workshopPitOrderPotTaskCollingDTO);

    /**
     * <AUTHOR>
     * @Des 获取摘酒记录数据
     * @Date 2022/5/10 14:45:29
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskPickWineVO getWorkshopPitOrderPotTaskPickWine(@Param("id") Integer id);

    /**
     * <AUTHOR>
     * @Des 获取摊晾记录数据
     * @Date 2022/5/10 15:06:52
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskCollingVO getWorkshopPitOrderPotTaskColling(@Param("id") Integer id);

    /**
     * <AUTHOR>
     * @Des 获取修改的数据
     * @Date 2022/5/10 16:48:26
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskUpdateVO getWorkshopPitOrderPotTaskUpdate(Integer id);

    /**
     * <AUTHOR>
     * @Des 甑口任务修改提交
     * @Date 2022/5/10 18:02:41
     * @Param
     * @Return
     */
    public Boolean workshopPitOrderPotTaskUpdate(WorkshopPitOrderPotTaskUpdateDTO workshopPitOrderPotTaskUpdateDTO);

    /**
     * <AUTHOR>
     * @Des 删除甑口任务
     * @Date 2022/5/11 10:25:51
     * @Param
     * @Return
     */
    public Boolean deleteWorkshopPitOrderPotTask(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取润粮拌和数据
     * @Date 2022/5/12 10:23:28
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskMgMxVO getWorkshopPitOrderPotTaskMgMx(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取上甑过程数据
     * @Date 2022/5/12 11:30:43
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskBeforeLoadingVO getWorkshopPitOrderPotTaskBeforeLoading(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取底锅投料数据
     * @Date 2022/5/12 15:26:19
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskBottomPotVO getWorkshopPitOrderPotTaskBottomPot(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取蒸馏数据
     * @Date 2022/5/12 17:29:27
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskDistillateVO getWorkshopPitOrderPotTaskDistillate(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取打量水出甑数据
     * @Date 2022/5/12 18:18:35
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskWaterProportioningVO getWorkshopPitOrderPotTaskWaterProportioning(Integer id);

    /**
     * <AUTHOR>
     * @Des 通过id获取甑口任务详情表格
     * @Date 2022/5/13 16:42:16
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskDetailsTableVO getWorkshopPitOrderPotTaskDetailsTable(Integer id);

    /**
     * <AUTHOR>
     * @Des 获取当前班次时间已完成的甑口任务数
     * @Date 2022/5/17 13:56:48
     * @Param
     * @Return
     */
    public Integer findWorkshopPitOrderPotTaskScheduleFinish(WorkshopPitOrderPotTaskScheduleDTO workshopPitOrderPotTaskScheduleDTO);

    /**
     * <AUTHOR>
     * @Des 通过班次的时间段、连窖名称、车间id和中心id，获取生产的起窖池连窖号
     * @Date 2022/5/19 9:24:05
     * @Param
     * @Return
     */
    public List<Map<String, Object>> findProductionOutPitByShift(WorkshopPitOrderPotAssociatedFindOutPitDTO workshopPitOrderPotAssociatedFindOutPitDTO);

    /**
     * <AUTHOR>
     * @Des 通过中心id和车间id，获取今天日计划的计划总甑口数
     * @Date 2022/5/19 13:30:29
     * @Param
     * @Return
     */
    public Integer findDayPlanPotCount(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId, @Param("outTime") String outTime);

    /**
     * <AUTHOR>
     * @Des 通过班次的时间段、连窖名称、车间id和中心id，获取生产的入窖池连窖号
     * @Date 2022/5/19 15:16:54
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedFindInPitVO> findProductionInPitByShift(WorkshopPitOrderPotAssociatedFindInPitDTO workshopPitOrderPotAssociatedFindInPitDTO);

    /**
     * <AUTHOR>
     * @Des 关联确认_甑口数据列表
     * @Date 2022/5/19 18:32:24
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedPotDataVO> WorkshopPitOrderPotAssociatedPotDataList(WorkshopPitOrderPotAssociatedPotDataDTO workshopPitOrderPotAssociatedPotDataDTO);

    /**
     * @Des 获取甑口窖号关联确认所有表格数据（用于左方表格点击事件
     * <AUTHOR>
     * @Date 2022/7/14 10:21:21
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedPotDataVO> WorkshopPitOrderPotAssociatedPotDataListByClick(WorkshopPitOrderPotAssociatedPotDataListDTO workshopPitOrderPotAssociatedPotDataListDTO);

    /**
     * <AUTHOR>
     * @Des 关联确认_入窖列表VO
     * @Date 2022/5/20 11:41:24
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedInPitDataVO> WorkshopPitOrderPotAssociatedInPitDataList(@Param("workshopPitOrderPotAssociatedPotDataVOList") List<WorkshopPitOrderPotAssociatedPotDataVO> workshopPitOrderPotAssociatedPotDataVOList, @Param("outTime") Date outTime);

    /**
     * <AUTHOR>
     * @Des 关联确认_开窖列表VO
     * @Date 2022/5/20 15:03:08
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedOutPitDataVO> WorkshopPitOrderPotAssociatedOutPitDataList(@Param("workshopPitOrderPotAssociatedPotDataVOList") List<WorkshopPitOrderPotAssociatedPotDataVO> workshopPitOrderPotAssociatedPotDataVOList, @Param("outTime") Date outTime);

    /**
     * @Des 获取批量窖号修正表格数据，通过车间id或者中心id获取今天的数据
     * <AUTHOR>
     * @Date 2022/5/24 14:18:44
     * @Param
     * @Return
     */
    List<WorkshopPitOrderPotAssociatedBatchPitReviseVO> getWorkshopPitOrderPotAssociatedBatchPitRevise(@Param("ids") List<Integer> ids);

    /**
     * @Des 批量窖号修正提交接口
     * <AUTHOR>
     * @Date 2022/5/24 14:54:25
     * @Param
     * @Return
     */
    public Integer batchPitReviseForIds(WorkshopPitOrderPotAssociatedBatchPitReviseDTO workshopPitOrderPotAssociatedFindInPitDTO);

    /**
     * @Des 核对完成提交接口
     * <AUTHOR>
     * @Date 2022/5/24 18:16:07
     * @Param
     * @Return
     */
    public Integer checkCompleted(WorkshopPitOrderPotAssociatedCheckDTO workshopPitOrderPotAssociatedFindInPitDTO);

    /**
     * @Des 获取生产起窖号数据
     * <AUTHOR>
     * @Date 2022/5/25 17:49:56
     * @Param
     * @Return
     */
    public List<OutPitAssociatedOrder> getOutPitAssociatedOrder(@Param("ids") List<Integer> ids);

    /**
     * @Des 获取生产入窖号数据
     * <AUTHOR>
     * @Date 2022/5/25 18:11:49
     * @Param
     * @Return
     */
    public List<InPitAssociatedOrder> getInPitAssociatedOrder(@Param("ids") List<Integer> ids);

    /**
     * @Des 起窖号订单关联提交接口
     * <AUTHOR>
     * @Date 2022/5/26 10:26:12
     * @Param
     * @Return
     */
    public Integer outPitAssociatedOrder(@Param("outPitAssociatedOrderDTO") OutPitAssociatedOrderDTO outPitAssociatedOrderDTO, @Param("ids") List<Integer> ids);

    /**
     * @Des 入窖号订单关联提交接口
     * <AUTHOR>
     * @Date 2022/5/26 13:36:15
     * @Param
     * @Return
     */
    public Integer inPitAssociatedOrder(@Param("inPitAssociatedOrder") InPitAssociatedOrderDTO inPitAssociatedOrder, @Param("ids") List<Integer> ids);

    /**
     * @Des 通过单窖号查找对应的在制订单，（用于窖号关联确认）
     * <AUTHOR>
     * @Date 2022/5/26 16:22:24
     * @Param
     * @Return
     */
    public List<String> findCurrentOrderByPitName(@Param("locationId") Integer locationId, @Param("pitCode") String pitCode);

    /**
     * @Des 通过车间id或中心id，获取单窖名称
     * <AUTHOR>
     * @Date 2022/5/27 15:20:09
     * @Param
     * @Return
     */
    public List<String> findPitByLocation(@Param("locationId") String locationId, @Param("pidName") String pidName);

    /**
     * @Des 用于获取旧的窖池订单id
     * <AUTHOR>
     * @Date 2022/6/6 13:39:13
     * @Param
     * @Return
     */
    public Integer getInOrderCodeByOld(@Param("inPitAssociatedOrder") InPitAssociatedOrderDTO inPitAssociatedOrder, @Param("ids") List<Integer> ids);

    /**
     * @Des 用于获取旧的Sap窖池订单数据
     * <AUTHOR>
     * @Date 2022/6/21 9:04:53
     * @Param
     * @Return
     */
    public TPoWorkshopPitOrderSap getInOrderCodeSapByOld(@Param("inPitAssociatedOrder") InPitAssociatedOrderDTO inPitAssociatedOrder, @Param("ids") List<Integer> ids);

    /**
     * @Des 获取窖池订单当前的入窖甑口数
     * <AUTHOR>
     * @Date 2022/6/14 9:25:00
     * @Param
     * @Return
     */
    public Integer getPotByOrderId(@Param("orderCodeId") Integer orderCodeId);

    /**
     * @Des 通过单窖订单id获取入窖甑口数
     * <AUTHOR>
     * @Date 2022/7/16 21:44:43
     * @Param
     * @Return
     */
    public Integer getPotBySapOrderId(@Param("sapOrderCodeId") Integer sapOrderCodeId);

    /**
     * @Des 通过单窖订单获取连窖池订单id
     * <AUTHOR>
     * @Date 2022/6/14 13:29:07
     * @Param
     * @Return
     */
    public Integer getOrderIdBySap(@Param("orderCode") String orderCode);

    /**
     * @Des 通过单窖订单编号获取大曲的总重量
     * <AUTHOR>
     * @Date 2022/6/20 18:06:47
     * @Param
     * @Return
     */
    public float getSumDaquByOrderCode(@Param("orderCode") String orderCode);

    /**
     * @Des 通过单窖订单编号获取高粱的总重量
     * <AUTHOR>
     * @Date 2022/6/20 18:16:53
     * @Param
     * @Return
     */
    public float getSumSorghumByOrderCode(@Param("orderCode") String orderCode);

    /**
     * @Des 通过单窖订单编号获取稻壳的总重量
     * <AUTHOR>
     * @Date 2022/6/20 18:21:07
     * @Param
     * @Return
     */
    public float getSumRiceHuskByOrderCode(@Param("orderCode") String orderCode);

    /**
     * @Des 通过单窖订单编号获取回酒的总重量
     * <AUTHOR>
     * @Date 2022/6/20 18:24:37
     * @Param
     * @Return
     */
    public float getSumBackAlcoholicByOrderCode(@Param("orderCode") String orderCode);

    /**
     * @Des 通过起窖号获取当前交接班任务id
     * <AUTHOR>
     * @Date 2022/6/24 11:34:49
     * @Param
     * @Return
     */
    public Integer getNowChangeShiftLogAttendanceId(@Param("outTime") Date outTime, @Param("outPitCode") String outPitCode);

    /**
     * @Des 通过中心车间id 修改甑口任务的考勤id
     * <AUTHOR>
     * @Date 2022/7/12 22:53:35
     * @Param 中心id，车间id，修改的时间（最后会转为日期），考勤id
     * @Return
     */
    public Integer updatePotTaskChangeShiftId(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId, @Param("dateTime") Date dateTime, @Param("changeShiftLogId") Integer changeShiftLogId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * @Des 通过单窖订单号，获取当前连窖订单的平均温度
     * <AUTHOR>
     * @Date 2022/8/3 0:30:42
     * @Param
     * @Return
     */
    public WorkshopPitOrderPotTaskAvgTemperatureVO getAvgTemperatureBySapOrderCode(@Param("sapOrderCode") String sapOrderCode);

    /**
     * @Des 分页获取甑口任务已同步数据
     * <AUTHOR>
     * @Date 2022/9/6 15:29:07
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderPotTaskCentralDTO> getPotTaskSyncDataPage(WorkshopPitOrderPotTaskSyncDTO workshopPitOrderPotTaskSyncDTO);

    /**
     * @Des 分页获取甑口任务已同步数据条数
     * <AUTHOR>
     * @Date 2022/9/6 15:58:45
     * @Param
     * @Return
     */
    public Integer getPotTaskSyncDataCount(WorkshopPitOrderPotTaskSyncDTO workshopPitOrderPotTaskSyncDTO);

    /**
     * 通过入窖任务号获取甑口数
     *
     * @param orderCodeList
     * @return
     */
    List<ChoosePitOrderVO> getInPutCountByOrderCode(@Param("list") List<String> orderCodeList);

    /**
     * @Des 获取批量平衡关联甑口数据
     * <AUTHOR>
     * @Date 2022/11/4 17:35:02
     * @Param
     * @Return
     */
    List<PotCountUpdateQueryVO> getPotCountUpdateQuery(PotCountUpdateQueryDTO potCountUpdateQueryDTO);

    /**
     * @Des 获取批量平衡关联甑口数据的条数
     * <AUTHOR>
     * @Date 2022/11/6 16:02:51
     * @Param
     * @Return
     */
    Integer getPotCountUpdateQueryCount(PotCountUpdateQueryDTO potCountUpdateQueryDTO);

    /**
     * @Des 通过交酒的点位和时间，获取对应的点位信息
     * <AUTHOR>
     * @Date 2022/11/17 15:57:55
     * @Param
     * @Return
     */
    List<TPoOpctagValue> getOpcDataByHandIn(HandInTaskQueryPotTaskDTO handInTaskQueryPotTaskDTO);

    /**
     * @Des 通过段次的时间段获取对应的甑口任务
     * <AUTHOR>
     * @Date 2022/11/17 18:02:03
     * @Param
     * @Return
     */
    List<HandTaskGetPitOrderPotTaskVO> getPotTaskByStageNumTime(@Param("startTime") LocalDateTime startTime,
                                                                @Param("endTime") LocalDateTime endTime,
                                                                @Param("stageNum") String stageNum,
                                                                @Param("location") String location,
                                                                @Param("vinasseId") Integer vinasseId);

    /**
     * @Description 获取3个中心的指定日期的损耗量
     *
     * <AUTHOR>
     * @Date 2024-1-9 15:54
     * @param materialType 物料类型名称（高粱）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return java.util.List<com.hvisions.brewage.dto.mkwine.productionPlanningBoard.putGrain.PutGrainDTO>
     **/
    List<PutGrainDTO> getMaterialTypeByData(@Param("materialType") String materialType, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /**
     * @Description 获取3个车间的指定日期的投粮数据
     *
     * <AUTHOR>
     * @Date 2024-1-9 13:56
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return java.util.List<com.hvisions.brewage.dto.mkwine.productionPlanningBoard.putGrain.PutGrainDTO>
     **/
    List<PutGrainDTO> getPutGrainQuantity(@Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /**
     * @Des 获取今日开窖信息
     * <AUTHOR>
     * @Date 2022/12/7 16:08:45
     * @Param
     * @Return
     */
    List<TodayOutPotDataVO> getTodayOutPotData();

    /**
     * @Des 时间范围内的高粱消耗粮
     * <AUTHOR>
     * @Date 2022/12/7 18:04:17
     * @Param
     * @Return
     */
    Float getSorghumConsumeByDate(@Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime,
                                  @Param("materialName") String materialName,
                                  @Param("centerId") Integer centerId);

    /**
     * 通过出窖订单号查找甑口任务
     *
     * @param outOrderCodes
     * @return
     */
    List<TPoWorkshopPitOrderPotTask> getPotTasksByOutOrderCode(@Param("outOrderCodes") List<String> outOrderCodes);

    List<PitOrderPageByBatchVo> selectPitOrderPageByBatch(BatchNodeQueryReq queryReq);

    List<ForwardBatchVO> getForwardBatchPage(ForwardBatchQueryReq queryReq);

    List<HandinTaskPageByBatchVO> getHandinTaskPageByBatch(HandinTaskByBatchQueryReq queryReq);

    List<PotTaskPageByBatchVO> getPotTaskPageByBatch(PotTaskByBatchQueryReq potTaskByBatchQueryReq);

    /**
     * 根据交酒任务查询订单信息--批次使用
     * @param batch
     * @return
     */
    List<ReverseBatchOrderVO> selectPitOrderByHandinTask(String batch);

    /**
     * 统计固定周期内回酒的使用情况
     * @param startTime
     * @param endTime
     * @param centerId
     * @param locationId
     * @return
     */
    BigDecimal selectHjUseTotal(@Param("startTime")LocalDate startTime, @Param("endTime")LocalDate endTime, @Param("centerId")Integer centerId, @Param("locationId")Integer locationId);

    /**
     * 实际甑口数
     * @param locationKanBanDTO
     * @return
     */
    List<Map<String, Object>> getActualPotCount(@Param("locationKanBanDTO") LocationKanBanDTO locationKanBanDTO);

    @Select("select * from equipment.hv_bm_location where parent_id = #{parentId}")
    List<LocationDTO> getLocationListByParentId(Integer parentId);

    /**
     * 根据订单id查询甑口数据--投料记录使用
     * @param orderId
     * @return
     */
    List<OrderPotTaskVO> selectOrderPotTaskByInputRecord(Integer orderId);

    /**
     * 根据物料批次和物料类型查询订单数据
     * @param batch
     * @param type
     * @return
     */
    List<String> selectOrderNoByBatch(@Param("batch")String batch, @Param("type")String type);

    /**
     * 根据交酒任务查询甑口任务号
     * @param batch
     * @return
     */
    List<ForwardBatchDetailLegendVO> selectSerialNumberByHandinTask(String batch);

    /**
     * 根据订单查询批次信息数据
     * @param orderCode
     * @return
     */
    List<ReverseBatchOrderVO> selectPitOrderByOrderNo(String orderCode);

    List<ForwardBatchDetailLegendVO> selectSerialNumberByOrder(String orderCode);
}
