package com.hvisions.brewage.mkwine.controller.ProductionProcess;

import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.*;
import com.hvisions.brewage.mkwine.service.WorkshopPitOrderOutAppraiseService;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraisePageVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO;
import com.hvisions.brewage.operlog.annotation.Log;
import com.hvisions.brewage.operlog.enums.BusinessType;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: brewage
 * @description: 开窖鉴定
 * @author: DengWeiTao
 **/
@RestController
@RequestMapping("/WorkshopPitOrderOutAppraise")
@Api(description = "开窖鉴定")
public class WorkshopPitOrderOutAppraiseController {

    @Autowired
    private WorkshopPitOrderOutAppraiseService workshopPitOrderOutAppraiseService;


    @PostMapping("/getAllWorkshopPitOrderOutAppraise")
    @ApiOperation(value = "根据条件获取所有开窖鉴定")
    public List<WorkshopPitOrderOutAppraiseVO> getAllWorkshopPitOrderOutAppraise(
            @RequestBody WorkshopPitOrderOutAppraiseDTO dto
    ) {
        return workshopPitOrderOutAppraiseService.getAllWorkshopPitOrderOutAppraise(dto);
    }

    @PostMapping("/getWorkshopPitOrderOutAppraiseByPage")
    @ApiOperation(value = "分页获取开窖鉴定")
    public WorkshopPitOrderOutAppraisePageVO getWorkshopPitOrderOutAppraiseByPage (
            @RequestBody WorkshopPitOrderOutAppraisePageDTO workshopPitOrderOutAppraisePageDTO) {
        return workshopPitOrderOutAppraiseService.getWorkshopPitOrderOutAppraiseByPage(
                workshopPitOrderOutAppraisePageDTO);
    }

    @PostMapping("/updateStatus")
    @ApiOperation(value = "更新执行状态")
    public ResultVO updateStatus(
            @RequestBody WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        return workshopPitOrderOutAppraiseService.updateStatus(workshopPitOrderOutAppraiseDTO);
    }

    @Log(title = "新增开窖鉴定", businessType = BusinessType.INSERT)
    @PostMapping("/insertWorkshopPitOrderOutAppraise")
    @ApiOperation(value = "新增开窖鉴定")
    public ResultVO insertWorkshopPitOrderOutAppraise(
            @RequestBody WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        return workshopPitOrderOutAppraiseService.insertWorkshopPitOrderOutAppraise(workshopPitOrderOutAppraiseDTO);
    }

    @PostMapping("/updateWorkshopPitOrderOutAppraise")
    @ApiOperation(value = "修改开窖鉴定")
    public ResultVO updateWorkshopPitOrderOutAppraise(
            @RequestBody WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO) {
        return workshopPitOrderOutAppraiseService.updateWorkshopPitOrderOutAppraise(workshopPitOrderOutAppraiseDTO);
    }

    @GetMapping("/getOrder/{centerId}&{locationId}")
    @ApiOperation(value = "根据中心车间获取状态未关闭的窖池订单")
    public List<WorkshopPitOrderDTO> getOrder(
            @ApiParam(value = "centerId",example = "4") @PathVariable Integer centerId,
            @ApiParam(value = "locationId",example = "5") @PathVariable  Integer locationId) {
        return workshopPitOrderOutAppraiseService.getOrder(centerId, locationId);
    }

    @DeleteMapping("/DeleteOutAppraise/{id}")
    @ApiOperation(value = "删除开窖鉴定")
    public ResultVO DeleteOutAppraise(@ApiParam(value = "id",example = "4") @PathVariable Integer id) {
        return workshopPitOrderOutAppraiseService.DeleteWorkshopPitOrderOutAppraise(id);
    }

    @GetMapping("/getOpenCellarInvestmentNotSynchronized/{centerId}&{locationId}")
    @ApiOperation(value = "订单的开窖任务在当前扎帐月，窖池订单开窖投入未同步")
    public List<WorkshopPitOrderDTO> OpenCellarInvestmentNotSynchronized(
            @ApiParam(value = "centerId",example = "2") @PathVariable Integer centerId,
            @ApiParam(value = "locationId",example = "5") @PathVariable  Integer locationId) {
        return workshopPitOrderOutAppraiseService.openCellarInvestmentNotSynchronized(centerId,locationId);
    }

}
