package com.hvisions.brewage.mkwine.dao.ProductionProcess;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.entity.tpo.WorkshopPitOrderInappraise;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderInAppraiseDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderInAppraiseQueryDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderInAppraiseVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * <AUTHOR>
 * @title: 入窖鉴定Mapper
 * @projectName brewage
 * @description:
 * @date 2022/4/20 11:20:58
 */
@Mapper
@Repository
public interface WorkshopPitOrderInAppraiseMapper extends BaseMapper<WorkshopPitOrderInappraise> {

    public List<WorkshopPitOrderInAppraiseVO> getAllWorkshopPitOrderInAppraise();

    /**
     * <AUTHOR>
     * @Des 分页查询入窖池鉴定
     * @Date 2022/4/20 18:24:38
     * @Param
     * @Return
     */
    public List<WorkshopPitOrderInAppraiseVO> getWorkshopPitOrderInAppraisePage(WorkshopPitOrderInAppraiseQueryDTO workshopPitOrderInAppraiseQueryDTO);

    //获取入窖鉴定条数
    Integer getCount(WorkshopPitOrderInAppraiseQueryDTO workshopPitOrderInAppraiseQueryDTO);

    /**
     * <AUTHOR>
     * @Des 新增入窖鉴定
     * @Date 2022/4/18 10:51:58
     * @Param
     * @Return
     */
    public Boolean addWorkshopPitOrderInAppraise(WorkshopPitOrderInAppraiseDTO workshopPitOrderInAppraiseDTO);

    /**
     * <AUTHOR>
     * @Des 修改入窖鉴定
     * @Date 2022/4/18 10:51:58
     * @Param
     * @Return
     */
    public Boolean updateWorkshopPitOrderInAppraise(WorkshopPitOrderInAppraiseDTO workshopPitOrderInAppraiseDTO);

    // 入窖鉴定获取窖池订单
    List<WorkshopPitOrderDTO> getOrder(Integer centerId, Integer locationId);

    // 根据窖池订单id查询入窖鉴定
    Integer checkOrder(String orderId);

    // 根据iot任务号查询入窖鉴定
    Integer checkIotTaskNo(String iotTaskNo);

    // 删除入窖鉴定
    boolean DeleteInAppraise(Integer id);
}
