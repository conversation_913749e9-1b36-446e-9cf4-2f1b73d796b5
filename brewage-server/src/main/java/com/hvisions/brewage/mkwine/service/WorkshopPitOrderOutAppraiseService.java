package com.hvisions.brewage.mkwine.service;

import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.*;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraisePageVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO;
import com.hvisions.common.vo.ResultVO;

import java.util.List;

/**
 * @program: brewage
 * @description: 开窖鉴定
 * @author: DengWeiTao
 **/

public interface WorkshopPitOrderOutAppraiseService {

    // 获取全部开窖鉴定
    List<WorkshopPitOrderOutAppraiseVO> getAllWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO dto);

    // 分页获取开窖鉴定
    WorkshopPitOrderOutAppraisePageVO getWorkshopPitOrderOutAppraiseByPage (
            WorkshopPitOrderOutAppraisePageDTO workshopPitOrderOutAppraisePageDTO);

    // 修改执行状态
    ResultVO updateStatus(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 日计划新增开窖鉴定
    ResultVO DayPlanAddOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 新增开窖鉴定
    ResultVO insertWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 修改开窖鉴定
    ResultVO updateWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 根据中心车间获取状态未关闭的窖池订单
    List<WorkshopPitOrderDTO> getOrder(Integer centerId, Integer locationId);

    // 删除开窖鉴定
    ResultVO DeleteWorkshopPitOrderOutAppraise(Integer id);

    /**
     * 窖池概览-开窖信息,数字孪生用
     * @param openCellarInformationDTO
     * @return
     */
    DigitalTwinDTO getOpenCellarInformation(OpenCellarInformationDTO openCellarInformationDTO);

    List<WorkshopPitOrderDTO> openCellarInvestmentNotSynchronized(Integer centerId,Integer locationId);
}
