package com.hvisions.brewage.mkwine.entity.ProductionProcess;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: 窖池订单
 * @projectName brewage
 * @description:
 * @date 2022/4/15 11:02:57
 */
@Data
@Entity
@ApiModel(value = "窖池订单实体类")
@Table(name = "t_po_workshop_pit_order")
public class TPoWorkshopPitOrder {
    @Id
    @TableField("id")
    @ApiModelProperty(value = "窖池中心Id", example = "1")
    @TableId(type = IdType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @TableField("order_code")
    @ApiModelProperty(value = "窖池订单号")
    private String orderCode;

    @TableField("center_id")
    @ApiModelProperty(value = "中心id", example = "1")
    private Integer centerId;

    @TableField("location_id")
    @ApiModelProperty(value = "车间ID", example = "2")
    private Integer locationId;

    @TableField("layer")
//    @ApiModelProperty(value = "层级，0不分层1上层2下层", example = "0")
    @ApiModelProperty(value = "层级，1、2、3代表下层、中层、上层", example = "0")
    private Integer layer;

    @TableField("order_category_id")
    @ApiModelProperty(value = "订单类型", example = "1")
    private Integer orderCategoryId;

    @TableField("pit_id")
    @ApiModelProperty(value = "窖池id (连窖的窖池ID)", example = "1")
    private Integer pitId;

    @TableField("vinasse_id")
    @ApiModelProperty(value = "当前糟源类别", example = "1")
    private Integer vinasseId;

    @TableField("upper_out_vinasse_id")
    @ApiModelProperty(value = "上一个订单的入窖糟源id", example = "1")
    private Integer upperOutVinasseId;

    @TableField("formula_id")
    @ApiModelProperty(value = "配方ID", example = "1")
    private Integer formulaId;

    @TableField("order_status")
    @ApiModelProperty(value = "执行状态 枚举 0 入窖完成 1翻窖完成 2起窖完成 3开窖完成 4交酒完成 5待执行 6 异常", example = "0")
    private Integer orderStatus;

    @TableField("pit_status")
    //@ApiModelProperty(value = "窖池状态（枚举0入窖 1 发酵 2开窖 3 空窖 4 异常 5 已空窖确认）", example = "0")
    @ApiModelProperty(value = "订单状态（枚举 -1新建 0入窖 1发酵 2滴窖 3起窖 4馏酒 5完成 9异常）", example = "0")
    private Integer pitStatus;

    @TableField("order_finish_time")
    @ApiModelProperty(value = "订单完成时间")
    private Date orderFinishTime;

    @TableField("cycle_year")
    @ApiModelProperty(value = "排次年份", example = "2022")
    private Integer cycleYear;

    @TableField("cycle_no_id")
    @ApiModelProperty(value = "糟源排次ID", example = "1")
    private Integer cycleNoId;

    @TableField("seal_confirm_time")
    @ApiModelProperty(value = "封窖完成时间")
    private Date sealConfirmTime;

    @TableField("turn_over_finish_time")
    @ApiModelProperty(value = "翻窖完成时间")
    private Date turnOverFinishTime;

    @TableField("out_pit_finish_time")
    @ApiModelProperty(value = "起窖完成时间")
    private Date outPitFinishTime;

    @TableField("empty_start_time")
    @ApiModelProperty(value = "空窖起始时间")
    private Date emptyStartTime;

    @TableField("open_pit_finish_time")
    @ApiModelProperty(value = "开窖完成时间")
    private Date openPitFinishTime;

    @TableField("handin_finish_time")
    @ApiModelProperty(value = "交酒完成时间")
    private Date handinFinishTime;

    @TableField("is_current")
    @ApiModelProperty(value = "是否在制 1在制 0历史")
    private Boolean isCurrent;

    @TableField("lot_id")
    @ApiModelProperty("批次")
    private String lotId;

    @TableField("in_pit_date")
    @ApiModelProperty("入窖日期")
    private Date inPitDate;

    @TableField("in_pot_team_id")
    @ApiModelProperty(value = "入窖班组id", example = "1")
    private Integer inPotTeamId;

    @TableField("in_pot_trick_id")
    @ApiModelProperty(value = "入窖班次", example = "1")
    private Integer inPotTrickId;

    @TableField("out_pit_date")
    @ApiModelProperty(value = "出窖日期")
    private Date outPitDate;

    @TableField("out_pot_team_id")
    @ApiModelProperty(value = "出窖班组", example = "1")
    private Integer outPotTeamId;

    @TableField("out_pot_trick_id")
    @ApiModelProperty(value = "出窖班次", example = "1")
    private Integer outPotTrickId;

    @TableField("priority_id")
    @ApiModelProperty(value = "优先级（1优先 0正常）", example = "0")
    private Integer priorityId;

    @TableField("technics_id")
    @ApiModelProperty(value = "生产工艺ID（预留）", example = "1")
    private Integer technicsId;

    @TableField("curr_process_step_Id")
    @ApiModelProperty(value = "当前工序", example = "1")
    private Integer currProcessStepId;

    @TableField("in_pit_num")
    @ApiModelProperty(value = "入窖甑口（在制甑口、蒸馏甑口）", example = "1")
    private Integer inPitNum;

    @TableField("turn_over_pit_num")
    @ApiModelProperty(value = "翻窖甑口", example = "1")
    private Integer turnOverPitNum;

    @TableField(value = "in_pit_appraise_code")
    @ApiModelProperty(value = "入窖鉴定编号")
    private String inPitAppraiseCode;

    @TableField("is_seal_confirm_tag")
    @ApiModelProperty(value = "封窖完成确认标志")
    private Integer isSealConfirmTag;

    @TableField("in_pit_qm_id")
    @ApiModelProperty(value = "入窖质检单id")
    private Integer inPitQmId;

    @TableField("seal_confirm_user_id")
    @ApiModelProperty(value = "封窖确认人（预留）", example = "1")
    private Integer sealConfirmUserId;

    @TableField("out_pit_appraise_code")
    @ApiModelProperty(value = "开窖鉴定编号")
    private String outPitAppraiseCode;

    @TableField("out_pit_qm_id")
    @ApiModelProperty(value = "出窖质检单id")
    private Integer outPitQmId;

    @TableField("is_Empty_Confirm_Tag")
    @ApiModelProperty(value = "空窖确认标志", example = "1")
    private Integer isEmptyConfirmTag;

    @TableField("empty_confirm_user_id")
    @ApiModelProperty(value = "空窖确认人")
    private Integer emptyConfirmUserId;

    @TableField("empty_confirm_time")
    @ApiModelProperty(value = "空窖确认时间")
    private Date emptyConfirmTime;

    @TableField("crude_quantity")
    @ApiModelProperty(value = "基酒净重")
    private Float crudeQuantity;

    @TableField("vol60quantity")
    @ApiModelProperty(value = "折算重量")
    private Float vol60Quantity;

    @TableField("ground_temperature")
    @ApiModelProperty("地温")
    private Float GroundTemperature;

    @TableField("avge_in_pit_temperature")
    @ApiModelProperty(value = "入窖平均温度")
    private Float avgeInPitTemperature;

    @TableField("top_temperature")
    @ApiModelProperty(value = "顶温")
    private Float topTemperature;

    @TableField("to_top_days")
    @ApiModelProperty(value = "升至顶温天数", example = "1")
    private Integer toTopDays;

    @TableField("rise_temperature")
    @ApiModelProperty(value = "升温幅度")
    private Float riseTemperature;

    @TableField(value = "to_sap_tag")
    @ApiModelProperty(value = "上报SAP标记")
    private Integer to_Sap_Tag;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @TableField(value = "create_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createId;

    @TableField(value = "collapse_choose_pit_id")
    @ApiModelProperty(value = "跨窖后选择的窖池id")
    private Integer collapseChoosePitId;

    /**
     * 翻窖状态（6：翻窖中 7：已翻窖）
     */
    @ApiModelProperty(value = "翻窖状态（6：翻窖中 7：已翻窖）")
    private Integer turnoverStatus;

    @TableField(value = "seal_water_test_time")
    @ApiModelProperty(value = "密封水检测时间")
    private Date sealWaterTestTime;

    @ApiModelProperty(value="订单种类(0:窖池订单 1：科研订单)")
    @TableField(value = "order_type")
    private Integer orderType;

    @ApiModelProperty(value="翻窖次数")
    @TableField(value = "turnover_count")
    private Integer turnoverCount;

    /**
     * 订单执行状态枚举拼接 枚举ExecutionStatus
     *  状态 1.执行中 9.完成
     */
    @ApiModelProperty(value = "订单执行状态枚举拼接")
    @TableField(value = "execution_status")
    private String executionStatus;
}
