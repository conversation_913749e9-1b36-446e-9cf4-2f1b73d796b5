package com.hvisions.brewage.mkwine.dao.ProductionProcess;

import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.OpenCellarInformationDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseDTO;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraisePageDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderOutAppraise;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.URVO;
import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO;
import com.hvisions.brewage.vo.tpo.SapSyncConfigVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @program: brewage
 * @description: 开窖鉴定DAO
 * @author: DengWeiTao
 **/
@Mapper
@Repository
public interface WorkshopPitOrderOutAppraiseMapper {

    // 获取全部开窖鉴定
    List<WorkshopPitOrderOutAppraiseVO> getAllWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO dto);

    // 分页获取开窖鉴定
    List<WorkshopPitOrderOutAppraiseVO> getWorkshopPitOrderOutAppraiseByPage(
            WorkshopPitOrderOutAppraisePageDTO workshopPitOrderOutAppraisePageDTO);

    // 统计获取的数据条数
    Integer getCount(WorkshopPitOrderOutAppraisePageDTO workshopPitOrderOutAppraisePageDTO);

    // 更新执行状态
    Boolean updateStatus(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 新增开窖鉴定
    Boolean insertWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 修改开窖鉴定
    Boolean updateWorkshopPitOrderOutAppraise(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 根据中心车间获取状态未关闭的窖池订单
    List<WorkshopPitOrderDTO> getOrder(Integer centerId, Integer locationId);

    // 修改开窖鉴定状态为 已完成 状态，更新起窖完成时间
    void updateOutPitFinishTime(Date date, Integer orderId);

    // 查询窖池订单状态
    Integer getPitStatus(Integer orderId);

    // 当该窖池订单的窖池状态为发酵时，自动修改为【开窖】
    void updatePitStatus(int pitStatus, Integer orderId);

    // 窖池订单执行状态
    Integer getOrderStatus(Integer orderId);

    void updateOrderStatus(int orderStatus, Integer orderId);

    // 获取用户信息
    URVO getRUInfo(Integer userId);

    // 根据窖池订单id获取窖池订单编号
    String getOrderCode(Integer orderId);

    // 新增校验
    Integer checkOrderId(Integer orderId);

    // 更新sap的起窖完成时间
    void updateOutPitFinishTimeSap(Date date, Integer orderId);

    // 获取当前新增的开窖鉴定id
    Integer getMaxId();

    // 更新sap窖池状态
    void updateSapPitStatus(int pitStatus, Integer orderId);

    // 更新sap订单执行状态
    void updateSapOrderStatus(int orderStatus, Integer orderId);

    // 根据窖池订单查询滴窖任务
    List<WorkshopPitOrderExudingTaskVO> getExudingTask(Integer orderId);

    // 更新滴窖任务状态
    void updateExudingStatus(Date outAppraiseTime, Integer id);

    // 判断日计划中是否有窖池订单
    Integer judgmentRJH(Integer orderId);

    // 根据id获取开窖鉴定
    WorkshopPitOrderOutAppraiseVO getOutById(Integer id);

    // 根据窖池订单号查询窖池订单状态
    Integer getOrderStatusByOrderCode(String orderCode);

    // 删除空窖确认生成的窖池订单
    boolean deleteOrder(String orderCode);

    // 删除开窖鉴定
    boolean deleteOutAppraise(Integer id);

    // 修改窖池订单
    boolean updateOrder(Integer orderId);

    // 修改sap
    boolean updateOrderSap(Integer orderId);

    // 开窖鉴定新增日志
    void insertOutLog(WorkshopPitOrderOutAppraiseDTO workshopPitOrderOutAppraiseDTO);

    // 获取开窖鉴定时间
    Date getOutTimeById(Integer outAppraiseId);

    /**
     * 根据窖池订单查询开窖鉴定
     * @param orderCodeId
     * @return
     */
    TPoWorkshopPitOrderOutAppraise getByOrderId(Integer orderCodeId);

    Integer selectById(Integer orderId);


    /**
     * 查询配置最大的流水号
     *
     * @return
     */
    Integer selectMaxTaskNo(@Param("typeName") String typeName);

    String selectTaskNoById(Integer id);

    /**
     * 窖池概览-开窖信息,数字孪生用
     * @param centerCode
     * @param startTime
     * @param endTime
     * @return
     */
    List<OpenCellarInformationDTO> getDigitalTwinOpenCellarInformation(@Param("centerCode") String centerCode, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<WorkshopPitOrderDTO> openCellarInvestmentNotSynchronized(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("centerId") Integer centerId, @Param("locationId") Integer locationId);

    SapSyncConfigVO findSapSyncConfig(@Param("year")Integer year,@Param("month")Integer month);
}
