package com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: DengWeiTao
 * @Project: IntelliJ IDEA
 * @Pcakage: com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.ExudingQueryOrderDTO
 * @Date: 2022年09月01日 11:39
 * @Description:
 */
@Data
@ApiModel(value = "滴窖任务查询窖池订单DTO")
public class ExudingQueryOrderDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private int page = 1;

    @ApiModelProperty(value = "每页条数", example = "10")
    private int pageSize = 10;

    @ApiModelProperty(value = "中心ID", example = "2")
    private Integer centerId;

    @ApiModelProperty(value = "车间ID", example = "6")
    private Integer locationId;

    @ApiModelProperty(value = "窖池编号（连窖）", example = "32978-93921")
    private String pitCode;

    @ApiModelProperty(value = "糟源id", example = "10")
    private Integer vinasseId;

    @ApiModelProperty(value = "是否提醒 0.不提醒；1.提醒")
    private String isRemind;

    @ApiModelProperty(value = "不在列表的订单")
    private List<Integer> notInList;

    @ApiModelProperty(value = "在列表的订单")
    private List<Integer> inList;
}
