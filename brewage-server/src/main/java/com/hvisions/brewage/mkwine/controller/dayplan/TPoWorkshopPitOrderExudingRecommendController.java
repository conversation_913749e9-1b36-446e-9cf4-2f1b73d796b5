package com.hvisions.brewage.mkwine.controller.dayplan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingRecommend;
import com.hvisions.brewage.mkwine.service.TPoWorkshopPitOrderExudingRecommendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 滴窖推荐 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
@Api(tags = {"滴窖推荐"})
@RestController
@RequestMapping("/exudingRecommend")
public class TPoWorkshopPitOrderExudingRecommendController {

    private Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private TPoWorkshopPitOrderExudingRecommendService tPoWorkshopPitOrderExudingRecommendService;


    @ApiOperation(value = "新增滴窖推荐")
    @PostMapping()
    public int add(@RequestBody TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend){
        return tPoWorkshopPitOrderExudingRecommendService.add(tPoWorkshopPitOrderExudingRecommend);
    }

    @ApiOperation(value = "删除滴窖推荐")
    @DeleteMapping("{id}")
    public int delete(@PathVariable("id") Long id){
        return tPoWorkshopPitOrderExudingRecommendService.delete(id);
    }

    @ApiOperation(value = "更新滴窖推荐")
    @PutMapping()
    public int update(@RequestBody TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend){
        return tPoWorkshopPitOrderExudingRecommendService.updateData(tPoWorkshopPitOrderExudingRecommend);
    }

    @ApiOperation(value = "查询滴窖推荐分页数据")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "page", value = "页码"),
        @ApiImplicitParam(name = "pageCount", value = "每页条数")
    })
    @GetMapping()
    public IPage<TPoWorkshopPitOrderExudingRecommend> findListByPage(@RequestParam Integer page,
                                   @RequestParam Integer pageCount){
        return tPoWorkshopPitOrderExudingRecommendService.findListByPage(page, pageCount);
    }

    @ApiOperation(value = "id查询滴窖推荐")
    @GetMapping("{id}")
    public TPoWorkshopPitOrderExudingRecommend findById(@PathVariable Long id){
        return tPoWorkshopPitOrderExudingRecommendService.findById(id);
    }

}
