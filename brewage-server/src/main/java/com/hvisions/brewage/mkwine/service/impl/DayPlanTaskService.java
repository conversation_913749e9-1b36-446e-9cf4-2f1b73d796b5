package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dao.base.ProdConfigItemMapper;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoIncreaseAmplitudeMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoWorkshopPitOrderExudingRecommendMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.WorkshopPitDayPlanMapper;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.ExecuteDayPlanTaskDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkMonthPlanDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoIncreaseAmplitude;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitDayPlan;
import com.hvisions.brewage.mkwine.service.WorkshopPitDayPlanService;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthDetailVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import com.hvisions.brewage.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DayPlanTaskService {

    @Resource
    WorkshopPitDayPlanService workshopPitDayPlanService;

    @Resource
    TPoIncreaseAmplitudeMapper increaseAmplitudeMapper;

    @Resource
    ProdConfigItemMapper prodConfigItemMapper;

    @Resource
    WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    WorkshopPitDayPlanMapper workshopPitDayPlanMapper;

    @Resource
    WorkshopPitOrderPotTaskMapper orderPotTaskMapper;

    @Resource
    TPoWorkshopPitOrderExudingRecommendMapper exudingRecommendMapper;

    /**
     * 执行日计划生成
     * @param executeDayPlanTaskDTO
     */
    public void executeDayPlanTask(ExecuteDayPlanTaskDTO executeDayPlanTaskDTO) {
        log.info("开始执行日计划生成:" + JSONObject.toJSONString(executeDayPlanTaskDTO));
        //剩余生产排班物料
        WorkMonthVO workMonth = null;
        workMonth = getWorkMonth(executeDayPlanTaskDTO.getCenterId(), executeDayPlanTaskDTO.getLocationId(), executeDayPlanTaskDTO.getPlanDate(), workMonth);
        List<WorkshopPitDayPlanDTO> addList = createDayPlan(executeDayPlanTaskDTO.getPlanDate(), executeDayPlanTaskDTO.getCenterId(), executeDayPlanTaskDTO.getLocationId(), true, workMonth);
        if (addList == null) return;
        try {
            workshopPitDayPlanService.addWorkshopPitDayPlan(addList);
        } catch (Exception e) {
            log.error("日计划自动排产失败:" + e.getMessage());
        }
    }

    /**
     * @param planDate   生成时间
     * @param centerId   中心
     * @param locationId 车间
     * @param workMonth
     * @return
     */
    public List<WorkshopPitDayPlanDTO> createDayPlan(String planDate, Integer centerId, Integer locationId, boolean isToday, WorkMonthVO workMonth) {
        //单窖入窖甑口
        List<ProdConfigItem> inputNumList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().like(ProdConfigItem::getItemName, "单窖入窖甑口"));
        if (CollectionUtils.isEmpty(inputNumList)) {
            throw new RuntimeException("单窖入窖甑口 没有配置,车间id:" + locationId);
        }
        int inputNum = Integer.parseInt(inputNumList.get(0).getItemValue());
        workMonth.setInputNum(inputNum);
        //1）单甑生产时长：每类糟源单甑生产时长；
        List<ProdConfigItem> prodTimeList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getWorkshop, locationId).like(ProdConfigItem::getItemName, "单甑生产时长"));
        if (CollectionUtils.isEmpty(prodTimeList)) {
            throw new RuntimeException("单甑生产时长 没有配置,车间id:" + locationId);
        }
        //2）每日生产产能：每日生产甑口能力（CJ）；
        List<ProdConfigItem> dayCapacityList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getWorkshop, locationId).like(ProdConfigItem::getItemName, "每日生产产能"));
        if (CollectionUtils.isEmpty(dayCapacityList)) {
            throw new RuntimeException("每日生产产能 没有配置,车间id:" + locationId);
        }
        //3）空窖数量：生产期间车间空窖数量；
        List<ProdConfigItem> emptyPitNumList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getWorkshop, locationId).eq(ProdConfigItem::getItemName, "空窖数量"));
        if (CollectionUtils.isEmpty(emptyPitNumList)) {
            throw new RuntimeException("车间 空间数量 没有配置,车间id:" + locationId);
        }
        int emptyPitNum = Integer.parseInt(emptyPitNumList.get(0).getItemValue());
        workMonth.setEmptyPitNum(emptyPitNum);
        //获取正在生产的日计划
        if (isToday) {
            getProdDayPlan(prodTimeList, planDate, workMonth, centerId, locationId);
        }
        //获取近期涨幅
        TPoIncreaseAmplitude amplitude = increaseAmplitudeMapper.selectOne(new LambdaQueryWrapper<TPoIncreaseAmplitude>().eq(TPoIncreaseAmplitude::getCenterId, 2).eq(TPoIncreaseAmplitude::getLocationId, 5).orderByDesc(TPoIncreaseAmplitude::getId).last("limit 1"));
        if (amplitude == null) {
            throw new RuntimeException("车间 未获取到近期涨幅数据,车间id:" + locationId);
        }
        BigDecimal increaseAmplitude = amplitude.getIncreaseAmplitude();
        workMonth.setIncreaseAmplitude(increaseAmplitude);
        List<WorkshopPitDayPlanDTO> addList = new ArrayList<>();
        generateDailyPlan(workMonth, dayCapacityList, addList, prodTimeList, isToday);
        return addList;
    }

    /**
     * 日计划排产
     *
     * @param workMonth
     * @param dayCapacityList
     * @param addList
     * @param prodTimeList
     * @return
     */
    private List<WorkshopPitDayPlanDTO> generateDailyPlan(WorkMonthVO workMonth, List<ProdConfigItem> dayCapacityList, List<WorkshopPitDayPlanDTO> addList, List<ProdConfigItem> prodTimeList, boolean isToday) {
        //默认的生产时间
        int allMinute = 1440;
        //今天不能生产完的日计划,第二天接着排
        List<TPoWorkshopPitDayPlan> continueDayPlans = workMonth.getContinueDayPlans();
        if (CollectionUtils.isNotEmpty(continueDayPlans)) {
            //计算今天剩下的计划要消耗多少时间
            int statisticsMinute = remainDayPlanSchedule(addList, prodTimeList, continueDayPlans);
            allMinute = allMinute - statisticsMinute;
        }
        //排产空窖的情况 这里需要的是所在窖池的所有订单都是丢糟订单数据
        //月开始时间
        LocalDate monthBeginDate = workMonth.getMonthBeginDate();
        //月截止时间
        LocalDate monthEndDate = workMonth.getMonthEndDate();
        //当日排班计划时间
        String planDate = workMonth.getPlanDate();
        LocalDate planLocalDate = LocalDate.parse(planDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        //如果是排班的月份的第一天，则制作空窖排产需求
        if (planDate.equals(monthBeginDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))){
            //开工排产
            //获取能够进行空窖排产的ZL、DZ、HZ、MZ订单
            List<ProductionSchedule> emptyProductionSchedules = getEmptySchedulesOrder(workMonth, planDate);
            addList.addAll(arrangeByProdTime(prodTimeList, emptyProductionSchedules, planDate, allMinute, 1, workMonth));
        } else if (planDate.equals(monthEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))){
            //如果是排班的最后一天。保证当日窖池无空窖
            //获取当前的空窖和正在执行的开窖窖池，分配投粮的窖池
            List<ProductionSchedule> productionSchedules = getProdSchedulesOrder(workMonth, planDate);
            prodDayPlanSchedule(productionSchedules, prodTimeList, allMinute, addList, planDate, 3, workMonth);
        } else if (!planLocalDate.isBefore(monthEndDate.minusDays(6)) && !planLocalDate.isAfter(monthEndDate)){
            //如果是排产最后一天的前七天，则进行兼容排产，保证最后一天只做投粮生产
            //计算最后一天排产需要的空窖窖池 -- 这里算出来的是需要保证的空窖数量
            int remainEmptyPitNum = computeProdNum(prodTimeList, dayCapacityList);
            if (!isEmptyProd(planDate, workMonth, isToday)) {
                //判断月计划回酒使用情况，如果使用的回酒已经超过了，则不在使用丢糟排产
                Boolean flag = checkHjFlag(monthBeginDate, monthEndDate, workMonth);
                if (flag && workMonth.getPlanEmptyNum() > (workMonth.getEmptyPitNum() - remainEmptyPitNum)) {
                    //空窖余量比设置的数量大的情况。直接进行投粮排产
                    List<ProductionSchedule> productionSchedules = getProdSchedulesOrder(workMonth, planDate);
                    //这里会把时间排完
                    allMinute= prodDayPlanSchedule(productionSchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
                if (allMinute > 0) {
                    //剩余的时间进行空窖排产
                    List<ProductionSchedule> emptySchedules = getEmptySchedulesOrder(workMonth, planDate);
                    prodDayPlanScheduleEmpty(emptySchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
            } else {
                //全天的生产时长
                //2.1获取能够进行空窖排产的CJ、CB订单
                List<ProductionSchedule> productionSchedules = getProdSchedulesOrder(workMonth, planDate);
                allMinute = prodDayPlanSchedule(productionSchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                if (allMinute > 0) {
                    //剩余的时间进行空窖排产
                    //空窖余量比设置的数量大的情况。直接进行投粮排产
                    List<ProductionSchedule> emptySchedules = getEmptySchedulesOrder(workMonth, planDate);
                    prodDayPlanScheduleEmpty(emptySchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
            }
        } else {
            //持续型排产期
            //判断当日排产情况
            if (isEmptyProd(planDate, workMonth, isToday)) {
                //判断月计划回酒使用情况，如果使用的回酒已经超过了，则不在使用丢糟排产
                Boolean flag = checkHjFlag(monthBeginDate, monthEndDate, workMonth);
                if (flag && workMonth.getPlanEmptyNum() < workMonth.getEmptyPitNum()) {
                    //空窖余量比设置的数量小的情况。直接进行空窖
                    List<ProductionSchedule> emptySchedules = getEmptySchedulesOrder(workMonth, planDate);
                    allMinute = prodDayPlanScheduleEmpty(emptySchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
                if (allMinute > 0) {
                    //剩余的时间进行投粮排产
                    List<ProductionSchedule> productionSchedules = getProdSchedulesOrder(workMonth,planDate);
                    //这里会把时间排完
                    prodDayPlanSchedule(productionSchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
            } else {
                //投粮排产
                //全天的生产时长
                //2.1获取能够进行空窖排产的CJ、CB订单
                List<ProductionSchedule> productionSchedules = getProdSchedulesOrder(workMonth, planDate);
                allMinute = prodDayPlanSchedule(productionSchedules, prodTimeList, allMinute, addList, planDate,2, workMonth);
                if (allMinute > 0) {
                    //剩余的时间进行空窖排产
                    //空窖余量比设置的数量大的情况。直接进行投粮排产
                    List<ProductionSchedule> emptySchedules = getEmptySchedulesOrder(workMonth, planDate);
                    prodDayPlanScheduleEmpty(emptySchedules, prodTimeList, allMinute, addList, planDate, 2, workMonth);
                }
            }
        }
        return addList;
    }

    /**
     * 获取正在生产的日计划
     * @param prodTimeList
     * @param planDate
     * @param workMonth
     * @param centerId
     * @param locationId
     */
    private void getProdDayPlan(List<ProdConfigItem> prodTimeList, String planDate, WorkMonthVO workMonth, Integer centerId, Integer locationId) {
        //未完成数量预留：假设排产时间为每日18:00，则当日18:00~24:00期间生产还在进行，排产时，需依据当天已生产数据完成率评估16:00~24:00期间可完成量，基于该数据再继续排产日次生产安排
        //预计空窖数量 -- 根据窖池判断
        List<Integer> pitList = new ArrayList<>();
        // 计算到次日凌晨的时间差（分钟） -- 今天还能生产的时间
        int minutesToMidnight = (int) (ChronoUnit.MINUTES.between(LocalDateTime.now().toLocalTime(), LocalTime.MAX) + 1);
        //获取当天的日计划 判断完成情况 -- 将正在执行的排在前面
        //计划剩余甑口数量
        List<WorkMonthDetailVO> workMonthDetailVOList = workMonth.getWorkMonthDetailVOList();
        List<TPoWorkshopPitDayPlan> tPoWorkshopPitDayPlans = workshopPitDayPlanMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitDayPlan>().eq(TPoWorkshopPitDayPlan::getWorkshopId, locationId).eq(TPoWorkshopPitDayPlan::getWorkshopCenterId, centerId).eq(TPoWorkshopPitDayPlan::getPlanDate, planDate).orderByDesc(TPoWorkshopPitDayPlan::getPlanStatus));
        for (TPoWorkshopPitDayPlan tPoWorkshopPitDayPlan : tPoWorkshopPitDayPlans) {
            if (5 == tPoWorkshopPitDayPlan.getPlanStatus() && tPoWorkshopPitDayPlan.getOrderRemainderCount() == null) {
                //如果没有剩余排产量并且已经完成。则不用单独计算
                if (!pitList.contains(tPoWorkshopPitDayPlan.getPitId())) {
                    pitList.add(tPoWorkshopPitDayPlan.getPitId());
                }
                tPoWorkshopPitDayPlan.setBalancePotCount(0);
                continue;
            }
            Integer prodTime = Integer.valueOf(prodTimeList.stream().filter(t -> t.getItemName().contains(tPoWorkshopPitDayPlan.getVinasseCode())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            if (minutesToMidnight - prodTime*tPoWorkshopPitDayPlan.getBalancePotCount() > 0) {
                //如果剩余时间足够生产的情况下
                minutesToMidnight = minutesToMidnight - prodTime*tPoWorkshopPitDayPlan.getBalancePotCount();
                //默认能生产完。需要扣除剩余的糟源数据
                workMonthDetailVOList.stream()
                        .filter(d -> tPoWorkshopPitDayPlan.getVinasseCode().equals(d.getVinasseCode()))
                        .findFirst()
                        .ifPresent(d -> d.setRemainCount(d.getRemainCount() - tPoWorkshopPitDayPlan.getBalancePotCount()));
                tPoWorkshopPitDayPlan.setBalancePotCount(0);
                if (!pitList.contains(tPoWorkshopPitDayPlan.getPitId())) {
                    pitList.add(tPoWorkshopPitDayPlan.getPitId());
                }
            } else {
                //剩余时间不够生产的情况，计算最后还能生产的甑口数据
                BigDecimal prodNum = new BigDecimal(minutesToMidnight).divide(new BigDecimal(prodTime), 0, RoundingMode.FLOOR);
                //今天不能生产的数据
                int balanceNum = tPoWorkshopPitDayPlan.getBalancePotCount() - prodNum.intValue();
                workMonthDetailVOList.stream()
                        .filter(d -> tPoWorkshopPitDayPlan.getVinasseCode().equals(d.getVinasseCode()))
                        .findFirst()
                        .ifPresent(d -> d.setRemainCount(d.getRemainCount() - tPoWorkshopPitDayPlan.getBalancePotCount() + balanceNum));
                tPoWorkshopPitDayPlan.setBalancePotCount(balanceNum + (tPoWorkshopPitDayPlan.getOrderRemainderCount() == null?0:tPoWorkshopPitDayPlan.getOrderRemainderCount()));
            }
            workMonth.setPlanEmptyNum(pitList.size());
            workMonth.setContinueDayPlans(tPoWorkshopPitDayPlans.stream().filter(p -> p.getBalancePotCount() > 0).collect(Collectors.toList()));
        }
    }

    /**
     * 空窖排产
     * @param productionSchedules
     * @param prodTimeList
     * @param allMinute
     * @param addList
     * @param planDate
     * @param scheduleType
     * @param workMonth
     * @return
     */
    private int prodDayPlanScheduleEmpty(List<ProductionSchedule> productionSchedules, List<ProdConfigItem> prodTimeList, int allMinute, List<WorkshopPitDayPlanDTO> addList, String planDate, int scheduleType, WorkMonthVO workMonth) {
        BigDecimal increaseAmplitude = workMonth.getIncreaseAmplitude();
        int idx = 0;
        if (workMonth.getPreparationSchedule() != null) {
            //上一天未生产完的计划
            ProductionSchedule productionSchedule = workMonth.getPreparationSchedule();
            idx ++;
            int prodTime = Integer.parseInt(prodTimeList.stream().filter(t -> t.getItemName().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            int prodMinute = productionSchedule.getRemainNum() * prodTime;
            allMinute = allMinute - prodMinute;
            addList.add(initDayPlan(productionSchedule, productionSchedule.getRemainNum(), 0, planDate, idx, scheduleType));
            //此时出窖的窖池已经生产完了，所以又空出来一个新的窖池
            if (3 != productionSchedule.getLayer()) {
                workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() + 1);
            }
            workMonth.setPreparationSchedule(null);
        }
        //糟源剩余生产数量
        Map<String, Integer> detailMap = workMonth.getDetailMap();
        List<String> keyList = new ArrayList<>(detailMap.keySet());
        for (ProductionSchedule productionSchedule : productionSchedules) {
            if (!keyList.contains(productionSchedule.getVinasseName())) {
                //如果糟源已经排产完了，就不在用于排产
                continue;
            }
            if (workMonth.getPlanEmptyNum() >= workMonth.getEmptyPitNum()){
                break;
            }
            idx ++;
            //计算产能
            Integer remainCount = detailMap.get(productionSchedule.getVinasseName());
            //根据涨幅计算出窖甑口 如果有剩余甑口，直接用剩余甑口
            int outNum = productionSchedule.getRemainNum() != null?productionSchedule.getRemainNum():new BigDecimal(productionSchedule.getInPitNum()).multiply(new BigDecimal(1).add(increaseAmplitude)).setScale(0, RoundingMode.UP).intValue();
            if (outNum < remainCount) {
                detailMap.put(productionSchedule.getVinasseName(), remainCount-outNum);
            } else {
                //月计划的甑口产能已经足够，剩余多少生产多少
                outNum = remainCount;
                detailMap.remove(productionSchedule.getVinasseName());
                keyList = new ArrayList<>(detailMap.keySet());
            }
            int prodTime = Integer.parseInt(prodTimeList.stream().filter(t -> t.getItemName().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            int prodMinute = outNum * prodTime;
            if (prodMinute < allMinute) {
                //没有超过当天的生产时长
                allMinute = allMinute - prodMinute;
                addList.add(initDayPlan(productionSchedule, outNum, 0, planDate, idx, scheduleType));
                if (3 != productionSchedule.getLayer()) {
                    workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() + 1);
                }
            } else {
                //当天剩余时长已经不足以生产完该订单,计算还能生产的订单
                int prodDay = new BigDecimal(allMinute).divide(new BigDecimal(prodTime), 0, RoundingMode.FLOOR).intValue();
                //剩余的数量不足的情况
                int orderRemainderCount = outNum - prodDay;
                addList.add(initDayPlan(productionSchedule, outNum, orderRemainderCount, planDate, idx, scheduleType));
                allMinute = 0;
                break;
            }
        }
        workMonth.setDetailMap(detailMap);
        List<Integer> orderIds = workMonth.getOrderIds();
        orderIds.addAll(addList.stream().map(WorkshopPitDayPlanDTO::getOrderId).collect(Collectors.toList()));
        workMonth.setOrderIds(orderIds);
        return allMinute;
    }

    /**
     * 判断是否空窖排产，根据前一天的排产情况
     *
     * @param planDate
     * @param workMonth
     * @param isToday
     * @return
     */
    private boolean isEmptyProd(String planDate, WorkMonthVO workMonth, boolean isToday) {
        ProductionSchedule preparationSchedule = workMonth.getPreparationSchedule();
        //丢糟生产的糟源
        List<String> vlist = Arrays.asList("ZL", "HZ", "MZ");
        if (!isToday && preparationSchedule != null) {
            //还有之前没有生产完的数据，直接使用最新的数据
            return vlist.contains(preparationSchedule.getVinasseName());
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(planDate, formatter);
        String yesterday = date.plusDays(-1).format(formatter);
        List<TPoWorkshopPitDayPlan> queryList = workshopPitDayPlanMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitDayPlan>()
                .eq(TPoWorkshopPitDayPlan::getPlanDate, yesterday)
                .eq(TPoWorkshopPitDayPlan::getWorkshopCenterId, workMonth.getCenterId())
                .eq(TPoWorkshopPitDayPlan::getWorkshopId, workMonth.getLocationId())
                .orderByDesc(TPoWorkshopPitDayPlan::getSortNo).last("limit 1"));
        return CollectionUtils.isEmpty(queryList) || vlist.contains(queryList.get(0).getVinasseCode());
    }

    /**
     * 计算最后一天生产需要剩余的空窖数量
     *
     * @param prodTimeList
     * @param dayCapacityList
     * @return
     */
    private int computeProdNum(List<ProdConfigItem> prodTimeList, List<ProdConfigItem> dayCapacityList) {
        //生产时长
        Integer vinasseProdTime = prodTimeList.stream()
                .filter(c -> c.getItemName().contains("CJ"))
                .map(ProdConfigItem::getItemValue)
                .filter(value -> value != null && !value.trim().isEmpty())
                .distinct()
                .findFirst()
                .map(value -> {
                    try {
                        return Integer.parseInt(value);
                    } catch (NumberFormatException e) {
                        return 60; // 默认值
                    }
                })
                .orElse(60);
        //入窖的数量
        Integer inputNum = dayCapacityList.stream()
                .filter(c -> c.getItemName().contains("CJ"))
                .map(ProdConfigItem::getItemValue)
                .filter(value -> value != null && !value.trim().isEmpty())
                .distinct()
                .findFirst()
                .map(value -> {
                    try {
                        return Integer.parseInt(value);
                    } catch (NumberFormatException e) {
                        return 18; // 默认值
                    }
                })
                .orElse(18);
        return (int) Math.ceil(1440.0 / vinasseProdTime / inputNum);
    }

    /**
     * 未做完的日计划排产
     * @param addList
     * @param prodTimeList
     * @param continueDayPlans
     * @return
     */
    private int remainDayPlanSchedule(List<WorkshopPitDayPlanDTO> addList, List<ProdConfigItem> prodTimeList, List<TPoWorkshopPitDayPlan> continueDayPlans) {
        List<WorkshopPitDayPlanDTO> finalAddList = new ArrayList<>();
        AtomicInteger statisticsMinute = new AtomicInteger(0);
        continueDayPlans.forEach(p -> {
            Integer vinasseProdTime = prodTimeList.stream()
                    .filter(c -> c != null && c.getItemName() != null && p.getVinasseCode() != null)
                    .filter(c -> c.getItemName().contains(p.getVinasseCode()))
                    .map(ProdConfigItem::getItemValue)
                    .filter(value -> value != null && !value.trim().isEmpty())
                    .distinct()
                    .findFirst()
                    .map(value -> {
                        try {
                            return Integer.parseInt(value);
                        } catch (NumberFormatException e) {
                            return 20; // 默认值
                        }
                    })
                    .orElse(20);
            Integer potCount = (p.getBalancePotCount() == null || p.getBalancePotCount() == 0) ? p.getOrderRemainderCount() : p.getBalancePotCount();
            statisticsMinute.addAndGet(vinasseProdTime * potCount);
            WorkshopPitDayPlanDTO planDTO = new WorkshopPitDayPlanDTO();
            BeanUtils.copyProperties(p, planDTO);
            planDTO.setId(null);
            finalAddList.add(planDTO);
        });
        addList.addAll(finalAddList);
        return statisticsMinute.get();
    }

    /**
     * 获取空窖排产订单
     *
     * @param workMonth
     * @param planDate
     * @return
     */
    private List<ProductionSchedule> getEmptySchedulesOrder(WorkMonthVO workMonth, String planDate) {
        List<String> vlist = Arrays.asList("ZL", "HZ", "MZ");
        List<ProductionSchedule> scheduleList = getLastSchedules(vlist, workMonth.getCenterId(), workMonth.getLocationId(), planDate);
        List<Integer> filtrList = scheduleList.stream().map(ProductionSchedule::getId).collect(Collectors.toList());
        //1.1 获取空窖的排产窖池，将这里的窖池获取出来，按照设置的空窖数量进行排产
        List<ProductionSchedule> emptyProductionSchedules = exudingRecommendMapper.selectOrderProductionSchedule(workMonth.getCenterId(), workMonth.getLocationId(), vlist, workMonth.getOrderIds());
        List<String> fullPit = emptyProductionSchedules.stream().map(ProductionSchedule::getFullPitId).distinct().collect(Collectors.toList());
        //查找这批窖池的上层订单
        List<ProductionSchedule> topFloorOrderSchedule = null;
        if (CollectionUtils.isNotEmpty(fullPit)) {
            topFloorOrderSchedule = workshopPitOrderMapper.selectTopFloorOrderSchedule(workMonth.getCenterId(), workMonth.getLocationId(), fullPit);
        }
        //丢糟订单生产优先安排HZ订单，再安排ZL生产
        emptyProductionSchedules.sort(Comparator.comparing(
                schedule -> {
                    String vinasseName = schedule.getVinasseName();
                    if ("HZ".equals(vinasseName)) {
                        return 1; // HZ排第一
                    } else if ("ZL".equals(vinasseName)) {
                        return 2; // ZL排第二
                    } else {
                        return 3; // 其他排后面
                    }
                }
        ));
        int idx = 0;
        //1.2 对窖池的所有订单进行汇总丢糟排产
        for (ProductionSchedule productionSchedule : emptyProductionSchedules) {
            idx ++;
            //如果有3层次订单也一并排产了
            assert topFloorOrderSchedule != null;
            ProductionSchedule floor = topFloorOrderSchedule.stream().filter(f -> f.getFullPitId().equals(productionSchedule.getFullPitId())).findFirst().orElse(null);
            if (floor != null) {
                scheduleList.add(floor);
            }
            if (filtrList.contains(productionSchedule.getId())) {
                continue;
            }
            scheduleList.add(productionSchedule);
        }
        return scheduleList;
    }

    private List<ProductionSchedule> getProdSchedulesOrder(WorkMonthVO workMonth, String planDate) {
        List<String> vlist = Arrays.asList("CJ", "CB");
        List<ProductionSchedule> scheduleList = getLastSchedules(vlist, workMonth.getCenterId(), workMonth.getLocationId(), planDate);
        List<ProductionSchedule> emptyProductionSchedules = exudingRecommendMapper.selectOrderProductionSchedule(workMonth.getCenterId(), workMonth.getLocationId(), vlist, workMonth.getOrderIds());
        //1.1 获取投粮的排产窖池，将这里的窖池获取出来，按照设置的空窖数量进行排产
        List<String> fullPit = emptyProductionSchedules.stream().map(ProductionSchedule::getFullPitId).distinct().collect(Collectors.toList());
        //查找这批窖池的上层订单
        List<ProductionSchedule> topFloorOrderSchedule = workshopPitOrderMapper.selectTopFloorOrderSchedule(workMonth.getCenterId(), workMonth.getLocationId(), fullPit);
        //1.2 对窖池的所有订单进行汇总排产
        for (int i = 0; i< workMonth.getPlanEmptyNum(); i++){
            //本轮需要排产的窖池
            ProductionSchedule productionSchedule = emptyProductionSchedules.get(i);
            //如果有3层次订单也一并排产了
            ProductionSchedule floor = topFloorOrderSchedule.stream().filter(f -> f.getFullPitId().equals(productionSchedule.getFullPitId())).findFirst().orElse(null);
            if (floor != null) {
                scheduleList.add(floor);
            }
            scheduleList.add(productionSchedule);
        }
        return scheduleList;
    }

    /**
     * 获取前一天未生产完的窖池数据
     *
     * @param vlist
     * @param planDate
     * @return
     */
    private List<ProductionSchedule> getLastSchedules(List<String> vlist, Integer centerId, Integer locationId, String planDate) {
        //检查前一天是否有未排完的订单
        //转换为LocalDate并减少一天
        LocalDate localDate = LocalDate.parse(planDate);
        String lastDate = localDate.minusDays(1).toString();
        return workshopPitDayPlanMapper.selectLastRemianderList(lastDate, vlist, centerId, locationId);
    }

    /**
     * 初始化日计划数据信息
     *
     * @param productionSchedule
     * @param outNum
     * @param orderRemainderCount
     * @param idx
     * @param scheduleType
     * @return
     */
    private WorkshopPitDayPlanDTO initDayPlan(ProductionSchedule productionSchedule, int outNum, int orderRemainderCount, String planDate, int idx, int scheduleType) {
        WorkshopPitDayPlanDTO workshopPitDayPlanDTO = new WorkshopPitDayPlanDTO();
        workshopPitDayPlanDTO.setOrderId(productionSchedule.getId());
        workshopPitDayPlanDTO.setPitId(productionSchedule.getPitId());
        workshopPitDayPlanDTO.setWorkshopCenterId(productionSchedule.getCenterId());
        workshopPitDayPlanDTO.setWorkshopId(productionSchedule.getLocationId());
        workshopPitDayPlanDTO.setCategoryId(productionSchedule.getCategoryId());
        workshopPitDayPlanDTO.setPlanDate(DateUtil.parse(planDate, "yyyy-MM-dd"));
        workshopPitDayPlanDTO.setIsOpened(true);
        workshopPitDayPlanDTO.setCalcOutPotCount(outNum);
        workshopPitDayPlanDTO.setBalancePotCount(outNum);
        workshopPitDayPlanDTO.setPlanOutPotCount(outNum);
        workshopPitDayPlanDTO.setPlanStatus(0);
        workshopPitDayPlanDTO.setSortNo(idx);
        workshopPitDayPlanDTO.setOrderRemainderCount(orderRemainderCount);
        workshopPitDayPlanDTO.setBaseVinasseId(productionSchedule.getVinasseId());
        workshopPitDayPlanDTO.setVinasseCode(productionSchedule.getVinasseName());
        workshopPitDayPlanDTO.setScheduleType(""+scheduleType);
        return workshopPitDayPlanDTO;
    }

    /**
     * 投粮生产排日计划
     *
     * @param productionSchedules
     * @param prodTimeList
     * @param allMinute
     * @param addList
     * @param planDate
     * @param workMonth
     */
    private int prodDayPlanSchedule(List<ProductionSchedule> productionSchedules, List<ProdConfigItem> prodTimeList, int allMinute, List<WorkshopPitDayPlanDTO> addList, String planDate, int scheduleType, WorkMonthVO workMonth) {
        BigDecimal increaseAmplitude = workMonth.getIncreaseAmplitude();
        Integer inputNum = workMonth.getInputNum();
        //总共生产的甑口/入窖甑口数 = 用掉的空窖池
        int remainPotNum = workMonth.getRemainPotNum();
        int idx = 0;
        if (workMonth.getPreparationSchedule() != null) {
            //上一天未生产完的计划
            ProductionSchedule productionSchedule = workMonth.getPreparationSchedule();
            idx ++;
            int prodTime = Integer.parseInt(prodTimeList.stream().filter(t -> t.getItemName().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            int prodMinute = productionSchedule.getRemainNum() * prodTime;
            allMinute = allMinute - prodMinute;
            addList.add(initDayPlan(productionSchedule, productionSchedule.getRemainNum(), 0, planDate, idx, scheduleType));
            //此时出窖的窖池已经生产完了，所以又空出来一个新的窖池
            if (3 != productionSchedule.getLayer()) {
                workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() + 1);
            }
            remainPotNum = remainPotNum + productionSchedule.getRemainNum();
            if (remainPotNum > inputNum) {
                remainPotNum = remainPotNum - inputNum;
                workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() - 1);
            }
            workMonth.setPreparationSchedule(null);
        }
        //糟源剩余生产数量
        Map<String, Integer> detailMap = workMonth.getDetailMap();
        List<String> keyList = new ArrayList<>(detailMap.keySet());
        for (ProductionSchedule productionSchedule : productionSchedules) {
            if (!keyList.contains(productionSchedule.getVinasseName())) {
                //如果糟源已经排产完了，就不在用于排产
                continue;
            }
            if (workMonth.getPlanEmptyNum() <= 0) {
                break;
            }
            idx ++;
            //计算产能
            Integer remainCount = detailMap.get(productionSchedule.getVinasseName());
            //根据涨幅计算出窖甑口 如果有剩余甑口，直接用剩余甑口
            int outNum = productionSchedule.getRemainNum() != null?productionSchedule.getRemainNum():new BigDecimal(productionSchedule.getInPitNum()).multiply(new BigDecimal(1).add(increaseAmplitude)).setScale(0, RoundingMode.UP).intValue();
            if (outNum < remainCount) {
                detailMap.put(productionSchedule.getVinasseName(), remainCount-outNum);
            } else {
                //月计划的甑口产能已经足够，剩余多少生产多少
                outNum = remainCount;
                log.info("糟源{}计划已经生产完", productionSchedule.getVinasseName());
                detailMap.remove(productionSchedule.getVinasseName());
                keyList = new ArrayList<>(detailMap.keySet());
            }
            if (3 != productionSchedule.getLayer() && remainPotNum == 0) {
                //当剩余数量为0的时候，默认会用一个新的窖池来装甑口
                workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() - 1);
            }
            int prodTime = Integer.parseInt(prodTimeList.stream().filter(t -> t.getItemName().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            int prodMinute = outNum * prodTime;
            if (prodMinute < allMinute) {
                //没有超过当天的生产时长
                allMinute = allMinute - prodMinute;
                addList.add(initDayPlan(productionSchedule, outNum, 0, planDate, idx, scheduleType));
                //此时出窖的窖池已经生产完了，所以又空出来一个新的窖池
                if (3 != productionSchedule.getLayer()) {
                    workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() + 1);
                }
                remainPotNum = remainPotNum + outNum;
                if (remainPotNum > inputNum) {
                    remainPotNum = remainPotNum - inputNum;
                    workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() - 1);
                }
            } else {
                //当天剩余时长已经不足以生产完该订单,计算还能生产的订单
                int prodDay = new BigDecimal(allMinute).divide(new BigDecimal(prodTime), 0, RoundingMode.FLOOR).intValue();
                //剩余的数量不足的情况
                int orderRemainderCount = outNum - prodDay;
                addList.add(initDayPlan(productionSchedule, outNum, orderRemainderCount, planDate, idx, scheduleType));
                allMinute = 0;
                remainPotNum = remainPotNum + prodDay;
                if (remainPotNum > inputNum) {
                    remainPotNum = remainPotNum - inputNum;
                    workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() - 1);
                }
                //未生产完的订单生成一个预备计划
                productionSchedule.setRemainNum(orderRemainderCount);
                workMonth.setPreparationSchedule(productionSchedule);
                break;
            }
        }
        workMonth.setRemainPotNum(remainPotNum);
        workMonth.setDetailMap(detailMap);
        List<Integer> orderIds = workMonth.getOrderIds();
        orderIds.addAll(addList.stream().map(WorkshopPitDayPlanDTO::getOrderId).collect(Collectors.toList()));
        workMonth.setOrderIds(orderIds);
        return allMinute;
    }

    /**
     * 根据排产周期去判断使用的回酒情况
     *
     * @param monthBeginDate
     * @param monthEndDate
     * @param workMonth
     * @return 判断标识是否通过
     */
    private Boolean checkHjFlag(LocalDate monthBeginDate, LocalDate monthEndDate, WorkMonthVO workMonth) {
        BigDecimal hjUseTotal = orderPotTaskMapper.selectHjUseTotal(monthBeginDate, monthEndDate, workMonth.getCenterId(), workMonth.getLocationId());
        return workMonth.getHjPlanQuality().compareTo(hjUseTotal) > 0;
    }

    /**
     * 根据今天的实际生产情况去更新月计划需求量
     *
     * @param centerId
     * @param locationId
     * @param planDate
     * @param workMonth
     * @return
     */
    private WorkMonthVO getWorkMonth(Integer centerId, Integer locationId, String planDate, WorkMonthVO workMonth) {
        if (StringUtils.isBlank(planDate)) {
            //获取当前日期的下一天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            //需要排班计划的日期
            planDate = DateUtil.dateFormat(calendar.getTime(), "yyyy-MM-dd");
        }
        //获取月计划排班情况
        //根据排班情况获取要排班的糟源
        WorkMonthPlanDTO workMonthPlanDTO = new WorkMonthPlanDTO();
        workMonthPlanDTO.setCenterId(centerId);
        workMonthPlanDTO.setLocationId(locationId);
        workMonthPlanDTO.setPlanTime(LocalDate.parse(planDate));
        WorkMonthVO workMonthVO = workshopPitDayPlanService.getMonthlyPlanRemainingProductionRetort(workMonthPlanDTO);
        //如果上次计算的物料信息和当前最新获取的物料信息是一样的，则不适用新的查询数据
        if (workMonth != null && workMonth.getMonthBeginDate().equals(workMonthVO.getMonthBeginDate())) {
            workMonth.setPlanDate(planDate);
            return workMonth;
        }
        if (workMonth != null) {
            //如果跨月的情况则需要将之前的遗留数据转到新的一个月上进行流转
            workMonthVO.setRemainPotNum(workMonth.getRemainPotNum());
            workMonthVO.setPreparationSchedule(workMonth.getPreparationSchedule());
            workMonthVO.setOrderIds(workMonth.getOrderIds());
        }
        //物料需求
        List<WorkMonthDetailVO> workMonthDetailVOList = workMonthVO.getWorkMonthDetailVOList();
        //CZ的生产应该计划到CJ上面。因为CJ会转换成CZ，这里需要转换
        List<WorkMonthDetailVO> CZ = workMonthDetailVOList.stream().filter(d -> "CZ".equals(d.getVinasseCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CZ)) {
            workMonthDetailVOList.forEach(v -> {
                if ("CJ".equals(v.getVinasseCode())) {
                    v.setPlanCount(CZ.get(0).getPlanCount() + v.getPlanCount());
                    v.setPotCount(CZ.get(0).getPotCount() + v.getPotCount());
                }
            });
        }
        workMonthDetailVOList.forEach(d -> d.setRemainCount(d.getPlanCount() - d.getPotCount()));
        Map<String, Integer> vinasseToCountMap = workMonthDetailVOList.stream()
                .collect(Collectors.toMap(
                        WorkMonthDetailVO::getVinasseCode,
                        WorkMonthDetailVO::getRemainCount
                ));
        //对最终数据进行过滤 --处理剩余甑口数量
        workMonthVO.setPlanDate(planDate);
        workMonthVO.setDetailMap(vinasseToCountMap);
        workMonthVO.setCenterId(centerId);
        workMonthVO.setLocationId(locationId);
        return workMonthVO;
    }

    /**
     * 多糟源排日计划，用单甑时长排产
     *
     * @param prodTimeList
     * @param scheduleList
     * @param planDate
     * @param scheduleType
     * @param workMonth
     * @return
     */
    private List<WorkshopPitDayPlanDTO> arrangeByProdTime(List<ProdConfigItem> prodTimeList, List<ProductionSchedule> scheduleList, String planDate, Integer allMinute, int scheduleType, WorkMonthVO workMonth) {
        BigDecimal increaseAmplitude = workMonth.getIncreaseAmplitude();
        //糟源剩余生产数量
        Map<String, Integer> detailMap = workMonth.getDetailMap();
        List<String> keyList = new ArrayList<>(detailMap.keySet());
        List<WorkshopPitDayPlanDTO> addList = new ArrayList<>();
        Map<String, Integer> vinasseProdTimeMap = new HashMap<>();
        //窖池订单使用的总分钟数
        int totalMinute = 0;
        int idx = 0;
        for (ProductionSchedule productionSchedule : scheduleList) {
            if (!keyList.contains(productionSchedule.getVinasseName())) {
                //如果糟源已经排产完了，就不在用于排产
                continue;
            }
            idx ++;
            //糟源剩余数量
            Integer remainCount = detailMap.get(productionSchedule.getVinasseName());
            //单甑时长
            Integer vinasseProdTime = vinasseProdTimeMap.get(productionSchedule.getVinasseName());
            if (vinasseProdTime == null) {
                vinasseProdTime = Integer.valueOf(prodTimeList.stream().filter(p -> p.getItemName().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).distinct().findFirst().orElse("20"));
                vinasseProdTimeMap.put(productionSchedule.getVinasseName(), vinasseProdTime);
            }
            //按订单进行扣除
            WorkshopPitDayPlanDTO workshopPitDayPlanDTO = new WorkshopPitDayPlanDTO();
            workshopPitDayPlanDTO.setOrderId(productionSchedule.getId());
            workshopPitDayPlanDTO.setPitId(productionSchedule.getPitId());
            workshopPitDayPlanDTO.setWorkshopCenterId(productionSchedule.getCenterId());
            workshopPitDayPlanDTO.setWorkshopId(productionSchedule.getLocationId());
            workshopPitDayPlanDTO.setSortNo(idx);
            workshopPitDayPlanDTO.setCategoryId(productionSchedule.getCategoryId());
            workshopPitDayPlanDTO.setBaseVinasseId(productionSchedule.getVinasseId());
            workshopPitDayPlanDTO.setVinasseCode(productionSchedule.getVinasseName());
            workshopPitDayPlanDTO.setPlanStatus(0);
            workshopPitDayPlanDTO.setPlanDate(DateUtil.parse(planDate, "yyyy-MM-dd"));
            workshopPitDayPlanDTO.setIsOpened(true);
            workshopPitDayPlanDTO.setScheduleType(""+scheduleType);
            //根据涨幅计算出窖甑口数量
            int outNum = productionSchedule.getRemainNum() != null?productionSchedule.getRemainNum():new BigDecimal(productionSchedule.getInPitNum()).multiply(new BigDecimal(1).add(increaseAmplitude)).setScale(0, RoundingMode.UP).intValue();
            if (outNum < remainCount) {
                detailMap.put(productionSchedule.getVinasseName(), remainCount-outNum);
            } else {
                //月计划的甑口产能已经足够，剩余多少生产多少
                outNum = remainCount;
                detailMap.remove(productionSchedule.getVinasseName());
                keyList = new ArrayList<>(detailMap.keySet());
            }
            int thisProdTime = new BigDecimal(outNum).multiply(new BigDecimal(vinasseProdTime)).intValue();
            if (totalMinute + thisProdTime < allMinute) {
                //没有超过当天的生产时长
                totalMinute += thisProdTime;
                workshopPitDayPlanDTO.setCalcOutPotCount(outNum);
                workshopPitDayPlanDTO.setBalancePotCount(outNum);
                workshopPitDayPlanDTO.setPlanOutPotCount(outNum);
                addList.add(workshopPitDayPlanDTO);
                if (3 != productionSchedule.getLayer()) {
                    workMonth.setPlanEmptyNum(workMonth.getPlanEmptyNum() + 1);
                }
            } else {
                //当天剩余时长已经不足以生产完该订单,计算还能生产的订单
                Integer prodDay = new BigDecimal(allMinute - totalMinute).divide(new BigDecimal(vinasseProdTime), 0, RoundingMode.FLOOR).intValue();
                workshopPitDayPlanDTO.setCalcOutPotCount(prodDay);
                workshopPitDayPlanDTO.setBalancePotCount(prodDay);
                workshopPitDayPlanDTO.setPlanOutPotCount(prodDay);
                //设置剩余排产甑口，下次排产
                workshopPitDayPlanDTO.setOrderRemainderCount(outNum - prodDay);
                productionSchedule.setRemainNum(outNum - prodDay);
                workMonth.setPreparationSchedule(productionSchedule);
                addList.add(workshopPitDayPlanDTO);
                break;
            }
        }
        //剩余的甑口数据
        workMonth.setDetailMap(detailMap);
        List<Integer> orderIds = workMonth.getOrderIds();
        orderIds.addAll(addList.stream().map(WorkshopPitDayPlanDTO::getOrderId).collect(Collectors.toList()));
        workMonth.setOrderIds(orderIds);
        return addList;
    }

    /**
     *
     * @param executeDayPlanTaskDTO
     */
    public List<WorkshopPitDayPlanDTO> executeCycleDayPlanTask(ExecuteDayPlanTaskDTO executeDayPlanTaskDTO) {
        List<WorkshopPitDayPlanDTO> all = new ArrayList<>();
        List<String> cycleDateList = DateUtil.formatDate(DateUtil.parse(executeDayPlanTaskDTO.getStartDate(), "yyyy-MM-dd"), DateUtil.parse(executeDayPlanTaskDTO.getEndDate(), "yyyy-MM-dd"));
        WorkMonthVO workMonth = null;
        //获取滴窖推荐的周期--默认7天
        for (String date : cycleDateList) {
            workMonth = getWorkMonth(executeDayPlanTaskDTO.getCenterId(), executeDayPlanTaskDTO.getLocationId(), date, workMonth);
            //判断是否是第一天
            if (date.equals(cycleDateList.get(0))) {
                all.addAll(createDayPlan(date, executeDayPlanTaskDTO.getCenterId(), executeDayPlanTaskDTO.getLocationId(), true, workMonth));
            } else {
                all.addAll(createDayPlan(date, executeDayPlanTaskDTO.getCenterId(), executeDayPlanTaskDTO.getLocationId(), false, workMonth));
            }
        }
        return all;
    }

    /**
     * 计算需要开启的窖池数和剩余空窖池数
     * @param x 空窖池数量
     * @param n 每个空窖池可入糟源数
     * @param y 已生产好的窖池数量
     * @param m 每个已生产窖池可出糟源数 --根据涨幅计算
     * @return 结果对象，包含需要开启的窖池数和剩余空窖池数
     */
    public static CalculationResult calculatePoolsAndRemaining(int x, int n, int y, int m) {
        // 计算总需求糟源量
        long totalNeeded = (long) x * n;

        // 如果总需求为0，则不需要开任何窖池，剩余空窖池为0
        if (totalNeeded == 0) {
            return new CalculationResult(0, 0);
        }

        // 计算最大可产出糟源量（开启所有y个窖池）
        long totalProduced = (long) y * m;

        // 如果最大产出量足够满足需求
        if (totalProduced >= totalNeeded) {
            // 计算需要开启的最小窖池数（向上取整）
            long kNeeded = (totalNeeded + m - 1) / m;
            return new CalculationResult((int) kNeeded, 0);
        } else {
            // 产出不足，计算剩余糟源缺口
            long remainingSourceNeeded = totalNeeded - totalProduced;

            // 计算剩余的空窖池数量（向上取整）
            long remainingPools = (remainingSourceNeeded + n - 1) / n;

            return new CalculationResult(y, (int) remainingPools);
        }
    }

    /**
     * 结果类，用于返回开启窖池数和剩余空窖池数
     */
    public static class CalculationResult {
        private int poolsToOpen;// 需要开启的窖池数
        private int remainingPools;// 剩余的空窖池数

        public CalculationResult(int poolsToOpen, int remainingPools) {
            this.poolsToOpen = poolsToOpen;
            this.remainingPools = remainingPools;
        }

        public int getPoolsToOpen() {
            return poolsToOpen;
        }

        public int getRemainingPools() {
            return remainingPools;
        }

        @Override
        public String toString() {
            return "需要开启 " + poolsToOpen + " 个已生产好的窖池，剩余 " + remainingPools + " 个空窖池";
        }
    }
}
