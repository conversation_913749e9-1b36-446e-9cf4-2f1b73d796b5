package com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("自然月单个车间剩余计划生产甑口数据返回详情VO")
@Data
public class WorkMonthDetailVO {

    @ApiModelProperty(value = "某个月份剩余生产的 糟源数量")
    private Integer potCount;

    @ApiModelProperty(value = "某个月份计划生产的 糟源数量")
    private Integer planCount;

    @ApiModelProperty(value = "槽源类型")
    private String vinasseCode;

    @ApiModelProperty(value = "某个月份剩余的 糟源数量")
    private Integer remainCount;
}
