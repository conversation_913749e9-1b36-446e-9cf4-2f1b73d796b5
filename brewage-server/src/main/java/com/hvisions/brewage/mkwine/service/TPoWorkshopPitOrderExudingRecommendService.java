package com.hvisions.brewage.mkwine.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingRecommend;

/**
 * <p>
 * 滴窖推荐 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
public interface TPoWorkshopPitOrderExudingRecommendService extends IService<TPoWorkshopPitOrderExudingRecommend> {

    /**
     * 查询滴窖推荐分页数据
     *
     * @param page      页码
     * @param pageCount 每页条数
     * @return IPage<TPoWorkshopPitOrderExudingRecommend>
     */
    IPage<TPoWorkshopPitOrderExudingRecommend> findListByPage(Integer page, Integer pageCount);

    /**
     * 添加滴窖推荐
     *
     * @param tPoWorkshopPitOrderExudingRecommend 滴窖推荐
     * @return int
     */
    int add(TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend);

    /**
     * 删除滴窖推荐
     *
     * @param id 主键
     * @return int
     */
    int delete(Long id);

    /**
     * 修改滴窖推荐
     *
     * @param tPoWorkshopPitOrderExudingRecommend 滴窖推荐
     * @return int
     */
    int updateData(TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend);

    /**
     * id查询数据
     *
     * @param id id
     * @return TPoWorkshopPitOrderExudingRecommend
     */
    TPoWorkshopPitOrderExudingRecommend findById(Long id);
}
