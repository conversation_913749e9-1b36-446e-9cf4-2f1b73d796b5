package com.hvisions.brewage.mkwine.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoWorkshopPitOrderExudingRecommendMapper;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingRecommend;
import com.hvisions.brewage.mkwine.service.TPoWorkshopPitOrderExudingRecommendService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 滴窖推荐 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
@Service
public class TPoWorkshopPitOrderExudingRecommendServiceImpl extends ServiceImpl<TPoWorkshopPitOrderExudingRecommendMapper, TPoWorkshopPitOrderExudingRecommend> implements TPoWorkshopPitOrderExudingRecommendService {

    @Override
    public  IPage<TPoWorkshopPitOrderExudingRecommend> findListByPage(Integer page, Integer pageCount){
        IPage<TPoWorkshopPitOrderExudingRecommend> wherePage = new Page<>(page, pageCount);
        TPoWorkshopPitOrderExudingRecommend where = new TPoWorkshopPitOrderExudingRecommend();

        return   baseMapper.selectPage(wherePage, Wrappers.query(where));
    }

    @Override
    public int add(TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend){
        return baseMapper.insert(tPoWorkshopPitOrderExudingRecommend);
    }

    @Override
    public int delete(Long id){
        return baseMapper.deleteById(id);
    }

    @Override
    public int updateData(TPoWorkshopPitOrderExudingRecommend tPoWorkshopPitOrderExudingRecommend){
        return baseMapper.updateById(tPoWorkshopPitOrderExudingRecommend);
    }

    @Override
    public TPoWorkshopPitOrderExudingRecommend findById(Long id){
        return  baseMapper.selectById(id);
    }
}
