package com.hvisions.brewage.mkwine.dao.dayplan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.ExecuteDayPlanTaskDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitOrderExudingTask.ExudingQueryOrderDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitOrderExudingRecommend;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.OrderExudingVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 滴窖推荐 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
@Mapper
public interface TPoWorkshopPitOrderExudingRecommendMapper extends BaseMapper<TPoWorkshopPitOrderExudingRecommend> {

    /**
     * 获取所有车间
     * @return
     */
    List<ExecuteDayPlanTaskDTO> getAllLocationList();

    List<ProductionSchedule> selectOrderProductionSchedule(@Param("centerId")Integer centerId, @Param("locationId")Integer locationId, @Param("vlist")List<String> vlist, @Param("orderIds")List<Integer> orderIds);

    List<OrderExudingVO> getOrderRecommend(ExudingQueryOrderDTO exudingQueryOrderDTO);

    /**
     * 查询滴窖任务的订单数据
     * @param exudingQueryOrderDTO
     * @return
     */
    List<ProductionSchedule> selectExudingTaskOrder(ExudingQueryOrderDTO exudingQueryOrderDTO);
}
