package com.hvisions.brewage.mkwine.config;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dao.base.ProdConfigItemMapper;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.ExecuteDayPlanTaskDTO;
import com.hvisions.brewage.mkwine.service.impl.DayPlanTaskService;
import com.hvisions.brewage.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;


@Slf4j
@Component
@EnableScheduling
public class DayPlanTask {

    @Resource
    ProdConfigItemMapper prodConfigItemMapper;

    @Resource
    DayPlanTaskService dayPlanTaskService;

    /**
     * 生成日计划
     */
    @Scheduled(cron = "0 */6 * * * ?")
    @SchedulerLock(name = "createDayPlan")
    public void createDayPlan() {
        log.info("自动生成日计划，开始执行");
        LocalTime now = LocalTime.now();
        //获取配置校验是否执行生成
        List<ProdConfigItem> taskConfig = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().like(ProdConfigItem::getItemName, "任务定时时间"));
        for (ProdConfigItem prodConfigItem : taskConfig) {
            String itemValue = prodConfigItem.getItemValue();
            if(DateUtil.checkTime(itemValue, now, 8)){
                //校验生成时间成功
                log.info("开始执行日计划生成, 车间id:" + prodConfigItem.getWorkshop());
                ExecuteDayPlanTaskDTO executeDayPlanTaskDTO = new ExecuteDayPlanTaskDTO();
                executeDayPlanTaskDTO.setLocationId(Integer.valueOf(prodConfigItem.getWorkshop()));
                executeDayPlanTaskDTO.setCenterId(Integer.valueOf(prodConfigItem.getCenter()));
                executeDayPlanTaskDTO.setPlanDate(DateUtil.format(DateUtil.addDay(new Date(), 1), "yyyy-MM-dd"));
                try {
                    dayPlanTaskService.executeDayPlanTask(executeDayPlanTaskDTO);
                } catch (Exception e) {
                    log.info("执行日计划生成失败:" + e.getMessage(), e);
                }
            }
        }
        log.info("自动生成日计划，执行结束");
    }

}
