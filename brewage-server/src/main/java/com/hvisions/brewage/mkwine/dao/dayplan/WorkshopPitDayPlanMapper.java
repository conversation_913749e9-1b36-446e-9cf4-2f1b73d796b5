package com.hvisions.brewage.mkwine.dao.dayplan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.dto.mkwine.vo.ParamVO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.*;
import com.hvisions.brewage.mkwine.dto.kanban.location.LocationKanBanDTO;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoOutAppraiseLog;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoFirstInFirstOut;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitDayPlan;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.*;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import com.hvisions.brewage.mkwine.vo.productiondisposition.WorkshopPitOrder.WorkshopPitOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @program: brewage
 * @description: 日计划
 * @author: DengWeiTao
 **/
@Mapper
@Repository
public interface WorkshopPitDayPlanMapper extends BaseMapper<TPoWorkshopPitDayPlan> {
    // 获取全部日计划
    List<WorkshopPitDayPlanVO> getAllWorkshopPitDayPlan();

    // 根据 id 查询日计划状态
    List<WorkshopPitDayPlanVO> getWorkshopPitDayPlanById(String dayPlayCode);

    // 根据 id 删除日计划
    Boolean deleteWorkshopPitDayPlanById(String dayPlayCode);

    // 获取日计划条数
    Integer getCount(WorkshopPitDayPlanPageDTO workshopPitDayPlanPageDTO);

    // 分页查询日计划
    List<WorkshopPitDayPlanVO> getWorkshopPitDayPlanPage(@Param("planCodes") String[] planCodes);

    // 新增日计划
    Boolean addWorkshopPitDayPlan(WorkshopPitDayPlanDTO workshopPitDayPlanDTO);

    // 修改日计划
    Boolean updateWorkshopPitDayPlan(WorkshopPitDayPlanDTO workshopPitDayPlanDTO);

    // 获取数据表中最大 id
    Integer getMaxId();

    // 周期查询接口
    ParamVO getCycleById(String param_id);

    // 周期修改接口
    Boolean updateCycle(ParamDTO paramDTO);

    // 周期删除接口
    Boolean deleteCycle(String param_id);

    // 周期添加接口
    Boolean insertCycle(ParamDTO paramDTO);

    // 查询窖池周期表中的最大 id
    Integer getMaxIdOfParam();

    // 日计划待选窖池工单查询接口
    List<WorkshopPitOrderVO> cellarOrderQuery(@Param("workshopPitDayPlanToOrderDTO") WorkshopPitDayPlanToOrderDTO workshopPitDayPlanToOrderDTO,
                                              @Param("vinaseeNames") List<WorkshopPitDayPlanVinasseVO> vinaseeNames);

    // 将每一条窖池订单跟甑口任务关联，统计出已出窖甑口的数量
    List<OutPotNumsVO> getWorkshopPitOrderPotTask(@Param("orderIds") List<Integer> orderIds);

    // 计算计划出窖甑口总和
    Integer getPlanOutPotCountTotal(Integer id);

    // 获取已出窖槽源
    List<WorkMonthProdVO> getMonthlyPlanRemainingProductionRetort(@Param("workMonthPlanDTO") WorkMonthPlanDTO workMonthPlanDTO,
                                                              @Param("startTime") String startTime,
                                                              @Param("endTime") String endTime);

    // 用来提供给物料需求的接口
    List<WorkshopPitDayPlanVO> getDayPlanToMaterielRequest(WorkshopPitDayPlanDTO workshopPitDayPlanDTO);

    // 中心车间下状态为发酵状态的窖池订单，按不同糟源类别分组，并且获取到不同糟源类别最早的入窖时间
    List<WorkshopPitDayPlanVinasseVO> getDifferentVinasse(WorkshopPitDayPlanToOrderDTO workshopPitDayPlanToOrderDTO);

    // 日计划下发
    Boolean workshopPitDayPlanIssued(String dayPlayId, Integer planStatus);

    // 根据日计划编号查询某条日计划详情
    WorkshopPitDayPlanVO getWorkshopPitDayPlanByCode(WorkshopPitDayPlanDTO workshopPitDayPlanDTO);

    // 甑口任务详情
    List<PotTaskVO> potTaskDetail(String orderCode);

    // 根据中心车间连窖号判断是否存在日计划
    Integer existDayPlans(@Param("centerId") Integer centerId, @Param("locationId") Integer locationId, @Param("fullPitId") String fullPitId, @Param("planDateStart") Date planDateStart, @Param("planDateEnd") Date planDateEnd);

    List<WorkshopPitDayPlanVO> judgePlanTimeIsExit(Integer centerId, Integer locationId, String time);

    // 先入先出查询接口
    Boolean getFirstInFirstOut(Integer centerId, Integer locationId);

    // 先入先出修改接口
    boolean updateFirstInFirstOut(TPoFirstInFirstOut out);

    // 为中心车间创建 先入先出配置
    boolean insertFirstInFirstOut(TPoFirstInFirstOut out);

    // 根据日计划号删除
    void deleteWorkshopPitDayPlan(String dayPlayId);

    // 根据日计划编号获取日计划的窖池订单 id
    List<WorkshopPitOrderVO> getCodeIds(String dayPlayId);

    // 查询涨幅系数
    List<BigDecimal> getIncreaseCoefficient(String vinasseName);

    // 获取实际的出窖甑口（窖池）
    Integer getCellarOutPotCount(String orderCode, Integer vinasseId);

    // 查询周期
    Integer[] getParamValue(Integer centerId, Integer locationId);

    // 查询班组出窖甑口数
    List<ExecutionDetailVO> getTeamOutPotCount(String orderCode, Integer vinasseId);

    // 根据日计划号获取日计划
    List<WorkshopPitDayPlanVO> getPitDayPlanByDayPlayId(String dayPlayId);

    // 根据日计划编号查询窖池订单id
    int[] getOrderIds(String dayPlayId);

    // 根据窖池订单id查询物料
    List<MaterialVO> getMaterial(@Param("orderIds") List<Integer> orderIds);

    // 先获取计划出窖的窖池
    List<PitVO> getPlanPot(String centerId, Integer locationId, String currentDate);

    // 计算已出窖的窖池
    List<PitVO> getOutPot(String centerId, Integer locationId, String currentDate);

    // 根据窖池订单id查询滴窖任务的计划开始时间
    List<WorkshopPitOrderExudingTaskVO> exudingTask(Integer orderId);

    List<OrderInfo> getOrderInfos(String dayPlayId);

    // 获取sap中的入窖甑口
    List<OutPotNumsVO> getPotBySap(@Param("orderIds") List<Integer> orderIds);

    // 根据糟源类别查询涨幅系数
    List<OutPotNumsVO> getIncreaseCoefficients(@Param("vinasseList") List<String> vinasseList);

    // 分页查询日计划编号
    String[] getPlanCode(WorkshopPitDayPlanPageDTO workshopPitDayPlanPageDTO);

    /**
     * @Author: LiuYu
     * @Description: 物料需求回退下发的日计划, 通过日计划号查找数据条数
     * @Date: 2022/9/27 13:53
     **/
    Integer getCountByDayPlayId(String dayPlanId);

    /**
     * @Author: LiuYu
     * @Description: 通过日计划号将数据的状态回退到初始状态
     * @Date: 2022/9/27 14:01
     **/
    void updateStateByDayPlayId(String dayPlanId);

    // 根据中心、车间、角色id，获取用户id
    List<Integer> getUserIds(Integer centerId, Integer locationId, Integer roleId);

    // 新增开窖鉴定时记录日志
    void insertOutLog(@Param("list") List<TPoOutAppraiseLog> list);

    /**
     * 正在生产的日计划订单情况 -- 糟源
     * @param workMonthPlanDTO
     * @return
     */
    List<WorkMonthDetailVO> selectProductionBeingPerformed(WorkMonthPlanDTO workMonthPlanDTO);

    /**
     * 正在生产的机器号订单情况 -- 物料
     * @param workMonthPlanDTO
     * @param startTime
     * @param endTime
     * @return
     */
    List<WorkMonthMaterialVO> getMonthlyPlanRemainingProductionMaterial(@Param("workMonthPlanDTO") WorkMonthPlanDTO workMonthPlanDTO,
                                                                        @Param("startTime") String startTime,
                                                                        @Param("endTime") String endTime);

    /**
     * 计划甑口数
     * @param locationKanBanDTO
     * @return
     */
    List<Map<String, Object>> getPlanPotCount(@Param("locationKanBanDTO") LocationKanBanDTO locationKanBanDTO);

    /**
     * 根据日计划号查询订单糟源
     * @param id
     * @return
     */
    String selectVinasseByDayPlan(Integer id);

    /**
     * 查询前一天未排完的订单
     * @param lastDate
     * @param vlist
     * @return
     */
    List<ProductionSchedule> selectLastRemianderList(@Param("lastDate")String lastDate, @Param("vlist")List<String> vlist, @Param("centerId")Integer centerId, @Param("locationId")Integer locationId);
}
