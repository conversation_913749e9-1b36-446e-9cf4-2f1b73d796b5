package com.hvisions.brewage.utils;

import com.google.common.collect.Lists;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.mkwine.enums.CheckCycleTimeUnitEnum;
import com.hvisions.common.exception.BaseKnownException;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class DateUtil {

    public DateUtil() {
    }

    public static List<Date> getOneMonth24To23(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            date = sdf1.parse(sdf1.format(date));//规范日期为 2023-11-24(示例)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            String end = sdf.format(calendar.getTime()) + "-23";
            if(sdf1.parse(end).getTime() < date.getTime()){
                calendar.add(Calendar.MONTH, 1);
                end = sdf.format(calendar.getTime()) + "-23";
            }
            calendar.add(Calendar.MONTH, -1);
            Date time = calendar.getTime();
            String start = sdf.format(time) + "-24";

            List<Date> dates = new ArrayList<>();
            dates.add(sdf1.parse(start));
            dates.add(sdf1.parse(end));
            return dates;
        } catch (Exception var2) {
            throw new BaseKnownException(10000, "获取上月23和本月24错误");
        }
    }

    public static Date getDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdf.format(new Date()));
        } catch (Exception var2) {
            return null;
        }
    }

    public static Date toDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(date);
        } catch (Exception var3) {
            return null;
        }
    }

    public static Date toDateDay(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(date);
        } catch (Exception var3) {
            return null;
        }
    }

    public static String dateFormat(String format) {
        return dateFormat(new Date(), format);
    }

    public static String dateFormat() {
        return dateFormat(new Date());
    }

    public static String dateFormat(Date date) {
        return dateFormat(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static String yearFormat(Date date) {
        return dateFormat(date, "yyyy");
    }

    public static String HourFormat(Date date) {
        return dateFormat(date, "HH");
    }

    public static String monthFormat(Date date) {
        return dateFormat(date, "MM");
    }

    public static String dateFormat(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.format(date);
        } catch (Exception var4) {
            return null;
        }
    }

    public static int getIdate() {
        return getIdate(new Date());
    }

    public static int getIdate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return Integer.parseInt(sdf.format(date));
    }

    public static Date afterMinuteTime(int num) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(12, num);
        return beforeTime.getTime();
    }

    public static Date beforMinuteTime(int num) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(12, -num);
        return beforeTime.getTime();
    }

    public static int differSecond(Date startDate, Date endDate) {
        int second = (int) ((endDate.getTime() - startDate.getTime()) / 1000L);
        return second;
    }

    public static List<Integer> getBeforDays(int num) {
        List<Integer> list = new ArrayList<>(num);
        for (int i = num - 1; i >= 0; i--) {
            list.add(getIdate(beforDayTime(i)));
        }
        return list;
    }

    public static Date beforDayTime(int num) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.DAY_OF_MONTH, -num);
        return beforeTime.getTime();
    }

    public static Date beforDayTime(Date date, int num) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.setTime(date);
        beforeTime.add(Calendar.DAY_OF_MONTH, -num);
        return beforeTime.getTime();
    }

    public static List<Integer> getDays(int start, int end) {
        List<Integer> list = new ArrayList<>(end - start);
        list.add(start);
        if (start == end) {
            return list;
        }
        while (true) {
            int cursor = getIdate(beforDayTime(getDateByIdate(start), -1));
            if (cursor >= end) {
                break;
            }
            list.add(cursor);
            start = cursor;
        }
        list.add(end);
        return list;
    }

    public static Date getDateByIdate(int date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            return sdf.parse(date + "");
        } catch (Exception e) {

        }
        return new Date();
    }

    // 根据开始时间与结束时间获取，时间列表
    public static List<Date> getDatesBetweenTwoDate(String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dBegin = sdf.parse(startDate);
            Date dEnd = sdf.parse(endDate);
            List<Date> lDate = new ArrayList<Date>();
            lDate.add(dBegin);
            Calendar cal = Calendar.getInstance();
            cal.setTime(dBegin);
            boolean bContinue = true;
            while (bContinue) {
                cal.add(Calendar.DAY_OF_MONTH, 1);
                if (dEnd.after(cal.getTime())) {
                    lDate.add(cal.getTime());
                } else {
                    break;
                }
            }
            lDate.add(dEnd);
            return lDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取日期区间的日期数据 LocalDate
     *
     * @param dateStart
     * @param dateEnd
     * @return
     */
    public static List<LocalDate> getTwoDaysDayLocalDate(Long dateStart, Long dateEnd) {
        List<LocalDate> dateList = new ArrayList<LocalDate>();
        //逐日打印日期
        LocalDate startDate = Instant.ofEpochMilli(dateStart).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(dateEnd).atZone(ZoneOffset.ofHours(8)).toLocalDate();
    /*
     几种方法用来判断日期，isBefore()、isAfter()、isEqual()
    */
        dateList.add(startDate);
        while (startDate.isBefore(endDate)) {
            startDate = startDate.plusDays(1);
            dateList.add(startDate);
        }
        return dateList;
    }

    // 返回当前星期
    public static String getWeekOfDate(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    // 根据当前时间与类型增加天数
    // 参数：date：需要操作的日期，n：需要增加多少天，type：类型（列：Calendar.DATE，Calendar.MONTH）
    public static String addDay(String date, int n, int type) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cd = Calendar.getInstance();
            cd.setTime(sdf.parse(date));
            cd.add(type, n);
            return sdf.format(cd.getTime());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * <AUTHOR>
     * @Des 增加时间
     * @Date 2022/3/17 17:44:26
     * @Param date 当前时间
     * addTime 需要增加的数值
     * checkCycleTimeUnitEnum 增加的类型 年月日时
     * @Return
     */
    public static Date addDate(Date date, int addTime, CheckCycleTimeUnitEnum checkCycleTimeUnitEnum) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 根据返回的类型增加时间
        switch (checkCycleTimeUnitEnum) {
            case hours:
                cal.add(Calendar.HOUR, addTime);
                break;
            case day:
                cal.add(Calendar.HOUR, addTime * 24);
                break;
            case week:
                cal.add(Calendar.HOUR, addTime * 24 * 7);
                break;
            case month:
                cal.add(Calendar.MONTH, addTime);
                break;
        }
        return cal.getTime();
    }

    /**
     * @Des 比较两个时间（可传空，如果两个都为空则返回null）
     * <AUTHOR>
     * @Date 2022/8/8 11:45:22
     * @Param
     * @Return
     */
    public static Date compareDate(Date date1, Date date2) {
        if (date1 == null && date2 == null) {
            return null;
        } else if (date1 != null && date2 == null) {
            return date1;
        } else if (date1 == null && date2 != null) {
            return date2;
        } else {
            int i = date1.compareTo(date2);
            if (i >= 0) {
                return date1;
            } else {
                return date2;
            }
        }
    }

    public static final String LD_PATTERN = "yyyy-MM-dd";
    public static final String LD_PATTERN_STR = "yyyyMMdd";
    public static final String LD_PATTERN_MD = "MM-dd";

    public static LocalDate parse2LocalDate(String dateStr, String pattern) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    public static String formatLocaldate(LocalDate ldt, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return dateTimeFormatter.format(ldt);
    }

    /**
     * DengWeiTao
     * 获取当前年月日
     *
     * @return
     */
    public static String getCurrentDate() {
        LocalDate localDate = LocalDate.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        return df.format(localDate);
    }

    /**
     * DengWeiTao
     * 当前时间: 2022-06-01 09:07:55
     *
     * @return
     */
    public static String currentDateFormat() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new Date());
    }

    /**
     * DengWeiTao
     * 当前时间: 2022-06-01 09:07:55
     *
     * @return
     */
    public static String currentDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date());
    }

    /**
     * 获取作日时间
     * @return
     */
    public static String getYesterdayDate() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return yesterday.format(formatter);
    }

    /**
     * DengWeiTao
     * 当前时间: 2022-06-01 09:07:55
     *
     * @return
     */
    public static String datetimeToDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /**
     * DengWeiTao
     *
     * @param days 需要相加的天数
     * @param date 从 date 开始相加 days 天
     * @return
     */
    public static Date datePlus(int days, Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        date = calendar.getTime();
        return date;
    }

    public static List<String> formatDate(Date beginTime, Date endTime) {
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(beginTime);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endTime);

            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return days;
    }

    /**
     * DengWeiTao
     * 获取两个日期相隔的天数
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return
     */
    public static int subtractTwoDates(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / (24 * 60 * 60 * 1000));
    }

    /**
     * DengWeiTao
     * 将检查项目中不是 hours 单位的周期转换为 hours
     *
     * @param cycle 周期数
     * @param unit  单位
     * @param date  时间点，根据时间点获取这个月的天数
     */
    public static int toHours(int cycle, String unit, Date date) {
        switch (unit) {
            case "hours":
                return cycle;
            case "day":
                return cycle * 24;
            case "week":
                return 7 * 24 * cycle;
            case "month": {
                int totalHours = 0;
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                for (int i = 0; i < cycle; i++) {
                    if (i == 0) {
                        totalHours = getDaysOfMonth(date) * 24;
                    } else {
                        calendar.add(Calendar.MONTH, 1);
                        totalHours = totalHours + (getDaysOfMonth(calendar.getTime()) * 24);
                    }
                }
                return totalHours;
            }
            default:
                return 0;
        }
    }

    /**
     * DengWeiTao
     * 获取某个月份最大天数
     *
     * @param date
     * @return
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * DengWeiTao
     * 提前生成时间
     *
     * @param firstCheckTime 首次执行时间
     * @param earlyBuildTime 提前生成多少个小时
     */
    public static Date earlyBuildTime(Date firstCheckTime, int earlyBuildTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(firstCheckTime);
        calendar.add(Calendar.HOUR, -earlyBuildTime);
        return calendar.getTime();
    }

    /**
     * 计算两个时间之间的小时数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 小时数
     */
    public static int subtractTwoHours(Date startTime, Date endTime) {
        long time = endTime.getTime() - startTime.getTime();
        long hours = time / (60 * 60 * 1000);
        return (int) hours;
    }

    /**
     * 增加分钟
     *
     * @param date
     * @return
     */
    public static Date addMinute(Date date, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        Date finishedTime = calendar.getTime();
        return finishedTime;
    }

    /**
     * @param
     * @return long
     * @description: 获取当前0点时间戳
     * <AUTHOR>
     * @date
     */
    public static long getTodayStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * @param
     * @return long
     * @description: 获取当前0点时间戳
     * <AUTHOR>
     * @date
     */
    public static long getTodayEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime().getTime();
    }

    /**
     * 年份
     */
    public static int year(Date date) {
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        if (date == null) {
            return Integer.parseInt(yearFormat.format(new Date()));
        } else {
            return Integer.parseInt(yearFormat.format(date));
        }
    }

    /**
     * 月份
     */
    public static int month(Date date) {
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        if (date == null) {
            return Integer.parseInt(monthFormat.format(new Date()));
        } else {
            return Integer.parseInt(monthFormat.format(date));
        }
    }

    /**
     * 年月日
     *
     * @return
     */
    public static Date date(String date) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.parse(date);
    }

    /**
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>   判断当前时间在时间区间内
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Date转LocalDateTime
     *
     * @param time
     * @return
     */
    public static LocalDateTime convertToLocalTime(Date time) {
        return time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转Date
     *
     * @param date
     * @return
     */
    public static Date convertToDate(LocalDateTime date) {
        LocalDateTime time1 = date;
        return Date.from(time1.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * @param date    时间
     * @param pattern 转换格式
     * @return
     */
    public static Date parse(String date, String pattern) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.parse(date);
        } catch (Exception e) {
            throw new RuntimeException("时间格式转换错误");
        }
    }

    /**
     * @param date    时间
     * @param pattern 转换格式
     * @return
     */
    public static String format(Date date, String pattern) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.format(date);
        } catch (Exception e) {
            throw new RuntimeException("时间格式转换错误");
        }
    }

    /**
     * @param
     * @return long
     * @description: 获取当前0点时间戳
     * <AUTHOR>
     * @date
     */
    public static LocalDateTime getBeginLocalDate(LocalDate localDate) {
        LocalDateTime startTime = LocalDateTime.of(localDate, LocalTime.MIN);
        return startTime;
    }

    /**
     * @param
     * @return long
     * @description: 获取当前0点时间戳
     * <AUTHOR>
     * @date
     */
    public static LocalDateTime getEndLocalDate(LocalDate localDate) {
        LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
        return endTime;
    }

    // 获取当前周日期
    public static List<Date> dateToCurrentWeek(Date myDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(myDate);

        int b = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (b == 0) {
            b = 7;
        }
        Date fdate;
        List<Date> list = new ArrayList<Date>();
        Long fTime = myDate.getTime() - b * 24 * 3600000;
        for (int a = 1; a <= 7; a++) {
            fdate = new Date();
            fdate.setTime(fTime + (a * 24 * 3600000));
            list.add(a - 1, fdate);
        }
        return list;
    }

    // 获取的当前月的第一天和最后一天
    public static List<Date> dateToCurrentMonth() {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.DATE, 1);//把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);//日期回滚一天，也就是最后一天
        //当月有多少天
        int maxDate = a.get(Calendar.DATE);

        SimpleDateFormat sdfTwo = new SimpleDateFormat("yyyy-MM-");
        String startTime = sdfTwo.format(new Date()) + "01 00:00:00";
        String endTime = sdfTwo.format(new Date()) + maxDate + " 23:59:59";
        List<Date> dates = new ArrayList<>();
        dates.add(toDate(startTime));
        dates.add(toDate(endTime));

        return dates;
    }

    //获取当前年的第一天和最后一天
    public static List<Date> dateToCurrentYear() {
        String startTime = new SimpleDateFormat("yyyy").format(new Date()) + "-01-01 00:00:00";

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, calendar.getActualMaximum(Calendar.MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date currYearLast = calendar.getTime();
        String endTime = new SimpleDateFormat("yyyy-MM-dd").format(currYearLast) + " 23:59:59";
        List<Date> dates = new ArrayList<>();
        dates.add(toDate(startTime));
        dates.add(toDate(endTime));
        return dates;
    }

    /***
     * @Description 获取当月每天日期
     *
     * <AUTHOR>
     * @Date 2022-12-6 15:42
     * @param
     * @return java.util.List<java.lang.String>
     **/
    public static List<String> getCurrentMonth() {
        List<String> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.getActualMaximum(Calendar.DATE);
        for (int i = 1; i <= day; i++) {
            String date = String.valueOf(year) + "-" + month + "-" + i;
            list.add(date);
        }
        return list;
    }

    /**
     * 判断是否有交集
     *
     * @return 下标0为开始时间 下标1为结束时间
     */
    public static ArrayList<String> getIntersectionTime(String start1, String end1, String start2, String end2) {
        Date startDateOne = DateUtil.parse(start1, "yyyy-MM-dd");
        Date endDateOne = DateUtil.parse(end1, "yyyy-MM-dd");
        Date startDateTwo = DateUtil.parse(start2, "yyyy-MM-dd");
        Date endDateTwo = DateUtil.parse(end2, "yyyy-MM-dd");
        Date maxStartDate = startDateOne;
        if (maxStartDate.before(startDateTwo)) {
            maxStartDate = startDateTwo;
        }
        Date minEndDate = endDateOne;
        if (endDateTwo.before(minEndDate)) {
            minEndDate = endDateTwo;
        }
        if (maxStartDate.before(minEndDate) || (maxStartDate.getTime() == minEndDate.getTime())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            return Lists.newArrayList(simpleDateFormat.format(maxStartDate), simpleDateFormat.format(minEndDate));
        } else {
            return null;
        }
    }

    /***
     * @Description 获取指定月份的指定日期
     *
     * <AUTHOR>
     * @Date 2022-12-15 14:01
     * @param day
     * @return java.util.Date
     **/
    public static Date getAppointDay(Date date, int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(date.getTime()) + "-" + day;
        try {
            Date parse = sdf1.parse(format);
            return parse;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return new Date();
    }

    public static Date getUpMonthAppointDay(int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        Date time = calendar.getTime();
        String format = sdf.format(time) + "-" + day;
        try {
            Date parse = sdf1.parse(format);
            return parse;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return new Date();
    }

    /**
     * 获取时间差方法
     *
     * @Des
     * <AUTHOR>
     * @Date 2022/12/21 13:28:16
     * @Param
     * @Return 日时分
     */
    public static String getDiffTime(Date endTime, Date startTime) {
        long nd = 1000 * 24 * 60 * 60;//每天毫秒数

        long nh = 1000 * 60 * 60;//每小时毫秒数

        long nm = 1000 * 60;//每分钟毫秒数

        long ns = 1000;// 每秒

        long diff = endTime.getTime() - startTime.getTime(); // 获得两个时间的毫秒时间差异

        long day = diff / nd;   // 计算差多少天

        long hour = diff % nd / nh; // 计算差多少小时

        long min = diff % nd % nh / nm;  // 计算差多少分钟

        long sec = diff % nd % nh % nm / ns;  // 计算差多少秒

        return day + "天" + hour + "小时" + min + "分钟" + sec + "秒";
    }

    /**
     * 增加天数
     * @param day 需要相加的天数
     * @param date 从 date 开始相加 天数
     * @return
     */
    public static Date addDay(Date date, int day) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        date = calendar.getTime();
        return date;
    }

    /**
     * 获取当天凌晨
     * @return
     */
    public static Date getTodayStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 计算两个 Date 时间的小时差值（保留1位小数）
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 小时差值（如 3.5 小时）
     */
    public static BigDecimal calculateHoursDifference(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("时间参数不能为 null");
        }

        // 将 Date 转换为 Instant（Java 8+）
        Instant startInstant = startDate.toInstant();
        Instant endInstant = endDate.toInstant();

        // 计算时间差
        Duration duration = Duration.between(startInstant, endInstant);

        // 转换为小时，并保留1位小数
        double diffInHours = duration.toMillis() / (1000.0 * 60 * 60);
        diffInHours = Math.round(diffInHours * 10) / 10.0; // 四舍五入保留1位小数

        return BigDecimal.valueOf(diffInHours);
    }

    /**
     * 字符串转LocalDate
     * @return
     */
    public static LocalDate getLocalDate(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(dateStr, formatter);
    }

    /**
     * 获取上个月的第一天和最后一天（格式：yyyy-MM-dd）
     * @return 字符串数组，[0]=上个月第一天，[1]=上个月最后一天
     */
    public static String[] getLastMonthRange() {
        LocalDate today = LocalDate.now();

        // 获取上个月的第一天
        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
        // 获取上个月的最后一天
        LocalDate lastDayOfLastMonth = today.minusMonths(1).withDayOfMonth(
                today.minusMonths(1).lengthOfMonth()
        );

        // 格式化为年月日字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String firstDayStr = firstDayOfLastMonth.format(formatter);
        String lastDayStr = lastDayOfLastMonth.format(formatter);

        return new String[]{firstDayStr, lastDayStr};
    }


    /**
     * 获取未来n天的数据
     * @param today 时间
     * @param day 天数
     * @param isAfter 查询之后数据
     * @return
     */
    public static List<String> getWeekDateList(LocalDate today, int day, boolean isAfter){
        // 获取当前日期
        if (today == null) {
            today = LocalDate.now();
        }
        // 创建一个列表来存储未来n天的日期
        List<String> futureDates = new ArrayList<>();
        // 创建一个日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 循环添加未来7天的日期到列表中
        for (int i = 0; i < day; i++) {
            LocalDate date = today.plusDays(isAfter?i:-i);
            String dateString = date.format(formatter);
            futureDates.add(dateString);
        }
        return futureDates;
    }

    /**
     * 校验时间是否满足
     * @param time 时间 00:35
     * @param now
     */
    public static Boolean checkTime(String time, LocalTime now, int skew) {
        //判断配置时间是否生成
        String[] split = time.split(":");
        LocalTime targetTime = LocalTime.of(Integer.parseInt(split[0]), Integer.parseInt(split[1])); // 小时，分钟
        Duration duration;
        if (now.isAfter(targetTime)) {
            duration = Duration.between(targetTime, now);
        } else {
            return false;
        }
        //将差值转换为分钟
        long minutesDiff = duration.toMinutes();
        return minutesDiff < skew;
    }
}