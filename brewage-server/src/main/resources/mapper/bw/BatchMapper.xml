<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.bw.dao.BatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.brewage.bw.entity.Batch">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId" />
        <result column="deleted" property="deleted" />
        <result column="site_num" property="siteNum" />
        <result column="production_id" property="productionId" />
        <result column="production" property="production" />
        <result column="tank_number" property="tankNumber" />
        <result column="current_quantity" property="currentQuantity" />
        <result column="batch" property="batch" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        creator_id, updater_id, deleted, site_num, production_id, production, tank_number, current_quantity, batch, state
    </sql>
    <select id="selectDeliveryBatchList" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select ddd.delivery_number legend, ddd.flow_batch batch
        from brewage.t_mp_daily_delivery_detail ddd
        where ddd.deleted = 0
        and ddd.flow_batch is not null
        <if test="batch != null and batch != ''">
            and ddd.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and ddd.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectSorghumTransferOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_st_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectSorghumShipmentOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_ss_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectSorghumProductionOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_sp_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectFlourTransferOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_ft_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectSorghumDispenseOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_sd_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectRiceTransferOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_rt_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectRiceDispenseOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_rd_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectBranProductionOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_bp_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectBranTransferOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_bt_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        left join brewage_rawmaterial_production.t_mpd_rl_management rl2 on rl2.id = od.accept_silo_id AND rl2.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectBranIssueOrderBatch" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT rl1.`name` legend, od.flow_batch batch
        from brewage_rawmaterial_production.t_mpd_bi_order_detail od
        left join brewage_rawmaterial_production.t_mpd_rl_management rl1 on rl1.id = od.send_silo_id AND rl1.deleted = 0
        where od.deleted = 0
          and rl1.`name` is not null
        <if test="batch != null and batch != ''">
            and od.flow_batch = #{batch}
        </if>
        <if test="batchList != null and batchList.size() != 0">
            and od.flow_batch IN
            <foreach collection="batchList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectNodeDetailByDelivery" resultType="com.hvisions.brewage.bw.vo.DeliveryNodeVO">
        select ddd.id, ddd.delivery_number, ddd.flow_batch, ddd.license_plate_number, ddd.srm_place_production,ddd.material_id,
               ddd.material_name, ddd.material_code, v.`name` vendor_name,v.`code` vendor_code, it.id inspectionId,
               it.inspection_order, it.actual_inspection_time, it.sampling_code, it.inspection_name, it.inspection_result
        from t_mp_daily_delivery_detail ddd
                 LEFT JOIN t_mp_delivery_item di on di.id = ddd.delivery_item_id and di.deleted = 0
                 LEFT JOIN t_mp_daily_delivery dd on dd.id = di.delivery_id and dd.deleted = 0
                 LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
                 LEFT JOIN t_mp_purchase_order po ON po.id = dd.purchase_id AND po.deleted = 0
                 LEFT JOIN t_mp_vendor v ON v.id = po.vendor_id AND v.deleted = 0
                 LEFT JOIN t_qa_inspection_task it on it.associated_document = ddd.delivery_number and it.id = (
            select max(id) from t_qa_inspection_task t where t.associated_document = ddd.delivery_number)
        where delivery_number = #{deliveryNumber}
    </select>
    <select id="selectInspectionDataByInspectionId"
            resultType="com.hvisions.quality.dto.quality.inspection.data.InspectionDataDetailDTO">
        SELECT id.id,id.value,id.indicator_type,id.indicator_name,id.entry_method,id.upper_specification_limit,id.lower_specification_limit,
               id.standard_value,id.unit,idi.item_name
        FROM t_qa_inspection_data id
                 LEFT JOIN t_qa_inspection_data_item idi ON idi.inspection_data_id = id.id
        WHERE id.deleted = 0 and id.type = "0" and id.inspection_id = #{inspectionId}
    </select>

</mapper>
