<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskPileUpDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskPileUpDetail">
    <!--@Table t_po_task_pile_up_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_pile_up_id" jdbcType="INTEGER" property="taskPileUpId" />
    <result column="total_duration" jdbcType="VARCHAR" property="totalDuration" />
    <result column="duration_steaming" jdbcType="VARCHAR" property="durationSteaming" />
    <result column="total_weight" jdbcType="VARCHAR" property="totalWeight" />
    <result column="fermentative_material_volume" jdbcType="VARCHAR" property="fermentativeMaterialVolume" />
    <result column="six_cubic_bucket_duration" jdbcType="VARCHAR" property="sixCubicBucketDuration" />
    <result column="six_cubic_bucket_id" jdbcType="VARCHAR" property="sixCubicBucketId" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="zeng_no" jdbcType="VARCHAR" property="zengNo" />
    <result column="change_bucket_start_time" jdbcType="VARCHAR" property="changeBucketStartTime" />
    <result column="change_bucket_end_time" jdbcType="VARCHAR" property="changeBucketEndTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_pile_up_id, 
    total_duration, duration_steaming, total_weight, fermentative_material_volume, six_cubic_bucket_duration, 
    six_cubic_bucket_id, start_time, end_time, pit_no, out_material_type, enter_material_type, material_layer, grain_input_type, record_time,
    steaming_task_no, iot_task_no, task_status, created_at, completed_at, zeng_no, change_bucket_start_time, change_bucket_end_time
  </sql>
</mapper>