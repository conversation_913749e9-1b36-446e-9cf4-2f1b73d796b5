<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskLoadingMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskLoading">
    <!--@Table t_po_task_loading-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="center_id" jdbcType="INTEGER" property="centerId" />
    <result column="location_id" jdbcType="INTEGER" property="locationId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="pit_order" jdbcType="VARCHAR" property="pitOrder" />
    <result column="empty_duration" jdbcType="DECIMAL" property="emptyDuration" />
    <result column="ground_temp" jdbcType="DECIMAL" property="groundTemp" />
    <result column="loading_temperature" jdbcType="VARCHAR" property="loadingTemperature" />
    <result column="loose_packing_status" jdbcType="VARCHAR" property="loosePackingStatus" />
    <result column="dense_packing_status" jdbcType="VARCHAR" property="densePackingStatus" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_no, center_id, location_id, task_name, task_type, status, pit_no, material_type,
    pit_order, empty_duration, ground_temp, loading_temperature, loose_packing_status, 
    dense_packing_status, start_time, end_time, create_time, update_time, creator_id, 
    updater_id, deleted
  </sql>

  <select id="selectMaxTaskNo" resultType="java.lang.Integer">
    SELECT
        IFNULL(CAST(SUBSTRING(task_no, -3) AS UNSIGNED), 0) AS serial_num
    FROM
        t_po_task_loading
    WHERE
        task_no LIKE CONCAT(#{typeName,jdbcType=VARCHAR}, DATE_FORMAT( CURRENT_DATE, '%Y%m%d' ), '%' )
    ORDER BY
        task_no DESC
    LIMIT 1;
  </select>

  <select id="selectPageList" resultType="com.hvisions.brewage.vo.tpo.TaskLoadingVO">
    SELECT
      a.*,
      t3.id as pitId,
      d.id as orderId,
      SUM(if(ad.id is not NULL,1,0)) as steamingBatchNo,
      SUM(if(ad.id is not NULL and ad.is_pit_confirmed=0,1,0)) as unconfirmedSteamingBatchNo,
      SUM(if(ad.id is not NULL and ad.is_pit_confirmed=1,1,0)) as confirmedSteamingBatchNo,
      b.user_name as creatorName,
      c.user_name as updaterName
    FROM
        t_po_task_loading a
      INNER JOIN t_po_workshop_pit_order d on a.pit_order=d.order_code
      LEFT JOIN t_po_task_loading_detail ad on a.id=ad.task_loading_id
      LEFT JOIN t_po_workshop_full_pit t3 ON a.pit_no = t3.full_pit_id AND t3.is_deleted = 0
      LEFT JOIN authority.sys_user b on a.creator_id=b.id
      LEFT JOIN authority.sys_user c on a.updater_id=c.id
    WHERE
      a.deleted =0
      <if test="query.centerId != null">
        and a.center_id= #{query.centerId,jdbcType=INTEGER}
      </if>

      <if test="query.locationId != null">
        and a.location_id= #{query.locationId,jdbcType=INTEGER}
      </if>

      <if test="query.taskNo != null and query.taskNo != ''">
        and a.task_no like CONCAT('%',#{query.taskNo,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.pitNo != null and query.pitNo != ''">
        and a.pit_no like CONCAT('%',#{query.pitNo,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.pitOrder != null and query.pitOrder != ''">
        and a.pit_order like CONCAT('%',#{query.pitOrder,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.status != null and query.status != ''">
        and a.`status`= #{query.status,jdbcType=VARCHAR}
      </if>

      <if test="query.startDate != null">
        and date_format(a.create_time,'%Y-%m-%d') BETWEEN date_format(#{query.startDate},'%Y-%m-%d') and date_format(#{query.endDate},'%Y-%m-%d')
      </if>
      GROUP BY a.id
    ORDER BY a.create_time,a.id
  </select>

  <select id="selectTargetPitNoList" resultType="com.hvisions.brewage.vo.tpo.TaskLoadingTargetPitNoVO">
      SELECT
          c.full_pit_id as pitNo,
          a.order_code as pitOrder,
          b.`code` as materialType,
          a.layer,
          a.center_id as centerId,
          a.location_id as locationId,
          d.task_no as taskNo,
          a.cycle_year as cycleYear,
          a.cycle_no_id as cycleNoId,
          t5.`name` as cycleNoName
      FROM
          t_po_workshop_pit_order a
              LEFT JOIN brewage_plan.t_pp_vinasse_source b ON a.vinasse_id = b.id
              INNER JOIN t_po_workshop_full_pit c ON a.pit_id = c.id
              LEFT JOIN t_po_task_loading d on a.order_code=d.pit_order and d.deleted=0
              LEFT JOIN brewage_plan.t_pp_row t5 on a.cycle_no_id = t5.id and t5.deleted=0
      WHERE
          a.is_deleted = 0 and b.deleted=0
        AND (a.pit_status =- 1 OR (a.pit_status = 0 AND b.`code` = #{materialType,jdbcType=VARCHAR}))
    </select>

  <select id="selectPagePDAList" resultType="com.hvisions.brewage.vo.tpo.TaskLoadingVO">
      SELECT
          a.*,
          po.layer,
          d.id as orderId,
          t3.id as pitId,
          SUM(if(ad.id is not NULL,1,0)) as steamingBatchNo,
          SUM(if(ad.id is not NULL and ad.is_pit_confirmed=0,1,0)) as unconfirmedSteamingBatchNo,
          SUM(if(ad.id is not NULL and ad.is_pit_confirmed=1,1,0)) as confirmedSteamingBatchNo,
          b.user_name as creatorName,
          c.user_name as updaterName
      FROM
          t_po_task_loading a
          INNER JOIN t_po_workshop_pit_order d on a.pit_order=d.order_code
          LEFT JOIN t_po_task_loading_detail ad on a.id=ad.task_loading_id
          LEFT JOIN t_po_workshop_pit_order po on a.pit_order=po.order_code and po.is_deleted=0
          LEFT JOIN t_po_workshop_full_pit t3 ON a.pit_no = t3.full_pit_id AND t3.is_deleted = 0
          LEFT JOIN authority.sys_user b on a.creator_id=b.id
          LEFT JOIN authority.sys_user c on a.updater_id=c.id
      WHERE
      a.deleted =0
          <if test="query.centerId != null">
              and a.center_id= #{query.centerId,jdbcType=INTEGER}
          </if>

          <if test="query.locationId != null">
              and a.location_id= #{query.locationId,jdbcType=INTEGER}
          </if>

          <if test="query.taskNo != null and query.taskNo != ''">
              and a.task_no like CONCAT('%',#{query.taskNo,jdbcType=VARCHAR},'%')
          </if>

          <if test="query.pitNo != null and query.pitNo != ''">
              and a.pit_no like CONCAT('%',#{query.pitNo,jdbcType=VARCHAR},'%')
          </if>

          <if test="query.pitOrder != null and query.pitOrder != ''">
              and a.pit_order like CONCAT('%',#{query.pitOrder,jdbcType=VARCHAR},'%')
          </if>

          <if test="query.status != null and query.status != '' and query.status =='待执行'">
              and a.`status` in('待执行','执行中')
          </if>

          <if test="query.status != null and query.status != '' and query.status =='已完成'">
              and a.`status` ='已完成'
          </if>

          <if test="query.startDate != null">
              and date_format(a.create_time,'%Y-%m-%d') BETWEEN date_format(#{query.startDate},'%Y-%m-%d') and date_format(#{query.endDate},'%Y-%m-%d')
          </if>
      GROUP BY a.id
      ORDER BY a.create_time,a.id
    </select>

  <select id="selectSourcePitNoList" resultType="com.hvisions.brewage.vo.tpo.TaskLoadingSourcePitNoVO">
      SELECT
          DISTINCT
          b.id as pitId,
          b.full_pit_id as pitNo
      FROM
          t_po_task_loading_detail a
              INNER JOIN t_po_workshop_full_pit b ON ( a.source_pit_no = b.pid_fir OR a.source_pit_no = b.pid_sec )
      WHERE
          a.deleted = 0 AND b.is_deleted =0 and a.task_loading_id=#{id,jdbcType=INTEGER}
    </select>

  <select id="selectPageIdentifyList" resultType="com.hvisions.brewage.vo.tpo.IdentifyVO">
      SELECT
          b.iot_task_no,
          b.steaming_task_no,
          b.source_pit_no,
          b.target_pit_no,
          b.out_material_type,
          b.enter_material_type,
          b.spreader_no,
          b.spread_to_pit_duration,
          b.record_time
      FROM
          `t_po_task_loading` a
              INNER JOIN t_po_task_loading_detail b ON a.id = b.task_loading_id
      WHERE
          a.deleted = 0 AND b.deleted = 0 AND b.is_identify = 0	AND a.id =#{query.id,jdbcType=INTEGER}
    </select>
</mapper>