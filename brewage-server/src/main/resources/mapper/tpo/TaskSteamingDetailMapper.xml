<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskSteamingDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskSteamingDetail">
    <!--@Table t_po_task_steaming_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_steaming_id" jdbcType="INTEGER" property="taskSteamingId" />
    <result column="steaming_time" jdbcType="VARCHAR" property="steamingTime" />
    <result column="steam_usage_duration" jdbcType="VARCHAR" property="steamUsageDuration" />
    <result column="flow_measurement_usage" jdbcType="VARCHAR" property="flowMeasurementUsage" />
    <result column="one_measure_water_weight" jdbcType="VARCHAR" property="oneMeasureWaterWeight" />
    <result column="two_measure_water_weight" jdbcType="VARCHAR" property="twoMeasureWaterWeight" />
    <result column="one_measure_water_temperature" jdbcType="VARCHAR" property="oneMeasureWaterTemperature" />
    <result column="two_measure_water_temperature" jdbcType="VARCHAR" property="twoMeasureWaterTemperature" />
    <result column="temperature_measuring_bucket" jdbcType="VARCHAR" property="temperatureMeasuringBucket" />
    <result column="measure_water_duration" jdbcType="VARCHAR" property="measureWaterDuration" />
    <result column="measure_water_interval_duration" jdbcType="VARCHAR" property="measureWaterIntervalDuration" />
    <result column="bottom_pot_water_emission_number" jdbcType="VARCHAR" property="bottomPotWaterEmissionNumber" />
    <result column="bottom_pot_water_discharge" jdbcType="VARCHAR" property="bottomPotWaterDischarge" />
    <result column="bottom_pot_water_total_discharge" jdbcType="VARCHAR" property="bottomPotWaterTotalDischarge" />
    <result column="bottom_pot_water_level" jdbcType="VARCHAR" property="bottomPotWaterLevel" />
    <result column="huizheng_run_duration" jdbcType="VARCHAR" property="huizhengRunDuration" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="extraction_time" jdbcType="VARCHAR" property="extractionTime" />
    <result column="one_measure_water_start_time" jdbcType="VARCHAR" property="oneMeasureWaterStartTime" />
    <result column="one_measure_water_end_time" jdbcType="VARCHAR" property="oneMeasureWaterEndTime" />
    <result column="two_measure_water_start_time" jdbcType="VARCHAR" property="twoMeasureWaterStartTime" />
    <result column="two_measure_water_end_time" jdbcType="VARCHAR" property="twoMeasureWaterEndTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_steaming_id, 
    steaming_time, steam_usage_duration, flow_measurement_usage, one_measure_water_weight,
    two_measure_water_weight, one_measure_water_temperature, two_measure_water_temperature, 
    temperature_measuring_bucket, measure_water_duration, measure_water_interval_duration, 
    bottom_pot_water_emission_number, bottom_pot_water_discharge, bottom_pot_water_total_discharge, 
    bottom_pot_water_level, huizheng_run_duration, pit_no, out_material_type, enter_material_type, 
    material_layer, grain_input_type, steaming_task_no, iot_task_no, task_status, created_at, record_time,
    completed_at
  </sql>
</mapper>