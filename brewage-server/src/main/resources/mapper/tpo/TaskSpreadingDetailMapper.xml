<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskSpreadingDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskSpreadingDetail">
    <!--@Table t_po_task_spreading_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_spreading_id" jdbcType="INTEGER" property="taskSpreadingId" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="source_pit_no" jdbcType="VARCHAR" property="sourcePitNo" />
    <result column="target_pit_no" jdbcType="VARCHAR" property="targetPitNo" />
    <result column="spreader_no" jdbcType="VARCHAR" property="spreaderNo" />
    <result column="floor_temp" jdbcType="VARCHAR" property="floorTemp" />
    <result column="humidity" jdbcType="VARCHAR" property="humidity" />
    <result column="spreading_start_time" jdbcType="VARCHAR" property="spreadingStartTime" />
    <result column="spreading_end_time" jdbcType="VARCHAR" property="spreadingEndTime" />
    <result column="koji_addition_start_time" jdbcType="VARCHAR" property="kojiAdditionStartTime" />
    <result column="koji_addition_end_time" jdbcType="VARCHAR" property="kojiAdditionEndTime" />
    <result column="hopper_temp" jdbcType="VARCHAR" property="hopperTemp" />
    <result column="hopper_temp_time" jdbcType="VARCHAR" property="hopperTempTime" />
    <result column="hopper_temp_user_id" jdbcType="INTEGER" property="hopperTempUserId" />
    <result column="hopper_temp_user" jdbcType="VARCHAR" property="hopperTempUser" />
    <result column="feeder_speed" jdbcType="VARCHAR" property="feederSpeed" />
    <result column="conveyor_speed" jdbcType="VARCHAR" property="conveyorSpeed" />
    <result column="fan_quantity" jdbcType="VARCHAR" property="fanQuantity" />
    <result column="yeast_feeder_speed" jdbcType="VARCHAR" property="yeastFeederSpeed" />
    <result column="weight_before_koji" jdbcType="VARCHAR" property="weightBeforeKoji" />
    <result column="weight_after_koji" jdbcType="VARCHAR" property="weightAfterKoji" />
    <result column="yeast_fineness" jdbcType="VARCHAR" property="yeastFineness" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="duration" jdbcType="VARCHAR" property="duration" />
    <result column="yeast_adding_time" jdbcType="VARCHAR" property="yeastAddingTime" />
    <result column="yeast_amount" jdbcType="VARCHAR" property="yeastAmount" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_spreading_id, pit_no, out_material_type, enter_material_type, material_layer, 
    grain_input_type, steaming_task_no, iot_task_no, task_status, created_at, completed_at, 
    source_pit_no, target_pit_no, spreader_no, floor_temp, humidity, spreading_start_time, 
    spreading_end_time, koji_addition_start_time, koji_addition_end_time, hopper_temp, 
    hopper_temp_time, hopper_temp_user_id, hopper_temp_user, feeder_speed, conveyor_speed, 
    fan_quantity, yeast_feeder_speed, weight_before_koji, weight_after_koji, yeast_fineness, 
    record_time, data_source, remarks, create_time, update_time, creator_id, updater_id, 
    deleted, duration, yeast_adding_time, yeast_amount, cellar_status
  </sql>

  <select id="selectWorkshopPitOrderTwo" resultType="java.lang.String">
    SELECT
    a.order_code
    FROM
      t_po_workshop_pit_order a
        INNER JOIN t_po_workshop_full_pit b ON a.pit_id = b.id
        INNER JOIN brewage_plan.t_pp_vinasse_source c ON a.vinasse_id = c.id
    WHERE
      a.is_deleted = 0 AND a.pit_status = 0
      AND b.full_pit_id = #{pitNo,jdbcType=VARCHAR} AND c.`code` = #{enterMaterialType,jdbcType=VARCHAR}
      limit 1
  </select>

  <select id="selectWorkshopPitOrderOne" resultType="java.lang.String">
    SELECT
        a.order_code
    FROM
        t_po_workshop_pit_order a
          INNER JOIN t_po_workshop_full_pit b ON a.pit_id = b.id
    WHERE
      a.is_deleted = 0 AND a.pit_status = -1
      AND b.full_pit_id = #{pitNo,jdbcType=VARCHAR} AND a.vinasse_id is null
      limit 1
  </select>

  <select id="selectWorkshopPitOrderLayer" resultType="java.lang.Integer">
    SELECT
        layer
    FROM
        t_po_workshop_pit_order a
            INNER JOIN t_po_workshop_full_pit b ON a.pit_id = b.id
    WHERE
        a.is_deleted = 0 AND a.pit_status IN ( -1,0,1)
        AND b.full_pit_id = #{pitNo,jdbcType=VARCHAR}
    limit 1
  </select>

  <select id="selectVinasseId" resultType="java.lang.Integer">
    SELECT id FROM brewage_plan.t_pp_vinasse_source WHERE deleted=0 and production_base_id=1 and `code`=#{materialType,jdbcType=VARCHAR}
  </select>

  <select id="selectMaterialType" resultType="java.lang.String">
    SELECT `code` FROM brewage_plan.t_pp_vinasse_source WHERE deleted=0 and production_base_id=1 and id=#{vinasseId,jdbcType=INTEGER}
  </select>

  <select id="selectOrderLintName" resultType="java.lang.String">
    SELECT
      t3.area AS area
    FROM
      t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_full_pit t2 ON t1.pit_id = t2.id AND t2.is_deleted = 0
        LEFT JOIN t_po_workshop_pit t3 ON t2.full_pit_id = t3.full_pit_id AND t3.is_deleted = 0
    WHERE
      t1.is_deleted = 0 and t1.order_code=#{orderCode,jdbcType=VARCHAR}
    LIMIT 1
  </select>

  <select id="selectQdMaterialCode" resultType="com.hvisions.brewage.vo.tpo.MachineBindVO">
    SELECT
      d.material_code as materialCode,
      b.id as machineBindLogId
    FROM
      t_po_colling_machine_bind a
        INNER JOIN t_po_colling_machine_bind_log b ON a.id = b.colling_machine_id
        left JOIN materials.hv_bm_material d ON b.material_id = d.id
    WHERE a.center_id=#{centerId,jdbcType=INTEGER} and a.location_id=#{locationId,jdbcType=INTEGER} and a.iot_colling_machine_code=#{spreaderNo,jdbcType=VARCHAR}
    ORDER BY
      b.id DESC
    LIMIT 1
  </select>

  <select id="selectQuDouId" resultType="java.lang.Integer">
    SELECT
        b.id
    FROM
        t_po_colling_machine_bind a
          INNER JOIN t_wp_parameter b ON a.qu_hopper_code = b.barcode
    WHERE a.center_id=#{centerId,jdbcType=INTEGER} and a.location_id=#{locationId,jdbcType=INTEGER} and a.iot_colling_machine_code=#{spreaderNo,jdbcType=VARCHAR}
    LIMIT 1
  </select>

  <update id="updateMachineBindLog">
    UPDATE t_po_colling_machine_bind_log
    SET qu_batch = #{batch,jdbcType=VARCHAR} , material_id = #{materialId,jdbcType=INTEGER}
    WHERE
      id = #{machineBindLogId,jdbcType=INTEGER}
  </update>

  <select id="selectMaterialIdByCode" resultType="java.lang.String">
    select material_code FROM materials.hv_bm_material where  id=#{materialId,jdbcType=INTEGER}
  </select>
</mapper>