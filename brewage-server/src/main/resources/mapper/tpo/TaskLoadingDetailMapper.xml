<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskLoadingDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskLoadingDetail">
    <!--@Table t_po_task_loading_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_loading_id" jdbcType="INTEGER" property="taskLoadingId" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="source_pit_no" jdbcType="VARCHAR" property="sourcePitNo" />
    <result column="target_pit_no" jdbcType="VARCHAR" property="targetPitNo" />
    <result column="spreader_no" jdbcType="VARCHAR" property="spreaderNo" />
    <result column="spread_to_pit_duration" jdbcType="VARCHAR" property="spreadToPitDuration" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="ground_temp" jdbcType="DECIMAL" property="groundTemp" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="is_pit_confirmed" jdbcType="BIT" property="isPitConfirmed" />
    <result column="is_identify" jdbcType="BIT" property="isIdentify" />
    <result column="confirmed_by_id" jdbcType="INTEGER" property="confirmedById" />
    <result column="confirmation_time" jdbcType="TIMESTAMP" property="confirmationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_loading_id, pit_no, out_material_type, enter_material_type, material_layer, 
    grain_input_type, steaming_task_no, iot_task_no, task_status, created_at, completed_at, 
    source_pit_no, target_pit_no, spreader_no, spread_to_pit_duration, record_time, ground_temp, 
    data_source, remarks, is_pit_confirmed, is_identify, confirmed_by_id, confirmation_time, 
    create_time, update_time, creator_id, updater_id, deleted
  </sql>

  <select id="selectPageList" resultType="com.hvisions.brewage.vo.tpo.TaskLoadingDetailVO">
    SELECT
      a.*,
      if(b.id is NULL,0,1) as isSteamingTask,
      if(b.id is NULL,'缺少甑口整包数据，待IOT上传或手动补全再操作！','甑口任务数据存在，可以入窖确认') as isSteamingTaskRemarks
    FROM
      t_po_task_loading_detail a
        LEFT JOIN t_po_workshop_pit_order_pot_task b on a.iot_task_no=b.iot_task_no and b.is_deleted=0 and b.is_callback=1
    WHERE a.deleted=0 and a.task_loading_id=#{query.taskLoadingId,jdbcType=INTEGER}
    <if test="query.isPitConfirmed != null">
      and a.is_pit_confirmed=#{query.isPitConfirmed,jdbcType=BOOLEAN}
    </if>
    GROUP BY a.id
    </select>
</mapper>