<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskWinePickingDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskWinePickingDetail">
    <!--@Table t_po_task_wine_picking_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_wine_picking_id" jdbcType="INTEGER" property="taskWinePickingId" />
    <result column="weight_flowing_wine_one" jdbcType="VARCHAR" property="weightFlowingWineOne" />
    <result column="weight_flowing_wine_two" jdbcType="VARCHAR" property="weightFlowingWineTwo" />
    <result column="weight_flowing_wine_three" jdbcType="VARCHAR" property="weightFlowingWineThree" />
    <result column="weight_flowing_wine_four" jdbcType="VARCHAR" property="weightFlowingWineFour" />
    <result column="alcohol_content_one" jdbcType="VARCHAR" property="alcoholContentOne" />
    <result column="alcohol_content_two" jdbcType="VARCHAR" property="alcoholContentTwo" />
    <result column="alcohol_content_three" jdbcType="VARCHAR" property="alcoholContentThree" />
    <result column="alcohol_content_four" jdbcType="VARCHAR" property="alcoholContentFour" />
    <result column="proportion_flowing_alcohol_one" jdbcType="VARCHAR" property="proportionFlowingAlcoholOne" />
    <result column="proportion_flowing_alcohol_two" jdbcType="VARCHAR" property="proportionFlowingAlcoholTwo" />
    <result column="proportion_flowing_alcohol_three" jdbcType="VARCHAR" property="proportionFlowingAlcoholThree" />
    <result column="proportion_flowing_alcohol_four" jdbcType="VARCHAR" property="proportionFlowingAlcoholFour" />
    <result column="flowing_wine_duration" jdbcType="VARCHAR" property="flowingWineDuration" />
    <result column="duration_one" jdbcType="VARCHAR" property="durationOne" />
    <result column="duration_two" jdbcType="VARCHAR" property="durationTwo" />
    <result column="duration_three" jdbcType="VARCHAR" property="durationThree" />
    <result column="duration_four" jdbcType="VARCHAR" property="durationFour" />
    <result column="flowing_wine_temperature" jdbcType="VARCHAR" property="flowingWineTemperature" />
    <result column="flowing_wine_speed" jdbcType="VARCHAR" property="flowingWineSpeed" />
    <result column="cover_plate_flowing_wine_duration" jdbcType="VARCHAR" property="coverPlateFlowingWineDuration" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="cover_time" jdbcType="VARCHAR" property="coverTime" />
    <result column="one_stage_wine_start_time" jdbcType="VARCHAR" property="oneStageWineStartTime" />
    <result column="two_stage_wine_start_time" jdbcType="VARCHAR" property="twoStageWineStartTime" />
    <result column="three_stage_wine_start_time" jdbcType="VARCHAR" property="threeStageWineStartTime" />
    <result column="cocktail_start_time" jdbcType="VARCHAR" property="cocktailStartTime" />
    <result column="cocktail_end_time" jdbcType="VARCHAR" property="cocktailEndTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_wine_picking_id, 
    weight_flowing_wine_one, weight_flowing_wine_two, weight_flowing_wine_three, weight_flowing_wine_four, 
    alcohol_content_one, alcohol_content_two, alcohol_content_three, alcohol_content_four, 
    proportion_flowing_alcohol_one, proportion_flowing_alcohol_two, proportion_flowing_alcohol_three, 
    proportion_flowing_alcohol_four, flowing_wine_duration, duration_one, duration_two, 
    duration_three, duration_four, flowing_wine_temperature, flowing_wine_speed, cover_plate_flowing_wine_duration, 
    pit_no, record_time, out_material_type, enter_material_type, material_layer, grain_input_type,
    steaming_task_no, iot_task_no, task_status, created_at, completed_at
  </sql>
</mapper>