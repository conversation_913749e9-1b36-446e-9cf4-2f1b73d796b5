<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskGrainLubricationMapper">
  <resultMap id="WorkshopPitOrderQuery"
             type="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderQueryVO">
    <result property="formulaName" column="formula_name"/>
    <result property="vinasseName" column="vinasse_name"/>
  </resultMap>
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskGrainLubrication">
    <!--@Table t_po_task_grain_lubrication-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="iot_no" jdbcType="VARCHAR" property="iotNo" />
    <result column="center_id" jdbcType="INTEGER" property="centerId" />
    <result column="location_id" jdbcType="INTEGER" property="locationId" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="pit_order" jdbcType="VARCHAR" property="pitOrder" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="vinasse_name" jdbcType="VARCHAR" property="vinasseName" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_no, task_type, start_time, end_time,
    iot_no, center_id, location_id, pit_no, pit_order, "state", vinasse_name
  </sql>

  <select id="selectMaxTaskNo" resultType="java.lang.Integer">
    SELECT
    IFNULL(CAST(SUBSTRING(task_no, -3) AS UNSIGNED), 0) AS serial_num
    FROM
    t_po_task_grain_lubrication
    WHERE
    task_no LIKE CONCAT(#{typeName,jdbcType=VARCHAR}, DATE_FORMAT( CURRENT_DATE, '%Y%m%d' ), '%' )
    ORDER BY
    task_no DESC
    LIMIT 1;
  </select>

  <select id="selectPageList" resultType="com.hvisions.brewage.vo.tpo.TaskGrainLubricationVO">
    SELECT
    a.*,a.state as status,
    b.user_name as creatorName,
    c.user_name as updaterName,
    d.`name` as centerName,
    e.`name` as locationName
    FROM
    t_po_task_grain_lubrication a
    LEFT JOIN authority.sys_user b on a.creator_id=b.id
    LEFT JOIN authority.sys_user c on a.updater_id=c.id
    LEFT JOIN equipment.hv_bm_location d on d.id = a.center_id
    LEFT JOIN equipment.hv_bm_location e on e.id = a.location_id
    WHERE
    deleted =0
    <if test="query.centerId != null">
      and a.center_id= #{query.centerId,jdbcType=INTEGER}
    </if>

    <if test="query.locationId != null">
      and a.location_id= #{query.locationId,jdbcType=INTEGER}
    </if>
    <if test="query.vinasseName != null and query.vinasseName != ''">
      and a.vinasse_name like CONCAT('%',#{query.vinasseName,jdbcType=VARCHAR},'%')
    </if>
    <if test="query.taskNo != null and query.taskNo != ''">
      and a.task_no like CONCAT('%',#{query.taskNo,jdbcType=VARCHAR},'%')
    </if>

    <if test="query.pitNo != null and query.pitNo != ''">
      and a.pit_no like CONCAT('%',#{query.pitNo,jdbcType=VARCHAR},'%')
    </if>

    <if test="query.pitOrder != null and query.pitOrder != ''">
      and a.pit_order like CONCAT('%',#{query.pitOrder,jdbcType=VARCHAR},'%')
    </if>

    <if test="query.state != null">
      and a.`state`= #{query.state,jdbcType=INTEGER}
    </if>

    <if test="query.startDate != null">
      and date_format(a.create_time,'%Y-%m-%d') BETWEEN date_format(#{query.startDate},'%Y-%m-%d') and date_format(#{query.endDate},'%Y-%m-%d')
    </if>

    ORDER BY a.create_time,a.id
  </select>

  <select id="selectPitNo" resultType="java.lang.String">
    select full_pit_id from t_po_workshop_full_pit where is_deleted=0 and pid_fir= #{pitNo,jdbcType=VARCHAR} or pid_sec=#{pitNo,jdbcType=VARCHAR}
  </select>

  <select id="getWorkshopPitOrderQuery" resultMap="WorkshopPitOrderQuery">
    SELECT distinct t1.id,
    t1.center_id,                                                    -- 中心id
    t1.location_id,                                                  -- 车间id
    <!--                        (SELECT dp.day_play_id-->
    <!--                         from t_po_workshop_pit_day_plan dp-->
    <!--                                      LEFT JOIN t_po_workshop_pit_order po on dp.order_id =-->
    <!--                                                                              po.id-->
    <!--                         WHERE po.id = t1.id-->
    <!--                         ORDER BY plan_date-->
    <!--                         limit 0,1)                                as day_play_id,       &#45;&#45; 日计划编号-->
    t1.order_code,-- 订单编号
    t1.layer,                                                        -- 层次
    t1.order_category_id,                                            -- 订单类型id
    t6.order_category_name,                                          -- 订单类型名称
    t1.pit_id,                                                       -- 连窖号id
    t4.full_pit_id,                                                  -- 连窖号
    t1.formula_id,                                                   -- 配方id
    t2.name                                    AS formula_name,-- 配方名称
    t1.vinasse_id,                                                   -- 糟源id
    t3.code                                    AS vinasse_name,-- 糟源名称
    t1.order_status,-- 订单状态
    t1.pit_status,                                                   -- 窖池状态
    t1.cycle_no_id,-- 糟赔排次id
    t7.name                                    as cycle_no_name,     -- 糟赔排次名称
    t1.seal_confirm_time,                                            -- 封窖完成时间
    t1.turn_over_finish_time,                                        -- 翻窖完成时间
    t1.out_pit_finish_time,                                          -- 起窖完成时间
    t1.empty_start_time,                                             -- 空窖起始时间
    t1.open_pit_finish_time,                                         -- 开窖完成时间
    t1.handin_finish_time,                                           -- 交酒完成时间
    IF(t1.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time),
    TO_DAYS(t1.out_pit_finish_time) -
    TO_DAYS(t1.seal_confirm_time))          AS fermentation_days, -- 发酵天数（修改成用状态判断）
    (ifnull(t1.in_pit_num, 0) + (select ifnull(SUM(in_pot_num_exc), 0)
    from t_po_workshop_pit_order_sap
    where order_code_id =
    t1.id)) as in_pit_num,
    (select TRUNCATE(SUM(to60_quantity), 2)
    from t_po_workshop_receive_wine_order wo
    LEFT JOIN t_po_workshop_pit_order_sap pos on wo.pit_order_id = pos.id
    where pos.order_code_id = t1.id
    )                                  as to60_quantity,
    t1.collapse_choose_pit_id,
    t8.full_pit_id                             as collapse_choose_pit
    FROM t_po_workshop_pit_order t1
    LEFT JOIN brewage_plan.t_pp_formula t2 ON t1.formula_id = t2.id
    LEFT JOIN brewage_plan.t_pp_vinasse_source t3 on t1.vinasse_id = t3.id
    LEFT JOIN t_po_workshop_full_pit t4 on t1.pit_id = t4.id
    LEFT JOIN t_po_order_type t6 on t1.order_category_id = t6.id
    LEFT JOIN brewage_plan.t_pp_row t7 on t1.cycle_no_id = t7.id
    LEFT JOIN t_po_workshop_full_pit t8 on t1.collapse_choose_pit_id = t8.id
    LEFT JOIN t_po_workshop_pit_order_sap t9 on t1.id = t9.order_code_id
    where t1.is_deleted = 0
    <if test="isMove == true">
      and t1.collapse_choose_pit_id is not null
    </if>
    <if test="isMove == false">
      and t1.collapse_choose_pit_id is null
    </if>
    <if test="orderCode != null">
      and t1.order_code like concat('%', #{orderCode}, '%')
    </if>
    <if test="centerId != null">
      and t1.center_id = #{centerId}
    </if>
    <if test="locationId != null">
      and t1.location_id = #{locationId}
    </if>
    <if test="vinasseId != null">
      and t1.vinasse_id = #{vinasseId}
    </if>
    <if test="vinasseName != null">
      and t3.code = #{vinasseName}
    </if>
    <if test="cycleYear != null">
      and t1.cycle_year = #{cycleYear}
    </if>
    <if test="cycleNoId != null">
      and t1.cycle_no_id = #{cycleNoId}
    </if>
    <if test="fullPitId != null &amp;&amp; fullPitId != ''">
      and t4.full_pit_id like concat('%', #{fullPitId}, '%')
    </if>
    <if test="pitStatus != null">
      and t1.pit_status = #{pitStatus}
    </if>
    <if test="orderStatus != null &amp;&amp; executionTimeStart == null">
      and t1.order_status = #{orderStatus}
    </if>
    <if test="sapOrderCode != null">
      and t9.sap_order_code like concat('%', #{sapOrderCode}, '%')
    </if>

    ORDER BY t1.create_time desc limit 1
  </select>

</mapper>