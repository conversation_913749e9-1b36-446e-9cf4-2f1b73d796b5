<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskShangzhengDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskShangzhengDetail">
    <!--@Table t_po_task_shangzheng_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_shangzheng_id" jdbcType="INTEGER" property="taskShangzhengId" />
    <result column="zeng_no" jdbcType="VARCHAR" property="zengNo" />
    <result column="preparation_time" jdbcType="VARCHAR" property="preparationTime" />
    <result column="moisture_content" jdbcType="VARCHAR" property="moistureContent" />
    <result column="duration_of_steaming" jdbcType="VARCHAR" property="durationOfSteaming" />
    <result column="steam_pressure_depth" jdbcType="VARCHAR" property="steamPressureDepth" />
    <result column="volume_of_fermented_grains" jdbcType="VARCHAR" property="volumeOfFermentedGrains" />
    <result column="shangzheng_layer" jdbcType="VARCHAR" property="shangzhengLayer" />
    <result column="raise_the_height" jdbcType="DOUBLE" property="raiseTheHeight" />
    <result column="robot_rotation_speed" jdbcType="DOUBLE" property="robotRotationSpeed" />
    <result column="delivery_duration" jdbcType="VARCHAR" property="deliveryDuration" />
    <result column="feeding_machine_duration" jdbcType="VARCHAR" property="feedingMachineDuration" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="take_it_start_time" jdbcType="VARCHAR" property="takeItStartTime" />
    <result column="shangzheng_start_time" jdbcType="VARCHAR" property="shangzhengStartTime" />
    <result column="shangzheng_end_time" jdbcType="VARCHAR" property="shangzhengEndTime" />
    <result column="steam_valve_opening" jdbcType="VARCHAR" property="steamValveOpening" />
    <result column="box_cover_time" jdbcType="VARCHAR" property="boxCoverTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_shangzheng_id, 
    zeng_no, preparation_time, moisture_content, duration_of_steaming, steam_pressure_depth, 
    volume_of_fermented_grains, shangzheng_layer, raise_the_height, robot_rotation_speed, 
    delivery_duration, feeding_machine_duration, pit_no, record_time, out_material_type, enter_material_type,
    material_layer, grain_input_type, steaming_task_no, iot_task_no, task_status, created_at, 
    completed_at
  </sql>
</mapper>