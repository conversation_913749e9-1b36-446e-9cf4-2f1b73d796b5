<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskSpreadingMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskSpreading">
    <!--@Table t_po_task_spreading-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="center_id" jdbcType="INTEGER" property="centerId" />
    <result column="location_id" jdbcType="INTEGER" property="locationId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="pit_order" jdbcType="VARCHAR" property="pitOrder" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_no, center_id, location_id, task_name, task_type, status, cellar_status,
    pit_no, material_type, pit_order, start_time, end_time, create_time, update_time, 
    creator_id, updater_id, deleted
  </sql>

  <select id="selectMaxTaskNo" resultType="java.lang.Integer">
    SELECT
        IFNULL(CAST(SUBSTRING(task_no, -3) AS UNSIGNED), 0) AS serial_num
    FROM
        t_po_task_maintain
    WHERE
        task_no LIKE CONCAT(#{typeName,jdbcType=VARCHAR}, DATE_FORMAT( CURRENT_DATE, '%Y%m%d' ), '%' )
    ORDER BY
        task_no DESC
    LIMIT 1;
  </select>

  <select id="selectPageList" resultType="com.hvisions.brewage.vo.tpo.TaskSpreadingVO">
    SELECT
      a.*,a.material_type as vinasseName,
      b.user_name as creatorName,
      c.user_name as updaterName,
      SUM(d.yeast_amount) as yeastAmountTotal
    FROM
      t_po_task_spreading a
      left join t_po_task_spreading_detail d on a.id=d.task_spreading_id and d.deleted=0
        LEFT JOIN authority.sys_user b on a.creator_id=b.id
        LEFT JOIN authority.sys_user c on a.updater_id=c.id
    WHERE
        a.deleted =0
      <if test="query.centerId != null">
        and a.center_id= #{query.centerId,jdbcType=INTEGER}
      </if>

      <if test="query.locationId != null">
        and a.location_id= #{query.locationId,jdbcType=INTEGER}
      </if>

      <if test="query.taskNo != null and query.taskNo != ''">
        and a.task_no like CONCAT('%',#{query.taskNo,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.pitNo != null and query.pitNo != ''">
        and a.pit_no like CONCAT('%',#{query.pitNo,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.pitOrder != null and query.pitOrder != ''">
        and a.pit_order like CONCAT('%',#{query.pitOrder,jdbcType=VARCHAR},'%')
      </if>

      <if test="query.status != null and query.status != ''">
        and a.`status`= #{query.status,jdbcType=VARCHAR}
      </if>

      <if test="query.startDate != null">
        and date_format(a.create_time,'%Y-%m-%d') BETWEEN date_format(#{query.startDate},'%Y-%m-%d') and date_format(#{query.endDate},'%Y-%m-%d')
      </if>
    GROUP BY a.id
    ORDER BY a.create_time,a.id
  </select>

  <select id="selectPitNo" resultType="java.lang.String">
    select full_pit_id from t_po_workshop_full_pit where is_deleted=0 and (pid_fir= #{pitNo,jdbcType=VARCHAR} or pid_sec=#{pitNo,jdbcType=VARCHAR})
  </select>

  <select id="selectPitNoReTheRow" resultType="java.lang.String">
    select the_row from t_po_workshop_pit where is_deleted=0 and pit_code =  #{targetPitNo,jdbcType=VARCHAR} limit 1
    </select>
</mapper>