<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskDishMixingDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskDishMixingDetail">
    <!--@Table t_po_task_dish_mixing_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_dish_mixing_id" jdbcType="INTEGER" property="taskDishMixingId" />
    <result column="single_container_rice_husk_setting_value" jdbcType="VARCHAR" property="singleContainerRiceHuskSettingValue" />
    <result column="single_container_rice_husk_actual_value" jdbcType="VARCHAR" property="singleContainerRiceHuskActualValue" />
    <result column="transport_bran_start_time" jdbcType="VARCHAR" property="transportBranStartTime" />
    <result column="transport_bran_end_time" jdbcType="VARCHAR" property="transportBranEndTime" />
    <result column="mixing_duration" jdbcType="VARCHAR" property="mixingDuration" />
    <result column="tilt_angle_of_mixer" jdbcType="VARCHAR" property="tiltAngleOfMixer" />
    <result column="mixing_speed" jdbcType="DOUBLE" property="mixingSpeed" />
    <result column="grain_addition_duration" jdbcType="VARCHAR" property="grainAdditionDuration" />
    <result column="duration_of_adding_dregs" jdbcType="VARCHAR" property="durationOfAddingDregs" />
    <result column="duration_of_adding_cooked_rice_husks" jdbcType="VARCHAR" property="durationOfAddingCookedRiceHusks" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="zeng_no" jdbcType="VARCHAR" property="zengNo" />
    <result column="add_grain_start_time" jdbcType="VARCHAR" property="addGrainStartTime" />
    <result column="add_grain_end_time" jdbcType="VARCHAR" property="addGrainEndTime" />
    <result column="add_dregs_start_time" jdbcType="VARCHAR" property="addDregsStartTime" />
    <result column="add_dregs_end_time" jdbcType="VARCHAR" property="addDregsEndTime" />
    <result column="add_cooked_rice_husks_start_time" jdbcType="VARCHAR" property="addCookedRiceHusksStartTime" />
    <result column="add_cooked_rice_husks_end_time" jdbcType="VARCHAR" property="addCookedRiceHusksEndTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, record_time, task_dish_mixing_id,
    single_container_rice_husk_setting_value, single_container_rice_husk_actual_value, 
    transport_bran_start_time, transport_bran_end_time, mixing_duration, tilt_angle_of_mixer, 
    mixing_speed, grain_addition_duration, duration_of_adding_dregs, duration_of_adding_cooked_rice_husks, 
    pit_no, out_material_type, enter_material_type, material_layer, grain_input_type, 
    steaming_task_no, iot_task_no, task_status, created_at, completed_at
  </sql>
</mapper>