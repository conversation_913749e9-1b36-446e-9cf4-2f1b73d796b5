<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskSistillationDetailMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskSistillationDetail">
    <!--@Table t_po_task_sistillation_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="task_sistillation_id" jdbcType="INTEGER" property="taskSistillationId" />
    <result column="steam_valve_opening" jdbcType="VARCHAR" property="steamValveOpening" />
    <result column="total_steam_flow_rate" jdbcType="VARCHAR" property="totalSteamFlowRate" />
    <result column="steam_flow_rate" jdbcType="VARCHAR" property="steamFlowRate" />
    <result column="steam_pressure" jdbcType="VARCHAR" property="steamPressure" />
    <result column="steam_consumption" jdbcType="VARCHAR" property="steamConsumption" />
    <result column="circulating_water_usage" jdbcType="VARCHAR" property="circulatingWaterUsage" />
<!--    <result column="circulating_water_return_temperature" jdbcType="VARCHAR" property="circulatingWaterReturnTemperature" />
    <result column="bottom_pot water_temperature" jdbcType="VARCHAR" property="bottomPotWaterTemperature" />-->
    <result column="clear_water_setting_value" jdbcType="VARCHAR" property="clearWaterSettingValue" />
    <result column="ysage_of_circulating_water" jdbcType="VARCHAR" property="ysageOfCirculatingWater" />
    <result column="yellow_water_set_value" jdbcType="VARCHAR" property="yellowWaterSetValue" />
    <result column="yellow_water_usage" jdbcType="VARCHAR" property="yellowWaterUsage" />
    <result column="yellow_water_alcohol_content" jdbcType="VARCHAR" property="yellowWaterAlcoholContent" />
    <result column="tail_wine_setting_value" jdbcType="VARCHAR" property="tailWineSettingValue" />
    <result column="tail_wine_usage" jdbcType="VARCHAR" property="tailWineUsage" />
    <result column="tail_wine_alcohol_content" jdbcType="VARCHAR" property="tailWineAlcoholContent" />
    <result column="hj1_set_value" jdbcType="VARCHAR" property="hj1SetValue" />
    <result column="hj1_single_container_usage" jdbcType="VARCHAR" property="hj1SingleContainerUsage" />
    <result column="hj1_alcohol_content" jdbcType="VARCHAR" property="hj1AlcoholContent" />
    <result column="hj1_single_cellar_usage" jdbcType="VARCHAR" property="hj1SingleCellarUsage" />
    <result column="total_amount_of_bottom_pot_water" jdbcType="VARCHAR" property="totalAmountOfBottomPotWater" />
    <result column="bottom_pot_water_level" jdbcType="VARCHAR" property="bottomPotWaterLevel" />
    <result column="pit_no" jdbcType="VARCHAR" property="pitNo" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="out_material_type" jdbcType="VARCHAR" property="outMaterialType" />
    <result column="enter_material_type" jdbcType="VARCHAR" property="enterMaterialType" />
    <result column="material_layer" jdbcType="VARCHAR" property="materialLayer" />
    <result column="grain_input_type" jdbcType="VARCHAR" property="grainInputType" />
    <result column="steaming_task_no" jdbcType="VARCHAR" property="steamingTaskNo" />
    <result column="iot_task_no" jdbcType="VARCHAR" property="iotTaskNo" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="created_at" jdbcType="VARCHAR" property="createdAt" />
    <result column="completed_at" jdbcType="VARCHAR" property="completedAt" />
    <result column="flowing_wine_start_time" jdbcType="VARCHAR" property="flowingWineStartTime" />
    <result column="opening_time" jdbcType="VARCHAR" property="openingTime" />
    <result column="cellar_status" jdbcType="VARCHAR" property="cellarStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator_id, create_time, update_time, updater_id, deleted, site_num, task_sistillation_id, 
    steam_valve_opening, total_steam_flow_rate, steam_flow_rate, steam_pressure, steam_consumption, 
    circulating_water_usage, clear_water_setting_value, ysage_of_circulating_water, yellow_water_set_value,
    yellow_water_usage,
    yellow_water_alcohol_content, tail_wine_setting_value, tail_wine_usage, tail_wine_alcohol_content, 
    hj1_set_value, hj1_single_container_usage, hj1_alcohol_content, hj1_single_cellar_usage, 
    total_amount_of_bottom_pot_water, bottom_pot_water_level, pit_no, out_material_type, 
    enter_material_type, material_layer, grain_input_type, steaming_task_no, iot_task_no, 
    task_status, created_at, completed_at, record_time
  </sql>
</mapper>