<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.dayplan.WorkshopPitDayPlanMapper">
    <resultMap id="MaterialMap"
               type="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.MaterialVO">
        <id property="orderId" column="order_id"/>
        <result property="orderCode" column="order_code"/>
        <result property="formulaId" column="formula_id"/>
        <collection property="materialDetails"
                    ofType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.MaterialDetailVO">
            <id property="materialDetailId" column="material_detail_id"/>
            <result property="materialId" column="material_id"/>
            <result property="materialName" column="material_name"/>
            <result property="materialTypeName" column="material_type_name"/>
            <result property="quality" column="quality"/>
            <result property="unit" column="unit"/>
        </collection>
    </resultMap>

    <!-- 获取全部日计划 -->
    <select id="getAllWorkshopPitDayPlan"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select t1.*, t2.order_code
        from t_po_workshop_pit_day_plan t1
                 left join t_po_workshop_pit_order t2 on t1.order_id = t2.id
        where t2.is_deleted = false
        order by create_time desc
    </select>

    <!-- 根据 id 获取日计划 -->
    <select id="getWorkshopPitDayPlanById"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select *
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlayCode}
        order by create_time desc;
    </select>

    <!-- 获取数据表中最大 id -->
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id)
        from t_po_workshop_pit_day_plan;
    </select>

    <!-- 删除日计划 -->
    <delete id="deleteWorkshopPitDayPlanById">
        delete
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlayCode};
    </delete>

    <!-- 获取日计划条数 -->
    <select id="getCount" resultType="java.lang.Integer">
        select
        count(*) as nums
        from
        (
        select
        day_play_id
        from
        t_po_workshop_pit_day_plan t1 left join
        t_po_workshop_pit_order t2 on t1.order_id = t2.id left join
        t_po_workshop_full_pit t3 on t3.id = t2.pit_id left join
        equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id left join
        equipment.hv_bm_location e2 on e2.id = t1.workshop_id left join
        brewage_plan.t_pp_vinasse_source t4 on t4.id = t1.category_id left join
        brewage_plan.t_pp_formula t5 on t5.id = t2.formula_id
        where
        t2.is_deleted = false and t3.is_deleted = false
        <if test="workshopCenterId !=null">
            and t1.workshop_center_id = #{workshopCenterId}
        </if>
        <if test="workshopId!=null">
            and t1.workshop_id = #{workshopId}
        </if>
        <if test="startTime!=null and endTime!=null">
            and (t1.plan_date between #{startTime} and #{endTime})
        </if>
        <if test="pitId!=null">
            and t3.full_pit_id like CONCAT('%', #{pitId}, '%')
        </if>
        group by day_play_id, plan_date
        ) plan
    </select>

    <!-- 分页查询日计划 -->
    <select id="getWorkshopPitDayPlanPage"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select
        t1.*,
        t2.order_code,
        t2.in_pit_date,
        t2.in_pit_num,
        t2.priority_id,
        t3.full_pit_id,
        t4.code as vinasse_name,
        e1.name as center_name,
        e2.name as location_name,
        t5.name as formula_name
        from
        t_po_workshop_pit_day_plan t1 left join
        t_po_workshop_pit_order t2 on t1.order_id = t2.id left join
        t_po_workshop_full_pit t3 on t3.id = t2.pit_id left join
        equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id left join
        equipment.hv_bm_location e2 on e2.id = t1.workshop_id left join
        brewage_plan.t_pp_vinasse_source t4 on t4.id = t1.category_id left join
        brewage_plan.t_pp_formula t5 on t5.id = t2.formula_id
        where
        <foreach collection="planCodes" item="planCode" separator="or">
            t1.day_play_id = #{planCode}
        </foreach>
        order by t1.plan_date desc, t1.day_play_id desc;
    </select>

    <!-- 用来提供给物料需求的接口 -->
    <select id="getDayPlanToMaterielRequest"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select t1.id,
               t1.day_play_id,
               t2.order_code,
               t5.full_pit_id,
               t4.code               as vinasse_name,
               t1.plan_out_pot_count as calc_out_pot_count,
               t3.code               as formula_code
        from t_po_workshop_pit_day_plan t1
                 left join t_po_workshop_pit_order t2 on t1.order_id = t2.id
                 left join brewage_plan.t_pp_formula t3 on t3.id = t2.formula_id
                 left join brewage_plan.t_pp_vinasse_source t4 on t4.id = t2.vinasse_id
                 left join t_po_workshop_full_pit t5 on t2.pit_id = t5.id
        where t1.plan_date = #{planDate}
          and t1.workshop_center_id = #{workshopCenterId}
          and t1.workshop_id = #{workshopId}
          and t2.is_deleted = false
    </select>

    <!-- 添加日计划 -->
    <insert id="addWorkshopPitDayPlan">
        insert into t_po_workshop_pit_day_plan
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="dayPlayId != null">
                day_play_id,
            </if>
            <if test="pitId != null">
                pit_id,
            </if>
            <if test="workshopCenterId != null">
                workshop_center_id,
            </if>
            <if test="workshopId != null">
                workshop_id,
            </if>
            <if test="planDate != null">
                plan_date,
            </if>
            <if test="potCount != null">
                pot_count,
            </if>
            <if test="potCjCount != null">
                pot_cj_count,
            </if>
            <if test="potCzCount != null">
                pot_cz_count,
            </if>
            <if test="potZlCount != null">
                pot_zl_count,
            </if>
            <if test="planStatus != null">
                plan_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="sortNo != null">
                sort_no,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="calcOutPotCount != null">
                calc_out_pot_count,
            </if>
            <if test="planOutPotCount != null">
                plan_out_pot_count,
            </if>
            <if test="outPotCount != null">
                out_pot_count,
            </if>
            <if test="balancePotCount != null">
                balance_pot_count,
            </if>
            <if test="isOpened != null">
                is_opened,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="baseVinasseId != null">
                base_vinasse_id,
            </if>
            <if test="vinasseCode != null">
                vinasse_code,
            </if>
            <if test="scheduleType != null">
                schedule_type,
            </if>
            <if test="orderRemainderCount != null">
                order_remainder_count,
            </if>
        </trim>
        )
        value
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="dayPlayId != null">
                #{dayPlayId},
            </if>
            <if test="pitId != null">
                #{pitId},
            </if>
            <if test="workshopCenterId != null">
                #{workshopCenterId},
            </if>
            <if test="workshopId != null">
                #{workshopId},
            </if>
            <if test="planDate != null">
                #{planDate},
            </if>
            <if test="potCount != null">
                #{potCount},
            </if>
            <if test="potCjCount != null">
                #{potCjCount},
            </if>
            <if test="potCzCount != null">
                #{potCzCount},
            </if>
            <if test="potZlCount != null">
                #{potZlCount},
            </if>
            <if test="planStatus != null">
                #{planStatus},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="sortNo != null">
                #{sortNo},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="categoryId != null">
                #{categoryId},
            </if>
            <if test="calcOutPotCount != null">
                #{calcOutPotCount},
            </if>
            <if test="planOutPotCount != null">
                #{planOutPotCount},
            </if>
            <if test="outPotCount != null">
                #{outPotCount},
            </if>
            <if test="balancePotCount != null">
                #{balancePotCount},
            </if>
            <if test="isOpened != null">
                #{isOpened},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="baseVinasseId != null">
                #{baseVinasseId},
            </if>
            <if test="vinasseCode != null">
                #{vinasseCode},
            </if>
            <if test="scheduleType != null">
                #{scheduleType},
            </if>
            <if test="orderRemainderCount != null">
                #{orderRemainderCount},
            </if>
        </trim>
        )
    </insert>

    <!-- 修改日计划 -->
    <update id="updateWorkshopPitDayPlan">
        update t_po_workshop_pit_day_plan
        <trim prefix="set" suffixOverrides=",">
            <if test="dayPlayId != null">
                day_play_id = #{dayPlayId},
            </if>
            <if test="pitId != null">
                pit_id = #{pitId},
            </if>
            <if test="workshopCenterId != null">
                workshop_center_id = #{workshopCenterId},
            </if>
            <if test="workshopId != null">
                workshop_id = #{workshopId},
            </if>
            <if test="potCount != null">
                pot_count = #{potCount},
            </if>
            <if test="potCjCount != null">
                pot_cj_count = #{potCjCount},
            </if>
            <if test="potCzCount != null">
                pot_cz_count = #{potCzCount},
            </if>
            <if test="potZlCount != null">
                pot_zl_count = #{potZlCount},
            </if>
            <if test="planStatus != null">
                plan_status = #{planStatus},
            </if>
            <if test="sortNo != null">
                sort_no = #{sortNo},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="calcOutPotCount != null">
                calc_out_pot_count = #{calcOutPotCount},
            </if>
            <if test="planOutPotCount != null">
                plan_out_pot_count = #{planOutPotCount},
            </if>
            <if test="outPotCount != null">
                out_pot_count = #{outPotCount},
            </if>
            <if test="balancePotCount != null">
                balance_pot_count = #{balancePotCount},
            </if>
            <if test="isOpened != null">
                is_opened = #{isOpened},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="baseVinasseId != null">
                base_vinasse_id = #{baseVinasseId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 周期查询接口 -->
    <select id="getCycleById"
            resultType="com.hvisions.brewage.dto.mkwine.vo.ParamVO">
        select *
        from t_po_param
        where param_id = #{param_id};
    </select>

    <!-- 周期修改接口 -->
    <update id="updateCycle">
        update t_po_param
        <trim prefix="set" suffixOverrides=",">
            <if test="paramName != null">
                param_name = #{paramName},
            </if>
            <if test="paramValue != null">
                param_value = #{paramValue},
            </if>
            <if test="notes != null">
                notes = #{notes},
            </if>
        </trim>
        where param_id = #{paramId}
    </update>

    <!-- 日计划下发 -->
    <update id="workshopPitDayPlanIssued">
        update t_po_workshop_pit_day_plan
        set plan_status = #{planStatus}
        where day_play_id = #{dayPlayId};
    </update>

    <update id="updateFirstInFirstOut">
        update t_po_first_in_first_out
        set is_open = #{isOpen}
        where center_id = #{centerId}
          and location_id = #{locationId};
    </update>

    <!-- 周期删除接口 -->
    <delete id="deleteCycle">
        delete
        from t_po_param
        where param_id = #{param_id};
    </delete>

    <delete id="deleteWorkshopPitDayPlan">
        delete
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlayId}
    </delete>

    <!-- 周期添加接口 -->
    <insert id="insertCycle">
        insert into t_po_param
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="paramId != null">
                param_id,
            </if>
            <if test="paramName != null">
                param_name,
            </if>
            <if test="paramValue != null">
                param_value,
            </if>
            <if test="notes != null">
                notes,
            </if>
        </trim>
        )
        value
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="paramId != null">
                #{paramId},
            </if>
            <if test="paramName != null">
                #{paramName},
            </if>
            <if test="paramValue != null">
                #{paramValue},
            </if>
            <if test="notes != null">
                #{notes},
            </if>
        </trim>
        )
    </insert>

    <insert id="insertFirstInFirstOut">
        insert into t_po_first_in_first_out (center_id, location_id, is_open) value (#{centerId}, #{locationId}, #{isOpen});
    </insert>

    <insert id="insertOutLog">
        insert into t_po_out_appraise_log(center_id, location_id, pit_id, day_plan_id, order_id, open_date, notes, data)
        values
        <foreach collection="list" item="log" separator=",">
            (#{log.centerId}, #{log.locationId}, #{log.pitId}, #{log.dayPlanId}, #{log.orderId}, #{log.openDate},
            #{log.notes}, #{log.data})
        </foreach>
    </insert>

    <!-- 查询窖池周期表中的最大id -->
    <select id="getMaxIdOfParam" resultType="java.lang.Integer">
        select max(id)
        from t_po_param;
    </select>

    <!-- 查询窖池订单 -->
    <select id="cellarOrderQuery"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.WorkshopPitOrder.WorkshopPitOrderVO">
        select
        t1.id as order_id,
        t1.in_pit_date,
        t1.order_code,
        t1.pit_id,
        t4.full_pit_id,
        t2.id as formula_id,
        t2.name as formula_name,
        t3.id as category_id,
        t3.code as vinasse_name,
        t1.in_pit_num,
        t1.priority_id,
        t1.pit_status,
        t1.location_id,
        t1.center_id
        from
        t_po_workshop_pit_order t1 left join
        brewage_plan.t_pp_formula t2 on t1.formula_id = t2.id left join
        brewage_plan.t_pp_vinasse_source t3 on t1.vinasse_id = t3.id left join
        t_po_workshop_full_pit t4 on t1.pit_id = t4.id
        where
        t1.is_deleted = false and t1.in_pit_date is not null and
        (
        (t1.order_status = 2 and t1.empty_start_time IS NULL and t1.empty_confirm_time IS NULL) or
        (t1.priority_id = 1 and t1.pit_status = 1) or
        pit_status = 2
        <if test="vinaseeNames != null">
            or (t1.pit_status = 1 and (
            <foreach collection="vinaseeNames" item="item" separator="or">
                (t3.name = #{item.vinasseName} and
                date_format(t1.in_pit_date, '%Y-%m-%d') between date_format(#{item.startTime}, '%Y-%m-%d') and
                date_format(#{item.endTime}, '%Y-%m-%d'))
            </foreach>
            ))
        </if>
        )
        <if test="workshopPitDayPlanToOrderDTO.centerId != null">
            and t1.center_id = #{workshopPitDayPlanToOrderDTO.centerId}
        </if>
        <if test="workshopPitDayPlanToOrderDTO.locationId != null">
            and t1.location_id = #{workshopPitDayPlanToOrderDTO.locationId}
        </if>
        order by IF(t1.priority_id = 0, null, t1.priority_id) desc, t3.code asc, t1.in_pit_date asc;
    </select>

    <!--
        # 根据窖池订单窖池id去找两个单窖号
        # 通过单窖号去匹配甑口任务中的起窖号，统计任务状态为已完成的甑口任务数量 -->
    <select id="getWorkshopPitOrderPotTask"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.OutPotNumsVO">
        select
        t1.id as order_id,
        count(t1.id) as nums
        from
        t_po_workshop_pit_order t1 inner join
        t_po_workshop_pit_order_sap t2 on t2.order_code_id = t1.id inner join
        t_po_workshop_pit_order_pot_task t3 on t3.out_order_code = t2.order_code
        where
        t1.is_deleted = false and t2.is_deleted = false and t3.is_deleted = false and
        t3.order_status = 2 and
        (<foreach collection="orderIds" item="orderId" separator="or">
        t1.id = #{orderId}
    </foreach>)
        group by t1.id;
    </select>

    <!-- 计算计划出窖甑口总和 -->
    <select id="getPlanOutPotCountTotal" resultType="java.lang.Integer">
        select sum(plan_out_pot_count)
        from t_po_workshop_pit_day_plan
        where order_id = #{id}
          and out_pot_count > 0;
    </select>

    <!-- 获取已出窖槽源，注意这里是统计不同槽源类型的数量的 -->
    <select id="getMonthlyPlanRemainingProductionRetort"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthProdVO">
        select t4.code  as vinasseCode,
               count(*) as prodNum
        from t_po_workshop_pit_order_pot_task t1
                 inner join
             t_po_workshop_pit_order_sap t2 on t2.order_code = t1.out_order_code
                 inner join
             t_po_workshop_pit_order t3 on t3.id = t2.order_code_id
                 left join
             brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
        where t1.create_time between #{startTime} and #{endTime}
          and t3.center_id = #{workMonthPlanDTO.centerId}
          and t3.location_id = #{workMonthPlanDTO.locationId}
          and t1.order_status = 2
          and t3.is_deleted = false
        group by t4.code;
    </select>

    <!-- 中心车间下状态为发酵状态的窖池订单，按不同糟源类别分组，并且获取到不同糟源类别最早的入窖时间 -->
    <select id="getDifferentVinasse"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVinasseVO">
        select t.name as vinasse_name, min(t.in_pit_date) as start_time, min(t.in_pit_date) end_time from
        (
        select tpvs.name, t1.in_pit_date from t_po_workshop_pit_order t1
        inner join brewage_plan.t_pp_vinasse_source tpvs on t1.vinasse_id = tpvs.id
        where t1.pit_status = 1 and t1.is_deleted = false
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        group by tpvs.id, t1.id
        ) t group by t.name;
    </select>

    <!-- 根据日计划编号查询某条日计划详情 -->
    <select id="getWorkshopPitDayPlanByCode"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
    </select>

    <!-- 甑口任务详情 -->
    <select id="potTaskDetail"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.PotTaskVO">
        select t1.id,
               t1.pot_serial_number,
               t3.vinasse_id,
               t4.code as vinasseCode,
               t1.out_pit_code,
               t1.pottask_starttime,
               t1.pot_num,
               t1.pottask_endtime,
               t1.in_pit_code
        from t_po_workshop_pit_order_pot_task t1
                 inner join
             t_po_workshop_pit_order_sap t2 on t1.out_order_code = t2.order_code
                 inner join
             t_po_workshop_pit_order t3 on t2.order_code_id = t3.id
                 left join
             brewage_plan.t_pp_vinasse_source t4 on t4.id = t3.vinasse_id
        where t3.order_code = #{orderCode}
          and t3.is_deleted = false;
    </select>

    <select id="existDayPlans" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_day_plan t1
                 inner join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
        where t2.full_pit_id = #{fullPitId}
          and t1.workshop_center_id = #{centerId}
          and t1.workshop_id = #{locationId}
          and date_format(t1.plan_date, '%Y-%m-%d') between date_format(#{planDateStart}, '%Y-%m-%d') and date_format(#{planDateEnd}, '%Y-%m-%d')
          and t1.plan_status = 3
    </select>

    <select id="judgePlanTimeIsExit"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select *
        from t_po_workshop_pit_day_plan
        where workshop_center_id = #{centerId}
          and workshop_id = #{locationId}
          and plan_date = #{time};
    </select>

    <select id="getFirstInFirstOut" resultType="java.lang.Boolean">
        select is_open
        from t_po_first_in_first_out
        where center_id = (select parent_id from equipment.hv_bm_location where id = #{locationId})
          and location_id = #{locationId};
    </select>

    <select id="getCodeIds"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.WorkshopPitOrder.WorkshopPitOrderVO">
        select *
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlayId};
    </select>

    <select id="getIncreaseCoefficient" resultType="java.math.BigDecimal">
        select gains_coefficient
        from brewage_plan.t_pp_retort_container
        where source_code = #{vinasseName}
          and state = 1;
    </select>

    <select id="getCellarOutPotCount" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_order t1
                 inner join
             t_po_workshop_pit_order_sap t2 on t2.order_code_id = t1.id
                 inner join
             t_po_workshop_pit_order_pot_task t3 on t3.out_order_code = t2.order_code
        where t1.order_code = #{orderCode}
          and t1.is_deleted = false
          and t1.vinasse_id = #{vinasseId};
    </select>

    <select id="getParamValue" resultType="java.lang.Integer">
        select cycle
        from t_po_daily_plan_configuration
        where is_deleted = false
          and center_id = #{centerId}
          and location_id = #{locationId}
    </select>

    <select id="getTeamOutPotCount"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.ExecutionDetailVO">
        select t3.out_order_code,
               t3.out_shift_id,
               h1.start_time,
               h1.end_time
        from t_po_workshop_pit_order t1
                 inner join
             t_po_workshop_pit_order_sap t2 on t2.order_code_id = t1.id
                 inner join
             t_po_workshop_pit_order_pot_task t3 on t2.order_code = t3.out_order_code
                 inner join
             schedule.hv_bm_shift h1 on h1.id = t3.out_shift_id
        where t1.order_code = #{orderCode}
          and t1.is_deleted = false
          and t1.vinasse_id = #{vinasseId};
    </select>

    <select id="getPitDayPlanByDayPlayId"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanVO">
        select t1.*,
               t2.order_code,
               t3.full_pit_id,
               t4.code as vinasse_name,
               e1.name as center_name,
               e2.name as location_name,
               t2.formula_id
        from t_po_workshop_pit_day_plan t1
                 left join t_po_workshop_pit_order t2 on t1.order_id = t2.id
                 left join t_po_workshop_full_pit t3 on t3.id = t2.pit_id
                 left join equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id
                 left join equipment.hv_bm_location e2 on e2.id = t1.workshop_id
                 left join brewage_plan.t_pp_vinasse_source t4 on t4.id = t1.category_id
        where t1.day_play_id = #{dayPlayId}
          and t2.is_deleted = false
    </select>

    <select id="getOrderIds" resultType="java.lang.Integer">
        select order_id
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlayId};
    </select>

    <select id="getMaterial"
            resultMap="MaterialMap">
        select
        t1.id as order_id,
        t1.order_code,
        t1.formula_id,
        t3.id as material_detail_id,
        t3.material_id,
        t3.material_name,
        t3.quality,
        t2.unit,
        m2.material_type_name
        from
        t_po_workshop_pit_order t1 inner join
        brewage_plan.t_pp_formula t2 on t1.formula_id = t2.id inner join
        brewage_plan.t_pp_formula_detail t3 on t3.formula_id = t2.id inner join
        materials.hv_bm_material m1 on t3.material_id = m1.id inner join
        materials.hv_bm_material_type m2 on m1.material_type = m2.id
        where
        t1.is_deleted = false and t3.type = 0 and
        t3.material_name != 'W1' and
        (
        <foreach collection="orderIds" item="orderId" separator="or">
            t1.id = #{orderId}
        </foreach>
        )
    </select>

    <select id="getPlanPot" resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.PitVO">
        select t2.full_pit_id        as pit_name,
               t1.pit_id,
               t1.order_id,
               t1.plan_out_pot_count as plan_nums,
               t4.code               as vinasse_code,
               t3.open_pit_finish_time
        from t_po_workshop_pit_day_plan t1
                 inner join
             t_po_workshop_full_pit t2 on t1.pit_id = t2.id
                 inner join
             t_po_workshop_pit_order t3 on t3.id = t1.order_id
                 left join
             brewage_plan.t_pp_vinasse_source t4 on t4.id = t3.vinasse_id
        where t1.workshop_center_id = (select parent_id from equipment.hv_bm_location where id = #{locationId})
          and t3.is_deleted = false
          and t1.workshop_id = #{locationId}
          and t1.plan_date = #{currentDate};
    </select>

    <select id="getOutPot" resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.PitVO">
        select t1.pit_id,
               t1.order_id,
               t1.plan_out_pot_count as plan_nums,
               t5.full_pit_id        as pit_name,
               t6.code               as vinasse_code,
               t2.open_pit_finish_time,
               count(*)              as actual_nums
        from t_po_workshop_pit_day_plan t1
                 inner join
             t_po_workshop_pit_order t2 on t1.order_id = t2.id
                 inner join
             t_po_workshop_pit_order_sap t3 on t2.id = t3.order_code_id
                 inner join
             t_po_workshop_pit_order_pot_task t4 on t3.order_code = t4.out_order_code
                 inner join
             t_po_workshop_full_pit t5 on t5.id = t1.pit_id
                 left join
             brewage_plan.t_pp_vinasse_source t6 on t6.id = t2.vinasse_id
        where t2.is_deleted = false
          and t3.is_deleted = false
          and t4.is_deleted = false
          and t1.workshop_center_id = (select parent_id from equipment.hv_bm_location where id = #{locationId})
          and t1.workshop_id = #{locationId}
          and t1.plan_date = #{currentDate}
        group by t1.pit_id, t1.order_id, t5.full_pit_id, t1.plan_out_pot_count, t6.code, t2.open_pit_finish_time;
    </select>

    <!-- 根据窖池订单id查询滴窖任务的计划开始时间 -->
    <select id="exudingTask"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO">
        select t1.*,
               t3.full_pit_id as pit_code,
               h1.code        as location_code
        from t_po_workshop_pit_order_exuding_task t1
                 inner join
             t_po_workshop_pit_order t2 on t1.order_code = t2.order_code
                 inner join
             t_po_workshop_full_pit t3 on t3.id = t1.pit_id
                 inner join
             equipment.hv_bm_location h1 on h1.id = t1.location_id
        where t2.is_deleted = false
          and t3.is_deleted = false
          and t2.id = #{orderId}
          and t1.plan_start_time =
              (
                  select max(t1.plan_start_time)
                  from t_po_workshop_pit_order_exuding_task t1
                           inner join
                       t_po_workshop_pit_order t2 on t1.order_code = t2.order_code
                  where t2.id = #{orderId}
                    and t2.is_deleted = false
              );
    </select>

    <select id="getOrderInfos"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.OrderInfo">
        select t1.id,
               t1.center_id,
               t1.location_id,
               t1.pit_id,
               t1.vinasse_id,
               t1.order_code,
               t1.layer,
               t1.order_status
        from t_po_workshop_pit_order t1
                 inner join
             t_po_workshop_pit_day_plan t2 on t2.order_id = t1.id
        where t2.day_play_id = #{dayPlayId}
          and t2.is_opened = true
          and t1.is_deleted = false;
    </select>

    <!-- 获取sap中的入窖甑口 -->
    <select id="getPotBySap" resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.OutPotNumsVO">
        select
        t1.id as order_id,
        sum(t2.in_pot_num_exc) as nums
        from
        t_po_workshop_pit_order t1 inner join
        t_po_workshop_pit_order_sap t2 on t2.order_code_id = t1.id
        where
        t1.is_deleted = false and t2.is_deleted = false and
        (<foreach collection="orderIds" item="orderId" separator="or">
        t1.id = #{orderId}
    </foreach>)
        group by t1.id;
    </select>

    <select id="getIncreaseCoefficients"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.OutPotNumsVO">
        select
        gains_coefficient,
        source_code as vinasse_code
        from
        brewage_plan.t_pp_retort_container
        where
        state = 1 and
        (<foreach collection="vinasseList" item="vinasse" separator="or">
        source_code = #{vinasse}
    </foreach>);
    </select>

    <select id="getPlanCode" resultType="java.lang.String">
        select
        day_play_id
        from
        t_po_workshop_pit_day_plan t1 left join
        t_po_workshop_pit_order t2 on t1.order_id = t2.id left join
        t_po_workshop_full_pit t3 on t3.id = t2.pit_id left join
        equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id left join
        equipment.hv_bm_location e2 on e2.id = t1.workshop_id left join
        brewage_plan.t_pp_vinasse_source t4 on t4.id = t1.category_id left join
        brewage_plan.t_pp_formula t5 on t5.id = t2.formula_id
        where
        t2.is_deleted = false and t3.is_deleted = false
        <if test="workshopCenterId !=null">
            and t1.workshop_center_id = #{workshopCenterId}
        </if>
        <if test="workshopId!=null">
            and t1.workshop_id = #{workshopId}
        </if>
        <if test="startTime!=null and endTime!=null">
            and (t1.plan_date between #{startTime} and #{endTime})
        </if>
        <if test="pitId!=null">
            and t3.full_pit_id like CONCAT('%', #{pitId}, '%')
        </if>
        group by day_play_id, plan_date
        order by plan_date desc
        limit #{page}, #{pageSize};
    </select>

    <select id="getCountByDayPlayId" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_day_plan
        where day_play_id = #{dayPlanId}
    </select>

    <select id="getUserIds" resultType="java.lang.Integer">
        select u.id as user_id
        from authority.sys_user u
                 inner join
             authority.sys_user_role ur on ur.user_id = u.id
                 inner join
             authority.sys_role r on ur.role_id = r.id
                 inner join
             authority.sys_department d ON u.department_id = d.id
                 inner join
             equipment.hv_bm_location h ON d.department_code = h.code
        where r.id = #{roleId}
          and h.id = #{locationId}
          and h.parent_id = #{centerId};
    </select>
    <select id="selectProductionBeingPerformed"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthDetailVO">
        select t4.code  as vinasseCode,
        count(*) as prodNum
        from t_po_workshop_pit_order_pot_task t1
        inner join
        t_po_workshop_pit_order_sap t2 on t2.order_code = t1.out_order_code
        inner join
        t_po_workshop_pit_order t3 on t3.id = t2.order_code_id
        left join
        brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
        <if test="dayPlayId != null and dayPlayId != ''">
            left join t_po_workshop_pit_day_plan t4 on t4.order_id = t3.id
        </if>
        where t1.create_time between #{startTime} and #{endTime}
        and t3.center_id = #{workMonthPlanDTO.centerId}
        and t3.location_id = #{workMonthPlanDTO.locationId}
        and t1.order_status = 2
        and t3.is_deleted = false
        <if test="dayPlayId != null and dayPlayId != ''">
            and t4.day_play_id = #{dayPlayId}
        </if>
        group by t4.code;
    </select>
    <select id="getMonthlyPlanRemainingProductionMaterial"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthMaterialVO">
        SELECT 'qu_code' AS code_type, t4.qu_code AS materialCode, t4.qu_name AS materialName, SUM(t4.qu_quantity) AS prodUseCount
        FROM t_po_workshop_pit_order_pot_task t1
                 INNER JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.out_order_code
                 INNER JOIN t_po_workshop_pit_order t3 ON t3.id = t2.order_code_id
                 INNER JOIN t_po_workshop_pit_order_pot_task_material t4 ON t4.pot_task_id = t1.id
        where t1.create_time between #{startTime} and #{endTime}
          and t3.center_id = #{workMonthPlanDTO.centerId}
          and t3.location_id = #{workMonthPlanDTO.locationId}
          AND t1.order_status = 2
          AND t3.is_deleted = FALSE
        GROUP BY t4.qu_code

        UNION ALL

        SELECT 'dk_code' AS code_type, t4.dk_code AS materialCode, t4.dk_name AS materialName, SUM(t4.dk_quantity) AS prodUseCount
        FROM t_po_workshop_pit_order_pot_task t1
                 INNER JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.out_order_code
                 INNER JOIN t_po_workshop_pit_order t3 ON t3.id = t2.order_code_id
                 INNER JOIN t_po_workshop_pit_order_pot_task_material t4 ON t4.pot_task_id = t1.id
        where t1.create_time between #{startTime} and #{endTime}
          and t3.center_id = #{workMonthPlanDTO.centerId}
          and t3.location_id = #{workMonthPlanDTO.locationId}
          AND t1.order_status = 2
          AND t3.is_deleted = FALSE
        GROUP BY t4.dk_code

        UNION ALL

        SELECT 'gl_code' AS code_type, t4.gl_code AS materialCode, t4.gl_name AS materialName, SUM(t4.gl_quantity) AS prodUseCount
        FROM t_po_workshop_pit_order_pot_task t1
                 INNER JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.out_order_code
                 INNER JOIN t_po_workshop_pit_order t3 ON t3.id = t2.order_code_id
                 INNER JOIN t_po_workshop_pit_order_pot_task_material t4 ON t4.pot_task_id = t1.id
        where t1.create_time between #{startTime} and #{endTime}
          and t3.center_id = #{workMonthPlanDTO.centerId}
          and t3.location_id = #{workMonthPlanDTO.locationId}
          AND t1.order_status = 2
          AND t3.is_deleted = FALSE
        GROUP BY t4.gl_code

        UNION ALL

        SELECT 'hj_code' AS code_type, t4.hj_code AS materialCode, t4.hj_name AS materialName, SUM(t4.hj_quantity) AS prodUseCount
        FROM t_po_workshop_pit_order_pot_task t1
                 INNER JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.out_order_code
                 INNER JOIN t_po_workshop_pit_order t3 ON t3.id = t2.order_code_id
                 INNER JOIN t_po_workshop_pit_order_pot_task_material t4 ON t4.pot_task_id = t1.id
        where t1.create_time between #{startTime} and #{endTime}
          and t3.center_id = #{workMonthPlanDTO.centerId}
          and t3.location_id = #{workMonthPlanDTO.locationId}
          AND t1.order_status = 2
          AND t3.is_deleted = FALSE
        GROUP BY t4.hj_code;
    </select>

    <update id="updateStateByDayPlayId">
        update t_po_workshop_pit_day_plan
        set plan_status = 0
        where day_play_id = #{dayPlanId}
    </update>

    <select id="getPlanPotCount" resultType="java.util.Map">
        select ifnull(sum(plan_out_pot_count),0) as count, workshop_center_id as centerId
            from t_po_workshop_pit_day_plan a
            LEFT JOIN equipment.hv_bm_location b on a.workshop_center_id = b.id
            LEFT JOIN brewage_plan.t_pp_vinasse_source c on a.category_id = c.id and c.production_base_id = 1
        where 1=1 and c.deleted = 0
        <if test="locationKanBanDTO.vinasse !=null">
            and c.code = #{locationKanBanDTO.vinasse}
        </if>
        <if test="locationKanBanDTO.startTime!=null and locationKanBanDTO.endTime!=null">
            and a.plan_date BETWEEN DATE_FORMAT(#{locationKanBanDTO.startTime}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{locationKanBanDTO.endTime}, '%Y-%m-%d 23:59:59')
        </if>
        GROUP BY a.workshop_center_id
    </select>
    <select id="selectVinasseByDayPlan" resultType="java.lang.String">
        select vs.`code`
        from t_po_workshop_pit_day_plan p
         left join t_po_workshop_pit_order o on o.id = p.order_id
         left join brewage_plan.t_pp_vinasse_source vs on vs.id = o.vinasse_id
        where p.id = #{id}
            limit 1
    </select>
    <select id="selectLastRemianderList"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        SELECT DISTINCT
            t1.id,
            t1.seal_confirm_time,
            t1.order_code,
            t1.layer,
            t1.pit_id,
            t3.full_pit_id,
            t1.center_id,
            t1.location_id,
            t2.id category_id,
            t2.CODE AS vinasse_name,
            dp.order_remainder_count remain_num,
            (COALESCE ( t1.in_pit_num, 0 ) + ( SELECT COALESCE ( SUM( in_pot_num_exc ), 0 ) FROM t_po_workshop_pit_order_sap WHERE order_code_id = t1.id )) AS in_pit_num
        FROM t_po_workshop_pit_day_plan dp
                 LEFT JOIN t_po_workshop_pit_order t1 on t1.id = dp.order_id
                 LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
                 LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
        WHERE
            t1.is_deleted = 0
          AND t1.location_id = #{locationId}
          AND t1.center_id = #{centerId}
          AND t1.pit_status = 1
          AND dp.plan_status != 5
          AND dp.order_remainder_count <![CDATA[ > ]]> 0
        <if test="vlist != null and vlist.size() != 0">
            AND t2.CODE IN
            <foreach collection="vlist" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>