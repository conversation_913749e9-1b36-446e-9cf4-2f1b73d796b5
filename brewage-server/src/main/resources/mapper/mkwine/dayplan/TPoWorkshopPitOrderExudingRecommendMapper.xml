<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.dayplan.TPoWorkshopPitOrderExudingRecommendMapper">

    <select id="getAllLocationList" resultType="com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.ExecuteDayPlanTaskDTO">
        select id location_id, parent_id center_id
        from equipment.hv_bm_location
          where parent_id in(
            select id from equipment.hv_bm_location where parent_id = 1)
              and `name` like "%车间" and code REGEXP '^[0-9]+$'
    </select>
    <select id="selectOrderProductionSchedule"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        SELECT DISTINCT
        t1.id,
        t1.seal_confirm_time,
        t1.order_code,
        t1.layer,
        t1.pit_id,
        t3.full_pit_id,
        t1.center_id,
        t1.location_id,
        t2.id category_id,
        t2.CODE AS vinasse_name,
        (
        COALESCE ( t1.in_pit_num, 0 ) + ( SELECT COALESCE ( SUM( in_pot_num_exc ), 0 ) FROM t_po_workshop_pit_order_sap WHERE order_code_id = t1.id )) AS in_pit_num
        FROM t_po_workshop_pit_order t1
        LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
        LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
        WHERE
          t1.is_deleted = 0 AND t1.layer = 2
          AND t1.location_id = #{locationId}
          AND t1.center_id = #{centerId}
          AND t1.pit_status = 1
        <if test="vlist != null and vlist.size() != 0">
            AND t2.CODE IN
            <foreach collection="vlist" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="orderIds != null and orderIds.size() != 0">
            AND t1.id NOT IN
            <foreach collection="orderIds" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="getOrderRecommend"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.OrderExudingVO">
        select
        t1.id,
        t1.center_id,
        t1.location_id,
        t1.pit_id,
        t3.full_pit_id as pit_code,
        t1.order_code  as order_code,
        t1.in_pit_date,
        t1.vinasse_id,
        vs.code as vinasse_code,
        t1.pit_status,
        h1.code as center_code,
        h2.code as location_code,
        IF(t1.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time),TO_DAYS(t1.out_pit_finish_time) - TO_DAYS(t1.seal_confirm_time)) AS fermentation_days
        from
        t_po_workshop_pit_order t1
        left join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        left join brewage_plan.t_pp_vinasse_source as vs on vs.id = t1.vinasse_id
        left join equipment.hv_bm_location h1 on h1.id = t1.center_id
        left join equipment.hv_bm_location h2 on h2.id = t1.location_id
        where
        t1.pit_status = 1 and t1.is_deleted = false
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="pitCode != null">
            and t3.full_pit_id like CONCAT('%', #{pitCode}, '%')
        </if>

        <if test="vinasseId != null">
            and t1.vinasse_id = #{vinasseId,jdbcType=INTEGER}
        </if>
        <if test="notInList != null and notInList.size() != 0">
            and t1.id not IN
            <foreach collection="notInList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="inList != null and inList.size() != 0">
            and t1.id IN
            <foreach collection="inList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by fermentation_days desc, vs.code, t1.in_pit_date
    </select>
    <select id="selectExudingTaskOrder"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        select
        t1.id,
        t1.center_id,
        t1.location_id,
        t1.pit_id,
        t3.full_pit_id as pit_code,
        t1.order_code  as order_code,
        t1.in_pit_date,
        t1.vinasse_id,
        vs.code as vinasse_code,
        t1.pit_status,
        h1.code as center_code,
        h2.code as location_code,
        IF(t1.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time),TO_DAYS(t1.out_pit_finish_time) - TO_DAYS(t1.seal_confirm_time)) AS fermentation_days
        from
        t_po_workshop_pit_order_exuding_task et
        left join t_po_workshop_pit_order t1 on t1.order_code = et.order_code
        left join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        left join brewage_plan.t_pp_vinasse_source as vs on vs.id = t1.vinasse_id
        left join equipment.hv_bm_location h1 on h1.id = t1.center_id
        left join equipment.hv_bm_location h2 on h2.id = t1.location_id
        where
        t1.pit_status = 1 and t1.is_deleted = false
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
    </select>
</mapper>