<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.productiondisposition.PitResponsibleTeamMapper">
    <!--查询全部窖池包干-->
    <select id="getAllPitResponsibleTeam"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.PitResponsibleTeam.PitResponsibleTeamVO">
        select *
        from t_po_pit_responsible_team
        where is_deleted = 0
        order by create_time DESC
    </select>

    <!--分页查询窖池包干-->
    <select id="getPitResponsibleTeamPage"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.PitResponsibleTeam.PitResponsibleTeamVO">
        select t1.*, h1.crew_code, h1.crew_name, s1.user_name, e1.name as center_name, e2.name as location_name
        from t_po_pit_responsible_team t1
        left join schedule.hv_bm_crew h1 on t1.team_id = h1.id
        left join authority.sys_user s1 on t1.team_monitor_id = s1.id
        left join equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id
        left join equipment.hv_bm_location e2 on e2.id = t1.workshop_id
        where is_deleted = 0
        <if test="dto.workshopCenterId != null">
            and t1.workshop_center_id like CONCAT('%',#{dto.workshopCenterId},'%')
        </if>
        <if test="dto.workshopId != null">
            and t1.workshop_id like CONCAT('%',#{dto.workshopId},'%')
        </if>
        <if test="dto.teamId != null">
            and t1.team_id like CONCAT('%',#{dto.teamId},'%')
        </if>
        <if test="ids.length > 0">
            <!--<if test="pitResponsibleIds.size > 0">
                and
                <foreach collection="pitResponsibleIds" item="item" separator="or" open="(" close=")">
                    find_in_set(#{item}, pit_responsible_ids)
                </foreach>
            </if>-->
            and (
            <foreach collection="ids" item="id" separator="or">
                find_in_set(#{id}, t1.pit_responsible_ids)
            </foreach>
            )
        </if>
        order by create_time DESC
        limit #{dto.page},#{dto.pageSize}
    </select>

    <!--查询所有的非包干连窖窖池-->
    <select id="getAllWrongWorkShopFullPit"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        select *
        from t_po_workshop_full_pit
        where is_deleted = 0
          and is_contract = 0
    </select>

    <!--获取最大Id值-->
    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id)
        from t_po_pit_responsible_team
    </select>

    <!--获取数据库数据条数-->
    <select id="getCount" resultType="java.lang.Integer">
        select count(*)
        from t_po_pit_responsible_team t1
        left join schedule.hv_bm_crew h1 on t1.team_id = h1.id
        left join authority.sys_user s1 on t1.team_monitor_id = s1.id
        left join equipment.hv_bm_location e1 on e1.id = t1.workshop_center_id
        left join equipment.hv_bm_location e2 on e2.id = t1.workshop_id
        where is_deleted = 0
        <if test="dto.workshopCenterId != null">
            and t1.workshop_center_id like CONCAT('%',#{dto.workshopCenterId},'%')
        </if>
        <if test="dto.workshopId != null">
            and t1.workshop_id like CONCAT('%',#{dto.workshopId},'%')
        </if>
        <if test="dto.teamId != null">
            and t1.team_id like CONCAT('%',#{dto.teamId},'%')
        </if>
        <if test="ids.length > 0">
            <!--<if test="pitResponsibleIds.size > 0">
                and
                <foreach collection="pitResponsibleIds" item="item" separator="or" open="(" close=")">
                    find_in_set(#{item}, pit_responsible_ids)
                </foreach>
            </if>-->
            and (
            <foreach collection="ids" item="id" separator="or">
                find_in_set(#{id}, t1.pit_responsible_ids)
            </foreach>
            )
        </if>
    </select>

    <!--校验是否存在重复项-->
    <select id="CheckRepetition" resultType="java.lang.Integer">
        select COUNT(*)
        FROM t_po_pit_responsible_team
        where is_deleted = 0
          and workshop_center_id = #{workshopCenterId}
          and team_id = #{teamId}
    </select>

    <!--校验窖池Id是否被选-->
    <select id="CheckPitResponsibleIds" resultType="java.lang.Integer">
        select COUNT(*) FROM t_po_pit_responsible_team where is_deleted = 0
        <if test="array != null and array.length != 0">
            and
            <foreach collection="array" item="item" separator="or" open="(" close=")">
                find_in_set(#{item}, pit_responsible_ids)
            </foreach>
        </if>
    </select>

    <!--新增窖池包干-->
    <insert id="addPitResponsibleTeam">
        insert into t_po_pit_responsible_team
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="workshopCenterId != null">
                workshop_center_id,
            </if>
            <if test="workshopId != null">
                workshop_id,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="teamMonitorId != null">
                team_monitor_id,
            </if>
            <if test="notes != null">
                notes,
            </if>
            <if test="rowStart != null">
                row_start,
            </if>
            <if test="rowEnd != null">
                row_end,
            </if>
            <if test="pitResponsibleIds != null">
                <if test="pitResponsibleIds.length > 0">
                    pit_responsible_ids,
                </if>
            </if>
        </trim>
        )
        value
        (
        <trim prefix="" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="workshopCenterId != null">
                #{workshopCenterId},
            </if>
            <if test="workshopId != null">
                #{workshopId},
            </if>
            <if test="teamId != null">
                #{teamId},
            </if>
            <if test="teamMonitorId != null">
                #{teamMonitorId},
            </if>
            <if test="notes != null">
                #{notes},
            </if>
            <if test="rowStart != null">
                #{rowStart},
            </if>
            <if test="rowEnd != null">
                #{rowEnd},
            </if>
            <if test="pitResponsibleIds != null">
                <if test="pitResponsibleIds.length > 0">
                    <foreach collection="pitResponsibleIds" item="pitResponsibleIds" separator="','">
                        #{pitResponsibleIds}
                    </foreach>
                </if>
            </if>
        </trim>
        )
    </insert>

    <!--修改窖池包干-->
    <update id="updatePitResponsibleTeam">
        update t_po_pit_responsible_team
        <trim prefix="set" suffixOverrides=",">
            <if test="teamMonitorId != null">
                team_monitor_id = #{teamMonitorId},
            </if>
            <if test="notes!=null">
                notes = #{notes},
            </if>
            <if test="rowStart!=null">
                row_start = #{rowStart},
            </if>
            <if test="rowEnd!=null">
                row_end = #{rowEnd},
            </if>
            <if test="modifyTime !=null">
                modify_time = #{modifyTime},
            </if>
            <if test="pitResponsibleIds.length > 0">
                pit_responsible_ids =
                <foreach collection="pitResponsibleIds" item="pitResponsibleIds" separator="','" close=",">
                    #{pitResponsibleIds}
                </foreach>
            </if>
        </trim>
        where id = #{id}
    </update>

    <!--检查该Id项的包干窖池是否为空,返回0为空,1则包干不为空-->
    <select id="getCountOfPitResponsibleIds" resultType="java.lang.Integer">
        select count(pit_responsible_ids)
        from t_po_pit_responsible_team
        where is_deleted = 0
          and pit_responsible_ids is not null
          and id = #{id}
    </select>

    <!--删除窖池包干-->
    <update id="deletePitResponsibleTeam">
        update t_po_pit_responsible_team
        set modify_time = #{now},
            is_deleted  = 1
        where id = #{id}
    </update>

    <!--查询连窖表-->
    <select id="getWorkshopFullPit"
            resultType="java.lang.String">
        select id from t_po_workshop_full_pit where is_deleted = 0
        <if test="pitResponsibleIds.size > 0">
            and
            <foreach collection="pitResponsibleIds" item="item" separator="or" open="(" close=")">
                find_in_set(#{item}, full_pit_id)
            </foreach>
        </if>
    </select>

    <!--查询全部连窖窖池-->
    <select id="getAllWrongFullPit"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        select *
        from t_po_workshop_full_pit
        where is_deleted = 0
          and full_pit_id != ''
    </select>

    <!--查询已被包干的窖池Id-->
    <select id="getAllPitResponsibleId" resultType="java.lang.String">
        select pit_responsible_ids
        from t_po_pit_responsible_team
        where is_deleted = 0
          and !ISNULL(pit_responsible_ids)
    </select>

    <!--同步修改连窖池表的连窖池包干状态(状态取反)-->
    <update id="updateIsContract">
        update t_po_workshop_full_pit set is_contract = #{isContract} where is_deleted = 0
        and
        <foreach collection="pitResponsibleIds" item="item" separator="or" open="(" close=")">
            id = #{item}
        </foreach>
    </update>

    <!--通过Id获取指定项窖池包干-->
    <select id="getPitResponsibleOfId"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.PitResponsibleTeam.PitResponsibleTeamVO">
        select *
        from t_po_pit_responsible_team
        where is_deleted = 0
          and id = #{id}
    </select>

    <!--通过包干id数组获取连窖窖池-->
    <select id="getWorkshopFullPitById"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        select * from t_po_workshop_full_pit where is_deleted = 0
        <if test="array.length > 0">
            and
            <trim suffixOverrides=",">
                <foreach collection="array" item="item" separator="or" open="(" close=")">
                    find_in_set(#{item}, id)
                </foreach>
            </trim>
        </if>
    </select>

    <!-- 通过连窖id查找连窖数据-->
    <select id="findWorkshopFullPitById"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        select *
        from t_po_workshop_full_pit
        where is_deleted = 0
          and id = #{id}
    </select>

    <select id="getIdByFullPitId" resultType="java.lang.Integer">
        select id
        from t_po_workshop_full_pit
        <if test="fullPitId != null">
            where full_pit_id like CONCAT('%', #{fullPitId}, '%')
        </if>;
    </select>
</mapper>
