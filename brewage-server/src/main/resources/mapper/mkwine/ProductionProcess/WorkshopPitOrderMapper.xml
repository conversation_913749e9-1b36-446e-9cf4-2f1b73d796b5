<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper">
    <resultMap id="WorkshopPitOrderQuery"
               type="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderQueryVO">
        <result property="formulaName" column="formula_name"/>
        <result property="vinasseName" column="vinasse_name"/>
    </resultMap>

    <select id="getAllWorkshopPitOrder"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderVO">
        select *
        from t_po_workshop_pit_order
        where is_deleted = 0
    </select>

    <insert id="addWorkshopPitOrder"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_po_workshop_pit_order
                (is_deleted, create_time,is_current,priority_id
        <if test="orderCategoryId != null">
            ,order_category_id
        </if>
        <if test="centerId != null">
            ,center_id
        </if>
        <if test="locationId != null">
            ,location_id
        </if>
        <if test="pitId != null">
            ,pit_id
        </if>
        <if test="layer != null">
            ,layer
        </if>
        <if test="orderStatus != null">
            ,order_status
        </if>
        <if test="pitStatus != null">
            ,pit_status
        </if>
        <if test="orderCode != null">
            ,order_code
        </if>
        <if test="vinasseId != null">
            ,vinasse_id
        </if>
        <if test="upperOutVinasseId != null">
            ,upper_out_vinasse_id
        </if>
        )
                value
                (0, #{createTime},1,0
        <if test="orderCategoryId != null">
            ,#{orderCategoryId}
        </if>
        <if test="centerId != null">
            ,#{centerId}
        </if>
        <if test="locationId != null">
            ,#{locationId}
        </if>
        <if test="pitId != null">
            ,#{pitId}
        </if>
        <if test="layer != null">
            ,#{layer}
        </if>
        <if test="orderStatus != null">
            ,#{orderStatus}
        </if>
        <if test="pitStatus != null">
            ,#{pitStatus}
        </if>
        <if test="orderCode != null">
            ,#{orderCode}
        </if>
        <if test="vinasseId != null">
            ,#{vinasseId}
        </if>
        <if test="upperOutVinasseId != null">
            ,#{upperOutVinasseId}
        </if>
        )
    </insert>

    <select id="getWorkshopPitOrderQueryPage" resultMap="WorkshopPitOrderQuery">
        SELECT distinct t1.id,
                        t1.center_id,                                                    -- 中心id
                        t1.location_id,                                                  -- 车间id
<!--                        (SELECT dp.day_play_id-->
<!--                         from t_po_workshop_pit_day_plan dp-->
<!--                                      LEFT JOIN t_po_workshop_pit_order po on dp.order_id =-->
<!--                                                                              po.id-->
<!--                         WHERE po.id = t1.id-->
<!--                         ORDER BY plan_date-->
<!--                         limit 0,1)                                as day_play_id,       &#45;&#45; 日计划编号-->
                        t1.order_code,-- 订单编号
                        t1.layer,                                                        -- 层次
                        t1.order_category_id,                                            -- 订单类型id
                        t6.order_category_name,                                          -- 订单类型名称
                        t1.pit_id,                                                       -- 连窖号id
                        t4.full_pit_id,                                                  -- 连窖号
                        t1.formula_id,                                                   -- 配方id
                        t2.name                                    AS formula_name,-- 配方名称
                        t1.vinasse_id,                                                   -- 糟源id
                        t3.code                                    AS vinasse_name,-- 糟源名称
                        t1.order_status,-- 订单状态
                        t1.pit_status,                                                   -- 窖池状态
                        t1.cycle_no_id,-- 糟赔排次id
                        t7.name                                    as cycle_no_name,     -- 糟赔排次名称
                        t1.seal_confirm_time,                                            -- 封窖完成时间
                        t1.turn_over_finish_time,                                        -- 翻窖完成时间
                        t1.out_pit_finish_time,                                          -- 起窖完成时间
                        t1.empty_start_time,                                             -- 空窖起始时间
                        t1.open_pit_finish_time,                                         -- 开窖完成时间
                        t1.handin_finish_time,                                           -- 交酒完成时间
                        IF(t1.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time),
                           TO_DAYS(t1.out_pit_finish_time) -
                           TO_DAYS(t1.seal_confirm_time))          AS fermentation_days, -- 发酵天数（修改成用状态判断）
                        (ifnull(t1.in_pit_num, 0) + (select ifnull(SUM(in_pot_num_exc), 0)
                                                     from t_po_workshop_pit_order_sap
                                                     where order_code_id =
                                                           t1.id)) as in_pit_num,
                        (select TRUNCATE(SUM(to60_quantity), 2)
                         from t_po_workshop_receive_wine_order wo
                                      LEFT JOIN t_po_workshop_pit_order_sap pos on wo.pit_order_id = pos.id
                         where pos.order_code_id = t1.id
                                )                                  as to60_quantity,
                        t1.collapse_choose_pit_id,
                        t8.full_pit_id                             as collapse_choose_pit
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN brewage_plan.t_pp_formula t2 ON t1.formula_id = t2.id
                     LEFT JOIN brewage_plan.t_pp_vinasse_source t3 on t1.vinasse_id = t3.id
                     LEFT JOIN t_po_workshop_full_pit t4 on t1.pit_id = t4.id
                     LEFT JOIN t_po_order_type t6 on t1.order_category_id = t6.id
                     LEFT JOIN brewage_plan.t_pp_row t7 on t1.cycle_no_id = t7.id
                     LEFT JOIN t_po_workshop_full_pit t8 on t1.collapse_choose_pit_id = t8.id
                     LEFT JOIN t_po_workshop_pit_order_sap t9 on t1.id = t9.order_code_id
                where t1.is_deleted = 0
        <if test="isMove == true">
            and t1.collapse_choose_pit_id is not null
        </if>
        <if test="isMove == false">
            and t1.collapse_choose_pit_id is null
        </if>
        <if test="orderCode != null">
            and t1.order_code like concat('%', #{orderCode}, '%')
        </if>
        <if test="layer != null">
            and t1.layer = #{layer}
        </if>
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="vinasseId != null">
            and t1.vinasse_id = #{vinasseId}
        </if>
        <if test="cycleYear != null">
            and t1.cycle_year = #{cycleYear}
        </if>
        <if test="cycleNoId != null">
            and t1.cycle_no_id = #{cycleNoId}
        </if>
        <if test="fullPitId != null &amp;&amp; fullPitId != ''">
            and t4.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="pitStatus != null">
            and t1.pit_status = #{pitStatus}
        </if>
        <if test="orderStatus != null &amp;&amp; executionTimeStart == null">
            and t1.order_status = #{orderStatus}
        </if>
        <if test="sapOrderCode != null">
            and t9.sap_order_code like concat('%', #{sapOrderCode}, '%')
        </if>

        -- 执行状态和时间的条件查询
        <if test="orderStatus == 0 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 0 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 1 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 1 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 2 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 2 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 3 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 3 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 4 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 4 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 5 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 5 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>
        ORDER BY t1.pit_status = 3 desc, t1.id DESC, t1.pit_status = 5
        limit #{page},#{pageSize}
    </select>

    <update id="updateWorkshopPitOrder">
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <if test="layer != null">
                layer = #{layer},
            </if>
            <if test="formulaId != null">
                formula_id = #{formulaId},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="pitStatus != null">
                pit_status = #{pitStatus},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode},
            </if>
            <if test="vinasseId != null">
                vinasse_id = #{vinasseId},
            </if>
            seal_confirm_time = #{sealConfirmTime},
            in_pit_date = #{sealConfirmTime},
            turn_over_finish_time = #{turnOverFinishTime},
            out_pit_finish_time = #{outPitFinishTime},
            empty_start_time = #{emptyStartTime},
            open_pit_finish_time = #{openPitFinishTime},
            handin_finish_time = #{handinFinishTime},
        </trim>
        where id = #{id}
    </update>

    <update id="deleteWorkshopPitOrder">
        update t_po_workshop_pit_order
        set is_deleted = 1
        where id = #{id}
    </update>

    <select id="findWorkshopPitOrderById"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderVO">
        select *
        from t_po_workshop_pit_order
        where id = #{id}
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select count(1)
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN brewage_plan.t_pp_formula t2 ON t1.formula_id = t2.id
                     LEFT JOIN brewage_plan.t_pp_vinasse_source t3 on t1.vinasse_id = t3.id
                     LEFT JOIN t_po_workshop_full_pit t4 on t1.pit_id = t4.id
                     LEFT JOIN t_po_order_type t6 on t1.order_category_id = t6.id
                     LEFT JOIN brewage_plan.t_pp_row t7 on t1.cycle_no_id = t7.id
                where t1.is_deleted = 0
        <if test="orderCode != null">
            and t1.order_code like concat('%', #{orderCode}, '%')
        </if>
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="vinasseId != null">
            and t1.vinasse_id = #{vinasseId}
        </if>
        <if test="cycleYear != null">
            and t1.cycle_year = #{cycleYear}
        </if>
        <if test="cycleNoId != null">
            and t1.cycle_no_id = #{cycleNoId}
        </if>
        <if test="fullPitId != null &amp;&amp; fullPitId != ''">
            and t4.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="pitStatus != null">
            and t1.pit_status = #{pitStatus}
        </if>
        <if test="orderStatus != null &amp;&amp; executionTimeStart == null">
            and t1.order_status = #{orderStatus}
        </if>

        -- 执行状态和时间的条件查询
        <if test="orderStatus == 0 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 0 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 1 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 1 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 2 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 2 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 3 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 3 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 4 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 4 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 5 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 5 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>
    </select>

    <select id="getWorkshopPitOrderSealConfirmQuery"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderSealConfirmQueryVO">
        SELECT t1.id,                -- 订单id
               t1.layer,             -- 层级
               t1.order_code,        -- 订单编号
               t1.order_status,      -- 订单状态
               t1.pit_status,        -- 窖池状态
               t1.pit_id,            -- 连窖id
               t3.full_pit_id,       -- 连窖号
               t1.formula_id,        -- 配方id
               t1.seal_confirm_time, -- 封窖完成时间
               t1.center_id,         -- 中心id
               t1.location_id        -- 车间id
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
                WHERE
                -- 自动筛选获取当前时间窖池订单处于入窖与空窖状态的订单以及窖池订单执行状态为待执行的窖池订单
                (t1.pit_status = 0 OR t1.pit_status = 3 OR t1.order_status = 5)
                  and t1.is_deleted = 0
                  and (t1.center_id = #{locationId} or t1.location_id = #{locationId})
        <if test="fullPitId != null">
            AND t3.full_pit_id LIKE CONCAT('%', #{fullPitId}, '%')
        </if>
        order by t1.id desc
    </select>

    <update id="WorkshopPitOrderSealConfirm">
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <if test="layer != null">
                layer = #{layer},
            </if>
            <if test="sealConfirmTime != null">
                seal_confirm_time = #{sealConfirmTime},
                in_pit_date = #{sealConfirmTime},
            </if>
            <if test="formulaId != null">
                formula_id = #{formulaId},
            </if>
            <if test="sealConfirmUserId != null">
                seal_confirm_user_id = #{sealConfirmUserId},
            </if>
            <if test="orderCode != null">
                order_code = #{orderCode},
            </if>
            <if test="vinasseId != null">
                vinasse_id = #{vinasseId},
            </if>
            <if test="cycleNoId != null">
                cycle_no_id = #{cycleNoId},
            </if>
            <if test="cycleYear != null">
                cycle_year = #{cycleYear},
            </if>
            is_seal_confirm_tag = 1,
            pit_status = 1,
            order_status = 0,
        </trim>
        where id = #{id}
    </update>

    <select id="getWorkshopPitOrderEmptyPitQuery"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderEmptyPitQueryVO">
        SELECT t1.id,-- 订单id
               t1.order_code,-- 订单编号
               t1.order_status,-- 订单状态
               t1.pit_status,-- 窖池状态
               t1.layer,-- 层级
               t1.empty_start_time, -- 空窖起始时间
               t1.pit_id,-- 连窖id
               t3.full_pit_id -- 连窖号
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
             -- 窖池订单的执行状态为起窖完成或者订单状态为起窖
                WHERE (t1.order_status = 2 or t1.pit_status = 3)
                  and t1.is_deleted = 0
                  and (t1.center_id = #{locationId} or t1.location_id = #{locationId})
        <if test="fullPitId != null">
            AND t3.full_pit_id LIKE CONCAT('%', #{fullPitId}, '%')
        </if>
        <if test="layer != null">
            AND t1.layer = #{layer}
        </if>
    </select>

    <update id="WorkshopPitOrderEmptyPit">
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <if test="layer != null">
                layer = #{layer},
            </if>
            <if test="emptyStartTime != null">
                empty_start_time = #{emptyStartTime},
            </if>
            <if test="emptyConfirmUserId != null">
                empty_confirm_user_id = #{emptyConfirmUserId},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus}, -- 订单状态为开窖完成
            </if>
            <if test="isCurrent != null">
                is_current = #{isCurrent},
            </if>
            -- 空窖确认标志
            is_empty_confirm_tag = 1,
            -- 窖池状态为空窖（bug修改，改为已空窖确认的状态）
            pit_status = 5,
            -- 空窖确认时间,设置为当前
            empty_confirm_time = now(),
        </trim>
        where id = #{id}
    </update>

    <select id="getCheckWorkshopPitOrder"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderVO">
        select *
        from t_po_workshop_pit_order
                where is_deleted = 0
        <if test="id != null">
            and id != #{id}
        </if>
        <if test="layer == 0">
            and pit_id = #{pitId}
            and (layer = 0 or layer = 1 or layer = 2)
            and create_time like CONCAT(DATE_FORMAT(#{createTime}, '%Y-%m-%d'), '%')
        </if>
        <if test="layer == 1">
            and pit_id = #{pitId}
            and (layer = 0 or layer = 1)
            and create_time like CONCAT(DATE_FORMAT(#{createTime}, '%Y-%m-%d'), '%')
        </if>
        <if test="layer == 2">
            and pit_id = #{pitId}
            and (layer = 0 or layer = 2)
            and create_time like CONCAT(DATE_FORMAT(#{createTime}, '%Y-%m-%d'), '%')
        </if>
        <if test="layer == 3">
            and pit_id = #{pitId}
            and layer = 3
            and create_time like CONCAT(DATE_FORMAT(#{createTime}, '%Y-%m-%d'), '%')
        </if>
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id)
        from t_po_workshop_pit_order
    </select>

    <update id="WorkshopPitOrderInPitQm">
        update t_po_workshop_pit_order
        set in_pit_qm_id = #{inPitQmId}
        where id = #{id}
    </update>

    <update id="WorkshopPitOrderOutPitQm">
        update t_po_workshop_pit_order
        set out_pit_qm_id = #{outPitQmId}
        where id = #{id}
    </update>

    <select id="getWorkshopPitOrderPriorityQuery"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderPriorityQueryVO">
        select t1.id,
               t2.full_pit_id,
               t1.pit_status,
               t1.layer,
               t1.priority_id
        from t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_full_pit t2 ON t1.pit_id = t2.id
             -- 订单在制，窖池状态为发酵
                where t1.is_current = 1
                  and t1.is_deleted = 0
                  and t1.pit_status = 1    -- 发酵状态
                  and t1.order_status != 2 -- 非起窖完成
        <if test="fullPitId != null">
            and t2.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        ORDER BY t1.priority_id DESC,
                 t1.create_time DESC
    </select>

    <update id="WorkshopPitOrderPriority">
        update t_po_workshop_pit_order
        set priority_id = #{priorityId}
        where id = #{id}
    </update>

    <select id="findOrderCodeByPitName" resultType="java.util.Map">
        SELECT DISTINCT t5.pit_code   AS pitCode,
                        t4.order_code AS orderCode
        FROM t_po_workshop_pit t1
                     LEFT JOIN t_po_workshop_full_pit t2 ON t1.full_pit_id = t2.full_pit_id
                     LEFT JOIN t_po_workshop_pit_order t3 ON t3.pit_id = t2.id
                     LEFT JOIN t_po_workshop_pit_order_sap t4 ON t3.id = t4.order_code_id
                     LEFT JOIN t_po_workshop_pit t5 on t4.pit_id = t5.id
        WHERE t3.is_deleted = 0
          AND t4.order_close_tag != 1
          AND t5.pit_code LIKE concat('%', #{pitName}, '%')
          AND (t1.workshop_id = #{locationId} OR t1.workshop_center_id = #{locationId})
          AND !ISNULL(t4.order_code)
        ORDER BY t4.order_code DESC
        LIMIT 0,20
    </select>

    <update id="WorkshopPitOrderUpdateStatus">
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="pitStatus != null">
                pit_status = #{pitStatus},
            </if>
        </trim>
        where pit_id = #{pitId}
          and is_current = 1
    </update>

    <select id="getMaterialIdByName" resultType="java.lang.Integer">
        select id
        from materials.hv_bm_material
        where material_name = #{name}
    </select>

    <select id="getMaterialByName" resultType="java.util.Map">
        select *
        from materials.hv_bm_material
        where material_name = #{name}
    </select>

    <select id="getLocationById" resultType="java.util.Map">
        select *
        from equipment.hv_bm_location
        where id = #{id}
    </select>

    <select id="getQueryChild"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderQuerySapVO">
        SELECT t1.id,
               t1.order_code,
               t1.order_code_id,
               t2.layer,
               t3.order_category_name,
               t4.pit_code                                                              as full_pit_id,
               t2.vinasse_id,
               t5.CODE                                                                  AS vinasse_name,
               t6.NAME                                                                  AS formula_name,
               t1.order_status,
               t1.pit_status,
               t1.pit_id,
               t7.name                                                                  AS cycle_no_name,
               t1.seal_confirm_time,-- 封窖完成时间
               t1.turn_over_finish_time,-- 翻窖完成时间
               t1.out_pit_finish_time,-- 起窖完成时间
               t1.empty_start_time,-- 空窖起始时间
               t1.open_pit_finish_time,-- 开窖完成时间
               t1.handin_finish_time,-- 交酒完成时间
               (SELECT dp.day_play_id
                from t_po_workshop_pit_day_plan dp
                             LEFT JOIN t_po_workshop_pit_order po on dp.order_id =
                                                                     po.id
                WHERE po.id = t2.id
                ORDER BY plan_date
                limit 0,1)                                                              as day_play_id, -- 日计划编号
               IF
                       (
                       t2.pit_status = 1,
                       TO_DAYS(
                               NOW()) - TO_DAYS(t1.seal_confirm_time),
                       TO_DAYS(t1.out_pit_finish_time) - TO_DAYS(t1.seal_confirm_time)) AS fermentation_days, -- 发酵天数
               ((SELECT ifnull(count(*), 0)
                 FROM t_po_workshop_pit_order_pot_task pt
                              LEFT JOIN t_po_workshop_pit_order_sap pos ON pos.order_code = pt.in_order_code
                 WHERE pos.id = t1.id
                   and pt.is_deleted = 0) + ifnull(t1.in_pot_num_exc, 0))               AS in_pit_num,
               (SELECT ifnull(count(*), 0)
                FROM t_po_workshop_pit_order_pot_task pt
                             LEFT JOIN t_po_workshop_pit_order_sap pos ON pos.order_code = pt.out_order_code
                WHERE pos.id = t1.id
                  and pt.is_deleted = 0)                                                AS out_pit_num,
               (select TRUNCATE(SUM(to60_quantity), 2)
                from t_po_workshop_receive_wine_order wo
                             LEFT JOIN t_po_workshop_pit_order_sap pos on wo.pit_order_id = pos.id
                where pos.id = t1.id
                       )                                                                as to60_quantity,
               t1.collapse_choose_pit_id,
               t8.pit_code                                                              as collapse_choose_pit
        FROM t_po_workshop_pit_order_sap t1
                     LEFT JOIN t_po_workshop_pit_order t2 ON t1.order_code_id = t2.id
                     LEFT JOIN t_po_order_type t3 ON t2.order_category_id = t3.id
                     LEFT JOIN t_po_workshop_pit t4 ON t1.pit_id = t4.id
                     LEFT JOIN brewage_plan.t_pp_vinasse_source t5 ON t2.vinasse_id = t5.id
                     LEFT JOIN brewage_plan.t_pp_formula t6 ON t2.formula_id = t6.id
                     LEFT JOIN brewage_plan.t_pp_row t7 ON t2.cycle_no_id = t7.id
                     left join t_po_workshop_pit t8 on t1.collapse_choose_pit_id = t8.id
        WHERE t1.order_code_id = #{id}
    </select>

    <select id="getPitByCode" resultType="java.util.Map">
        select id, pit_code
        from t_po_workshop_pit
        where pit_code like concat('%', #{pitCode}, '%')
          and (workshop_center_id = #{locationId} or workshop_id = #{locationId})
    </select>

    <select id="getOrderQmInfo"
            resultType="com.hvisions.brewage.dto.mkwine.vo.WorkshopPitOrderQmVO">
        SELECT t2.`code`    AS vinasseCode,
               t2.type_code AS vinasseType,
               t1.center_id,
               l.`name`        center_name,
               t1.pit_id,
               t3.full_pit_id
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
                     LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
                     LEFT JOIN equipment.hv_bm_location l ON t1.center_id = l.id
        where t1.order_code = #{orderCode}
          and t1.is_deleted = 0
    </select>

    <select id="getFullPitByLocation"
            resultType="com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit">
        SELECT t1.*,
               t2.workshop_id,
               t2.workshop_center_id
        FROM t_po_workshop_full_pit t1
                     LEFT JOIN t_po_workshop_pit t2 on t1.full_pit = t2.pit_code
        WHERE t1.is_deleted = 0
          AND t2.is_deleted = 0
          and t1.full_pit_id != ''
          and t2.workshop_id = #{locationId}
          and t2.workshop_center_id = #{centerId}
    </select>

    <select id="getMaterialDataByOrderIdAndName" resultType="java.util.Map">
        SELECT DISTINCT t3.id,
                        t3.material_name      as materialName,
                        t4.material_type_name as materialTypeName,
                        'KG'                  as unit
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN brewage_plan.t_pp_formula_detail t2 ON t1.formula_id = t2.formula_id
                     LEFT JOIN materials.hv_bm_material t3 ON t2.material_id = t3.id
                     LEFT JOIN materials.hv_bm_material_type t4 ON t3.material_type = t4.id
        WHERE t1.id = #{orderCodeId}
          AND t4.material_type_name = #{materialName}
    </select>

    <select id="getOpenPitFinishTimeById" resultType="java.util.Date">
        SELECT t3.out_pot_time
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
                     LEFT JOIN t_po_workshop_pit_order_pot_task t3 ON t2.order_code = t3.out_order_code
        WHERE t3.is_deleted = 0
          AND t3.out_pot_time IS NOT NULL
          and t1.id = #{id}
        ORDER BY t3.out_pot_time DESC
        limit 1
    </select>

    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ground_temperature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.groundTemperature != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.groundTemperature,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="avge_in_pit_temperature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.avgeInPitTemperature != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.avgeInPitTemperature,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="center_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.centerId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.centerId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="crude_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.crudeQuantity != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.crudeQuantity,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="curr_process_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currProcessStepId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.currProcessStepId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cycle_no_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cycleNoId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.cycleNoId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cycle_year = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cycleYear != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.cycleYear,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="empty_confirm_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.emptyConfirmTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.emptyConfirmTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="empty_confirm_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.emptyConfirmUserId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.emptyConfirmUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="empty_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.emptyStartTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.emptyStartTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="formula_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.formulaId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.formulaId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="handin_finish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.handinFinishTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.handinFinishTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pit_appraise_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPitAppraiseCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPitAppraiseCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pit_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPitDate != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPitDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pit_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPitNum != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPitNum,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pit_qm_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPitQmId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPitQmId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pot_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPotTeamId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPotTeamId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="in_pot_trick_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inPotTrickId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.inPotTrickId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_current = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isCurrent != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isCurrent,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_empty_confirm_tag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isEmptyConfirmTag != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isEmptyConfirmTag,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_seal_confirm_tag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isSealConfirmTag != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isSealConfirmTag,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="layer = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layer != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.layer,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="location_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.locationId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.locationId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lotId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.lotId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="open_pit_finish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.openPitFinishTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.openPitFinishTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_category_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderCategoryId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderCategoryId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderStatus != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pit_appraise_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPitAppraiseCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPitAppraiseCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pit_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPitDate != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPitDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pit_finish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPitFinishTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPitFinishTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pit_qm_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPitQmId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPitQmId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pot_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPotTeamId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPotTeamId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_pot_trick_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outPotTrickId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.outPotTrickId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pitId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pitId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pit_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pitStatus != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pitStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="priority_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priorityId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.priorityId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rise_temperature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.riseTemperature != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.riseTemperature,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="seal_confirm_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sealConfirmTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.sealConfirmTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="seal_confirm_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sealConfirmUserId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.sealConfirmUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="technics_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.technicsId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.technicsId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="to_top_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.toTopDays != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.toTopDays,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="to_sap_tag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.to_Sap_Tag != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.to_Sap_Tag,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="top_temperature = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.topTemperature != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.topTemperature,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="turn_over_finish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.turnOverFinishTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.turnOverFinishTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vinasse_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vinasseId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.vinasseId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vol60quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vol60Quantity != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.vol60Quantity,jdbcType=FLOAT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="turn_over_pit_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.turnOverPitNum != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.turnOverPitNum,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="turnover_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.turnoverStatus != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.turnoverStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateFormulaByVO">
        UPDATE t_po_workshop_pit_order
        SET formula_id = #{formulaId}, vinasse_id = #{vinasseId}
        WHERE id = #{id}
    </update>

    <select id="getWorkshopPitOrderSapSealConfirm"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderSapSealConfirmQueryVO">
        SELECT t1.id,               -- 单窖订单id
               t3.layer,            -- 层级
               t1.order_code,       -- 订单状态
               t1.pit_status,-- 窖池状态
               t1.pit_id,           -- 单窖id
               t2.pit_code,         -- 单窖号
               t3.formula_id,-- 配方id
               t1.seal_confirm_time,-- 封窖完成时间
               t3.center_id,        -- 中心id
               t3.location_id       -- 车间id
        FROM t_po_workshop_pit_order_sap t1
                     LEFT JOIN t_po_workshop_pit t2 ON t1.pit_id = t2.id
                     LEFT JOIN t_po_workshop_pit_order t3 ON t1.order_code_id = t3.id
                WHERE t1.is_deleted = 0
                -- 自动筛选获取当前时间窖池订单处于入窖与空窖状态的订单以及窖池订单执行状态为待执行的窖池订单
                  AND (t1.pit_status = 0 OR t1.pit_status = 3 OR t1.order_status = 5)
                  and (t3.center_id = #{locationId} or t3.location_id = #{locationId})
        <if test="pitCode">
            AND t2.pit_code like concat('%', #{pitCode}, '%')
        </if>
        order by t1.id desc
    </select>

    <select id="getWorkshopPitOrderSapEmptyPitQuery"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderSapEmptyPitQueryVO">
        SELECT t1.id,               -- 单窖订单id
               t1.order_status,-- 订单状态
               t1.pit_status,       -- 窖池状态
               t2.layer,            -- 层级
               t1.empty_start_time, -- 空窖起始时间
               t1.pit_id,-- 窖池id
               t3.pit_code          -- 单窖池编号
        FROM t_po_workshop_pit_order_sap t1
                     LEFT JOIN t_po_workshop_pit_order t2 ON t1.order_code_id = t2.id
                     LEFT JOIN t_po_workshop_pit t3 ON t1.pit_id = t3.id
                where t1.is_deleted = 0
                -- 窖池订单的执行状态为起窖完成或者订单状态为起窖
                  and (t1.order_status = 2 or t1.pit_status = 3)
        <if test="pitCode != null">
            and t3.pit_code like concat('%', #{pitCode}, '%')
        </if>
        <if test="layer != null">
            AND t2.layer = #{layer}
        </if>
        and (t2.center_id = #{locationId} or t2.location_id = #{locationId})
    </select>

    <select id="getOpenPitFinishTimeBySapId" resultType="java.util.Date">
        SELECT t3.out_pot_time
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
                     LEFT JOIN t_po_workshop_pit_order_pot_task t3 ON t2.order_code = t3.out_order_code
        WHERE t3.is_deleted = 0
          AND t3.out_pot_time IS NOT NULL
          AND t2.id = #{id}
        ORDER BY t3.out_pot_time DESC
        LIMIT 1
    </select>

    <select id="getWorkshopPitOrderPriorityQueryPage"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderPriorityQueryVO">
        select t1.id,
               t2.full_pit_id,
               t1.pit_status,
               t1.layer,
               t1.priority_id
        from t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_full_pit t2 ON t1.pit_id = t2.id
             -- 订单在制，窖池状态为发酵
                where t1.is_current = 1
                  and t1.is_deleted = 0
                  and t1.pit_status = 1    -- 发酵状态
                  and t1.order_status != 2 -- 非起窖完成
        <if test="fullPitId != null">
            and t2.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        ORDER BY t1.priority_id DESC,
                 t1.create_time DESC
        LIMIT #{page},#{pageSize}
    </select>

    <select id="getWorkshopPitOrderPriorityQueryCount" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_order t1
                     LEFT JOIN t_po_workshop_full_pit t2 ON t1.pit_id = t2.id
             -- 订单在制，窖池状态为发酵
                where t1.is_current = 1
                  and t1.is_deleted = 0
                  and t1.pit_status = 1    -- 发酵状态
                  and t1.order_status != 2 -- 非起窖完成
        <if test="fullPitId != null">
            and t2.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
    </select>

    <select id="getOrderByVinasse" resultType="java.lang.Integer">
        select count(*)
        from t_po_workshop_pit_order t1
                     left join brewage_plan.t_pp_vinasse_source t2 on t1.vinasse_id = t2.id and t2.deleted = false
        where t1.is_deleted = false
          and t2.code = #{vinasse}
    </select>

    <select id="getOutPitNumByOrderId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_po_workshop_pit_order_pot_task pt
                     LEFT JOIN t_po_workshop_pit_order_sap pos ON pos.order_code = pt.out_order_code
        WHERE pos.order_code_id = #{orderId}
          and pt.is_deleted = 0
    </select>

    <select id="exportOrderData" resultMap="WorkshopPitOrderQuery">
        SELECT t1.id,
               t1.center_id,                                                                                                              -- 中心id
               t1.location_id,                                                                                                            -- 车间id
               (SELECT dp.day_play_id
                from t_po_workshop_pit_day_plan dp
                             LEFT JOIN t_po_workshop_pit_order po on dp.order_id =
                                                                     po.id
                WHERE po.id = t1.id
                ORDER BY plan_date
                limit 0,1)                                                                                          as day_play_id,       -- 日计划编号
               t1.order_code,-- 订单编号
               t1.layer,                                                                                                                  -- 层次
               t1.order_category_id,                                                                                                      -- 订单类型id
               t6.order_category_name,                                                                                                    -- 订单类型名称
               t1.pit_id,                                                                                                                 -- 连窖号id
               t4.full_pit_id,                                                                                                            -- 连窖号
               t1.formula_id,                                                                                                             -- 配方id
               t2.name                                                                                              AS formula_name,-- 配方名称
               t1.vinasse_id,                                                                                                             -- 糟源id
               t3.code                                                                                              AS vinasse_name,-- 糟源名称
               t1.order_status,-- 订单状态
               t1.pit_status,                                                                                                             -- 窖池状态
               t1.cycle_no_id,-- 糟赔排次id
               t7.name                                                                                              as cycle_no_name,     -- 糟赔排次名称
               t1.seal_confirm_time,                                                                                                      -- 封窖完成时间
               t1.turn_over_finish_time,                                                                                                  -- 翻窖完成时间
               t1.out_pit_finish_time,                                                                                                    -- 起窖完成时间
               t1.empty_start_time,                                                                                                       -- 空窖起始时间
               t1.open_pit_finish_time,                                                                                                   -- 开窖完成时间
               t1.handin_finish_time,                                                                                                     -- 交酒完成时间
               IF(t1.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time), TO_DAYS(t1.out_pit_finish_time) -
                                                                                     TO_DAYS(t1.seal_confirm_time)) AS fermentation_days, -- 发酵天数（修改成用状态判断）
               (ifnull(t1.in_pit_num, 0) + (select ifnull(SUM(in_pot_num_exc), 0)
                                            from t_po_workshop_pit_order_sap
                                            where order_code_id =
                                                  t1.id))                                                           as in_pit_num,
               (select ifnull(count(*), 0)
                from t_po_workshop_pit_order_pot_task pot
                             left join t_po_workshop_pit_order_sap pos on pot.out_order_code = pos.order_code
                where pos.order_code_id = t1.id
                  and pot.is_deleted = 0
                       )                                                                                            as out_pit_num,
               (select TRUNCATE(SUM(to60_quantity), 2)
                from t_po_workshop_receive_wine_order wo
                             LEFT JOIN t_po_workshop_pit_order_sap pos on wo.pit_order_id = pos.id
                where pos.order_code_id = t1.id
                       )                                                                                            as to60_quantity
        FROM t_po_workshop_pit_order t1
                     LEFT JOIN brewage_plan.t_pp_formula t2 ON t1.formula_id = t2.id
                     LEFT JOIN brewage_plan.t_pp_vinasse_source t3 on t1.vinasse_id = t3.id
                     LEFT JOIN t_po_workshop_full_pit t4 on t1.pit_id = t4.id
                     LEFT JOIN t_po_order_type t6 on t1.order_category_id = t6.id
                     LEFT JOIN brewage_plan.t_pp_row t7 on t1.cycle_no_id = t7.id
                where t1.is_deleted = 0
        <if test="orderCode != null">
            and t1.order_code like concat('%', #{orderCode}, '%')
        </if>
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="vinasseId != null">
            and t1.vinasse_id = #{vinasseId}
        </if>
        <if test="cycleYear != null">
            and t1.cycle_year = #{cycleYear}
        </if>
        <if test="cycleNoId != null">
            and t1.cycle_no_id = #{cycleNoId}
        </if>
        <if test="orderStatus != null">
            and t1.order_status = #{orderStatus}
        </if>
        <if test="fullPitId != null &amp;&amp; fullPitId != ''">
            and t4.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="pitStatus != null &amp;&amp; executionTimeStart == null">
            and t1.pit_status = #{pitStatus}
        </if>

        -- 执行状态和时间的条件查询
        <if test="orderStatus == 0 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 0 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 1 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 1 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 2 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 2 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 3 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 3 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 4 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 4 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 5 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 5 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>
        ORDER BY t1.pit_status = 3 desc, t1.id DESC, t1.pit_status = 5
    </select>

    <select id="roleTelephone" resultType="java.lang.String">
        select distinct mobile_phone
        from authority.sys_user u
                     inner join
                     authority.sys_user_role ur on ur.user_id = u.id
                     inner join
                     authority.sys_role r on ur.role_id = r.id
                     inner join
                     authority.sys_department d ON u.department_id = d.id
                     inner join
                     equipment.hv_bm_location h ON d.department_code = h.code
                where
                (r.name = '酿酒中心生产主管' or r.name = '酿酒中心车间主任' or r.name = '酿酒中心车间班长')
        <if test="centerId != null or locationId != null">
            and (
            <trim prefix="" suffixOverrides="or">
                <if test="centerId != null">
                    h.id = #{centerId} or
                </if>
                <if test="locationId != null">
                    h.id = #{locationId} or
                </if>
            </trim>
            )
        </if>
    </select>

    <select id="getFormulaDetailByFormulaId" resultType="java.util.Map">
        SELECT t1.formula_id,
               t1.material_name,
               t1.type,
               t1.quality,
               t3.material_type_name
        FROM brewage_plan.`t_pp_formula_detail` t1
                     LEFT JOIN materials.hv_bm_material t2 ON t1.material_id = t2.id
                     LEFT JOIN materials.hv_bm_material_type t3 ON t2.material_type = t3.id
        WHERE t1.formula_id = #{id}
          AND t1.type = 0
          AND t3.material_type_name = '高粱'
    </select>

    <select id="getFormulaUsed" resultType="com.hvisions.brewage.mkwine.vo.WorkshopPit.FormulaUsedVO">
        SELECT t1.formula_id, sum(ifnull(t1.quality, 0)) as quantity
        FROM brewage_plan.`t_pp_formula_detail` t1
                     JOIN materials.hv_bm_material t2 ON t1.material_id = t2.id
                     JOIN materials.hv_bm_material_type t3 ON t2.material_type = t3.id
        WHERE t1.type = 0
          AND t3.material_type_name = '高粱'
        group by t1.formula_id;
    </select>

    <select id="selectByOrderId" resultMap="OrderFormulaVOMap">
        select id, vinasse_id, formula_id, order_status, pit_status
        from t_po_workshop_pit_order
        where id = #{id}
    </select>
    <select id="selectPitConfirmData"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.PitConfirmDatVO">
        select po.id, po.order_code, s.`code` vinasse_code
        ,TIMESTAMPDIFF(YEAR, p.pit_create_date, now()) pitAge
        ,TIMESTAMPDIFF(DAY, po.seal_confirm_time, po.open_pit_finish_time) fermentationPeriod
        from t_po_workshop_pit_order po
        left join t_po_workshop_pit p on po.pit_id = p.id
        left join brewage_plan.t_pp_vinasse_source s on s.id = po.vinasse_id
        where po.is_deleted = 0
        and po.order_status = 3
        <if test="centerId != null">
            and po.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and po.location_id = #{locationId}
        </if>
        <if test="executionTimeStart != null">
            <![CDATA[
            and DATE_FORMAT(po.open_pit_finish_time,'%Y-%m-%d') >= date_format(#{executionTimeStart},'%Y-%m-%d')
            ]]>
        </if>
        <if test="executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(po.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>
        order by po.order_code desc
    </select>

    <resultMap id="OrderFormulaVOMap" type="com.hvisions.brewage.mkwine.vo.WorkshopPit.WorkShopPitOrderFormulaVO">
        <result column="id" property="id"/>
        <result column="vinasse_id" property="vinasseId"/>
        <result column="formula_id" property="formulaId"/>
        <result column="order_status" property="orderStatus"/>
        <result column="pit_status" property="pitStatus"/>
    </resultMap>

    <select id="selectWorkshopPitOrder" resultType="com.hvisions.brewage.dto.tpo.MaintainTaskAddDTO">
        SELECT
            a.center_id as centerId,
            a.location_id as locationId,
            b.full_pit_id as pitNo,
            a.order_code as pitOrder
        FROM
            t_po_workshop_pit_order a
                INNER JOIN t_po_workshop_full_pit b ON a.pit_id = b.id
        WHERE a.id=#{workshopPitOrderId,jdbcType=INTEGER}
    </select>
    <select id="selectOrderEmptyProductionSchedule"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        SELECT DISTINCT
            t1.id,
            t1.seal_confirm_time,
            t1.order_code,
            t1.layer,
            t1.pit_id,
            t3.full_pit_id,
            t1.center_id,
            t1.location_id,
            t2.id category_id,
            t2.CODE AS vinasse_name,
            (
                    COALESCE ( t1.in_pit_num, 0 ) + ( SELECT COALESCE ( SUM( in_pot_num_exc ), 0 ) FROM t_po_workshop_pit_order_sap WHERE order_code_id = t1.id )) AS in_pit_num
        FROM
            t_po_workshop_pit_order t1
                LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
                LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
        WHERE
            t1.is_deleted = 0
          AND t1.location_id = #{locationId}
          AND t1.center_id = #{centerId}
          AND t1.pit_status = 1
          and t3.full_pit_id not in (select t9.full_pit_id
             FROM t_po_workshop_pit_order t7
              LEFT JOIN brewage_plan.t_pp_vinasse_source t8 ON t7.vinasse_id = t8.id
              LEFT JOIN t_po_workshop_full_pit t9 ON t7.pit_id = t9.id
             WHERE t7.is_deleted = 0
               AND t7.location_id = #{locationId}
               AND t7.center_id = #{centerId}
               AND t7.pit_status != 5
               AND (t7.pit_status in (0,2,3,4,9) or t8.CODE in ("CJ", "CB", "CZ"))
             GROUP BY t9.full_pit_id)
        ORDER BY
            t1.seal_confirm_time
    </select>
    <select id="selectOrderProductionSchedule"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        SELECT DISTINCT
        t1.id,
        t1.seal_confirm_time,
        t1.order_code,
        t1.layer,
        t1.pit_id,
        t3.full_pit_id,
        t1.center_id,
        t1.location_id,
        t2.id category_id,
        t2.CODE AS vinasse_name,
        (
        COALESCE ( t1.in_pit_num, 0 ) + ( SELECT COALESCE ( SUM( in_pot_num_exc ), 0 ) FROM t_po_workshop_pit_order_sap WHERE order_code_id = t1.id )) AS in_pit_num
        FROM t_po_workshop_pit_order t1
        LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
        LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
        WHERE
        t1.is_deleted = 0
        AND t1.location_id = #{locationId}
        AND t1.center_id = #{centerId}
        AND t1.pit_status = 1
        AND t1.layer = 2
        <if test="dayPlanVinasseList != null and dayPlanVinasseList.size() != 0">
            AND t2.CODE IN
            <foreach collection="dayPlanVinasseList" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="continueIds != null and continueIds.size() != 0">
            and t1.id NOT IN
            <foreach collection="continueIds" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY
            t1.seal_confirm_time
    </select>
    <select id="selectWorkshopPitOrderPage"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderPageVO">
        SELECT t1.id, t1.order_code_id, t2.pit_status, DATEDIFF(NOW(), t2.seal_confirm_time)  as fermentationDays,t1.order_code, t2.layer, t3.pit_code, t2.vinasse_id, t4.CODE AS vinasse_name, t2.order_status, t2.cycle_no_id, t5.NAME AS cycle_no_name, t1.sap_order_code,
        t1.approve_status, e1.`name` center_name, e2.`name` location_name, t1.order_close_tag, t2.order_type,t2.pit_id,
        t2.in_pit_date, t1.seal_confirm_time, t1.turn_over_finish_time, t1.out_pit_finish_time, t1.empty_start_time, t1.open_pit_finish_time, t1.handin_finish_time, t6.full_pit_id,
        t2.center_id,t2.location_id,t2.order_code full_order_code,t2.order_category_id,t9.order_category_name,
        t2.collapse_choose_pit_id,t8.full_pit_id as collapse_choose_pit, t3.full_pit,
        t2.seal_water_test_time,
        t2.turnover_count, t2.execution_status,
        (IF(ISNULL(t2.out_pit_finish_time),TO_DAYS(NOW()) - TO_DAYS(t2.seal_confirm_time),TO_DAYS(t2.out_pit_finish_time) - TO_DAYS(t2.seal_confirm_time))) AS fermentation_time
       from t_po_workshop_pit_order t2
        LEFT JOIN t_po_workshop_pit_order_sap t1 on t1.order_code_id = t2.id
        LEFT JOIN t_po_workshop_pit t3 ON t1.pit_id = t3.id
        LEFT JOIN brewage_plan.t_pp_vinasse_source t4 ON t2.vinasse_id = t4.id
        LEFT JOIN brewage_plan.t_pp_row t5 ON t2.cycle_no_id = t5.id
        LEFT JOIN equipment.hv_bm_location e1 on e1.id = t2.center_id
        LEFT JOIN equipment.hv_bm_location e2 on e2.id = t2.location_id
        LEFT JOIN t_po_workshop_full_pit t6 on t2.pit_id = t6.id
        LEFT JOIN t_po_workshop_full_pit t8 on t2.collapse_choose_pit_id = t8.id
        LEFT JOIN t_po_order_type t9 on t2.order_category_id = t9.id
        where t2.is_deleted = 0 and t1.is_deleted = 0
        <if test="isFinish != null &amp;&amp; isFinish == true">
            and t2.pit_status = 5
        </if>
        <if test="isFinish != null &amp;&amp; isFinish == false">
            and t2.pit_status in (0, 1, 2, 3, 4)
        </if>
        <if test="isMove == true">
            and t2.collapse_choose_pit_id is not null
        </if>
        <if test="isMove == false">
            and t2.collapse_choose_pit_id is null
        </if>
        <if test="orderCode != null">
            and t2.order_code like concat('%', #{orderCode}, '%')
        </if>
        <if test="layer != null">
            and t2.layer = #{layer}
        </if>
        <if test="centerId != null">
            and t2.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t2.location_id = #{locationId}
        </if>
        <if test="vinasseId != null">
            and t2.vinasse_id = #{vinasseId}
        </if>
        <if test="cycleYear != null">
            and t2.cycle_year = #{cycleYear}
        </if>
        <if test="cycleNoId != null">
            and t2.cycle_no_id = #{cycleNoId}
        </if>
        <if test="fullPitId != null &amp;&amp; fullPitId != ''">
            and t6.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="pitCode != null &amp;&amp; pitCode != ''">
            and t3.pit_code like concat('%', #{pitCode}, '%')
        </if>
        <if test="pitStatus != null">
            and t1.pit_status = #{pitStatus}
        </if>
        <if test="orderStatus != null &amp;&amp; executionTimeStart == null">
            and t1.order_status = #{orderStatus}
        </if>
        <if test="sapOrderCode != null">
            and t1.sap_order_code like concat('%', #{sapOrderCode}, '%')
        </if>
        <if test="orderCodes != null and orderCodes.size() != 0">
            and t2.order_code IN
            <foreach collection="orderCodes" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        -- 执行状态和时间的条件查询
        <if test="orderStatus == 0 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 0 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.in_pit_date, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 1 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 1 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 2 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 2 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 3 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 3 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 4 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 4 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 5 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 5 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>
        ORDER BY t1.pit_status = 3 desc, t1.id DESC, t1.pit_status = 5
    </select>
    <select id="getWorkshopPitOrderSapPage"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.WorkshopPitOrderSapPageListVO">
        SELECT t1.id, t1.order_code, t2.layer, t3.pit_code, t2.vinasse_id, t4.CODE AS vinasse_name, t2.order_status, t2.cycle_no_id, t5.NAME AS cycle_no_name, t1.sap_order_code,
                t1.sync_input_tag, t1.sync_workhours_tag, t1.sync_turnover_tag, t1.sync_fermentation_workinghours_tag, t1.sync_turnover_input_tag, t1.sync_output_sec_tag,
                t1.sync_output_tag, t1.order_close_tag, t1.approve_status, t1.order_code_id, e1.`name` center_name, e2.`name` location_name,
                t2.in_pit_date, t1.sync_input_time, t1.sync_output_sec_time, t1.sync_turnover_input_time, t1.sync_turnover_time,
                t1.sync_fermentation_workinghours_time, t1.sync_workinghours_time, t1.sync_output_time, t1.order_finish_time,
                t1.receive_wine_time, t1.receive_wine_sync,
                (IF(ISNULL(t2.out_pit_finish_time),TO_DAYS(NOW()) - TO_DAYS(t2.seal_confirm_time),TO_DAYS(t2.out_pit_finish_time) - TO_DAYS(t2.seal_confirm_time))) AS fermentation_time, ifnull(t1.steamer_num_sync, 0) as steamer_num_sync,
                ((SELECT count(*) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = t1.order_code AND is_deleted = 0) + coalesce(t1.in_pot_num_exc, 0)) AS in_pit_num
        from t_po_workshop_pit_order t2
        LEFT JOIN t_po_workshop_pit_order_sap t1 on t1.order_code_id = t2.id
        LEFT JOIN t_po_workshop_pit t3 ON t1.pit_id = t3.id
        LEFT JOIN brewage_plan.t_pp_vinasse_source t4 ON t2.vinasse_id = t4.id
        LEFT JOIN brewage_plan.t_pp_row t5 ON t2.cycle_no_id = t5.id
        LEFT JOIN equipment.hv_bm_location e1 on e1.id = t2.center_id
        LEFT JOIN equipment.hv_bm_location e2 on e2.id = t2.location_id
        where t2.is_deleted = 0 and t1.is_deleted = 0
        <if test="centerId != null">
            and t2.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t2.location_id = #{locationId}
        </if>
        <if test="vinasseId != null">
            and t2.vinasse_id = #{vinasseId}
        </if>
        <if test="cycleYear != null">
            and t2.cycle_year = #{cycleYear}
        </if>
        <if test="cycleNoId != null">
            and t2.cycle_no_id = #{cycleNoId}
        </if>
        <if test="orderStatus != null &amp;&amp; executionTimeStart == null">
            and t1.order_status = #{orderStatus}
        </if>

        -- 执行状态和时间的条件查询
        <if test="orderStatus == 0 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t2.in_pit_date, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 0 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t2.in_pit_date, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 1 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 1 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.turn_over_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 2 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 2 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.out_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 3 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t6.open_pit_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 3 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t6.open_pit_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 4 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 4 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>

        <if test="orderStatus == 5 &amp;&amp; executionTimeStart != null">
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') >= date_format(#{executionTimeStart}, '%Y-%m-%d')
        </if>
        <if test="orderStatus == 5 &amp;&amp; executionTimeEnd != null">
            <![CDATA[
            and DATE_FORMAT(t1.handin_finish_time, '%Y-%m-%d') <= date_format(#{executionTimeEnd}, '%Y-%m-%d')
            ]]>
        </if>


        <if test="fullPitId != null">
            and t4.full_pit_id like concat('%', #{fullPitId}, '%')
        </if>
        <if test="dayPlayId != null">
            and t5.day_play_id like concat('%', #{dayPlayId}, '%')
        </if>
        <if test="orderCode != null and orderCode != ''">
            and t1.order_code like concat('%', #{orderCode}, '%')
        </if>
        <if test="sapOrderCode != null and sapOrderCode != '' ">
            and t1.sap_order_code like concat('%', #{sapOrderCode}, '%')
        </if>

        -- 发酵天数
        <if test="fermentationDaysMax != null">
            <![CDATA[
            AND IF(ISNULL(t1.out_pit_finish_time),
                   TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time) <= #{fermentationDaysMax}, TO_DAYS(
                                                                                                     t1.out_pit_finish_time) -
                                                                                             TO_DAYS(t1.seal_confirm_time) <=
                                                                                             #{fermentationDaysMax})
            ]]>
        </if>
        <if test="fermentationDaysMin != null">
            <![CDATA[
            AND IF(ISNULL(t1.out_pit_finish_time),
                   TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time) >= #{fermentationDaysMin}, TO_DAYS(
                                                                                                     t1.out_pit_finish_time) -
                                                                                             TO_DAYS(t1.seal_confirm_time) >=
                                                                                             #{fermentationDaysMin})
            ]]>
        </if>

        -- 订单关闭状态
        <if test="orderCloseTag == 0">
            and (
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            0 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            1)
            or
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            1 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            0)
            or
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            0 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            0)
            )
        </if>
        <if test="orderCloseTag == 1">
            and (
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            1 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) = 1
            )
        </if>
        <if test="orderCloseTag == 2">
            and (
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            2 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            3)
            or
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            3 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            2)
            or
            ((SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            2 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) =
            2)
            )
        </if>
        <if test="orderCloseTag == 3">
            and (
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 0,1) =
            3 and
            (SELECT order_close_tag from t_po_workshop_pit_order_sap WHERE order_code_id = t1.id LIMIT 1,1) = 3
            )
        </if>
        ORDER BY t1.pit_status = 3 desc, t1.id DESC, t1.pit_status = 5
    </select>
    <select id="selectOrderCollect"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderCollectVO">
        select t1.id,
               (select ifnull(TRUNCATE(SUM(to60_quantity), 2), 0) FROM t_po_workshop_receive_wine_order WHERE pit_order_id = t1.id) as to60_quantity,
               (select ifnull(TRUNCATE(SUM(quantity), 2), 0) FROM t_po_workshop_receive_wine_order WHERE pit_order_id = t1.id) as wine_quantity,
               (SELECT ifnull(count(*), 0) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = t1.order_code AND is_deleted = 0) AS in_pit_num,
               (SELECT ifnull(count(*), 0) FROM t_po_workshop_pit_order_pot_task WHERE t1.order_code = out_order_code AND is_deleted = 0) AS out_pit_num
        from t_po_workshop_pit_order_sap t1
         left join t_po_workshop_pit_order t2 on t2.id = t1.order_code_id and t2.is_deleted = 0
        where t1.id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectWorkshopPitOrderById"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkshopPitOrderDetailVO">
        SELECT t1.id, t1.order_code, t1.layer, t1.vinasse_id, t4.CODE AS vinasse_name, t1.order_status, t1.cycle_no_id, t5.NAME AS cycle_no_name, dp.day_play_id, t1.seal_water_test_time, t1.turnover_count, t1.execution_status, t1.pit_status, t1.order_type,
               e1.`name` center_name, e2.`name` location_name, t6.full_pit_id, t1.collapse_choose_pit_id,t8.full_pit_id as collapse_choose_pit, t1.in_pit_date, t1.seal_confirm_time, t1.turn_over_finish_time, t1.out_pit_finish_time, t1.empty_start_time, t1.open_pit_finish_time, t1.handin_finish_time,
               (IF(ISNULL(t1.out_pit_finish_time),TO_DAYS(NOW()) - TO_DAYS(t1.seal_confirm_time),TO_DAYS(t1.out_pit_finish_time) - TO_DAYS(t1.seal_confirm_time))) AS fermentation_time,
               (select TRUNCATE(SUM(to60_quantity), 2) FROM t_po_workshop_receive_wine_order
                    LEFT JOIN t_po_workshop_pit_order_sap pos on pos.order_code_id = t1.id WHERE pit_order_id = pos.id) as to60_quantity,
               (select TRUNCATE(SUM(quantity), 2) FROM t_po_workshop_receive_wine_order
                    LEFT JOIN t_po_workshop_pit_order_sap pos on pos.order_code_id = t1.id WHERE pit_order_id = pos.id) as wine_quantity,
               (SELECT ifnull(count(*), 0) FROM t_po_workshop_pit_order_pot_task
                    LEFT JOIN t_po_workshop_pit_order_sap pos on pos.order_code_id = t1.id WHERE in_order_code = pos.order_code) AS in_pit_num,
               (SELECT ifnull(count(*), 0) FROM t_po_workshop_pit_order_pot_task
                    LEFT JOIN t_po_workshop_pit_order_sap pos on pos.order_code_id = t1.id WHERE out_order_code = pos.order_code) AS out_pit_num,
               (select TRUNCATE(SUM(use_count), 2) FROM t_po_task_order_batch WHERE deleted = 0 and task_name = 'cellarTurn'  and material_name = 'HJ2' and order_code = t1.order_code) as hj2_quantity,
               (select TRUNCATE(SUM(use_count), 2) FROM t_po_task_order_batch WHERE deleted = 0 and task_name = 'cellarTurn' and task_material_type = '大曲' and order_code = t1.order_code) as turnover_qu_quantity,
               temp.fermentative_material_quantity, temp.crushed_grains_quantity,
               temp.ricehull_quantity, temp.back_alcoholic_quantity, temp.qu_quantity
        from t_po_workshop_pit_order t1
         LEFT JOIN brewage_plan.t_pp_vinasse_source t4 ON t1.vinasse_id = t4.id
         LEFT JOIN brewage_plan.t_pp_row t5 ON t1.cycle_no_id = t5.id
         LEFT JOIN equipment.hv_bm_location e1 on e1.id = t1.center_id
         LEFT JOIN equipment.hv_bm_location e2 on e2.id = t1.location_id
         LEFT JOIN t_po_workshop_full_pit t6 on t1.pit_id = t6.id
         LEFT JOIN t_po_workshop_full_pit t8 on t1.collapse_choose_pit_id = t8.id
         left join t_po_workshop_pit_day_plan dp on dp.order_id = t1.id and dp.id = (select max(id) from t_po_workshop_pit_day_plan where order_id = t1.id)
         left join (SELECT pos.order_code_id, ifnull(sum(t.fermentative_material_quantity), 0) fermentative_material_quantity,
                           ifnull(sum(t.crushed_grains_quantity), 0) crushed_grains_quantity,
                           ifnull(sum(t.ricehull_quantity), 0) ricehull_quantity,
                           ifnull(sum(t.back_alcoholic_quantity), 0) back_alcoholic_quantity,
                           ifnull(sum(t.qu_quantity), 0) qu_quantity
                    FROM t_po_workshop_pit_order_pot_task t
                             LEFT JOIN t_po_workshop_pit_order_sap pos on in_order_code = pos.order_code
                    GROUP BY pos.order_code_id) as temp on temp.order_code_id = t1.id
        where t1.is_deleted = 0 and t1.is_deleted = 0
        and t1.id = #{orderCodeId}
    </select>
    <select id="selectOrderOutputList"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkShopPitOrderOutputVO">
        select t.*, os.sap_order_code, os.order_code, wo.quantity, wo.to60_quantity,
               l1.name AS center_name,l2.name AS location_name,
               (CASE t.user_id IS NULL WHEN TRUE THEN '自动' ELSE u1.user_name END) AS user_name,
               rt.tank_id AS receive_tank_code,
               vs.code AS vinasse_name, vs.id AS vinasse_id, u2.user_name AS send_user_name, u3.user_name AS receive_user_name, tt.workshop_tmp_tank_code AS tmptank_code
        from t_po_workshop_pit_order_sap os
                 left join t_po_workshop_receive_wine_order wo on wo.pit_order_id = os.id
                 left join t_po_workshop_handin_task t on t.id = wo.handin_task_id
                 LEFT JOIN t_po_workshop_tmp_tank AS tt ON tt.id = t.tmptank_id
                 LEFT JOIN t_po_liquor_store_room_tank AS rt ON rt.id = t.tank_id
                 LEFT JOIN t_po_workshop_tmp_tank_category AS tc ON tc.id = t.tmp_tank_category_id
                 LEFT JOIN brewage_plan.t_pp_vinasse_source AS vs ON tc.fermentation_category_id = vs.id
                 LEFT JOIN `authority`.sys_user AS u1 ON u1.id = t.user_id
                 LEFT JOIN `authority`.sys_user AS u2 ON u2.id = t.send_userid
                 LEFT JOIN `authority`.sys_user AS u3 ON u3.id = t.receive_userid
                 LEFT JOIN `equipment`.hv_bm_location l1 ON l1.id = t.center_id
                 LEFT JOIN `equipment`.hv_bm_location l2 ON l2.id = t.location_id
        where os.is_deleted = 0 and os.order_code_id = #{orderCodeId}
    </select>
    <select id="selectOrderQualityList"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrder.WorkShopPitOrderQualityVO">
        select id.*, task.inspection_name, task.inspection_order, task.actual_inspection_time
        from(
                select max(it.id) id, ss.scenes_id
                from t_po_workshop_pit_order o
                 left join t_qa_inspection_task it on it.associated_document = o.order_code
                 LEFT JOIN t_qa_sampling_group sg ON sg.id = it.sampling_group_id AND sg.deleted = 0
                 LEFT JOIN t_qa_standard_scene ss ON ss.id = it.standard_scenes_id and ss.deleted = 0
                where it.deleted = 0 and o.id = #{orderCodeId}
                group by ss.scenes_id
            ) t
                left join t_qa_inspection_data id on t.id = id.inspection_id
                join t_qa_inspection_task task on task.id = t.id
        where id.deleted = 0 and id.type = 0
    </select>
    <select id="selectPotTaskAdjustInDataByOrder"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.InPitAssociatedOrderDTO">
        select os.order_code inOrderCode, v.`code` vinasse, p.pit_code inPitCode
        from t_po_workshop_pit_order o
         left join t_po_workshop_pit_order_sap os on os.order_code_id = o.id and os.is_deleted = 0
         left join t_po_workshop_pit p on os.pit_id = p.id
         left join brewage_plan.t_pp_vinasse_source v on v.id = o.vinasse_id
        where o.is_deleted = 0
          and o.order_code = #{inPitOrder}
        limit 1
    </select>
    <select id="selectPotTaskAdjustOutDataByOrder"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotAssociated.OutPitAssociatedOrderDTO">
        select os.order_code outOrderCode, v.`code` vinasse, p.pit_code outPitCode
        from t_po_workshop_pit_order o
         left join t_po_workshop_pit_order_sap os on os.order_code_id = o.id and os.is_deleted = 0
         left join t_po_workshop_pit p on os.pit_id = p.id
         left join brewage_plan.t_pp_vinasse_source v on v.id = o.vinasse_id
        where o.is_deleted = 0
          and o.order_code = #{outPitOrder}
        limit 1
    </select>
    <select id="selectOrderByIotPitCode"
            resultType="com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder">
        select o.* from t_po_workshop_pit_order o
        left join brewage_plan.t_pp_vinasse_source v on v.id = o.vinasse_id
        left join t_po_workshop_full_pit fp on fp.id = o.pit_id
        left join t_po_workshop_pit p on p.full_pit_id = fp.full_pit_id
        where o.is_deleted = 0 and v.production_base_id = 1 and o.pit_status != 5
            and v.`code` = #{vinasse}
            and p.pit_code = #{pitCode}
        order by o.id desc
            limit 1
    </select>
    <select id="getSapVinasse" resultType="java.util.Map">
        SELECT st.bad_source_id badSourceId, st.bad_source_type badSourceType
        FROM t_po_workshop_pit_order o
                 join brewage_plan.t_pp_vinasse_source vs on vs.id = o.vinasse_id
                 join brewage_technology.base_feeding_formula_sap_source_type st on st.bad_source_type = vs.`code`
        where o.is_deleted = 0 and o.id = #{id}
            limit 1
    </select>
    <select id="selectTopFloorOrderSchedule"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule">
        SELECT DISTINCT
            t1.id,
            t1.seal_confirm_time,
            t1.order_code,
            t1.layer,
            t1.pit_id,
            t3.full_pit_id,
            t1.center_id,
            t1.location_id,
            t2.id category_id,
            t2.CODE AS vinasse_name,
            (
                    COALESCE ( t1.in_pit_num, 0 ) + ( SELECT COALESCE ( SUM( in_pot_num_exc ), 0 ) FROM t_po_workshop_pit_order_sap WHERE order_code_id = t1.id )) AS in_pit_num
        FROM t_po_workshop_pit_order t1
                 LEFT JOIN brewage_plan.t_pp_vinasse_source t2 ON t1.vinasse_id = t2.id
                 LEFT JOIN t_po_workshop_full_pit t3 ON t1.pit_id = t3.id
        WHERE
            t1.is_deleted = 0
          AND t1.location_id = #{locationId}
          AND t1.center_id = #{centerId}
          AND t1.pit_status = 1
          AND t1.layer = 3
        <if test="fullPit != null and fullPit.size() != 0">
            AND t3.full_pit_id IN
            <foreach collection="fullPit" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>
