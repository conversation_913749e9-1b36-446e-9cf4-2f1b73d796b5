<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper">
    <resultMap id="WorkshopPitOrderPotTaskPickWineMap"
               type="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskPickWineVO">
        <result property="distillate2_Starttime" column="distillate2_starttime"/>
        <result property="distillate3_Starttime" column="distillate3_starttime"/>
        <result property="distillate4_Starttime" column="distillate4_starttime"/>
    </resultMap>
    <resultMap id="HandTaskGetPitOrderPotTaskVOMap"
               type="com.hvisions.brewage.mkwine.vo.LiquorConnectManage.HandTaskGetPitOrderPotTaskVO">
        <result property="distillateFirstStarttime" column="distillate_first_starttime"/>
        <result property="distillate2_Starttime" column="distillate2_starttime"/>
        <result property="distillate3_Starttime" column="distillate3_starttime"/>
        <result property="distillate4_Starttime" column="distillate4_starttime"/>
        <result property="distillateLastwaterStarttime" column="distillate_lastwater_starttime"/>
        <result property="distillateFirstTankId" column="distillate_first_tank_id"/>
        <result property="distillate2_TankId" column="distillate2_tank_id"/>
        <result property="distillate3_TankId" column="distillate3_tank_id"/>
        <result property="distillate4_TankId" column="distillate4_tank_id"/>
        <result property="distillateLastwaterTankId" column="distillate_lastwater_tank_id"/>
    </resultMap>
    <resultMap id="TPoWorkshopPitOrderPotTask"
               type="com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderPotTask">
        <result property="distillateFirstStarttime" column="distillate_first_starttime"/>
        <result property="distillate2Starttime" column="distillate2_starttime"/>
        <result property="distillate3Starttime" column="distillate3_starttime"/>
        <result property="distillate4Starttime" column="distillate4_starttime"/>
        <result property="distillateLastwaterStarttime" column="distillate_lastwater_starttime"/>
        <result property="distillateFirstTankId" column="distillate_first_tank_id"/>
        <result property="distillate2_TankId" column="distillate2_tank_id"/>
        <result property="distillate3_TankId" column="distillate3_tank_id"/>
        <result property="distillate4_TankId" column="distillate4_tank_id"/>
        <result property="distillateLastwaterTankId" column="distillate_lastwater_tank_id"/>
    </resultMap>


    <select id="getAllWorkshopPitOrderPotTask"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskVO">
        select *
        from t_po_workshop_pit_order_pot_task
        where is_deleted = 0
    </select>

    <insert id="addWorkshopPitOrderPotTask" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_po_workshop_pit_order_pot_task
                (
                create_time,
                is_deleted
        <if test="vinasse != null">
            ,vinasse
        </if>
        <if test="outPitCode != null">
            ,out_pit_code
        </if>
        <if test="outOrderCode">
            ,out_order_code
        </if>
        <if test="fermentativeMaterialQuantity != null">
            ,fermentative_material_quantity
        </if>
        <if test="crushedGrainsQuantity != null">
            ,crushed_grains_quantity
        </if>
        <if test="ricehullQuantity != null">
            ,ricehull_quantity
        </if>
        <if test="outTime != null">
            ,out_time
            ,pottask_starttime
        </if>
        <if test="quQuantity != null">
            ,qu_quantity
        </if>
        <if test="inPitTemperature != null">
            ,in_pit_temperature
        </if>
        <if test="groundTemperature != null">
            ,ground_temperature
        </if>
        <if test="inPitCode != null">
            ,in_pit_code
        </if>
        <if test="inOrderCode != null">
            ,in_order_code
        </if>
        <if test="orderStatus != null">
            ,order_status
        </if>
        <if test="backAlcoholicQuantity != null">
            ,back_alcoholic_quantity
        </if>
        <if test="changeShiftLogId != null">
            ,change_shift_log_id
        </if>
        <if test="backAlcoholicQuantityRevise != null">
            ,back_alcoholic_quantity_revise
        </if>
        <if test="collingEndtime != null">
            ,colling_endtime
        </if>
        <if test="collingStarttime != null">
            ,colling_starttime
        </if>
        <if test="outPotTime != null">
            ,out_pot_time
        </if>
        <if test="pottaskEndtime != null">
            ,pottask_endtime
        </if>

        <if test="outStartTime != null">
            ,out_start_time
        </if>
        <if test="fermentativeMaterialLevels != null">
            ,fermentative_material_levels
        </if>
        <if test="outDregsStartTime != null">
            ,out_dregs_start_time
        </if>
        <if test="outDregsEndTime != null">
            ,out_dregs_end_time
        </if>

        <if test="outDregsTimespan != null">
            ,out_dregs_timespan
        </if>

        <if test="augerStartTime != null">
            ,auger_start_time
        </if>

        <if test="augerEndTime != null">
            ,auger_end_time
        </if>

        <if test="moistenGrainNo != null">
            ,moisten_grain_no
        </if>

        <if test="moistenGrainWeight != null">
            ,moisten_grain_weight
        </if>

        <if test="moistenGrainLiquidLevel != null">
            ,moisten_grain_liquid_level
        </if>

        <if test="moistenGrainStartLevel != null">
            ,moisten_grain_start_level
        </if>

        <if test="moistenGrainEndLevel != null">
            ,moisten_grain_end_level
        </if>

        <if test="moistenGrainPumpTimespan != null">
            ,moisten_grain_pump_timespan
        </if>

        <if test="stirStartTime != null">
            ,stir_start_time
        </if>

        <if test="stirEndTime != null">
            ,stir_end_time
        </if>

        <if test="hjWeight != null">
            ,hj_weight
        </if>

        <if test="wjPumpStartTime != null">
            ,wj_pump_start_time
        </if>

        <if test="wjPumpEndTime != null">
            ,wj_pump_end_time
        </if>

        <if test="wjPumpTimespan != null">
            ,wj_pump_timespan
        </if>

        <if test="potTimespan != null">
            ,pot_timespan
        </if>

        <if test="feedMachinStartTime != null">
            ,feed_machin_starttime
        </if>

        <if test="ztBottomPotFlowRateStartReading != null">
            ,zt_bottom_pot_flow_rate_start_reading
        </if>

        <if test="ztBottomPotFlowRateEndReading != null">
            ,zt_bottom_pot_flow_rate_end_reading
        </if>

        <if test="hsBottomPotFlowRateStartReading != null">
            ,hs_bottom_pot_flow_rate_start_reading
        </if>

        <if test="hsBottomPotFlowRateEndReading != null">
            ,hs_bottom_pot_flow_rate_end_reading
        </if>

        <if test="qsBottomPotFlowRateStartReading != null">
            ,qs_bottom_pot_flow_rate_start_reading
        </if>

        <if test="qsBottomPotFlowRateEndReading != null">
            ,qs_bottom_pot_flow_rate_end_reading
        </if>

        <if test="wjBottomPotFlowRateStartReading != null">
            ,wj_bottom_pot_flow_rate_start_reading
        </if>

        <if test="wjBottomPotFlowRateEndReading != null">
            ,wj_bottom_pot_flow_rate_end_reading
        </if>

        <if test="outPotStartTime != null">
            ,out_pot_starttime
        </if>

        <if test="materialLssuanceStartTime != null">
            ,material_lssuance_starttime
        </if>

        <if test="distillate2_Timespan != null">
            ,distillate2_timespan
        </if>

        <if test="distillate3_Timespan != null">
            ,distillate3_timespan
        </if>

        <if test="distillate4_Timespan != null">
            ,distillate4_timespan
        </if>

        <if test="waterProportioning1 != null">
            ,water_proportioning_1
        </if>

        <if test="waterProportioning1_temperature != null">
            ,water_proportioning_1_temperature
        </if>

        <if test="waterProportioning2 != null">
            ,water_proportioning_2
        </if>

        <if test="waterProportioning2_temperature != null">
            ,water_proportioning_2_temperature
        </if>

        <if test="inYeastStartWeight != null">
            ,in_yeast_start_weight
        </if>

        <if test="inYeastEndWeight != null">
            ,in_yeast_end_weight
        </if>

        <if test="inYeastTemperature != null">
            ,in_yeast_temperature
        </if>

        <if test="inYeastStartTime != null">
            ,in_yeast_starttime
        </if>

        <if test="inYeastEndTime != null">
            ,in_yeast_endtime
        </if>

        <if test="fanRunningQuantity != null">
            ,fan_running_quantity
        </if>

        <if test="fanRunningTimespan != null">
            ,fan_running_timespan
        </if>

        <if test="fanStartTime != null">
            ,fan_starttime
        </if>

        <if test="fanEndTime != null">
            ,fan_endtime
        </if>

        )
                value
                (
                NOW(),
                0
        <if test="vinasse != null">
            ,#{vinasse}
        </if>
        <if test="outPitCode != null">
            ,#{outPitCode}
        </if>
        <if test="outOrderCode">
            ,#{outOrderCode}
        </if>
        <if test="fermentativeMaterialQuantity != null">
            ,#{fermentativeMaterialQuantity}
        </if>
        <if test="crushedGrainsQuantity != null">
            ,#{crushedGrainsQuantity}
        </if>
        <if test="ricehullQuantity != null">
            ,#{ricehullQuantity}
        </if>
        <if test="outTime != null">
            ,#{outTime}
                    ,#{outTime}
        </if>
        <if test="quQuantity != null">
            ,#{quQuantity}
        </if>
        <if test="inPitTemperature != null">
            ,#{inPitTemperature}
        </if>
        <if test="groundTemperature != null">
            ,#{groundTemperature}
        </if>
        <if test="inPitCode != null">
            ,#{inPitCode}
        </if>
        <if test="inOrderCode != null">
            ,#{inOrderCode}
        </if>
        <if test="orderStatus != null">
            ,#{orderStatus}
        </if>
        <if test="backAlcoholicQuantity != null">
            ,#{backAlcoholicQuantity}
        </if>
        <if test="changeShiftLogId != null">
            ,#{changeShiftLogId}
        </if>
        <if test="backAlcoholicQuantityRevise != null">
            ,#{backAlcoholicQuantityRevise}
        </if>
        <if test="collingEndtime != null">
            ,#{collingEndtime}
        </if>
        <if test="collingStarttime != null">
            ,#{collingStarttime}
        </if>
        <if test="outPotTime != null">
            ,#{outPotTime}
        </if>
        <if test="pottaskEndtime != null">
            ,#{pottaskEndtime}
        </if>

        <if test="outStartTime != null">
            ,#{outStartTime}
        </if>

        <if test="fermentativeMaterialLevels != null">
            ,#{fermentativeMaterialLevels}
        </if>

        <if test="outDregsStartTime != null">
            ,#{outDregsStartTime}
        </if>

        <if test="outDregsEndTime != null">
            ,#{outDregsEndTime}
        </if>

        <if test="outDregsTimespan != null">
            ,#{outDregsTimespan}
        </if>

        <if test="augerStartTime != null">
            ,#{augerStartTime}
        </if>

        <if test="augerEndTime != null">
            ,#{augerEndTime}
        </if>

        <if test="moistenGrainNo != null">
            ,#{moistenGrainNo}
        </if>

        <if test="moistenGrainWeight != null">
            ,#{moistenGrainWeight}
        </if>

        <if test="moistenGrainLiquidLevel != null">
            ,#{moistenGrainLiquidLevel}
        </if>

        <if test="moistenGrainStartLevel != null">
            ,#{moistenGrainStartLevel}
        </if>

        <if test="moistenGrainEndLevel != null">
            ,#{moistenGrainEndLevel}
        </if>

        <if test="moistenGrainPumpTimespan != null">
            ,#{moistenGrainPumpTimespan}
        </if>

        <if test="stirStartTime != null">
            ,#{stirStartTime}
        </if>

        <if test="stirEndTime != null">
            ,#{stirEndTime}
        </if>

        <if test="hjWeight != null">
            ,#{hjWeight}
        </if>

        <if test="wjPumpStartTime != null">
            ,#{wjPumpStartTime}
        </if>

        <if test="wjPumpEndTime != null">
            ,#{wjPumpEndTime}
        </if>

        <if test="wjPumpTimespan != null">
            ,#{wjPumpTimespan}
        </if>

        <if test="potTimespan != null">
            ,#{potTimespan}
        </if>

        <if test="feedMachinStartTime != null">
            ,#{feedMachinStartTime}
        </if>

        <if test="ztBottomPotFlowRateStartReading != null">
            ,#{ztBottomPotFlowRateStartReading}
        </if>

        <if test="ztBottomPotFlowRateEndReading != null">
            ,#{ztBottomPotFlowRateEndReading}
        </if>

        <if test="hsBottomPotFlowRateStartReading != null">
            ,#{hsBottomPotFlowRateStartReading}
        </if>

        <if test="hsBottomPotFlowRateEndReading != null">
            ,#{hsBottomPotFlowRateEndReading}
        </if>

        <if test="qsBottomPotFlowRateStartReading != null">
            ,#{qsBottomPotFlowRateStartReading}
        </if>

        <if test="qsBottomPotFlowRateEndReading != null">
            ,#{qsBottomPotFlowRateEndReading}
        </if>

        <if test="wjBottomPotFlowRateStartReading != null">
            ,#{wjBottomPotFlowRateStartReading}
        </if>

        <if test="wjBottomPotFlowRateEndReading != null">
            ,#{wjBottomPotFlowRateEndReading}
        </if>

        <if test="outPotStartTime != null">
            ,#{outPotStartTime}
        </if>

        <if test="materialLssuanceStartTime != null">
            ,#{materialLssuanceStartTime}
        </if>

        <if test="distillate2_Timespan != null">
            ,#{distillate2_Timespan}
        </if>

        <if test="distillate3_Timespan != null">
            ,#{distillate3_Timespan}
        </if>

        <if test="distillate4_Timespan != null">
            ,#{distillate4_Timespan}
        </if>

        <if test="waterProportioning1 != null">
            ,#{waterProportioning1}
        </if>

        <if test="waterProportioning1_temperature != null">
            ,#{waterProportioning1_temperature}
        </if>

        <if test="waterProportioning2 != null">
            ,#{waterProportioning2}
        </if>

        <if test="waterProportioning2_temperature != null">
            ,#{waterProportioning2_temperature}
        </if>

        <if test="inYeastStartWeight != null">
            ,#{inYeastStartWeight}
        </if>

        <if test="inYeastEndWeight != null">
            ,#{inYeastEndWeight}
        </if>

        <if test="inYeastTemperature != null">
            ,#{inYeastTemperature}
        </if>

        <if test="inYeastStartTime != null">
            ,#{inYeastStartTime}
        </if>

        <if test="inYeastEndTime != null">
            ,#{inYeastEndTime}
        </if>

        <if test="fanRunningQuantity != null">
            ,#{fanRunningQuantity}
        </if>

        <if test="fanRunningTimespan != null">
            ,#{fanRunningTimespan}
        </if>

        <if test="fanStartTime != null">
            ,#{fanStartTime}
        </if>

        <if test="fanEndTime != null">
            ,#{fanEndTime}
        </if>
        )
    </insert>

    <select id="workshopPitOrderPotTaskQueryPage"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskQueryVO">
        SELECT t1.id,
               t1.pot_serial_number,-- 甑口任务号
               t1.vinasse,-- 糟源类型
               t1.out_pit_code,-- 中控输入的起窖号（单窖）,起窖号
               IFNULL(t1.out_dregs_start_time, t1.out_time) as out_time,-- 出糟时间
               t1.fermentative_material_quantity,-- 糟醅重量,糟重
               t1.crushed_grains_quantity,-- 粉粮（高粱）重量,粮重
               t1.ricehull_quantity,-- 稻壳重量,糠重
               t1.back_alcoholic_quantity,-- 回酒重量
               t1.back_alcoholic_quantity_revise, -- 回酒修正
               t1.pot_num,-- 甑号（一条产线6口甑）
               t1.colling_machine_code,-- 摊晾机编号
               t1.qu_changed_weight,-- 加曲机重量变化
               t1.qu_hopper_code, -- 曲粉斗
               t1.qu_quantity,-- 曲粉重量(加曲机重量变化+添加重量)
               t1.in_pit_temperature,-- 入窖温度
               t1.ground_temperature,-- 地温
               t1.colling_endtime,-- 摊晾结束时间
               t1.in_pit_code,-- 中控输入的入窖号（单窖）
               t1.order_status,-- 任务状态（0待执行 1执行中 2已完成）
               t1.is_check,-- 核对确认
               t1.check_time,-- 确认时间,核对完成时间
               t1.check_user_id, -- 确认人id,核对人
               t2.user_name                                 as check_name,
               t1.in_order_code, -- 入窖窖池订单编号
               t1.out_pot_time, -- 出甑时间
               t1.loading_starttime, -- 开始上甑时间
               t1.loading_endtime, -- 结束上甑时间
               t1.controller_id,
               t1.qu_dou_id,
               t1.out_order_code -- 出窖窖池订单编号
        FROM t_po_workshop_pit_order_pot_task t1
                     left join authority.sys_user t2 on t1.check_user_id = t2.id
        left join t_po_workshop_pit_order_sap a on a.order_code = t1.in_order_code
        LEFT JOIN t_po_workshop_pit_order b on a.order_code_id = b.id
        <!--        LEFT JOIN t_po_workshop_pit t3 on (t1.out_pit_code = t3.pit_code AND t3.is_deleted = 0)-->
        where t1.is_deleted = 0
        <if test="export != null and export != ''">
            and t1.pot_serial_number is not null
        </if>
        <if test="lianjiaoCode != null">
            and b.order_code = #{lianjiaoCode}
        </if>
        <if test="orderCode != null">
            and (t1.in_order_code like concat('%', #{orderCode}, '%') or
                 t1.out_order_code like concat('%', #{orderCode}, '%'))
        </if>
        <if test="lineId != null">
            and t1.line_id = #{lineId}
        </if>
        <if test="outTime != null">
            and TO_DAYS(t1.out_time) = TO_DAYS(#{outTime})
        </if>
        <if test="outTimeStart != null">
            and DATE_FORMAT(t1.out_time, '%Y-%m-%d') between DATE_FORMAT(#{outTimeStart}, '%Y-%m-%d') and DATE_FORMAT(#{outTimeEnd}, '%Y-%m-%d')
        </if>
        <if test="outTimeStart == null">
            and DATE_FORMAT(t1.out_time, '%Y-%m-%d') between DATE_SUB(CURDATE(), INTERVAL 6 MONTH) and DATE_FORMAT(now(), '%Y-%m-%d')
        </if>
        <if test="vinasse != null">
            and t1.vinasse = #{vinasse}
        </if>
        <if test="outPitCode != null">
            and t1.out_pit_code like concat('%', #{outPitCode}, '%')
        </if>
        <if test="inPitCode != null">
            and t1.in_pit_code like concat('%', #{inPitCode}, '%')
        </if>
        <if test="outPitCode != null">
            and t1.out_pit_code like concat('%', #{outPitCode}, '%')
        </if>
        <if test="inPitCode != null">
            and t1.in_pit_code like concat('%', #{inPitCode}, '%')
        </if>
        <if test="centerId != null">
            and (t1.controller_id like concat('', #{controllerId}, '%'))
        </if>
        <if test="locationId != null">
            and (t1.controller_id like concat('', #{controllerId}, '%'))
        </if>
        <if test="potSerialNumber != null and potSerialNumber != ''">
            and t1.pot_serial_number = #{potSerialNumber}
        </if>
        <if test="crushedGrainsLotId != null">
            and t1.crushed_grains_lot_id = #{crushedGrainsLotId}
        </if>
        <if test="backAlcoholicLotId != null">
            and t1.back_alcoholic_lot_id = #{backAlcoholicLotId}
        </if>
        <if test="ricehullLotId != null">
            and t1.ricehull_lot_id = #{ricehullLotId}
        </if>
        <if test="quLotId != null">
            and t1.qu_lot_id = #{quLotId}
        </if>
        <if test="orderCodes != null and orderCodes.size() != 0">
            and a.order_code IN
            <foreach collection="orderCodes" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by out_time desc
        limit #{page},#{pageSize}
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_po_workshop_pit_order_pot_task t1
        left join authority.sys_user t2 on t1.check_user_id = t2.id
        left join t_po_workshop_pit_order_sap a on a.order_code = t1.in_order_code
        LEFT JOIN t_po_workshop_pit_order b on a.order_code_id = b.id
        <!--        LEFT JOIN t_po_workshop_pit t3 on (t1.out_pit_code = t3.pit_code AND t3.is_deleted = 0)-->
        where t1.is_deleted = 0
        <if test="export != null and export != ''">
            and t1.pot_serial_number is not null
        </if>
        <if test="orderCode != null">
            and (t1.in_order_code like concat('%', #{orderCode}, '%') or
                 t1.out_order_code like concat('%', #{orderCode}, '%'))
        </if>
        <if test="lineId != null">
            and t1.line_id = #{lineId}
        </if>
        <if test="outTime != null">
            and TO_DAYS(t1.out_time) = TO_DAYS(#{outTime})
        </if>
        <if test="outTimeStart != null">
            and DATE_FORMAT(t1.out_time, '%Y-%m-%d') between DATE_FORMAT(#{outTimeStart}, '%Y-%m-%d') and DATE_FORMAT(#{outTimeEnd}, '%Y-%m-%d')
        </if>
        <if test="vinasse != null">
            and t1.vinasse = #{vinasse}
        </if>
        <if test="outPitCode != null">
            and t1.out_pit_code like concat('%', #{outPitCode}, '%')
        </if>
        <if test="inPitCode != null">
            and t1.in_pit_code like concat('%', #{inPitCode}, '%')
        </if>
        <if test="outPitCode != null">
            and t1.out_pit_code like concat('%', #{outPitCode}, '%')
        </if>
        <if test="inPitCode != null">
            and t1.in_pit_code like concat('%', #{inPitCode}, '%')
        </if>
        <if test="centerId != null">
            and (t1.controller_id like concat('', #{controllerId}, '%'))
        </if>
        <if test="locationId != null">
            and (t1.controller_id like concat('', #{controllerId}, '%'))
        </if>
        <if test="potSerialNumber != null and potSerialNumber != ''">
            and t1.pot_serial_number = #{potSerialNumber}
        </if>
        <if test="crushedGrainsLotId != null">
            and t1.crushed_grains_lot_id = #{crushedGrainsLotId}
        </if>
        <if test="backAlcoholicLotId != null">
            and t1.back_alcoholic_lot_id = #{backAlcoholicLotId}
        </if>
        <if test="ricehullLotId != null">
            and t1.ricehull_lot_id = #{ricehullLotId}
        </if>
        <if test="quLotId != null">
            and t1.qu_lot_id = #{quLotId}
        </if>
        <if test="orderCodes != null and orderCodes.size() != 0">
            and a.order_code IN
            <foreach collection="orderCodes" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <insert id="addWorkshopPitOrderPotTaskCentral" useGeneratedKeys="false"
            keyProperty="id" keyColumn="id">
        insert into t_po_workshop_pit_order_pot_task
                (
                create_time,
                is_deleted
        <if test="moistenGrainWaterWeight != null">
            ,moisten_grain_water_weight
        </if>
        <if test="vinasse != null">
            ,vinasse
        </if>
        <if test="controllerId != null">
            ,controller_id
        </if>
        <if test="id != null">
            ,pot_serial_number
        </if>
        <if test="outTime != null">
            ,out_time
        </if>
        <if test="pottaskStarttime != null">
            ,pottask_starttime
        </if>
        <if test="vinasseId != null">
            ,vinasse_id
        </if>
        <if test="crushedGrainsQuantity != null">
            ,crushed_grains_quantity
            ,crushed_grains_quantity_back
        </if>
        <if test="ricehullQuantity != null">
            ,ricehull_quantity
            ,ricehull_quantity_back
        </if>
        <if test="fermentativeMaterialQuantity != null">
            ,fermentative_material_quantity
        </if>
        <if test="fermentativeMaterialVolume != null">
            ,fermentative_material_volume
        </if>
        <if test="outPitCode != null">
            ,out_pit_code
        </if>
        <if test="inPitCode != null">
            ,in_pit_code
        </if>
        <if test="beforeMixingTransmitStarttime != null">
            ,before_mixing_transmit_starttime
        </if>
        <if test="beforeMixingTransmitEndtime != null">
            ,before_mixing_transmit_endtime
        </if>
        <if test="beforeMixingTransmitTimespan != null">
            ,before_mixing_transmit_timespan
        </if>
        <if test="mixingStarttime != null">
            ,mixing_starttime
        </if>
        <if test="mixingEndtime != null">
            ,mixing_endtime
        </if>
        <if test="mixingTimespan != null">
            ,mixing_timespan
        </if>
        <if test="moistenGrainTimespan != null">
            ,moisten_grain_timespan
        </if>
        <if test="moistenGrainWaterTemperature != null">
            ,moisten_grain_water_temperature
        </if>
        <if test="beforeLoadingTransmitStarttime != null">
            ,before_loading_transmit_starttime
        </if>
        <if test="beforeLoadingTransmitEndtime != null">
            ,before_loading_transmit_endtime
        </if>
        <if test="beforeLoadingTransmitTimespan != null">
            ,before_loading_transmit_timespan
        </if>
        <if test="feedMachineCode != null">
            ,feed_machine_code
        </if>
        <if test="potNum != null">
            ,pot_num
        </if>
        <if test="loadingStarttime != null">
            ,loading_starttime
        </if>
        <if test="loadingEndtime != null">
            ,loading_endtime
        </if>
        <if test="loadingTimespan != null">
            ,loading_timespan
        </if>
        <if test="closeLidTime != null">
            ,close_lid_time
        </if>
        <if test="openLidTime != null">
            ,open_lid_time
        </if>
        <if test="distillateStarttime != null">
            ,distillate_starttime
        </if>
        <if test="distillateEndtime != null">
            ,distillate_endtime
        </if>
        <if test="distillateTimespan != null">
            ,distillate_timespan
        </if>
        <if test="steamingStarttime != null">
            ,steaming_starttime
        </if>
        <if test="steamingEndtime != null">
            ,steaming_endtime
        </if>
        <if test="steamingTimespan != null">
            ,steaming_timespan
        </if>
        <if test="outflowStarttime != null">
            ,outflow_starttime
        </if>
        <if test="distillateFirstStarttime != null">
            ,distillate_first_starttime
        </if>
        <if test="distillate2_Starttime != null">
            ,distillate2_starttime
        </if>
        <if test="distillate3_Starttime != null">
            ,distillate3_starttime
        </if>
        <if test="distillate4_Starttime != null">
            ,distillate4_starttime
        </if>
        <if test="distillateLastwaterStarttime != null">
            ,distillate_lastwater_starttime
        </if>
        <if test="outflowEndtime != null">
            ,outflow_endtime
        </if>
        <if test="turnPotTime != null">
            ,turn_pot_time
        </if>
        <if test="bottomPotLevel != null">
            ,bottom_pot_level
        </if>
        <if test="bottomPotTemperature != null">
            ,bottom_pot_temperature
        </if>
        <if test="liquorTailingQuantity != null">
            ,liquor_tailing_quantity
            ,back_alcoholic_quantity
            ,back_alcoholic_quantity_revise
        </if>
        <if test="waterQuantity != null">
            ,water_quantity
        </if>
        <if test="yellowWaterQuantity != null">
            ,yellow_water_quantity
        </if>
        <if test="waterProportioningStarttime != null">
            ,water_proportioning_starttime
        </if>
        <if test="waterProportioningEndtime != null">
            ,water_proportioning_endtime
        </if>
        <if test="waterProportioningTimespan != null">
            ,water_proportioning_timespan
        </if>
        <if test="waterProportioningTemperature != null">
            ,water_proportioning_temperature
        </if>
        <if test="waterProportioningQuantity != null">
            ,water_proportioning_quantity
        </if>
        <if test="collingFeedMachineCode != null">
            ,colling_feed_machine_code
        </if>
        <if test="collingStarttime != null">
            ,colling_starttime
        </if>
        <if test="collingEndtime != null">
            ,colling_endtime
        </if>
        <if test="collingMachineCode != null">
            ,colling_machine_code
        </if>
        <if test="collingTemperature != null">
            ,colling_temperature
        </if>
        <if test="quChangedWeight != null">
            ,qu_changed_weight
            ,qu_quantity
        </if>
        <if test="transportStarttime != null">
            ,transport_starttime
        </if>
        <if test="arriveLiftingAreaTime != null">
            ,arrive_lifting_area_time
        </if>
        <if test="updateTime != null">
            ,update_time
        </if>
        <if test="orderStatus != null">
            ,order_status
        </if>
        <if test="outPotTime != null">
            ,out_pot_time
        </if>
        <if test="pottaskEndtime != null">
            ,pottask_endtime
        </if>
        <if test="changeShiftLogId != null">
            ,change_shift_log_id
        </if>
        <if test="potSerialNumber != null">
            ,serial_number
        </if>
        <if test="outStartTime != null">
            ,out_start_time
        </if>
        <if test="fermentativeMaterialLevels != null">
            ,fermentative_material_levels
        </if>
        <if test="outDregsStartTime != null">
            ,out_dregs_start_time
        </if>
        <if test="outDregsEndTime != null">
            ,out_dregs_end_time
        </if>

        <if test="outDregsTimespan != null">
            ,out_dregs_timespan
        </if>

        <if test="augerStartTime != null">
            ,auger_start_time
        </if>

        <if test="augerEndTime != null">
            ,auger_end_time
        </if>

        <if test="moistenGrainNo != null">
            ,moisten_grain_no
        </if>

        <if test="moistenGrainWeight != null">
            ,moisten_grain_weight
        </if>

        <if test="moistenGrainLiquidLevel != null">
            ,moisten_grain_liquid_level
        </if>

        <if test="moistenGrainStartLevel != null">
            ,moisten_grain_start_level
        </if>

        <if test="moistenGrainEndLevel != null">
            ,moisten_grain_end_level
        </if>

        <if test="moistenGrainPumpTimespan != null">
            ,moisten_grain_pump_timespan
        </if>

        <if test="stirStartTime != null">
            ,stir_start_time
        </if>

        <if test="stirEndTime != null">
            ,stir_end_time
        </if>

        <if test="hjWeight != null">
            ,hj_weight
        </if>

        <if test="wjPumpStartTime != null">
            ,wj_pump_start_time
        </if>

        <if test="wjPumpEndTime != null">
            ,wj_pump_end_time
        </if>

        <if test="wjPumpTimespan != null">
            ,wj_pump_timespan
        </if>

        <if test="potTimespan != null">
            ,pot_timespan
        </if>

        <if test="feedMachinStartTime != null">
            ,feed_machin_starttime
        </if>

        <if test="ztBottomPotFlowRateStartReading != null">
            ,zt_bottom_pot_flow_rate_start_reading
        </if>

        <if test="ztBottomPotFlowRateEndReading != null">
            ,zt_bottom_pot_flow_rate_end_reading
        </if>

        <if test="hsBottomPotFlowRateStartReading != null">
            ,hs_bottom_pot_flow_rate_start_reading
        </if>

        <if test="hsBottomPotFlowRateEndReading != null">
            ,hs_bottom_pot_flow_rate_end_reading
        </if>

        <if test="qsBottomPotFlowRateStartReading != null">
            ,qs_bottom_pot_flow_rate_start_reading
        </if>

        <if test="qsBottomPotFlowRateEndReading != null">
            ,qs_bottom_pot_flow_rate_end_reading
        </if>

        <if test="wjBottomPotFlowRateStartReading != null">
            ,wj_bottom_pot_flow_rate_start_reading
        </if>

        <if test="wjBottomPotFlowRateEndReading != null">
            ,wj_bottom_pot_flow_rate_end_reading
        </if>

        <if test="outPotStartTime != null">
            ,out_pot_starttime
        </if>

        <if test="materialLssuanceStartTime != null">
            ,material_lssuance_starttime
        </if>

        <if test="distillate2_Timespan != null">
            ,distillate2_timespan
        </if>

        <if test="distillate3_Timespan != null">
            ,distillate3_timespan
        </if>

        <if test="distillate4_Timespan != null">
            ,distillate4_timespan
        </if>

        <if test="waterProportioning1 != null">
            ,water_proportioning_1
        </if>

        <if test="waterProportioning1_temperature != null">
            ,water_proportioning_1_temperature
        </if>

        <if test="waterProportioning2 != null">
            ,water_proportioning_2
        </if>

        <if test="waterProportioning2_temperature != null">
            ,water_proportioning_2_temperature
        </if>

        <if test="inYeastStartWeight != null">
            ,in_yeast_start_weight
        </if>

        <if test="inYeastEndWeight != null">
            ,in_yeast_end_weight
        </if>

        <if test="inYeastTemperature != null">
            ,in_yeast_temperature
        </if>

        <if test="inYeastStartTime != null">
            ,in_yeast_starttime
        </if>

        <if test="inYeastEndTime != null">
            ,in_yeast_endtime
        </if>

        <if test="fanRunningQuantity != null">
            ,fan_running_quantity
        </if>

        <if test="fanRunningTimespan != null">
            ,fan_running_timespan
        </if>

        <if test="fanStartTime != null">
            ,fan_starttime
        </if>

        <if test="fanEndTime != null">
            ,fan_endtime
        </if>

        )
                value
                (
                NOW(),
                0
        <if test="moistenGrainWaterWeight != null">
            ,moisten_grain_water_weight
        </if>
        <if test="vinasse != null">
            ,#{vinasse}
        </if>
        <if test="controllerId != null">
            ,#{controllerId}
        </if>
        <if test="id != null">
            ,#{id}
        </if>
        <if test="outTime != null">
            ,#{outTime}
        </if>
        <if test="pottaskStarttime != null">
            ,#{pottaskStarttime}
        </if>
        <if test="vinasseId != null">
            , #{vinasseId}
        </if>
        <if test="crushedGrainsQuantity != null">
            ,#{crushedGrainsQuantity}
                    ,#{crushedGrainsQuantity}
        </if>
        <if test="ricehullQuantity != null">
            ,#{ricehullQuantity}
                    ,#{ricehullQuantity}
        </if>
        <if test="fermentativeMaterialQuantity != null">
            , #{fermentativeMaterialQuantity}
        </if>
        <if test="fermentativeMaterialVolume != null">
            ,#{fermentativeMaterialVolume}
        </if>
        <if test="outPitCode != null">
            ,#{outPitCode}
        </if>
        <if test="inPitCode != null">
            ,#{inPitCode}
        </if>
        <if test="beforeMixingTransmitStarttime != null">
            ,#{beforeMixingTransmitStarttime}
        </if>
        <if test="beforeMixingTransmitEndtime != null">
            ,#{beforeMixingTransmitEndtime}
        </if>
        <if test="beforeMixingTransmitTimespan != null">
            , #{beforeMixingTransmitTimespan}
        </if>
        <if test="mixingStarttime != null">
            ,#{mixingStarttime}
        </if>
        <if test="mixingEndtime != null">
            ,#{mixingEndtime}
        </if>
        <if test="mixingTimespan != null">
            ,#{mixingTimespan}
        </if>
        <if test="moistenGrainTimespan != null">
            ,#{moistenGrainTimespan}
        </if>
        <if test="moistenGrainWaterTemperature != null">
            ,#{moistenGrainWaterTemperature}
        </if>
        <if test="beforeLoadingTransmitStarttime != null">
            , #{beforeLoadingTransmitStarttime}
        </if>
        <if test="beforeLoadingTransmitEndtime != null">
            ,#{beforeLoadingTransmitEndtime}
        </if>
        <if test="beforeLoadingTransmitTimespan != null">
            ,#{beforeLoadingTransmitTimespan}
        </if>
        <if test="feedMachineCode != null">
            ,#{feedMachineCode}
        </if>
        <if test="potNum != null">
            ,#{potNum}
        </if>
        <if test="loadingStarttime != null">
            ,#{loadingStarttime}
        </if>
        <if test="loadingEndtime != null">
            ,#{loadingEndtime}
        </if>
        <if test="loadingTimespan != null">
            ,#{loadingTimespan}
        </if>
        <if test="closeLidTime != null">
            ,#{closeLidTime}
        </if>
        <if test="openLidTime != null">
            ,#{openLidTime}
        </if>
        <if test="distillateStarttime != null">
            ,#{distillateStarttime}
        </if>
        <if test="distillateEndtime != null">
            ,#{distillateEndtime}
        </if>
        <if test="distillateTimespan != null">
            ,#{distillateTimespan}
        </if>
        <if test="steamingStarttime != null">
            ,#{steamingStarttime}
        </if>
        <if test="steamingEndtime != null">
            ,#{steamingEndtime}
        </if>
        <if test="steamingTimespan != null">
            ,#{steamingTimespan}
        </if>
        <if test="outflowStarttime != null">
            ,#{outflowStarttime}
        </if>
        <if test="distillateFirstStarttime != null">
            ,#{distillateFirstStarttime}
        </if>
        <if test="distillate2_Starttime != null">
            ,#{distillate2_Starttime}
        </if>
        <if test="distillate3_Starttime != null">
            ,#{distillate3_Starttime}
        </if>
        <if test="distillate4_Starttime != null">
            ,#{distillate4_Starttime}
        </if>
        <if test="distillateLastwaterStarttime != null">
            ,#{distillateLastwaterStarttime}
        </if>
        <if test="outflowEndtime != null">
            ,#{outflowEndtime}
        </if>
        <if test="turnPotTime != null">
            ,#{turnPotTime}
        </if>
        <if test="bottomPotLevel != null">
            ,#{bottomPotLevel}
        </if>
        <if test="bottomPotTemperature != null">
            ,#{bottomPotTemperature}
        </if>
        <if test="liquorTailingQuantity != null">
            ,#{liquorTailingQuantity}
                    ,#{liquorTailingQuantity}
                    ,#{liquorTailingQuantity}
        </if>
        <if test="waterQuantity != null">
            ,#{waterQuantity}
        </if>
        <if test="yellowWaterQuantity != null">
            ,#{yellowWaterQuantity}
        </if>
        <if test="waterProportioningStarttime != null">
            ,#{waterProportioningStarttime}
        </if>
        <if test="waterProportioningEndtime != null">
            ,#{waterProportioningEndtime}
        </if>
        <if test="waterProportioningTimespan != null">
            ,#{waterProportioningTimespan}
        </if>
        <if test="waterProportioningTemperature != null">
            ,#{waterProportioningTemperature}
        </if>
        <if test="waterProportioningQuantity != null">
            ,#{waterProportioningQuantity}
        </if>
        <if test="collingFeedMachineCode != null">
            ,#{collingFeedMachineCode}
        </if>
        <if test="collingStarttime != null">
            ,#{collingStarttime}
        </if>
        <if test="collingEndtime != null">
            ,#{collingEndtime}
        </if>
        <if test="collingMachineCode != null">
            ,#{collingMachineCode}
        </if>
        <if test="collingTemperature != null">
            ,#{collingTemperature}
        </if>
        <if test="quChangedWeight != null">
            ,#{quChangedWeight}
                    ,#{quChangedWeight}
        </if>
        <if test="transportStarttime != null">
            ,#{transportStarttime}
        </if>
        <if test="arriveLiftingAreaTime != null">
            ,#{arriveLiftingAreaTime}
        </if>
        <if test="updateTime != null">
            ,#{updateTime}
        </if>
        <if test="orderStatus != null">
            ,#{orderStatus}
        </if>
        <if test="outPotTime != null">
            ,#{outPotTime}
        </if>
        <if test="pottaskEndtime != null">
            ,#{pottaskEndtime}
        </if>
        <if test="changeShiftLogId != null">
            ,#{changeShiftLogId}
        </if>
        <if test="potSerialNumber != null">
            ,#{potSerialNumber}
        </if>
        <if test="outStartTime != null">
            ,#{outStartTime}
        </if>

        <if test="fermentativeMaterialLevels != null">
            ,#{fermentativeMaterialLevels}
        </if>

        <if test="outDregsStartTime != null">
            ,#{outDregsStartTime}
        </if>

        <if test="outDregsEndTime != null">
            ,#{outDregsEndTime}
        </if>

        <if test="outDregsTimespan != null">
            ,#{outDregsTimespan}
        </if>

        <if test="augerStartTime != null">
            ,#{augerStartTime}
        </if>

        <if test="augerEndTime != null">
            ,#{augerEndTime}
        </if>

        <if test="moistenGrainNo != null">
            ,#{moistenGrainNo}
        </if>

        <if test="moistenGrainWeight != null">
            ,#{moistenGrainWeight}
        </if>

        <if test="moistenGrainLiquidLevel != null">
            ,#{moistenGrainLiquidLevel}
        </if>

        <if test="moistenGrainStartLevel != null">
            ,#{moistenGrainStartLevel}
        </if>

        <if test="moistenGrainEndLevel != null">
            ,#{moistenGrainEndLevel}
        </if>

        <if test="moistenGrainPumpTimespan != null">
            ,#{moistenGrainPumpTimespan}
        </if>

        <if test="stirStartTime != null">
            ,#{stirStartTime}
        </if>

        <if test="stirEndTime != null">
            ,#{stirEndTime}
        </if>

        <if test="hjWeight != null">
            ,#{hjWeight}
        </if>

        <if test="wjPumpStartTime != null">
            ,#{wjPumpStartTime}
        </if>

        <if test="wjPumpEndTime != null">
            ,#{wjPumpEndTime}
        </if>

        <if test="wjPumpTimespan != null">
            ,#{wjPumpTimespan}
        </if>

        <if test="potTimespan != null">
            ,#{potTimespan}
        </if>

        <if test="feedMachinStartTime != null">
            ,#{feedMachinStartTime}
        </if>

        <if test="ztBottomPotFlowRateStartReading != null">
            ,#{ztBottomPotFlowRateStartReading}
        </if>

        <if test="ztBottomPotFlowRateEndReading != null">
            ,#{ztBottomPotFlowRateEndReading}
        </if>

        <if test="hsBottomPotFlowRateStartReading != null">
            ,#{hsBottomPotFlowRateStartReading}
        </if>

        <if test="hsBottomPotFlowRateEndReading != null">
            ,#{hsBottomPotFlowRateEndReading}
        </if>

        <if test="qsBottomPotFlowRateStartReading != null">
            ,#{qsBottomPotFlowRateStartReading}
        </if>

        <if test="qsBottomPotFlowRateEndReading != null">
            ,#{qsBottomPotFlowRateEndReading}
        </if>

        <if test="wjBottomPotFlowRateStartReading != null">
            ,#{wjBottomPotFlowRateStartReading}
        </if>

        <if test="wjBottomPotFlowRateEndReading != null">
            ,#{wjBottomPotFlowRateEndReading}
        </if>

        <if test="outPotStartTime != null">
            ,#{outPotStartTime}
        </if>

        <if test="materialLssuanceStartTime != null">
            ,#{materialLssuanceStartTime}
        </if>

        <if test="distillate2_Timespan != null">
            ,#{distillate2_Timespan}
        </if>

        <if test="distillate3_Timespan != null">
            ,#{distillate3_Timespan}
        </if>

        <if test="distillate4_Timespan != null">
            ,#{distillate4_Timespan}
        </if>

        <if test="waterProportioning1 != null">
            ,#{waterProportioning1}
        </if>

        <if test="waterProportioning1_temperature != null">
            ,#{waterProportioning1_temperature}
        </if>

        <if test="waterProportioning2 != null">
            ,#{waterProportioning2}
        </if>

        <if test="waterProportioning2_temperature != null">
            ,#{waterProportioning2_temperature}
        </if>

        <if test="inYeastStartWeight != null">
            ,#{inYeastStartWeight}
        </if>

        <if test="inYeastEndWeight != null">
            ,#{inYeastEndWeight}
        </if>

        <if test="inYeastTemperature != null">
            ,#{inYeastTemperature}
        </if>

        <if test="inYeastStartTime != null">
            ,#{inYeastStartTime}
        </if>

        <if test="inYeastEndTime != null">
            ,#{inYeastEndTime}
        </if>

        <if test="fanRunningQuantity != null">
            ,#{fanRunningQuantity}
        </if>

        <if test="fanRunningTimespan != null">
            ,#{fanRunningTimespan}
        </if>

        <if test="fanStartTime != null">
            ,#{fanStartTime}
        </if>

        <if test="fanEndTime != null">
            ,#{fanEndTime}
        </if>
        )
    </insert>

    <update id="workshopPitOrderPotTaskPickWine">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            <if test="vinasse != null">
                vinasse = #{vinasse},
            </if>
            <if test="potNum != null">
                pot_num = #{potNum},
            </if>
            <if test="outPitCode != null">
                out_pit_code = #{outPitCode},
            </if>
            <if test="distillateTimespan != null">
                distillate_timespan = #{distillateTimespan},
            </if>
            <if test="closeLidTime != null">
                close_lid_time = #{closeLidTime},
            </if>
            <if test="distillateFlowspeed != null">
                distillate_flowspeed = #{distillateFlowspeed},
            </if>
            <if test="distillateTemperature != null">
                distillate_temperature = #{distillateTemperature},
            </if>
            <if test="distillateFirstStarttime != null">
                distillate_first_starttime = #{distillateFirstStarttime},
            </if>
            distillate2_starttime = #{distillate2_Starttime},
            distillate3_starttime = #{distillate3_Starttime},
            distillate4_starttime = #{distillate4_Starttime},
            distillate_lastwater_starttime = #{distillateLastwaterStarttime},
            <if test="distillateEndtime != null">
                distillate_endtime = #{distillateEndtime},
                outflow_endtime = #{distillateEndtime},
            </if>
            <if test="distillateNotes != null">
                distillate_notes = #{distillateNotes},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="workshopPitOrderPotTaskColling">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            <if test="turnPotTime != null">
                turn_pot_time = #{turnPotTime},
            </if>
            <if test="collingMachineCode != null">
                colling_machine_code = #{collingMachineCode},
            </if>
            <if test="collingStarttime != null">
                colling_starttime = #{collingStarttime},
            </if>
            <if test="collingEndtime != null">
                colling_endtime = #{collingEndtime},
            </if>
            <if test="collingTimespan != null">
                colling_timespan = #{collingTimespan},
            </if>
            <if test="collingNotes != null">
                colling_notes = #{collingNotes},
            </if>
            in_pit_temperature = #{inPitTemperature},
            ground_temperature = #{groundTemperature},
            <if test="quChangedWeight != null">
                qu_changed_weight = #{quChangedWeight},
            </if>
            <if test="quHopperCode != null">
                qu_hopper_code = #{quHopperCode},
            </if>
            <if test="quQuantity != null">
                qu_quantity = #{quQuantity},
            </if>
            colling_machine_code = #{collingMachineCode}
        </trim>
        where id = #{id}
    </update>

    <select id="getWorkshopPitOrderPotTaskPickWine" resultMap="WorkshopPitOrderPotTaskPickWineMap">
        SELECT id,
        vinasse,
        pot_num,
        out_pit_code,
        TIMESTAMPDIFF(MINUTE, distillate_first_starttime, distillate_endtime) AS distillate_timespan,
        close_lid_time,
        distillate_flowspeed,
        distillate_temperature,
        distillate_first_starttime,
        distillate2_starttime,
        distillate3_starttime,
        distillate4_starttime,
        distillate_lastwater_starttime,
        ifnull(distillate_endtime, outflow_endtime) as distillate_endtime,
        distillate_notes
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskColling"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskCollingVO">
        SELECT id,
        turn_pot_time,
        colling_machine_code,
        colling_starttime,
        colling_endtime,
        TIMESTAMPDIFF(SECOND, colling_starttime, colling_endtime) AS colling_timespan,
        colling_notes,
        in_pit_temperature,
        ground_temperature,
        qu_changed_weight,
        qu_hopper_code,
        qu_quantity,
        vinasse
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskUpdate"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskUpdateVO">
        SELECT *
        FROM
        -- 起窖号找窖池订单
        t_po_workshop_pit_order_pot_task t1
        WHERE t1.id = #{id}
    </select>

    <update id="workshopPitOrderPotTaskUpdate">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            <if test="outPotTime != null">
                out_pot_time = #{outPotTime},
            </if>
            <if test="vinasse != null">
                vinasse = #{vinasse},
            </if>
            <if test="outPitCode">
                out_pit_code = #{outPitCode},
            </if>
            <if test="crushedGrainsQuantity != null">
                crushed_grains_quantity = #{crushedGrainsQuantity},
                crushed_grains_quantity_back = #{crushedGrainsQuantity},
            </if>
            <if test="ricehullQuantity != null">
                ricehull_quantity = #{ricehullQuantity},
                ricehull_quantity_back = #{ricehullQuantity},
            </if>
            <if test="backAlcoholicQuantity != null">
                back_alcoholic_quantity = #{backAlcoholicQuantity},
            </if>
            <if test="quQuantity != null">
                qu_quantity = #{quQuantity},
                qu_changed_weight = #{quQuantity},
            </if>
            <if test="inPitTemperature != null">
                in_pit_temperature = #{inPitTemperature},
            </if>
            <if test="groundTemperature != null">
                ground_temperature = #{groundTemperature},
            </if>
            <if test="inPitCode != null">
                in_pit_code = #{inPitCode},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="outOrderCode != null">
                out_order_code = #{outOrderCode},
            </if>
            in_order_code = #{inOrderCode},
            <if test="backAlcoholicQuantityRevise != null">
                back_alcoholic_quantity_revise = #{backAlcoholicQuantityRevise},
                back_alcoholic_quantity = #{backAlcoholicQuantityRevise},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteWorkshopPitOrderPotTask">
        update t_po_workshop_pit_order_pot_task
        set is_deleted = 1
        where id = #{id}
    </update>

    <select id="getWorkshopPitOrderPotTaskMgMx"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskMgMxVO">
        SELECT id,
        moisten_grain_timespan,
        before_mixing_transmit_starttime,
        feed_machine_code,
        mixing_starttime,
        moisten_grain_water_temperature,
        -- 拌合时长
        TIMESTAMPDIFF(MINUTE, mixing_starttime, mixing_endtime) AS mixing_timespan,
        mixing_endtime
        FROM t_po_workshop_pit_order_pot_task
        WHERE id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskBeforeLoading"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskBeforeLoadingVO">
        SELECT id,
        before_loading_transmit_starttime,
        pot_num,
        loading_starttime,
        before_loading_transmit_endtime,
        -- 上甑前输送结束时间
        TIMESTAMPDIFF(MINUTE, loading_starttime, loading_endtime) AS loading_timespan,
        loading_endtime
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskBottomPot"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskBottomPotVO">
        SELECT id,
        bottom_pot_level,
        liquor_tailing_quantity,
        water_quantity,
        bottom_pot_temperature,
        yellow_water_quantity,
        back_alcoholic_quantity
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskDistillate"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskDistillateVO">
        SELECT id,
        close_lid_time,
        steam_valve_starttime,
        steam_valve_endtime,
        -- 蒸馏时长
        distillate_timespan,
        -- 蒸酒时长
        TIMESTAMPDIFF(MINUTE, distillate_first_starttime,
        distillate_lastwater_starttime) as distillate_wine_timespan,
        -- 蒸粮时长
        TIMESTAMPDIFF(MINUTE, distillate4_starttime, out_pot_time) as distillate_grain_Timespan
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskWaterProportioning"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskWaterProportioningVO">
        SELECT id,
        water_proportioning_temperature,
        water_proportioning_timespan,
        open_lid_time,
        water_proportioning_quantity,
        out_pot_time
        FROM t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <select id="getWorkshopPitOrderPotTaskDetailsTable"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskDetailsTableVO">
        select *
        from t_po_workshop_pit_order_pot_task
        where id = #{id}
    </select>

    <update id="updateWorkshopPitOrderPotTaskCentral">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            modify_time = now(),
            <if test="moistenGrainWaterWeight != null">
                moisten_grain_water_weight = #{moistenGrainWaterWeight},
            </if>
            <if test="vinasse != null">
                vinasse = #{vinasse},
            </if>
            <if test="outTime != null">
                out_time = #{outTime},
            </if>
            <if test="pottaskStarttime != null">
                pottask_starttime = #{pottaskStarttime},
            </if>
            <if test="vinasseId != null">
                vinasse_id = #{vinasseId},
            </if>
            <if test="crushedGrainsQuantity != null">
                crushed_grains_quantity = #{crushedGrainsQuantity},
            </if>
            <if test="ricehullQuantity != null">
                ricehull_quantity = #{ricehullQuantity},
            </if>
            <if test="fermentativeMaterialQuantity != null">
                fermentative_material_quantity = #{fermentativeMaterialQuantity},
            </if>
            <if test="fermentativeMaterialVolume != null">
                fermentative_material_volume = #{fermentativeMaterialVolume},
            </if>
            <if test="outPitCode != null">
                out_pit_code = #{outPitCode},
            </if>
            <if test="inPitCode != null">
                in_pit_code = #{inPitCode},
            </if>
            <if test="beforeMixingTransmitStarttime != null">
                before_mixing_transmit_starttime = #{beforeMixingTransmitStarttime},
            </if>
            <if test="beforeMixingTransmitEndtime != null">
                before_mixing_transmit_endtime = #{beforeMixingTransmitEndtime},
            </if>
            <if test="beforeMixingTransmitTimespan != null">
                before_mixing_transmit_timespan = #{beforeMixingTransmitTimespan},
            </if>
            <if test="mixingStarttime != null">
                mixing_starttime = #{mixingStarttime},
            </if>
            <if test="mixingEndtime != null">
                mixing_endtime = #{mixingEndtime},
            </if>
            <if test="mixingTimespan != null">
                mixing_timespan = #{mixingTimespan},
            </if>
            <if test="moistenGrainTimespan != null">
                moisten_grain_timespan = #{moistenGrainTimespan},
            </if>
            <if test="moistenGrainWaterTemperature != null">
                moisten_grain_water_temperature = #{moistenGrainWaterTemperature},
            </if>
            <if test="beforeLoadingTransmitStarttime != null">
                before_loading_transmit_starttime = #{beforeLoadingTransmitStarttime},
            </if>
            <if test="beforeLoadingTransmitEndtime != null">
                before_loading_transmit_endtime = #{beforeLoadingTransmitEndtime},
            </if>
            <if test="beforeLoadingTransmitTimespan != null">
                before_loading_transmit_timespan = #{beforeLoadingTransmitTimespan},
            </if>
            <if test="feedMachineCode != null">
                feed_machine_code = #{feedMachineCode},
            </if>
            <if test="potNum != null">
                pot_num = #{potNum},
            </if>
            <if test="loadingStarttime != null">
                loading_starttime = #{loadingStarttime},
            </if>
            <if test="loadingEndtime != null">
                loading_endtime = #{loadingEndtime},
            </if>
            <if test="loadingTimespan != null">
                loading_timespan = #{loadingTimespan},
            </if>
            <if test="closeLidTime != null">
                close_lid_time = #{closeLidTime},
            </if>
            <if test="openLidTime != null">
                open_lid_time = #{openLidTime},
            </if>
            <if test="distillateStarttime != null">
                distillate_starttime = #{distillateStarttime},
            </if>
            <if test="distillateEndtime != null">
                distillate_endtime = #{distillateEndtime},
            </if>
            <if test="distillateTimespan != null">
                distillate_timespan = #{distillateTimespan},
            </if>
            <if test="steamingStarttime != null">
                steaming_starttime = #{steamingStarttime},
            </if>
            <if test="steamingEndtime != null">
                steaming_endtime = #{steamingEndtime},
            </if>
            <if test="steamingTimespan != null">
                steaming_timespan = #{steamingTimespan},
            </if>
            <if test="outflowStarttime != null">
                outflow_starttime = #{outflowStarttime},
            </if>
            <if test="distillateFirstStarttime != null">
                distillate_first_starttime = #{distillateFirstStarttime},
            </if>
            <if test="distillate2Starttime != null">
                distillate2_starttime = #{distillate2Starttime},
            </if>
            <if test="distillate3Starttime != null">
                distillate3_starttime = #{distillate3Starttime},
            </if>
            <if test="distillate4Starttime != null">
                distillate4_starttime = #{distillate4Starttime},
            </if>
            <if test="distillateLastwaterStarttime != null">
                distillate_lastwater_starttime = #{distillateLastwaterStarttime},
            </if>
            <if test="outflowEndtime != null">
                outflow_endtime = #{outflowEndtime},
            </if>
            <if test="turnPotTime != null">
                turn_pot_time = #{turnPotTime},
            </if>
            <if test="bottomPotLevel != null">
                bottom_pot_level = #{bottomPotLevel},
            </if>
            <if test="bottomPotTemperature != null">
                bottom_pot_temperature = #{bottomPotTemperature},
            </if>
            <if test="liquorTailingQuantity != null">
                liquor_tailing_quantity = #{liquorTailingQuantity},
            </if>
            <if test="waterQuantity != null">
                water_quantity = #{waterQuantity},
            </if>
            <if test="yellowWaterQuantity != null">
                yellow_water_quantity = #{yellowWaterQuantity},
            </if>
            <if test="waterProportioningStarttime != null">
                water_proportioning_starttime = #{waterProportioningStarttime},
            </if>
            <if test="waterProportioningEndtime != null">
                water_proportioning_endtime = #{waterProportioningEndtime},
            </if>
            <if test="waterProportioningTimespan != null">
                water_proportioning_timespan = #{waterProportioningTimespan},
            </if>
            <if test="waterProportioningTemperature != null">
                water_proportioning_temperature = #{waterProportioningTemperature},
            </if>
            <if test="waterProportioningQuantity != null">
                water_proportioning_quantity = #{waterProportioningQuantity},
            </if>
            <if test="collingFeedMachineCode != null">
                colling_feed_machine_code = #{collingFeedMachineCode},
            </if>
            <if test="collingStarttime != null">
                colling_starttime = #{collingStarttime},
            </if>
            <if test="collingEndtime != null">
                colling_endtime = #{collingEndtime},
            </if>
            <if test="collingMachineCode != null">
                colling_machine_code = #{collingMachineCode},
            </if>
            <if test="collingTemperature != null">
                colling_temperature = #{collingTemperature},
            </if>
            <if test="quChangedWeight != null">
                qu_changed_weight = #{quChangedWeight},
                qu_quantity = #{quChangedWeight},
            </if>
            <if test="transportStarttime != null">
                transport_starttime = #{transportStarttime},
            </if>
            <if test="arriveLiftingAreaTime != null">
                arrive_lifting_area_time = #{arriveLiftingAreaTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="outPotTime != null">
                out_pot_time = #{outPotTime},
            </if>
            <if test="pottaskEndtime != null">
                pottask_endtime = #{pottaskEndtime},
            </if>
            <if test="potSerialNumber != null">
                serial_number = #{potSerialNumber},
            </if>
            <if test="outStartTime != null">
                out_start_time = #{outStartTime},
            </if>
            <if test="fermentativeMaterialLevels != null">
                fermentative_material_levels = #{fermentativeMaterialLevels},
            </if>
            <if test="outDregsStartTime != null">
                out_dregs_start_time = #{outDregsStartTime},
            </if>
            <if test="outDregsEndTime != null">
                out_dregs_end_time = #{outDregsEndTime},
            </if>

            <if test="outDregsTimespan != null">
                out_dregs_timespan = #{outDregsTimespan},
            </if>

            <if test="augerStartTime != null">
                auger_start_time = #{augerStartTime},
            </if>

            <if test="augerEndTime != null">
                auger_end_time = #{augerEndTime},
            </if>

            <if test="moistenGrainNo != null">
                moisten_grain_no = #{moistenGrainNo},
            </if>

            <if test="moistenGrainWeight != null">
                moisten_grain_weight = #{moistenGrainWeight},
            </if>

            <if test="moistenGrainLiquidLevel != null">
                moisten_grain_liquid_level = #{moistenGrainLiquidLevel},
            </if>

            <if test="moistenGrainStartLevel != null">
                moisten_grain_start_level = #{moistenGrainStartLevel},
            </if>

            <if test="moistenGrainEndLevel != null">
                moisten_grain_end_level = #{moistenGrainEndLevel},
            </if>

            <if test="moistenGrainPumpTimespan != null">
                moisten_grain_pump_timespan = #{moistenGrainPumpTimespan},
            </if>

            <if test="stirStartTime != null">
                stir_start_time = #{stirStartTime},
            </if>

            <if test="stirEndTime != null">
                stir_end_time = #{stirEndTime},
            </if>

            <if test="hjWeight != null">
                hj_weight = #{hjWeight},
            </if>

            <if test="wjPumpStartTime != null">
                wj_pump_start_time = #{wjPumpStartTime},
            </if>

            <if test="wjPumpEndTime != null">
                wj_pump_end_time = #{wjPumpEndTime},
            </if>

            <if test="wjPumpTimespan != null">
                wj_pump_timespan = #{wjPumpTimespan},
            </if>

            <if test="potTimespan != null">
                pot_timespan = #{potTimespan},
            </if>

            <if test="feedMachinStartTime != null">
                feed_machin_starttime = #{feedMachinStartTime},
            </if>

            <if test="ztBottomPotFlowRateStartReading != null">
                zt_bottom_pot_flow_rate_start_reading = #{ztBottomPotFlowRateStartReading},
            </if>

            <if test="ztBottomPotFlowRateEndReading != null">
                zt_bottom_pot_flow_rate_end_reading = #{ztBottomPotFlowRateEndReading},
            </if>

            <if test="hsBottomPotFlowRateStartReading != null">
                hs_bottom_pot_flow_rate_start_reading = #{hsBottomPotFlowRateStartReading},
            </if>

            <if test="hsBottomPotFlowRateEndReading != null">
                hs_bottom_pot_flow_rate_end_reading = #{hsBottomPotFlowRateEndReading},
            </if>

            <if test="qsBottomPotFlowRateStartReading != null">
                qs_bottom_pot_flow_rate_start_reading = #{qsBottomPotFlowRateStartReading},
            </if>

            <if test="qsBottomPotFlowRateEndReading != null">
                qs_bottom_pot_flow_rate_end_reading = #{qsBottomPotFlowRateEndReading},
            </if>

            <if test="wjBottomPotFlowRateStartReading != null">
                wj_bottom_pot_flow_rate_start_reading = #{wjBottomPotFlowRateStartReading},
            </if>

            <if test="wjBottomPotFlowRateEndReading != null">
                wj_bottom_pot_flow_rate_end_reading = #{wjBottomPotFlowRateEndReading},
            </if>

            <if test="outPotStartTime != null">
                out_pot_starttime = #{outPotStartTime},
            </if>

            <if test="materialLssuanceStartTime != null">
                material_lssuance_starttime = #{materialLssuanceStartTime},
            </if>

            <if test="distillate2Timespan != null">
                distillate2_timespan = #{distillate2Timespan},
            </if>

            <if test="distillate3Timespan != null">
                distillate3_timespan = #{distillate3Timespan},
            </if>

            <if test="distillate4Timespan != null">
                distillate4_timespan = #{distillate4Timespan},
            </if>

            <if test="waterProportioning1 != null">
                water_proportioning_1 = #{waterProportioning1},
            </if>

            <if test="waterProportioning1Temperature != null">
                water_proportioning_1_temperature = #{waterProportioning1Temperature},
            </if>

            <if test="waterProportioning2 != null">
                water_proportioning_2 = #{waterProportioning2},
            </if>

            <if test="waterProportioning2Temperature != null">
                water_proportioning_2_temperature = #{waterProportioning2Temperature},
            </if>

            <if test="inYeastStartWeight != null">
                in_yeast_start_weight = #{inYeastStartWeight},
            </if>

            <if test="inYeastEndWeight != null">
                in_yeast_end_weight = #{inYeastEndWeight},
            </if>

            <if test="inYeastTemperature != null">
                in_yeast_temperature = #{inYeastTemperature},
            </if>

            <if test="inYeastStartTime != null">
                in_yeast_starttime = #{inYeastStartTime},
            </if>

            <if test="inYeastEndTime != null">
                in_yeast_endtime = #{inYeastEndTime},
            </if>

            <if test="fanRunningQuantity != null">
                fan_running_quantity = #{fanRunningQuantity},
            </if>

            <if test="fanRunningTimespan != null">
                fan_running_timespan = #{fanRunningTimespan},
            </if>

            <if test="fanStartTime != null">
                fan_starttime = #{fanStartTime},
            </if>

            <if test="fanEndTime != null">
                fan_endtime = #{fanEndTime},
            </if>

            <if test="moistenGrainStartTime != null">
                moisten_grain_starttime = #{moistenGrainStartTime},
            </if>

            <if test="moistenGrainEndTime != null">
                moisten_grain_endtime = #{moistenGrainEndTime},
            </if>
            <if test="backAlcoholicQuantity != null">
                back_alcoholic_quantity = #{backAlcoholicQuantity},
                back_alcoholic_quantity_revise = #{backAlcoholicQuantity},
            </if>
            <if test="collingTimespan != null">
                colling_timespan = #{collingTimespan},
            </if>
            <if test="dz2Timespan != null">
                dz2_timespan = #{dz2Timespan},
            </if>
            <if test="dz2StartTime != null">
                dz2_starttime = #{dz2StartTime},
            </if>
        </trim>
        where pot_serial_number = #{id}
        and controller_id = #{controllerId}
    </update>


    <select id="findWorkshopPitOrderPotTaskScheduleFinish" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit t3 ON (t1.out_pit_code = t3.pit_code and t3.is_deleted = 0)
        WHERE t1.is_deleted = 0
        <![CDATA[
                  and t1.out_time >= CONCAT(DATE(#{outTime}), ' ', TIME(#{startTime}))
                  and t1.out_time <= CONCAT(DATE(#{outTime}), ' ', TIME(#{endTime}))
        ]]>
        <if test="centerId != null">
            and t3.workshop_center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.workshop_id = #{locationId}
        </if>
        <if test="orderStatus != null">
            and t1.order_status = #{orderStatus}
        </if>
    </select>

    <select id="findProductionOutPitByShift" resultType="java.util.Map">
        SELECT DISTINCT
        t1.vinasse,
        t2.full_pit_id as fullPitId
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit t2 ON t1.out_pit_code = t2.pit_code and t2.is_deleted = 0
        WHERE t1.is_deleted = 0
        <if test="centerId != null">
            and t2.workshop_center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t2.workshop_id = #{locationId}
        </if>
        <if test="fullPitId != null">
            and t2.full_pit_id like concat('%',#{fullPitId},'%')
        </if>
        <if test="startTime != null">
            AND CONCAT(date( t1.pottask_starttime ),' ',TIME(t1.pottask_starttime)) >= CONCAT(date( #{date} ),'
            ',DATE_FORMAT(#{startTime},'%H:%i:%S') )
        </if>
        <if test="endTime != null">
            <![CDATA[AND CONCAT(date( t1.pottask_starttime ),' ',TIME(t1.pottask_starttime))<= CONCAT(date( #{date} ),' ',DATE_FORMAT(#{endTime},'%H:%i:%S') )]]>
        </if>
        <if test="startTime == null &amp;&amp; endTime == null">
            and date(t1.pottask_starttime) = date(#{date})
        </if>
    </select>

    <select id="findDayPlanPotCount" resultType="java.lang.Integer">
        SELECT pot_count
        FROM t_po_workshop_pit_day_plan
        WHERE workshop_center_id = #{centerId}
        AND workshop_id = #{locationId}
        AND plan_date = DATE(#{outTime})
        LIMIT 0,1
    </select>

    <select id="findProductionInPitByShift"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedFindInPitVO">
        SELECT DISTINCT
        t1.vinasse,
        t4.pit_code,
        t3.pit_status
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        LEFT JOIN t_po_workshop_pit t4 ON t2.pit_id = t4.id and t4.is_deleted = 0
        WHERE
        t1.is_deleted = 0
        <if test="centerId != null">
            and t3.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.location_id = #{locationId}
        </if>
        <if test="pitCode != null">
            and t4.pit_code like concat('%',#{pitCode},'%')
        </if>
        <if test="startTime != null">
            AND CONCAT(date( t1.pottask_starttime ),' ',TIME(t1.pottask_starttime)) >= CONCAT(date( #{date} ),'
            ',DATE_FORMAT(#{startTime},'%H:%i:%S') )
        </if>
        <if test="endTime != null">
            <![CDATA[AND CONCAT(date( t1.pottask_starttime ),' ',TIME(t1.pottask_starttime))<= CONCAT(date( #{date} ),' ',DATE_FORMAT(#{endTime},'%H:%i:%S') )]]>
        </if>
        <if test="startTime == null &amp;&amp; endTime == null">
            and date(t1.pottask_starttime) = date(#{date})
        </if>
    </select>

    <select id="WorkshopPitOrderPotAssociatedPotDataList"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedPotDataVO">
        SELECT DISTINCT
        t1.id,
        t1.pot_serial_number,
        t1.vinasse,
        t1.out_pit_code,
        t1.out_order_code,
        t5.layer as out_pit_layer, -- 起窖窖池层级
        t1.out_time,
        t1.pot_num,
        t1.out_pot_time,
        t1.colling_machine_code,
        t1.colling_endtime,
        t1.in_pit_code,
        t1.in_order_code,
        t6.layer as in_pit_layer, -- 入窖窖池层级
        t1.order_status,
        t1.is_check,
        t1.check_time,
        t1.check_user_id
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.out_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t5 on t2.order_code_id = t5.id

        Left JOIN t_po_workshop_pit_order_sap t3 on t1.in_order_code = t3.order_code
        LEFT JOIN t_po_workshop_pit_order t6 on t3.order_code_id = t6.id
        <!--        LEFT JOIN t_po_workshop_pit t4 on t1.out_pit_code = t4.pit_code-->
        WHERE t1.is_deleted = 0
        and t1.order_status != 0
        <if test="centerId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="locationId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="outTimeStartTime != null">
            and DATE_FORMAT(t1.out_time,'%Y-%m-%d') between DATE_FORMAT(#{outTimeStartTime},'%Y-%m-%d') and
            DATE_FORMAT(#{outTimeEndTime},'%Y-%m-%d')
        </if>
        <if test="loadingStarttimeStartTime != null">
            and DATE_FORMAT(t1.loading_starttime,'%Y-%m-%d') between
            DATE_FORMAT(#{loadingStarttimeStartTime},'%Y-%m-%d') and
            DATE_FORMAT(#{loadingStarttimeEndTime},'%Y-%m-%d')
        </if>
        <if test="outPotTimeStartTime != null">
            and DATE_FORMAT(t1.out_pot_time,'%Y-%m-%d') between DATE_FORMAT(#{outPotTimeStartTime},'%Y-%m-%d') and
            DATE_FORMAT(#{outPotTimeEndTime},'%Y-%m-%d')
        </if>
        <if test="outPitCode != null">
            and t1.out_pit_code like concat('%',#{outPitCode},'%')
        </if>
        <if test="inPitCode != null">
            and t1.in_pit_code like concat('%',#{inPitCode},'%')
        </if>
        <if test="vinasse != null">
            and t1.vinasse = #{vinasse}
        </if>
        <if test="startTime != null &amp;&amp; endTime != null">
            <![CDATA[
            and t1.pottask_starttime >= CONCAT(DATE(#{outTime}),' ',TIME(#{startTime}))
            and t1.pottask_starttime <= CONCAT(DATE(#{outTime}),' ',TIME(#{endTime}))
            ]]>
        </if>
        <!--        <if test="startTime == null &amp;&amp; endTime == null">-->
        <!--            <![CDATA[-->
        <!--            and DATE(t1.pottask_starttime) >= DATE(#{outTime})-->
        <!--            and DATE(t1.pottask_starttime) <= DATE(#{outTime})-->
        <!--            ]]>-->
        <!--        </if>-->
        <if test="isCheck == true">
            and t1.is_check = #{isCheck}
        </if>
        <if test="isCheck == false">
            and (t1.is_check = #{isCheck} or isnull(t1.is_check))
        </if>

        order by t1.out_time desc
    </select>

    <select id="WorkshopPitOrderPotAssociatedInPitDataList"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedInPitDataVO">
        SELECT DISTINCT
        t5.std_in_pit_num,
        (
        SELECT
        count(*)
        FROM
        t_po_workshop_pit_order_pot_task
        WHERE in_order_code = t2.order_code and vinasse = t1.vinasse
        and
        <foreach collection="workshopPitOrderPotAssociatedPotDataVOList" index="index" item="item" open="("
                 separator=" or" close=")">
            id = #{item.id}
        </foreach>
        ) as in_pit_num,
        t1.vinasse,
        t2.seal_confirm_time,
        t1.in_pit_code,
        t3.pit_status
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.in_order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        LEFT JOIN t_po_workshop_pit t4 on t1.in_pit_code = t4.pit_code
        LEFT JOIN t_po_standard_pot_configuration t5 ON t4.workshop_center_id = t5.workshop_center_id -- 标准甑口配置
        where
        t1.is_deleted = 0
        and t4.is_deleted = 0
        and
        <foreach collection="workshopPitOrderPotAssociatedPotDataVOList" index="index" item="item" open="("
                 separator=" or" close=")">
            t1.id = #{item.id}
        </foreach>
    </select>

    <select id="WorkshopPitOrderPotAssociatedOutPitDataList"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedOutPitDataVO">
        SELECT DISTINCT
        (
        SELECT
        sum(plan_out_pot_count)
        FROM
        `t_po_workshop_pit_day_plan`
        where
        DATE_FORMAT(plan_date,'%Y-%m-%d') = DATE_FORMAT(t1.out_time,'%Y-%m-%d')
        and pit_id = t2.id
        and plan_status = 3
        ) as plan_out_pot_count,
        (
        SELECT
        count(*)
        FROM
        t_po_workshop_pit_order_pot_task
        WHERE out_pit_code = t1.out_pit_code and vinasse = t1.vinasse
        and
        <foreach collection="workshopPitOrderPotAssociatedPotDataVOList" index="index" item="item" open="("
                 separator=" or" close=")">
            id = #{item.id}
        </foreach>
        ) as out_pit_num,
        t1.vinasse,
        t1.out_pit_code,
        t4.out_pit_finish_time,
        t2.full_pit_id
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_full_pit t2 ON t1.out_pit_code = t2.pid_fir
        OR t1.out_pit_code = t2.pid_sec
        Left JOIN t_po_workshop_pit_order_sap t3 on t1.out_order_code = t3.order_code
        LEFT JOIN t_po_workshop_pit_order t4 on t3.order_code_id = t4.id
        WHERE
        t1.is_deleted = 0
        AND t2.is_deleted = 0
        and
        <foreach collection="workshopPitOrderPotAssociatedPotDataVOList" index="index" item="item" open="("
                 separator=" or" close=")">
            t1.id = #{item.id}
        </foreach>
    </select>

    <select id="getWorkshopPitOrderPotAssociatedBatchPitRevise"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedBatchPitReviseVO">
        SELECT t1.id,
        t1.pot_serial_number,
        t1.vinasse,
        t1.out_pit_code,
        t1.out_time,
        t1.colling_endtime,
        t1.in_pit_code
        FROM t_po_workshop_pit_order_pot_task t1
        WHERE t1.is_deleted = 0 and
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=" or">
            t1.id = #{item}
        </foreach>
    </select>

    <update id="batchPitReviseForIds">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            <if test="outPitCode != null">
                out_pit_code = #{outPitCode},
            </if>
            <if test="inPitCode != null">
                in_pit_code = #{inPitCode},
            </if>
            <if test="vinasse != null">
                vinasse = #{vinasse},
            </if>
        </trim>
        where
        <foreach collection="ids" index="index" item="item" separator=" or">
            id = #{item}
        </foreach>
    </update>

    <update id="checkCompleted">
        update t_po_workshop_pit_order_pot_task
        <trim prefix="set" suffixOverrides=",">
            <if test="checkUserId != null">
                check_user_id = #{checkUserId},
            </if>
            check_time = now(),
            is_check = true,
        </trim>
        where
        <foreach collection="ids" index="index" item="item" separator=" or">
            id = #{item}
        </foreach>
    </update>

    <select id="getOutPitAssociatedOrder"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.OutPitAssociatedOrder">
        SELECT DISTINCT
        -- t1.id,
        t1.vinasse,
        t1.out_pit_code,
        t1.out_order_code
        FROM
        t_po_workshop_pit_order_pot_task t1
        WHERE
        is_deleted = 0
        AND
        <foreach collection="ids" index="index" item="item" open="(" separator=" or" close=")">
            t1.id = #{item}
        </foreach>
    </select>

    <select id="getInPitAssociatedOrder"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.InPitAssociatedOrder">
        SELECT DISTINCT
        -- t1.id,
        t1.vinasse,
        t1.in_pit_code,
        t1.in_order_code
        FROM
        t_po_workshop_pit_order_pot_task t1
        WHERE
        is_deleted = 0
        and
        <foreach collection="ids" index="index" item="item" open="(" separator=" or" close=")">
            t1.id = #{item}
        </foreach>
    </select>

    <update id="outPitAssociatedOrder">
        update t_po_workshop_pit_order_pot_task
        set out_order_code =#{outPitAssociatedOrderDTO.outOrderCode}
        where out_pit_code = #{outPitAssociatedOrderDTO.outPitCode}
        and vinasse = #{outPitAssociatedOrderDTO.vinasse}
        and
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=" or">
            id = #{item}
        </foreach>
    </update>

    <update id="inPitAssociatedOrder">
        update t_po_workshop_pit_order_pot_task
        set in_order_code =#{inPitAssociatedOrder.inOrderCode}
        where in_pit_code = #{inPitAssociatedOrder.inPitCode}
        and vinasse = #{inPitAssociatedOrder.vinasse}
        and
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=" or">
            id = #{item}
        </foreach>
    </update>

    <select id="findCurrentOrderByPitName" resultType="java.lang.String">
        SELECT t3.order_code
        FROM t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_pit_order_sap t3 on t1.id = t3.order_code_id
        LEFT JOIN t_po_workshop_pit t2 ON t3.pit_id = t2.id
        WHERE t1.is_deleted = 0
        and t3.order_close_tag != 1
        and (t1.center_id = #{locationId} or t1.location_id = #{locationId})
        and t2.pit_code like concat('%', #{pitCode}, '%')
    </select>

    <select id="findPitByLocation" resultType="java.lang.String">
        SELECT pit_code
        FROM t_po_workshop_pit
        WHERE is_deleted = 0
        <if test="locationId != null">
            and(workshop_id = #{locationId} or workshop_center_id = #{locationId})
        </if>
        <if test="pidName != null">
            and pit_code like concat('%',#{pidName},'%')
        </if>
    </select>

    <select id="getInOrderCodeByOld" resultType="java.lang.Integer">
        select t2.order_code_id
        from t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        where t1.in_pit_code = #{inPitAssociatedOrder.inPitCode}
        and t1.vinasse = #{inPitAssociatedOrder.vinasse}
        and
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=" or">
            t1.id = #{item}
        </foreach>
        limit 0,1
    </select>

    <select id="getPotByOrderId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_po_workshop_pit_order_pot_task t1
        left join t_po_workshop_pit_order_sap t2 on t1.in_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 on t2.order_code_id = t3.id
        WHERE t3.id = #{orderCodeId}
        and t1.is_deleted = 0
    </select>

    <select id="getOrderIdBySap" resultType="java.lang.Integer">
        SELECT t1.id
        FROM t_po_workshop_pit_order t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.id = t2.order_code_id
        WHERE t2.order_code = #{orderCode}
        AND t1.is_deleted = 0
        and t2.is_deleted = 0
    </select>

    <select id="getSumDaquByOrderCode" resultType="float">
        SELECT SUM(fermentative_material_quantity)
        FROM t_po_workshop_pit_order_pot_task
        WHERE in_order_code = #{orderCode}
    </select>

    <select id="getSumSorghumByOrderCode" resultType="float">
        SELECT SUM(crushed_grains_quantity)
        FROM t_po_workshop_pit_order_pot_task
        WHERE in_order_code = #{orderCode}
    </select>

    <select id="getSumRiceHuskByOrderCode" resultType="float">
        SELECT SUM(ricehull_quantity)
        FROM t_po_workshop_pit_order_pot_task
        WHERE in_order_code = #{orderCode}
    </select>

    <select id="getSumBackAlcoholicByOrderCode" resultType="float">
        SELECT SUM(back_alcoholic_quantity)
        FROM t_po_workshop_pit_order_pot_task
        WHERE in_order_code = #{orderCode}
    </select>

    <select id="getInOrderCodeSapByOld"
            resultType="com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap">
        SELECT
        t2.*
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        where t1.in_pit_code = #{inPitAssociatedOrder.inPitCode}
        and t1.vinasse = #{inPitAssociatedOrder.vinasse}
        and
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=" or">
            t1.id = #{item}
        </foreach>
        limit 0,1
    </select>

    <select id="getNowChangeShiftLogAttendanceId" resultType="java.lang.Integer">
        SELECT DISTINCT t1.*
        FROM `t_po_change_shift_log_attendance` t1
        LEFT JOIN `schedule`.hv_bm_shift t2 ON t1.shift = t2.id
        LEFT JOIN t_po_workshop_pit t3
        on t1.center_id = t3.workshop_center_id and t1.location_id = t3.workshop_id
        WHERE TIME(#{outTime}) >= t2.start_time
        <![CDATA[and TIME(#{outTime}) <= t2.end_time ]]>
        and DATE(#{outTime}) = t1.log_date
        and t3.pit_code = #{outPitCode}
        ORDER BY id DESC
        limit 0,1
    </select>

    <update id="updatePotTaskChangeShiftId">
        <![CDATA[
        UPDATE t_po_workshop_pit_order_pot_task t1
            LEFT JOIN t_po_workshop_pit t2 ON (t1.out_pit_code = t2.pit_code and t2.is_deleted = 0)
                AND t2.is_deleted = 0
        SET t1.change_shift_log_id = #{changeShiftLogId}
        WHERE t2.workshop_center_id = #{centerId}
          AND t2.workshop_id = #{locationId}
          AND t1.pottask_starttime >= CONCAT(DATE(#{dateTime}), ' ', #{startTime})
          AND t1.pottask_starttime <= CONCAT(DATE(#{dateTime}), ' ', #{endTime})
        ]]>
    </update>

    <select id="WorkshopPitOrderPotAssociatedPotDataListByClick"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.WorkshopPitOrderPotAssociatedPotDataVO">
        SELECT DISTINCT
        t1.id,
        t1.pot_serial_number,
        t1.vinasse,
        t1.out_pit_code,
        t1.out_order_code,
        t5.layer as out_pit_layer, -- 起窖窖池层级
        t1.out_time,
        t1.pot_num,
        t1.colling_machine_code,
        t1.colling_endtime,
        t1.in_pit_code,
        t1.in_order_code,
        t6.layer as in_pit_layer, -- 入窖窖池层级
        t1.order_status,
        t1.is_check,
        t1.check_time,
        t1.check_user_id
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.out_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t5 on t2.order_code_id = t5.id

        Left JOIN t_po_workshop_pit_order_sap t3 on t1.in_order_code = t3.order_code
        LEFT JOIN t_po_workshop_pit_order t6 on t3.order_code_id = t6.id
        LEFT JOIN t_po_workshop_pit t4 on (t1.out_pit_code = t4.pit_code and t4.is_deleted = 0)
        WHERE t1.is_deleted = 0
        and t1.order_status != 0
        <if test="centerId != null">
            and t4.workshop_center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t4.workshop_id = #{locationId}
        </if>
        <if test="outTime != null">
            and DATE(t1.out_time) = DATE(#{outTime})
        </if>
        <if test="outPitCodes != null">
            <foreach collection="outPitCodes" index="index" item="item" open="and (" close=")" separator=" or">
                t1.out_pit_code = #{item}
            </foreach>
        </if>
        <if test="inPitCodes != null">
            <foreach collection="inPitCodes" index="index" item="item" open="and (" close=")" separator=" or">
                t1.in_pit_code = #{item}
            </foreach>
        </if>
        <if test="vinasse != null">
            and t1.vinasse = #{vinasse}
        </if>
        <if test="startTime != null &amp;&amp; endTime != null">
            <![CDATA[
            and t1.pottask_starttime >= CONCAT(DATE(#{outTime}),' ',TIME(#{startTime}))
            and t1.pottask_starttime <= CONCAT(DATE(#{outTime}),' ',TIME(#{endTime}))
            ]]>
        </if>
        <if test="startTime == null &amp;&amp; endTime == null">
            <![CDATA[
            and DATE(t1.pottask_starttime) >= DATE(#{outTime})
            and DATE(t1.pottask_starttime) <= DATE(#{outTime})
            ]]>
        </if>
    </select>

    <select id="getPotBySapOrderId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_po_workshop_pit_order_pot_task t1
        left join t_po_workshop_pit_order_sap t2 on t1.in_order_code = t2.order_code
        WHERE t2.id = #{sapOrderCodeId}
        and t1.is_deleted = 0
    </select>

    <select id="getAvgTemperatureBySapOrderCode"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskAvgTemperatureVO">
        SELECT IFNULL(AVG(t1.in_pit_temperature), 0) AS avg_in_pit_temperature,
        IFNULL(AVG(t1.ground_temperature), 0) AS avg_ground_temperature
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t2.order_code = t1.in_order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        WHERE is_check = 1
        AND t3.id = (
        SELECT order_code_id
        FROM t_po_workshop_pit_order_sap
        WHERE order_code = #{sapOrderCode}
        AND is_deleted = 0)
    </select>

    <select id="getPotTaskSyncDataPage"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderPotTask.WorkshopPitOrderPotTaskCentralDTO">
        SELECT t1.id,
        t1.pot_serial_number,-- 甑口任务号
        t1.vinasse,-- 糟源类型
        t1.out_pit_code,-- 中控输入的起窖号（单窖）,起窖号
        IFNULL(t1.out_dregs_start_time,t1.out_time) as out_time,-- 出糟时间
        t1.fermentative_material_quantity,-- 糟醅重量,糟重
        t1.crushed_grains_quantity,-- 粉粮（高粱）重量,粮重
        t1.ricehull_quantity,-- 稻壳重量,糠重
        t1.back_alcoholic_quantity,-- 回酒重量
        t1.back_alcoholic_quantity_revise, -- 回酒修正
        t1.pot_num,-- 甑号（一条产线6口甑）
        t1.colling_machine_code,-- 摊晾机编号
        t1.qu_changed_weight,-- 加曲机重量变化
        t1.qu_hopper_code, -- 曲粉斗
        t1.qu_quantity,-- 曲粉重量(加曲机重量变化+添加重量)
        t1.in_pit_temperature,-- 入窖温度
        t1.ground_temperature,-- 地温
        t1.colling_endtime,-- 摊晾结束时间
        t1.in_pit_code,-- 中控输入的入窖号（单窖）
        t1.order_status,-- 任务状态（0待执行 1执行中 2已完成）
        t1.is_check,-- 核对确认
        t1.check_time,-- 确认时间,核对完成时间
        t1.check_user_id, -- 确认人id,核对人
        t1.in_order_code, -- 入窖窖池订单编号
        t1.out_pot_time, -- 出甑时间
        t1.out_order_code,-- 出窖窖池订单编号
        t1.controller_id
        FROM t_po_workshop_pit_order_pot_task t1
        <!--        LEFT JOIN t_po_workshop_pit t3 on t1.out_pit_code = t3.pit_code-->
        where t1.is_deleted = 0
        <if test="centerId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="locationId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="outTime != null">
            and date_format(IFNULL(t1.out_dregs_start_time,t1.out_time),'%Y-%m-%d') = date_format(#{outTime},'%Y-%m-%d')
        </if>
        order by out_time desc
        limit #{page},#{pageSize}
    </select>

    <select id="getPotTaskSyncDataCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_po_workshop_pit_order_pot_task t1
        <!--        LEFT JOIN t_po_workshop_pit t3 on t1.out_pit_code = t3.pit_code-->
        where t1.is_deleted = 0
        <if test="centerId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="locationId != null">
            and (t1.controller_id like concat('',#{controllerId},'%'))
        </if>
        <if test="outTime != null">
            and date_format(t1.out_time,'%Y-%m-%d') = date_format(#{outTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="getInPutCountByOrderCode"
            resultType="com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.ChoosePitOrderVO">
        SELECT in_order_code AS order_code,COUNT(*) AS in_pit_num
        FROM t_po_workshop_pit_order_pot_task
        WHERE is_deleted = 0 AND in_order_code IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY in_order_code
    </select>

    <select id="getPotCountUpdateQuery"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderPotAssociated.PotCountUpdateQueryVO">
        SELECT
        t3.id,
        t3.order_code,
        (select count(*) from t_po_workshop_pit_order_pot_task pt left join t_po_workshop_pit_order_sap pos on
        pt.in_order_code = pos.order_code where pt.is_deleted = 0 and pos.order_code_id = t3.id ) as in_pot_num,
        (select count(*) from t_po_workshop_pit_order_pot_task pt left join t_po_workshop_pit_order_sap pos on
        pt.out_order_code = pos.order_code where pt.is_deleted = 0 and pos.order_code_id = t3.id ) as out_pot_num
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        OR t1.out_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        WHERE
        t3.is_deleted = 0
        and t1.is_deleted = 0
        AND(
        (select ABS((SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = pos.order_code and is_deleted =
        0)
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 0,1) - (SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = pos.order_code and is_deleted =
        0)
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 1,1))>1)
        or
        (select ABS((SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE out_order_code = pos.order_code and is_deleted =
        0)
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 0,1) - (SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE out_order_code = pos.order_code and is_deleted =
        0)
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 1,1))>1)
        )
        <if test="centerId != null">
            and t3.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.location_id = #{locationId}
        </if>
        <if test="outTimeStart != null">
            and DATE_FORMAT(t1.out_time,'%Y-%m-%d %H:%i:%s')>=date_format(#{outTimeStart},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="outTimeEnd != null">
            <![CDATA[and (DATE_FORMAT(t1.out_time,'%Y-%m-%d %H:%i:%s'))<= date_format(#{outTimeEnd},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        GROUP BY
        t3.id
        ORDER BY
        t3.create_time desc
        limit #{page},#{pageSize}
    </select>

    <select id="getPotCountUpdateQueryCount" resultType="java.lang.Integer">
        select count(*) from (SELECT
        t3.id,
        t3.order_code,
        (select count(*) from t_po_workshop_pit_order_pot_task pt left join t_po_workshop_pit_order_sap pos on
        pt.in_order_code = pos.order_code where pt.is_deleted = 0 and pos.order_code_id = t3.id ) as in_pot_num,
        (select count(*) from t_po_workshop_pit_order_pot_task pt left join t_po_workshop_pit_order_sap pos on
        pt.out_order_code = pos.order_code where pt.is_deleted = 0 and pos.order_code_id = t3.id ) as out_pot_num
        FROM
        t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        OR t1.out_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        WHERE
        t3.is_deleted = 0
        and t1.is_deleted = 0
        AND(
        (select ABS((SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = pos.order_code )
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 0,1) - (SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = pos.order_code )
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 1,1))>1)
        or
        (select ABS((SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE out_order_code = pos.order_code )
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 0,1) - (SELECT
        ( SELECT count( * ) FROM t_po_workshop_pit_order_pot_task WHERE out_order_code = pos.order_code )
        FROM
        t_po_workshop_pit_order_sap pos
        WHERE
        pos.order_code_id = t3.id
        LIMIT 1,1))>1)
        )
        <if test="centerId != null">
            and t3.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.location_id = #{locationId}
        </if>
        <if test="outTimeStart != null">
            and DATE_FORMAT(t1.out_time,'%Y-%m-%d %H:%i:%s')>=date_format(#{outTimeStart},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="outTimeEnd != null">
            <![CDATA[and (DATE_FORMAT(t1.out_time,'%Y-%m-%d %H:%i:%s'))<= date_format(#{outTimeEnd},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        GROUP BY
        t3.id
        ) as pp
    </select>

    <select id="getOpcValueByStageTime" resultType="java.lang.String">
        SELECT tag_id
        FROM t_po_opctag_value
        WHERE
        <![CDATA[read_time <= #{stageTime}]]>
        and tag_value = 1
        and tag_id in (SELECT t1.input_valve_tag_id
        FROM `t_po_workshop_tmp_tank` t1
        LEFT JOIN t_po_workshop_tmp_tank_category t2
        on t1.workshop_tmp_tank_category_id = t2.id
        WHERE t2.fermentation_category_id = #{vinasse_id}
        and FIND_IN_SET(#{stageNum}, t2.stage_num)
        and t1.work_shop = #{locationId}
        and t1.is_deleted = 0
        )
        ORDER BY read_time desc
        LIMIT 1
    </select>

    <select id="getOpcDataByHandIn"
            resultType="com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoOpctagValue">
        SELECT t1.id,
        t1.read_time,
        t1.tag_id,
        t1.tag_value
        FROM `t_po_opctag_value` t1
        WHERE t1.tag_id = #{inputValveTagId}
        AND (t1.read_time BETWEEN #{startTime} AND #{endTime})
        AND t1.tag_value != (
        SELECT tt.last_val
        FROM (
        SELECT id,
        lag(tag_value, 1, 2) over ( PARTITION BY tag_id ORDER BY read_time ) AS last_val
        FROM `t_po_opctag_value`
        WHERE tag_id = #{inputValveTagId}
        ORDER BY read_time
        ) AS tt
        WHERE id = t1.id
        )
        ORDER BY t1.read_time
    </select>

    <select id="getPotTaskByStageNumTime"
            resultMap="HandTaskGetPitOrderPotTaskVOMap">
        SELECT t1.id,
        t5.id AS pit_id,
        t1.out_order_code,
        t1.out_pit_code,
        t1.distillate_first_starttime,
        t1.distillate2_starttime as distillate2_Starttime,
        t1.distillate3_starttime,
        t1.distillate4_starttime,
        t1.distillate_lastwater_starttime,
        t1.distillate_first_tank_id,
        t1.distillate2_tank_id,
        t1.distillate3_tank_id,
        t1.distillate4_tank_id,
        t1.distillate_lastwater_tank_id,
        t1.outflow_endtime,
        t3.layer,
        t3.cycle_year,
        t3.cycle_no_id,
        t3.id AS orderId,
        t2.id AS pitOrderId,
        t2.in_pot_num_exc
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.out_order_code = t2.order_code
        AND t2.is_deleted = 0
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        AND t3.is_deleted = 0
        LEFT JOIN brewage_plan.t_pp_vinasse_source t4 ON t4.CODE = t1.vinasse
        AND deleted = 0
        LEFT JOIN t_po_workshop_pit t5 ON t5.pit_code = t1.out_pit_code
        AND t5.is_deleted = 0
        WHERE t1.is_deleted = 0
        and t1.controller_id = #{location}
        AND t4.id = #{vinasseId}
        <if test="stageNum == 1">
            and (t1.distillate_first_starttime between #{startTime} and #{endTime})
        </if>
        <if test="stageNum == 2">
            and (t1.distillate2_starttime between #{startTime} and #{endTime})
        </if>
        <if test="stageNum == 3">
            and (t1.distillate3_starttime between #{startTime} and #{endTime})
        </if>
        <if test="stageNum == 4">
            and (t1.distillate4_tank_id between #{startTime} and #{endTime})
        </if>
        <if test="stageNum == 5">
            and (t1.distillate_lastwater_starttime between #{startTime} and #{endTime})
        </if>
    </select>

    <!--    通过物料类型，车间名称，时间，获取消耗量-->
    <select id="getMaterialTypeByData" resultType="com.hvisions.brewage.dto.mkwine.dto.PutGrainDTO">
        SELECT LEFT(t1.controller_id,3) center, DATE_FORMAT(t1.out_time,'%Y-%m-%d') date, ifnull(sum(t5.quality), 0) quantity
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        LEFT JOIN brewage_plan.t_pp_formula t4 ON t3.formula_id = t4.id
        LEFT JOIN brewage_plan.t_pp_formula_detail t5 ON t4.id = t5.formula_id
        LEFT JOIN materials.hv_bm_material t6 ON t5.material_id = t6.id
        LEFT JOIN materials.hv_bm_material_type t7 ON t6.material_type = t7.id
        WHERE t1.is_deleted = 0
        AND t7.material_type_name = #{materialType}
        AND t5.quality > 0
        AND t1.out_time BETWEEN DATE_FORMAT(#{startDate}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
        GROUP BY LEFT(t1.controller_id,3) ,DATE_FORMAT(t1.out_time,'%Y-%m-%d')
        ORDER BY DATE_FORMAT(t1.out_time,'%Y-%m-%d') ASC
    </select>

    <select id="getPutGrainQuantity" resultType="com.hvisions.brewage.dto.mkwine.dto.PutGrainDTO">
        SELECT LEFT(pt.controller_id,3) center, DATE_FORMAT(pt.out_time,'%Y-%m-%d') date, ifnull(sum(pt.crushed_grains_quantity ),0) quantity
        FROM t_po_workshop_pit_order_pot_task pt
        WHERE pt.is_deleted = 0
		AND pt.out_time BETWEEN DATE_FORMAT(#{startDate}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
		GROUP BY LEFT(pt.controller_id,3) ,DATE_FORMAT(pt.out_time,'%Y-%m-%d')
		ORDER BY DATE_FORMAT(pt.out_time,'%Y-%m-%d') ASC
    </select>

    <select id="getTodayOutPotData"
            resultType="com.hvisions.brewage.mkwine.vo.productiondisposition.ProductionPlanningBoard.TodayOutPotDataVO">
        SELECT tt.controller_id,
			 tt.out_pit_code,
			 tt.vinasse,
			 tt.out_time,
			 (
					 SELECT count(1)
					 FROM t_po_workshop_pit_order_pot_task
					 WHERE out_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
						 and controller_id = tt.controller_id
					 ) as out_pot_num,
			 (
				 SELECT count(1)
				 FROM t_po_workshop_pit_order_pot_task
				 WHERE out_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
					 and controller_id = tt.controller_id
					 and in_pit_code != 0
				 ) as in_pot_num
        FROM t_po_workshop_pit_order_pot_task tt
        WHERE tt.out_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')

    </select>

    <select id="getSorghumConsumeByDate" resultType="java.lang.Float">
        SELECT IFNULL(SUM(t4.quality), 0)
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id
        LEFT JOIN brewage_plan.t_pp_formula_detail t4 ON t4.formula_id = t3.formula_id
        LEFT JOIN materials.hv_bm_material t5 ON t4.material_id = t5.id
        LEFT JOIN materials.hv_bm_material_type t6 ON t5.material_type = t6.id
        WHERE t1.is_deleted = 0
        AND t4.quality > 0
        and t4.type = 0
        AND t6.material_type_name = #{materialName}
        AND t3.center_id = #{centerId}
        <if test="endTime != null">
            and t1.out_time between DATE_FORMAT(#{startTime},'%Y-%m-%d 00:00:00') and
            DATE_FORMAT(#{endTime},'%Y-%m-%d 23:59:59')
        </if>
        <if test="endTime == null">
            and t1.out_time between DATE_FORMAT(#{startTime},'%Y-%m-%d 00:00:00') and
            DATE_FORMAT(#{startTime},'%Y-%m-%d 23:59:59')
        </if>
    </select>

    <select id="getPotTasksByOutOrderCode"
            resultMap="TPoWorkshopPitOrderPotTask">
        select * from t_po_workshop_pit_order_pot_task
        where is_deleted = false
        and out_order_code in
        <foreach collection="outOrderCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectPitOrderPageByBatch" resultType="com.hvisions.brewage.bw.vo.PitOrderPageByBatchVo">
        select t3.id, t3.order_code, t4.full_pit, l1.`name` locationName, l2.`name` centerName, t5.`name` cycleName, t6.`code` vinasseCode, t3.in_pit_date, t3.out_pit_date,
        ((SELECT ifnull(count(*), 0)
            FROM t_po_workshop_pit_order_pot_task pt
            LEFT JOIN t_po_workshop_pit_order_sap pos ON pos.order_code = pt.in_order_code
            WHERE pos.id = t2.id
            and pt.is_deleted = 0) + ifnull(t2.in_pot_num_exc, 0)) AS in_pit_num,
        (SELECT ifnull(count(*), 0)
            FROM t_po_workshop_pit_order_pot_task pt
            LEFT JOIN t_po_workshop_pit_order_sap pos ON pos.order_code = pt.out_order_code
            WHERE pos.id = t2.id
            and pt.is_deleted = 0) AS out_pit_num
        FROM t_po_workshop_pit_order_pot_task t1
         LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
         LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
         LEFT JOIN t_po_workshop_full_pit t4 on t4.id = t3.pit_id
         LEFT JOIN brewage_plan.t_pp_row t5 on t3.cycle_no_id = t5.id
         LEFT JOIN brewage_plan.t_pp_vinasse_source t6 on t6.id = t3.vinasse_id
         LEFT JOIN equipment.hv_bm_location l1 ON l1.id = t3.location_id
         LEFT JOIN equipment.hv_bm_location l2 ON l2.id = t3.center_id
        where t1.is_deleted = 0
        <if test="crushedGrainsLotId != null">
            and t1.crushed_grains_lot_id = #{crushedGrainsLotId}
        </if>
        <if test="backAlcoholicLotId != null">
            and t1.back_alcoholic_lot_id = #{backAlcoholicLotId}
        </if>
        <if test="ricehullLotId != null">
            and t1.ricehull_lot_id = #{ricehullLotId}
        </if>
        <if test="quLotId != null">
            and t1.qu_lot_id = #{quLotId}
        </if>
        <if test="centerId != null">
            and t3.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.location_id = #{locationId}
        </if>
        order by t3.id desc
    </select>
    <select id="getForwardBatchPage" resultType="com.hvisions.brewage.bw.vo.ForwardBatchVO">
        select b.batch batch, '回酒' as type, b.material, t.pot_task_num, t.order_num, t.handin_task_num
        from t_bw_batch b
        join (select t1.back_alcoholic_lot_id, count(1) pot_task_num, COUNT(DISTINCT out_pit_code) order_num, COUNT(DISTINCT t4.handin_task_id) handin_task_num
                from t_po_workshop_pit_order_pot_task t1
                LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
                LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
                LEFT JOIN t_po_workshop_receive_wine_order t4 on t4.pit_order_code = t2.order_code
                where t1.is_deleted = 0
                and back_alcoholic_lot_id is not null
                and t3.location_id is not null
                GROUP BY t1.back_alcoholic_lot_id) t on t.back_alcoholic_lot_id = b.batch
        where b.deleted = 0
        <if test="batch != null">
            and b.batch = #{batch}
        </if>
        <if test="materialId != null">
            and b.material_id = #{materialId}
        </if>
        GROUP BY b.batch,b.material,t.pot_task_num,t.order_num,t.handin_task_num
        union all
        select bi.qu_batch batch, '大曲' as type, m.material_name, t.pot_task_num, t.order_num, t.handin_task_num
        from t_po_colling_machine_bind_log bi
        left join t_wp_issue_order_detail od on od.batch = bi.qu_batch
        left join materials.`hv_bm_material` m ON m.id = od.material_id
        join (select t1.qu_lot_id, count(1) pot_task_num, COUNT(DISTINCT out_pit_code) order_num, COUNT(DISTINCT t4.								 handin_task_id) handin_task_num
                from t_po_workshop_pit_order_pot_task t1
                LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
                LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
                LEFT JOIN t_po_workshop_receive_wine_order t4 on t4.pit_order_code = t2.order_code
                where t1.is_deleted = 0
                and qu_lot_id is not null
                and t3.location_id is not null
                order BY t1.qu_lot_id) t on t.qu_lot_id = bi.qu_batch
        where od.deleted = 0 and od.type = 1
        <if test="batch != null">
            and bi.qu_batch = #{batch}
        </if>
        <if test="materialId != null">
            and od.material_id = #{materialId}
        </if>
        group by bi.qu_batch, m.material_name
    union all
        select od.flow_batch batch, '高粱' as type, od.material_name, t.pot_task_num, t.order_num, t.handin_task_num
        from brewage_rawmaterial_production.t_mpd_st_order_detail od
        join (select t1.crushed_grains_lot_id, count(1) pot_task_num, COUNT(DISTINCT out_pit_code) order_num, COUNT(												 DISTINCT t4.handin_task_id) handin_task_num
                from t_po_workshop_pit_order_pot_task t1
                LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
                LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
                LEFT JOIN t_po_workshop_receive_wine_order t4 on t4.pit_order_code = t2.order_code
                where t1.is_deleted = 0
                and crushed_grains_lot_id is not null
                and t3.location_id is not null
                GROUP BY t1.crushed_grains_lot_id) t on t.crushed_grains_lot_id = od.flow_batch
        where od.deleted = 0
        <if test="batch != null">
            and od.flow_batch = #{batch}
        </if>
        <if test="materialId != null">
            and od.material_id = #{materialId}
        </if>
        group by od.flow_batch, od.material_name
    union all
        select od.flow_batch batch, '稻壳' as type, od.material_name, t.pot_task_num, t.order_num, t.handin_task_num
        from brewage_rawmaterial_production.t_mpd_rt_order_detail od
        join (select t1.ricehull_lot_id, count(1) pot_task_num, COUNT(DISTINCT out_pit_code) order_num, COUNT(DISTINCT 												 t4.handin_task_id) handin_task_num
                from t_po_workshop_pit_order_pot_task t1
                LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
                LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
                LEFT JOIN t_po_workshop_receive_wine_order t4 on t4.pit_order_code = t2.order_code
                where t1.is_deleted = 0
                and ricehull_lot_id is not null
                and t3.location_id is not null
                GROUP BY t1.ricehull_lot_id) t on t.ricehull_lot_id = od.flow_batch
        where od.deleted = 0
        <if test="batch != null">
            and od.flow_batch = #{batch}
        </if>
        <if test="materialId != null">
            and od.material_id = #{materialId}
        </if>
        group by od.flow_batch, od.material_name
    </select>
    <select id="getHandinTaskPageByBatch" resultType="com.hvisions.brewage.bw.vo.HandinTaskPageByBatchVO">
        SELECT t1.id,
               t1.handin_task_code,
               t2.workshop_tmp_tank_code,
               t3.tank_id,
               t1.start_time,
               t1.stage_num,
               t5.`code`                      AS vinasse,
               t1.quantity,
               t1.to60_quantity,
               t1.standard_vol,
               t1.`level`,
               t6.`name`                      AS center_id,
               t7.`name`                      AS location_id,
               (    SELECT count(*)
                    FROM t_po_workshop_handin_task a1
                             LEFT JOIN t_po_workshop_receive_wine_order a2 ON a1.id = a2.handin_task_id
                             LEFT JOIN t_po_workshop_pit_order_pot_task a3 ON a2.pit_order_code = a3.out_order_code
                    where a1.id = t1.id
                      and a3.is_deleted = 0
               ) AS out_pit_num,
               ( SELECT GROUP_CONCAT(p.pit_code)
                 FROM t_po_workshop_receive_wine_order rwo
                          LEFT JOIN t_po_workshop_pit p ON p.id = rwo.pit_id
                 WHERE rwo.handin_task_id = t1.id
               ) AS pit_codes
        FROM t_po_workshop_handin_task t1
         LEFT JOIN t_po_workshop_tmp_tank t2 ON t1.tmptank_id = t2.id
         LEFT JOIN t_po_liquor_store_room_tank t3 ON t1.tank_id = t3.id
         LEFT JOIN t_po_workshop_tmp_tank_category t4 ON t4.id = t2.workshop_tmp_tank_category_id
         LEFT JOIN brewage_plan.t_pp_vinasse_source t5 ON t5.id = t4.fermentation_category_id
         LEFT JOIN `equipment`.hv_bm_location t6 ON t1.center_id = t6.id
         LEFT JOIN `equipment`.hv_bm_location t7 ON t1.location_id = t7.id
        <if test="pitOrderCode != null or potTaskId != null">
             JOIN (select DISTINCT wo.handin_task_id handin_task_id from t_po_workshop_receive_wine_order wo
                 JOIN t_po_workshop_wine_pot_task pt on pt.wine_order_id = wo.id
                   where 1 = 1
                    <if test="pitOrderCode != null">
                        and wo.pit_order_code = #{pitOrderCode}
                    </if>
                    <if test="potTaskId != null">
                        and pt.pot_task_id = #{potTaskId}
                    </if>
            ) temp on temp.handin_task_id = t1.id
        </if>
        where 1 = 1
        and t1.id in (
            select DISTINCT t4.handin_task_id
            from t_po_workshop_pit_order_pot_task t1
            LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
            LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
            LEFT JOIN t_po_workshop_receive_wine_order t4 on t4.pit_order_code = t2.order_code
            where t1.is_deleted = 0
                <if test="crushedGrainsLotId != null">
                    and t1.crushed_grains_lot_id = #{crushedGrainsLotId}
                </if>
                <if test="backAlcoholicLotId != null">
                    and t1.back_alcoholic_lot_id = #{backAlcoholicLotId}
                </if>
                <if test="ricehullLotId != null">
                    and t1.ricehull_lot_id = #{ricehullLotId}
                </if>
                <if test="quLotId != null">
                    and t1.qu_lot_id = #{quLotId}
                </if>
            )
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        order by t1.id
    </select>
    <select id="getPotTaskPageByBatch" resultType="com.hvisions.brewage.bw.vo.PotTaskPageByBatchVO">
        SELECT t1.id, t1.pot_serial_number, t1.controller_id, t1.pot_num, t1.outflow_starttime, t1.outflow_endtime,
               l1.`name` AS centerName, l2.`name` AS locationName
        FROM t_po_workshop_pit_order_pot_task t1
        LEFT JOIN t_po_workshop_pit_order_sap t2 ON t1.in_order_code = t2.order_code and t2.is_deleted = 0
        LEFT JOIN t_po_workshop_pit_order t3 ON t2.order_code_id = t3.id and t3.is_deleted = 0
        LEFT JOIN equipment.hv_bm_location l1 ON l1.id = t3.location_id
        LEFT JOIN equipment.hv_bm_location l2 ON l2.id = t3.center_id
        where 1 = 1
          and t3.location_id is not null
        <if test="crushedGrainsLotId != null">
            and t1.crushed_grains_lot_id = #{crushedGrainsLotId}
        </if>
        <if test="backAlcoholicLotId != null">
            and t1.back_alcoholic_lot_id = #{backAlcoholicLotId}
        </if>
        <if test="ricehullLotId != null">
            and t1.ricehull_lot_id = #{ricehullLotId}
        </if>
        <if test="quLotId != null">
            and t1.qu_lot_id = #{quLotId}
        </if>
        <if test="centerId != null">
            and t3.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t3.location_id = #{locationId}
        </if>
        order by t1.id desc
    </select>
    <select id="selectPitOrderByHandinTask" resultType="com.hvisions.brewage.bw.vo.ReverseBatchOrderVO">
        select DISTINCT t5.order_code, t4.crushed_grains_lot_id, t4.ricehull_lot_id, t4.back_alcoholic_lot_id, t4.qu_lot_id
        FROM t_po_workshop_handin_task t1
        JOIN t_po_workshop_receive_wine_order t2 ON t1.id = t2.handin_task_id
        LEFT JOIN t_po_workshop_pit_order_sap t3 ON t2.pit_order_code = t3.order_code and t3.is_deleted = 0
        LEFT JOIN t_po_workshop_pit_order_pot_task t4 on t4.in_order_code = t3.order_code and t4.is_deleted = 0
        LEFT JOIN t_po_workshop_pit_order t5 ON t5.id = t3.order_code_id and t5.is_deleted = 0
        where 1=1
          and t1.handin_task_code = #{batch}
        order by t5.id desc
    </select>
    <select id="selectHjUseTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(t.back_alcoholic_quantity_revise),0)
        from t_po_workshop_pit_order_pot_task t
         left join t_po_workshop_pit_order_sap os on os.order_code = t.in_order_code and os.is_deleted = 0
         left join t_po_workshop_pit_order o on o.id = os.order_code_id and o.is_deleted = 0
        where o.location_id = 5
        and o.center_id = #{centerId}
        and o.location_id = #{locationId}
        and t.create_time between #{startTime} and #{endTime}
    </select>
    <delete id="updateBatchForMaterials">
        <foreach collection="list" item="item" separator=";">
            update t_po_workshop_pit_order_pot_task
            set ricehull_quantity = #{item.ricehullQuantity},
            back_alcoholic_quantity_revise = #{item.backAlcoholicQuantityRevise},
            qu_quantity = #{item.quQuantity},
            crushed_grains_quantity = #{item.crushedGrainsQuantity}
            where id = #{item.id}
        </foreach>
    </delete>
    <select id="getActualPotCount" resultType="java.util.Map">
        select ifnull(sum(a.vinasse),0) as count,b.center_id as centerId
        from t_po_workshop_pit_order_pot_task a
        LEFT JOIN t_po_workshop_pit_order b on a.out_doublepit_code = b.order_code
        where 1=1 and a.is_deleted = 0 and b.is_deleted = 0
        <if test="locationKanBanDTO.vinasse !=null">
            and a.vinasse = #{locationKanBanDTO.vinasse}
        </if>
        <if test="locationKanBanDTO.startTime!=null and locationKanBanDTO.endTime!=null">
            and a.out_time BETWEEN DATE_FORMAT(#{locationKanBanDTO.startTime}, '%Y-%m-%d 00:00:00') AND DATE_FORMAT(#{locationKanBanDTO.endTime}, '%Y-%m-%d 23:59:59')
        </if>
        GROUP BY b.center_id
    </select>
    <select id="selectOrderPotTaskByInputRecord"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.OrderPotTaskVO">
        select pt.id, pt.serial_number, pt.iot_task_no, pt.vinasse, p.pit_code, pt.create_time, pt.crushed_grains_quantity, pt.ricehull_quantity, pt.back_alcoholic_quantity, pt.qu_quantity
        from t_po_workshop_pit_order_pot_task pt
         left join t_po_workshop_pit_order_sap os on pt.in_order_code = os.order_code
         left join t_po_workshop_pit p on p.id = os.pit_id
        where os.is_deleted = 0  and pt.is_deleted = 0
          and os.id = #{orderId}
    </select>
    <select id="selectOrderNoByBatch" resultType="java.lang.String">
        select DISTINCT o.order_code
        from t_po_workshop_pit_order_pot_task pt
         join t_po_workshop_pit_order_sap os on os.order_code = pt.in_order_code and os.is_deleted = 0
         join t_po_workshop_pit_order o on o.id = os.order_code_id and o.is_deleted = 0
        where pt.is_deleted = 0
        <if test="type == '高粱'">
           and pt.crushed_grains_lot_id = #{batch}
        </if>
        <if test="type == '稻壳'">
            and pt.ricehull_lot_id = #{batch}
        </if>
        <if test="type == '回酒'">
            and pt.back_alcoholic_lot_id = #{batch}
        </if>
        <if test="type == '曲粉'">
            and pt.qu_lot_id = #{batch}
        </if>
    </select>
    <select id="selectSerialNumberByHandinTask" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT pt.pot_serial_number legend,     t.handin_task_code batch
        FROM t_po_workshop_pit_order_pot_task pt
        JOIN t_po_workshop_receive_wine_order wo ON wo.pit_order_code = pt.in_order_code
        JOIN t_po_workshop_handin_task t ON t.id = wo.handin_task_id
        AND t.is_delete = 0
        where pt.is_deleted = 0
        and t.handin_task_code = #{batch}
    </select>
    <select id="selectPitOrderByOrderNo" resultType="com.hvisions.brewage.bw.vo.ReverseBatchOrderVO">
        select DISTINCT t5.order_code, t4.crushed_grains_lot_id, t4.ricehull_lot_id, t4.back_alcoholic_lot_id, t4.qu_lot_id
        FROM t_po_workshop_handin_task t1
         JOIN t_po_workshop_receive_wine_order t2 ON t1.id = t2.handin_task_id
         LEFT JOIN t_po_workshop_pit_order_sap t3 ON t2.pit_order_code = t3.order_code and t3.is_deleted = 0
         LEFT JOIN t_po_workshop_pit_order_pot_task t4 on t4.in_order_code = t3.order_code and t4.is_deleted = 0
         LEFT JOIN t_po_workshop_pit_order t5 ON t5.id = t3.order_code_id and t5.is_deleted = 0
        where 1=1
          and t5.order_code = #{orderCode}
        order by t5.id desc
    </select>
    <select id="selectSerialNumberByOrder" resultType="com.hvisions.brewage.bw.vo.ForwardBatchDetailLegendVO">
        select DISTINCT pt.pot_serial_number legend
        from t_po_workshop_pit_order_pot_task pt
         JOIN t_po_workshop_pit_order_sap os ON pt.in_order_code = os.order_code and os.is_deleted = 0
         LEFT JOIN t_po_workshop_pit_order o ON o.id = os.order_code_id and o.is_deleted = 0
        where pt.is_deleted = 0
          and o.order_code = #{orderCode}
        order by pt.id desc
    </select>
</mapper>
