<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderInAppraiseMapper">

    <resultMap id="inAppraiseResultMap"
               type="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderInAppraiseVO">
        <result column="sense_form" property="senseForm"
                typeHandler="com.hvisions.brewage.utils.JsonTypeHandler"/>
    </resultMap>

    <update id="DeleteInAppraise">
        update t_po_workshop_pit_order_inappraise set is_deleted = true where id = #{id};
    </update>

    <select id="getAllWorkshopPitOrderInAppraise" resultMap="inAppraiseResultMap">
        select *
        from t_po_workshop_pit_order_inappraise
        where is_deleted = 0
    </select>
<!-- 新增-->
    <insert id="addWorkshopPitOrderInAppraise"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_po_workshop_pit_order_inappraise
        (is_deleted, create_time
        <if test="inAppraiseTime != null">
            ,in_appraise_time
        </if>
        <if test="centerId != null">
            ,center_id
        </if>
        <if test="locationId != null">
            ,location_id
        </if>
        <if test="pitId != null">
            ,pit_id
        </if>

        <if test="orderId != null">
            ,order_id
        </if>
        <if test="fromPitId != null">
            ,from_pit_id
        </if>
        <if test="senseForm != null">
            ,sense_form
        </if>
        <if test="senseFermentMoist != null">
            ,sense_ferment_moist
        </if>
        <if test="user != null">
            ,`user`
        </if>
        )
        value
        (0, NOW()
        <if test="inAppraiseTime != null">
            ,#{inAppraiseTime}
        </if>
        <if test="centerId != null">
            ,#{centerId}
        </if>
        <if test="locationId != null">
            ,#{locationId}
        </if>
        <if test="pitId != null">
            ,#{pitId}
        </if>

        <if test="orderId != null">
            ,#{orderId}
        </if>
        <if test="fromPitId != null">
            ,#{fromPitId}
        </if>
        <if test="senseForm != null">
            ,#{senseForm,typeHandler=com.hvisions.brewage.utils.JsonTypeHandler}
        </if>
        <if test="senseFermentMoist != null">
            ,#{senseFermentMoist}
        </if>
        <if test="user != null">
            ,#{user}
        </if>
        )
    </insert>
<!--分页查询-->
    <select id="getWorkshopPitOrderInAppraisePage" resultMap="inAppraiseResultMap">
        select
            t4.code as vinasse_name,
            t2.full_pit_id,
            t5.full_pit_id as from_pit,
            t5.id as from_pit_id,
            t3.order_code,
            t3.layer,
            t3.out_pit_finish_time,
            t3.seal_confirm_time,
            e1.name as center_name,
            e2.name as location_name,
            s1.user_name as username,
            t1.*
        from t_po_workshop_pit_order_inappraise t1
            inner join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
            inner join t_po_workshop_full_pit t5 on t5.id = t1.from_pit_id
            left join t_po_workshop_pit_order t3 on t1.order_id = t3.id
            left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
            left join equipment.hv_bm_location e1 on e1.id = t1.center_id
            left join equipment.hv_bm_location e2 on e2.id = t1.location_id
            left join authority.sys_user s1 on t1.user = s1.id
        where
            t1.from_pit_id is not null and t1.is_deleted = false and t3.is_deleted = false
        <if test="centerId != null">and t1.center_id = #{centerId}</if>
        <if test="locationId != null">and t1.location_id = #{locationId}</if>
        <if test="inAppraiseTimeStart != null and inAppraiseTimeEnd != null">and t1.In_appraise_time between #{inAppraiseTimeStart} and #{inAppraiseTimeEnd}</if>
        <if test="closePitTimeStart != null and closePitTimeEnd != null">and t3.seal_confirm_time between #{closePitTimeStart} and #{closePitTimeEnd}</if>
        <if test="username != null">and s1.user_name like CONCAT('%', #{username}, '%') </if>
        <if test="fullPitId != null">and t2.full_pit_id like CONCAT('%',#{fullPitId},'%')</if>
        <if test="fromPitId != null">and t5.full_pit_id like CONCAT('%',#{fromPitId},'%')</if>
        <if test="orderCode != null">and t3.order_code like CONCAT('%',#{orderCode},'%')</if>
        order by in_appraise_time desc limit #{page}, #{pageSize}
    </select>

    <!--获取分页条数-->
    <select id="getCount" resultType="java.lang.Integer">
        select
            count(t1.id)
        from t_po_workshop_pit_order_inappraise t1
            inner join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
            inner join t_po_workshop_full_pit t5 on t5.id = t1.from_pit_id
            left join t_po_workshop_pit_order t3 on t1.order_id = t3.id
            left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
            left join equipment.hv_bm_location e1 on e1.id = t1.center_id
            left join equipment.hv_bm_location e2 on e2.id = t1.location_id
            left join authority.sys_user s1 on t1.user = s1.id
        where
            t1.from_pit_id is not null and t1.is_deleted = false and t3.is_deleted = false
        <if test="centerId != null">and t1.center_id = #{centerId}</if>
        <if test="locationId != null">and t1.location_id = #{locationId}</if>
        <if test="inAppraiseTimeStart != null and inAppraiseTimeEnd != null">and t1.In_appraise_time between #{inAppraiseTimeStart} and #{inAppraiseTimeEnd}</if>
        <if test="closePitTimeStart != null and closePitTimeEnd != null">and t3.seal_confirm_time between #{closePitTimeStart} and #{closePitTimeEnd}</if>
        <if test="username != null">and s1.user_name like CONCAT('%', #{username}, '%') </if>
        <if test="fullPitId != null">and t2.full_pit_id like CONCAT('%',#{fullPitId},'%')</if>
        <if test="fromPitId != null">and t2.full_pit_id like CONCAT('%',#{fromPitId},'%')</if>
        <if test="orderCode != null">and t3.order_code like CONCAT('%',#{orderCode},'%')</if>
    </select>

    <select id="getOrder"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO">
        select t1.id,
               t1.order_code,
               t1.vinasse_id,
               t1.layer,
               t1.pit_id,
               t2.code as vinasse_code,
               t2.name as vinasse_name,
               t3.full_pit_id
        from t_po_workshop_pit_order t1
                 left join brewage_plan.t_pp_vinasse_source t2 on t2.id = t1.vinasse_id
                 left join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        where t1.is_current = true
            and t1.is_deleted = 0
        and t1.center_id = #{centerId}
          and t1.location_id = #{locationId};
    </select>

    <!-- 根据窖池订单id查询入窖鉴定 -->
    <select id="checkOrder" resultType="java.lang.Integer">
        select
            count(id)
        from
            t_po_workshop_pit_order_inappraise
        where
            is_deleted = false and
            order_id = #{orderId};
    </select>

    <!--    修改-->
    <update id="updateWorkshopPitOrderInAppraise">
        update t_po_workshop_pit_order_inappraise
        <trim prefix="set" suffixOverrides=",">

            <if test="inAppraiseTime != null"> In_appraise_time = #{inAppraiseTime},</if>

            <if test="senseForm != null">
                sense_form = #{senseForm,typeHandler=com.hvisions.brewage.utils.JsonTypeHandler},
            </if>

            <if test="senseFermentMoist != null">
                sense_ferment_moist = #{senseFermentMoist},
            </if>

            <if test="minLoadingTemp != null">
                min_loading_temp = #{minLoadingTemp},
            </if>

            <if test="maxLoadingTemp != null">
                max_loading_temp = #{maxLoadingTemp},
            </if>

            <if test="riceHullPercent != null">
                rice_hull_percent = #{riceHullPercent},
            </if>

            <if test="waterAdditionPercent != null">
                water_addition_percent = #{waterAdditionPercent},
            </if>

            <if test="fermentedGrainSamples != null">
                fermented_grain_samples = #{fermentedGrainSamples},
            </if>

            <if test="user != null">`user` = #{user},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="checkIotTaskNo" resultType="java.lang.Integer">
        select
            count(id)
        from
            t_po_workshop_pit_order_inappraise
        where
            is_deleted = false and iot_task_no = #{iotTaskNo,jdbcType=VARCHAR}
    </select>
</mapper>