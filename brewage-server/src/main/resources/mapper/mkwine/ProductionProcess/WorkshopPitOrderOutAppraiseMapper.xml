<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderOutAppraiseMapper">
    <resultMap id="orderOutAppraiseResultMap"
               type="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO">
        <result column="fermentative_material_sense_form" property="fermentativeMaterialSenseForm"
                typeHandler="com.hvisions.brewage.utils.JsonTypeHandler"/>
    </resultMap>

    <update id="deleteOrder">
        update t_po_workshop_pit_order set is_deleted = true where order_code = #{orderCode};
    </update>

    <update id="deleteOutAppraise">
        update t_po_workshop_pit_order_out_appraise set is_deleted = true where id = #{id};
    </update>

    <!-- 开窖鉴定 -->
    <!-- 查询全部 -->
    <select id="getAllWorkshopPitOrderOutAppraise"
            resultMap="orderOutAppraiseResultMap">
        select
            t4.code              as vinasse_name,
            t2.full_pit_id,
            t3.open_pit_finish_time,
            t1.out_appraise_time as open_pit_finish_time_one,
            t3.order_code,
            t3.layer,
            t1.*
        from
            t_po_workshop_pit_order_out_appraise t1
            left join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
            left join t_po_workshop_pit_order t3 on t1.order_id = t3.id
            left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
        where
            t1.approve_status = 2 and t1.is_deleted = false
        <if test="centerId != null">and t1.center_id = #{centerId}</if>
        <if test="locationId != null">and t1.location_id = #{locationId}</if>
        order by t1.status asc, t1.out_appraise_time desc;
    </select>

    <!-- 分页查询 -->
    <select id="getWorkshopPitOrderOutAppraiseByPage"
            resultMap="orderOutAppraiseResultMap">
        select
            t4.code as vinasse_name,
            t2.full_pit_id,
            t2.full_pit_id as pitNo,
            t3.order_code,
            t3.order_code as pitOrder,
            t3.layer,
            e1.name as center_name,
            e2.name as location_name,
            s1.user_name as username,
        IF(t3.pit_status = 1, TO_DAYS(NOW()) - TO_DAYS(t3.seal_confirm_time),
        TO_DAYS(t3.out_pit_finish_time) -
        TO_DAYS(t3.seal_confirm_time))          AS fermentation_days,

        (ifnull(t3.in_pit_num, 0) + (select ifnull(SUM(in_pot_num_exc), 0)
        from t_po_workshop_pit_order_sap
        where order_code_id =
        t3.id)) as in_pit_num,
            t1.*,t1.create_time as startTime,t1.out_appraise_time as endTime
        from
            t_po_workshop_pit_order_out_appraise t1
            left join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
            left join t_po_workshop_pit_order t3 on t1.order_id = t3.id
            left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
            left join equipment.hv_bm_location e1 on e1.id = t1.center_id
            left join equipment.hv_bm_location e2 on e2.id = t1.location_id
            left join authority.sys_user s1 on t1.user = s1.id
        where
            t1.approve_status = 2 and t1.is_deleted = false
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="startTime != null and endTime != null">
            and t1.out_appraise_time between #{startTime} and #{endTime}
        </if>
        <if test="username != null">
            and s1.user_name like CONCAT('%', #{username}, '%')
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        <if test="fullPitId != null">
            and t2.full_pit_id like CONCAT('%', #{fullPitId}, '%')
        </if>
        <if test="vinasseId != null">
            and t3.vinasse_id = #{vinasseId}
        </if>
        <if test="orderCode != null">
            and t3.order_code like CONCAT('%', #{orderCode}, '%')
        </if>
        <if test="layer != null">
            and t3.layer = #{layer}
        </if>
        order by t1.status asc, t1.out_appraise_time desc limit #{page}, #{pageSize}
    </select>

    <!-- 获取查询条数 -->
    <select id="getCount"
            resultType="java.lang.Integer">
        select
            count(t1.id)
        from
            t_po_workshop_pit_order_out_appraise t1
            left join t_po_workshop_full_pit t2 on t1.pit_id = t2.id
            left join t_po_workshop_pit_order t3 on t1.order_id = t3.id
            left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
            left join equipment.hv_bm_location e1 on e1.id = t1.center_id
            left join equipment.hv_bm_location e2 on e2.id = t1.location_id
            left join authority.sys_user s1 on t1.user = s1.id
        where
            t1.approve_status = 2 and t1.is_deleted = false
        <if test="centerId != null">
            and t1.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and t1.location_id = #{locationId}
        </if>
        <if test="startTime != null and endTime != null">
            and t1.out_appraise_time between #{startTime} and #{endTime}
        </if>
        <if test="user != null">
            and t1.user like CONCAT('%', #{user}, '%')
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        <if test="fullPitId != null">
            and t2.full_pit_id like CONCAT('%', #{fullPitId}, '%')
        </if>
        <if test="vinasseId != null">
            and t3.vinasse_id = #{vinasseId}
        </if>
        <if test="orderCode != null">
            and t3.order_code like CONCAT('%', #{orderCode}, '%')
        </if>
        <if test="layer != null">
            and t3.layer = #{layer}
        </if>
    </select>

    <select id="getOrder"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO">
        select t1.id,
               t1.order_code,
               t1.vinasse_id,
               t1.layer,
               t1.pit_id,
               t2.code as vinasse_code,
               t2.name as vinasse_name,
               t3.full_pit_id
        from t_po_workshop_pit_order t1
                 left join brewage_plan.t_pp_vinasse_source t2 on t2.id = t1.vinasse_id
                 left join t_po_workshop_full_pit t3 on t1.pit_id = t3.id
        where t1.pit_status = 1
          and t1.is_deleted = 0
          and t1.is_current = true
          and t1.center_id = #{centerId}
          and t1.location_id = #{locationId};
    </select>

    <select id="getPitStatus" resultType="java.lang.Integer">
        select pit_status from t_po_workshop_pit_order where is_deleted = false and id = #{orderId};
    </select>

    <select id="getOrderStatus" resultType="java.lang.Integer">
        select order_status from t_po_workshop_pit_order where is_deleted = false and id = #{orderId};
    </select>

    <select id="getRUInfo"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.URVO">
        select
            s1.id as user_id,
            s1.user_name,
            s2.id as role_id,
            s2.name as role_name
        from
            authority.sys_user s1 inner join
            authority.sys_user_role ur on ur.user_id = s1.id inner join
            authority.sys_role s2 on s2.id = ur.role_id
        where
            s1.id = #{userId}
            limit 1;
    </select>

    <select id="getOrderCode" resultType="java.lang.String">
        select
            order_code
        from
            t_po_workshop_pit_order
        where
            id = #{orderId} and is_deleted = false;
    </select>

    <select id="checkOrderId" resultType="java.lang.Integer">
        select
            count(id) as nums
        from
            t_po_workshop_pit_order_out_appraise
        where
            order_id = #{orderId} and is_deleted = false;
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        select
            max(id)
        from
            t_po_workshop_pit_order_out_appraise
    </select>

    <select id="getExudingTask"
            resultType="com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitOrderExudingTask.WorkshopPitOrderExudingTaskVO">
        select
            *
        from
            t_po_workshop_pit_order_exuding_task t1 inner join
            t_po_workshop_pit_order t2 on t1.order_code = t2.order_code
        where
            t2.id = #{orderId};
    </select>

    <select id="judgmentRJH" resultType="java.lang.Integer">
        select
            count(id)
        from
            t_po_workshop_pit_day_plan
        where
            order_id = #{orderId};
    </select>

    <select id="getOutById"
            resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderOutAppraiseVO">
        select
            t4.code as vinasse_name,
            t2.full_pit_id,
            t3.open_pit_finish_time,
            t3.order_code,
            t3.layer,
            t3.empty_start_time,
            e1.name as center_name,
            e2.name as location_name,
            s1.user_name as username,
            t1.*
        from
            t_po_workshop_pit_order_out_appraise t1
                inner join t_po_workshop_pit_order t3 on t1.order_id = t3.id
                inner join t_po_workshop_full_pit t2 on t3.pit_id = t2.id
                left join brewage_plan.t_pp_vinasse_source t4 on t3.vinasse_id = t4.id
                left join equipment.hv_bm_location e1 on e1.id = t1.center_id
                left join equipment.hv_bm_location e2 on e2.id = t1.location_id
                left join authority.sys_user s1 on t1.user = s1.id
        where
            t3.is_deleted = false and t2.is_deleted = false and
            t1.id = #{id};
    </select>

    <select id="getOrderStatusByOrderCode" resultType="java.lang.Integer">
        select order_status from t_po_workshop_pit_order where is_deleted = false and order_code = #{orderCode};
    </select>

    <select id="getOutTimeById" resultType="java.util.Date">
        select
            out_appraise_time
        from
            t_po_workshop_pit_order_out_appraise
        where
            id = #{outAppraiseId};
    </select>
    <select id="getByOrderId"
            resultType="com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderOutAppraise">
        select * from t_po_workshop_pit_order_out_appraise oa
        where order_id = #{orderCodeId}
    </select>

    <select id="getDigitalTwinOpenCellarInformation"
            resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.OpenCellarInformationDTO">
        select * from
            (select t1.center_id,loc.`code` as centerCode,loc.`name` as centerName,t1.out_appraise_time,
                    (ifnull(t3.in_pit_num, 0) + (select ifnull(SUM(in_pot_num_exc), 0)
                                                 from t_po_workshop_pit_order_sap
                                                 where order_code_id =
                                                       t3.id)) as planNum,
                    ((SELECT count(*) FROM t_po_workshop_pit_order_pot_task WHERE in_order_code = t4.order_code AND is_deleted = 0) + coalesce(t4.in_pot_num_exc, 0)) AS prodNum
             from t_po_workshop_pit_order_out_appraise t1
                      LEFT JOIN t_po_workshop_full_pit t2 on t1.pit_id = t2.id
                      LEFT JOIN t_po_workshop_pit_order t3 on t1.order_id = t3.id
                      LEFT JOIN t_po_workshop_pit_order_sap t4 on t4.order_code_id = t3.id
                      LEFT JOIN equipment.hv_bm_location loc on loc.id = t1.center_id
             where 1=1
        <if test="startTime != null and endTime != null">
            and t1.out_appraise_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
             GROUP BY t1.center_id) tb
        where 1=1
        <if test="centerCode != null and centerCode != ''">
            and tb.centerCode = #{centerCode,jdbcType=VARCHAR}
        </if>

    </select>

    <!-- 根据id修改执行状态 -->
    <update id="updateStatus">
        update t_po_workshop_pit_order_out_appraise
        set status = #{status}
        where id = #{id};
    </update>

    <!-- 新增开窖鉴定 -->
    <insert id="insertWorkshopPitOrderOutAppraise">
        insert into t_po_workshop_pit_order_out_appraise(
        <trim prefix="" suffixOverrides=",">
            <if test="outAppraiseTime != null">
                out_appraise_time,
            </if>
            <if test="taskNo != null">
                task_no,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="centerId != null">
                center_id,
            </if>
            <if test="locationId != null">
                location_id,
            </if>
            <if test="pitId != null">
                pit_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="waterWeight != null">
                water_weight,
            </if>
            <if test="waterColor != null">
                water_color,
            </if>
            <if test="waterFlavour != null">
                water_flavour,
            </if>
            <if test="waterTurbidity != null">
                water_turbidity,
            </if>
            <if test="inPitMinTemperature != null">
                in_pit_min_temperature,
            </if>
            <if test="inPitMaxTemperature != null">
                in_pit_max_temperature,
            </if>
            <if test="inPitRicehullPercent != null">
                in_pit_ricehull_percent,
            </if>
            <if test="inPitWaterPercent != null">
                in_pit_water_percent,
            </if>
            <if test="fermentativeMaterialColor != null">
                fermentative_material_color,
            </if>
            <if test="fermentativeMaterialFlavour != null">
                fermentative_material_flavour,
            </if>
            <if test="fermentativeMaterialSenseFermentMoist != null">
                fermentative_material_sense_ferment_moist,
            </if>
            <if test="fermentativeMaterialSenseForm != null">
                fermentative_material_sense_form,
            </if>
            <if test="user != null">
                `user`,
            </if>
            <if test="notes != null">
                notes,
            </if>
            <if test="status != null">
                status,
            </if>
            approve_status,
            <if test="firstLevelApproveId != null">
                first_level_approve_id,
            </if>
            <if test="firstLevelApproveName != null">
                first_level_approve_name,
            </if>
            <if test="secondLevelApproveId != null">
                second_level_approve_id,
            </if>
            <if test="secondLevelApproveName != null">
                second_level_approve_name,
            </if>
            is_deleted,create_time
        </trim>
        ) value (
        <trim prefix="" suffixOverrides=",">
            <if test="outAppraiseTime != null">
                #{outAppraiseTime},
            </if>
            <if test="taskNo != null">
                #{taskNo},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="centerId != null">
                #{centerId},
            </if>
            <if test="locationId != null">
                #{locationId},
            </if>
            <if test="pitId != null">
                #{pitId},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="waterWeight != null">
                #{waterWeight},
            </if>
            <if test="waterColor != null">
                #{waterColor},
            </if>
            <if test="waterFlavour != null">
                #{waterFlavour},
            </if>
            <if test="waterTurbidity != null">
                #{waterTurbidity},
            </if>
            <if test="inPitMinTemperature != null">
                #{inPitMinTemperature},
            </if>
            <if test="inPitMaxTemperature != null">
                #{inPitMaxTemperature},
            </if>
            <if test="inPitRicehullPercent != null">
                #{inPitRicehullPercent},
            </if>
            <if test="inPitWaterPercent != null">
                #{inPitWaterPercent},
            </if>
            <if test="fermentativeMaterialColor != null">
                #{fermentativeMaterialColor},
            </if>
            <if test="fermentativeMaterialFlavour != null">
                #{fermentativeMaterialFlavour},
            </if>
            <if test="fermentativeMaterialSenseFermentMoist != null">
                #{fermentativeMaterialSenseFermentMoist},
            </if>
            <if test="fermentativeMaterialSenseForm != null">
                #{fermentativeMaterialSenseForm, typeHandler=com.hvisions.brewage.utils.JsonTypeHandler},
            </if>
            <if test="user != null">
                #{user},
            </if>
            <if test="notes != null">
                #{notes},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="approveStatus != null">#{approveStatus}</if>
            <if test="approveStatus == null">1</if>,
            <if test="firstLevelApproveId != null">
                #{firstLevelApproveId},
            </if>
            <if test="firstLevelApproveName != null">
                #{firstLevelApproveName},
            </if>
            <if test="secondLevelApproveId != null">
                #{secondLevelApproveId},
            </if>
            <if test="secondLevelApproveName != null">
                #{secondLevelApproveName},
            </if>
            false,NOW()
        </trim>
        )
    </insert>

    <insert id="insertOutLog">
        insert into t_po_out_appraise_log(center_id, location_id, pit_id, day_plan_id, order_id, open_date, notes, data)
        value(#{centerId}, #{locationId}, #{pitId}, null, #{orderId}, #{outAppraiseTime}, #{notes}, #{userName});
    </insert>

    <!-- 修改开窖鉴定 -->
    <update id="updateWorkshopPitOrderOutAppraise">
        update t_po_workshop_pit_order_out_appraise
        <trim prefix="set" suffixOverrides=",">
            <if test="outAppraiseTime != null">
                out_appraise_time = #{outAppraiseTime},
            </if>
            <if test="centerId != null">
                center_id = #{centerId},
            </if>
            <if test="locationId != null">
                location_id = #{locationId},
            </if>
            <if test="pitId != null">
                pit_id = #{pitId},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="waterWeight != null">
                water_weight = #{waterWeight},
            </if>
            <if test="waterColor != null">
                water_color = #{waterColor},
            </if>
            <if test="waterFlavour != null">
                water_flavour = #{waterFlavour},
            </if>
            <if test="waterTurbidity != null">
                water_turbidity = #{waterTurbidity},
            </if>
            <if test="inPitMinTemperature != null">
                in_pit_min_temperature = #{inPitMinTemperature},
            </if>
            <if test="inPitMaxTemperature != null">
                in_pit_max_temperature = #{inPitMaxTemperature},
            </if>
            <if test="inPitRicehullPercent != null">
                in_pit_ricehull_percent = #{inPitRicehullPercent},
            </if>
            <if test="inPitWaterPercent != null">
                in_pit_water_percent = #{inPitWaterPercent},
            </if>
            <if test="fermentativeMaterialColor != null">
                fermentative_material_color = #{fermentativeMaterialColor},
            </if>
            <if test="fermentativeMaterialFlavour != null">
                fermentative_material_flavour = #{fermentativeMaterialFlavour},
            </if>
            <if test="fermentativeMaterialSenseFermentMoist != null">
                fermentative_material_sense_ferment_moist = #{fermentativeMaterialSenseFermentMoist},
            </if>
            <if test="fermentativeMaterialSenseForm != null">
                fermentative_material_sense_form =
                #{fermentativeMaterialSenseForm, typeHandler=com.hvisions.brewage.utils.JsonTypeHandler2},
            </if>
            <if test="user != null">
                `user` = #{user},
            </if>
            <if test="notes != null">
                notes = #{notes},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="fermentationDecline != null">
                fermentation_decline = #{fermentationDecline},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateOutPitFinishTime">
        update t_po_workshop_pit_order set out_pit_finish_time = #{date} where is_deleted = false and id = #{orderId};
    </update>

    <update id="updatePitStatus">
        update t_po_workshop_pit_order set pit_status = #{pitStatus} where is_deleted = false and id = #{orderId};
    </update>

    <update id="updateOrderStatus">
        update t_po_workshop_pit_order set order_status = #{orderStatus} where is_deleted = false and id = #{orderId};
    </update>

    <update id="updateOutPitFinishTimeSap">
        update t_po_workshop_pit_order_sap set out_pit_finish_time = #{date} where is_deleted = false and order_code_id = #{orderId};
    </update>

    <update id="updateSapPitStatus">
        update t_po_workshop_pit_order_sap set pit_status = #{pitStatus} where is_deleted = false and order_code_id = #{orderId};
    </update>

    <update id="updateSapOrderStatus">
        update t_po_workshop_pit_order_sap set order_status = #{orderStatus} where is_deleted = false and order_code_id = #{orderId};
    </update>

    <update id="updateExudingStatus">
        update t_po_workshop_pit_order_exuding_task set status = 3, complete_time = #{outAppraiseTime} where id = #{id};
    </update>

    <update id="updateOrder">
        update t_po_workshop_pit_order
        set order_status = 0, pit_status = 1, is_current = 1, empty_start_time = null, out_pit_finish_time = null
        where id = #{orderId};
    </update>

    <update id="updateOrderSap">
        update t_po_workshop_pit_order_sap
        set order_status = 0, pit_status = 1, empty_start_time = null, out_pit_finish_time = null
        where order_code_id = #{orderId};
    </update>

    <select id="selectById" resultType="java.lang.Integer">
        select  max(id) from t_po_workshop_pit_order_out_appraise where order_id =#{orderId,jdbcType=INTEGER}
    </select>
    <select id="selectMaxTaskNo" resultType="java.lang.Integer">
        SELECT
            IFNULL(CAST(SUBSTRING(task_no, -3) AS UNSIGNED), 0) AS serial_num
        FROM
            t_po_workshop_pit_order_out_appraise
        WHERE
                task_no LIKE CONCAT(#{typeName,jdbcType=VARCHAR}, DATE_FORMAT( CURRENT_DATE, '%Y%m%d' ), '%' )
        ORDER BY
            task_no DESC
            LIMIT 1;
    </select>

    <select id="selectTaskNoById" resultType="java.lang.String">
        select task_no from t_po_workshop_pit_order_out_appraise where  id= #{id,jdbcType=INTEGER}
    </select>

    <select id="findSapSyncConfig" resultType="com.hvisions.brewage.vo.tpo.SapSyncConfigVO">
        select * from brewage_plan.t_pp_sap_sync_config where deleted = 0 and `year` = #{year} and `month` = #{month}
    </select>

    <select id="openCellarInvestmentNotSynchronized" resultType="com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO">
        SELECT t2.id,t2.order_code,t3.full_pit_id,t2.order_status, ifnull(t1.steamer_num_sync, 0) as steamer_num_sync
        from t_po_workshop_pit_order_out_appraise t4
                 LEFT JOIN t_po_workshop_pit_order t2 on t4.order_id = t2.id
                 LEFT JOIN t_po_workshop_pit_order_sap t1 on t1.order_code_id = t2.id
                 LEFT JOIN t_po_workshop_full_pit t3 ON t2.pit_id = t3.id
        where 1=1 and t2.is_deleted = 0 and t1.is_deleted = 0 and t4.is_deleted = 0 and t2.center_id = #{centerId} and t2.location_id = #{locationId}
          and t2.order_status = 3 and ifnull(t1.steamer_num_sync, 0) = 0 and t4.create_time between #{startTime} and #{endTime}
        GROUP BY t3.full_pit_id
    </select>

</mapper>