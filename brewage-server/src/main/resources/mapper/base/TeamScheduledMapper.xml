<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.base.TeamScheduledMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.base.TeamScheduled">
    <!--@Table base_team_scheduled-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
    <result column="center_name" jdbcType="VARCHAR" property="centerName" />
    <result column="workshop_code" jdbcType="VARCHAR" property="workshopCode" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="shift_id" jdbcType="VARCHAR" property="shiftId" />
    <result column="shift_name" jdbcType="VARCHAR" property="shiftName" />
    <result column="calendar_date" jdbcType="DATE" property="calendarDate" />
    <result column="shiftStart_time" jdbcType="TIME" property="shiftstartTime" />
    <result column="shiftEnd_time" jdbcType="TIME" property="shiftendTime" />
    <result column="staff_code" jdbcType="VARCHAR" property="staffCode" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="scheduled_work_hours" jdbcType="DECIMAL" property="scheduledWorkHours" />
    <result column="actual_work_hours" jdbcType="DECIMAL" property="actualWorkHours" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, center_code, center_name, workshop_code, workshop_name, group_code, group_name, 
    shift_id, shift_name, calendar_date, shiftStart_time, shiftEnd_time, staff_code, 
    staff_name, scheduled_work_hours, actual_work_hours, create_time, update_time, creator_id, 
    updater_id, deleted
  </sql>

  <select id="selectTeamScheduledList" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      base_team_scheduled a
        INNER JOIN equipment.hv_bm_location b ON a.center_code = b.rzgx_code
        INNER JOIN equipment.hv_bm_location c ON a.workshop_code = c.rzgx_code
    WHERE a.deleted=0
      <if test="query.centerId != null">
        and b.id=#{query.centerId,jdbcType=INTEGER}
      </if>

      <if test="query.locationId != null">
        and c.id=#{query.locationId,jdbcType=INTEGER}
      </if>

      <if test="query.calendarDate != null and query.calendarDate != ''">
        and DATE_FORMAT(a.calendar_date,'%Y-%m')=#{query.calendarDate,jdbcType=VARCHAR}
      </if>
  </select>

  <select id="selectPageList" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      base_team_scheduled a
        INNER JOIN equipment.hv_bm_location b ON a.center_code = b.rzgx_code
        INNER JOIN equipment.hv_bm_location c ON a.workshop_code = c.rzgx_code
    WHERE a.deleted=0
      <if test="query.centerId != null">
        and b.id=#{query.centerId,jdbcType=INTEGER}
      </if>

      <if test="query.locationId != null">
        and c.id=#{query.locationId,jdbcType=INTEGER}
      </if>

      <if test="query.calendarDate != null and query.calendarDate != ''">
        and DATE_FORMAT(a.calendar_date,'%Y-%m')=#{query.calendarDate,jdbcType=VARCHAR}
      </if>
  </select>

  <select id="selectYzgxCode" resultType="java.lang.String">
    SELECT rzgx_code from equipment.hv_bm_location WHERE id=#{locationId,jdbcType=INTEGER}
    </select>
</mapper>