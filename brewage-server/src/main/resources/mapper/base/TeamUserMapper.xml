<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.base.TeamUserMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.base.TeamUser">
    <!--@Table base_team_user-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="staff_code" jdbcType="VARCHAR" property="staffCode" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, team_id, staff_code, staff_name, create_time, update_time, creator_id, updater_id, 
    deleted
  </sql>
</mapper>