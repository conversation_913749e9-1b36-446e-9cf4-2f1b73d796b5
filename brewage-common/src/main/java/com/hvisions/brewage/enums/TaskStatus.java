package com.hvisions.brewage.enums;

/**
 * 任务状态枚举
 */
public enum TaskStatus {
    // 枚举常量定义
  //  CREATED(1, "创建"),
    // IN_MAINTENANCE(2, "养护中"),
    //  MAINTENANCE_COMPLETED(3, "养护完成"),
    PENDING_EXE(4, "待执行"),
    COMPLETED(5, "已完成"),
    NOT_STARTED(6, "未开始"),
    IN_PROGRESS(7, "执行中"),
    TERMINATED(8, "已终止"),
    NEW(9, "新建"),
    DELETED(10, "删除"),
    UNSYNCED(11, "未同步"),
    SYNC_FAILED(12, "同步失败"),
    SYNCED(13, "已同步"),
    UNREPORTED(14, "未提报"),
    REPORTED(15, "已提报"),
    CANCELED(16, "已取消"),
    REJECTED(17, "已驳回"),
    CELLAR_OUT_STATUS(18, "出窖"),
    CELLAR_IN_STATUS(19, "入窖"),
    ;

    // 枚举字段
    private final int code;
    private final String name;

    // 私有构造函数
    TaskStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // Getter方法
    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    // 根据code获取枚举
    public static TaskStatus getByCode(int code) {
        for (TaskStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的状态码: " + code);
    }

    @Override
    public String toString() {
        return this.name + "(" + this.code + ")";
    }
}
