package com.hvisions.brewage.enums;


public interface TaskSpreadingParameterEnum {
    public static final String CONVEYORLEFTGRAINTEMP = "摊晾机输送链板左侧糟醅温度";
    public static final String CONVEYORRIGHTGRAINTEMP = "摊晾机输送链板右侧糟醅温度";
    public static final String CONSUME = "摊晾机输送链板前端糟醅温度";
    public static final String CONVEYORMIDTEMP = "摊晾温度1";
    public static final String CONVEYORREARTEMP = "摊晾温度2";
    public static final String FANRUNTIMEPERUNIT = "每台风机运行时长";
    public static final String FANSPEED = "风机运行转速";
    public static final String CirculatingWaterReturnTemperature = "循环水回水温度";
    public static final String BottomPotWaterTemperature = "底锅水温度";
    public static final String TemperatureMeasuringBucket = "量水桶量水温度";
    public static final String FlowingWineTemperatureTemp = "流酒温度";
    public static final String FlowingWineSpeedTemp = "流酒速度";
}
