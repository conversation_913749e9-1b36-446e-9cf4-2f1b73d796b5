package com.hvisions.powder.enums;

/**
 * <AUTHOR>
 * @date 2025/9/23 14:19
 */
public enum BusinessModulesInvolvedEnum {
    // 涉及的业务模块枚举
    /**
     * 曲斗运输管理系统
     */
    linkToTms("车辆位置接收、曲斗信息接收、曲斗库存接收", "车辆位置接收、曲斗信息接收、曲斗库存接收"),
    /**
     * 曲房温湿度
     */
    linkToQufang("温湿度点位配置、地温配置、摊晾", "温湿度点位配置、地温配置、摊晾"),
    /**
     * 酿酒8中心MES
     */
    linkToLuohan("排程计划同步、月计划同步", "排程计划同步、月计划同步"),
    /**
     * 安消一体系统
     */
    linkArt("",""),
    /**
     * 地磅系统
     */
    linkToAvs("无法正常接收过磅数据","无法正常接收过磅数据"),
    /**
     * 压曲码垛控制
     */
    linkToZongheng("",""),
    /**
     * 库房管理
     */
    linkToWms("",""),
    /**
     * 小麦预处理
     */
    linkToWincos("高粱转运任务、高粱发料任务、高粱破碎任务、高粱碎料转运任务、高粱碎料发放任务、原辅料库存可视化、停产物料拉动需求计算、原辅料差异处理、原辅料库存管理、原辅料盘点","高粱转运任务、高粱发料任务、高粱破碎任务、高粱碎料转运任务、高粱碎料发放任务、原辅料库存可视化、停产物料拉动需求计算、原辅料差异处理、原辅料库存管理、原辅料盘点"),
    /**
     * PI实时数据库
     */
    linkToPi("1、原辅料部分：稻壳转运任务、稻壳清蒸进料任务、稻壳清蒸任务、熟稻壳发料任务、熟稻壳发放任务、原辅料库存可视化、停产物料拉动需求计算、原辅料差异处理、原辅料库存管理、原辅料盘点、回酒液位查看、回酒需求提报、回酒发放记录\n" +
            "2、生产部分：交酒液位检测","1、原辅料部分：稻壳转运任务、稻壳清蒸进料任务、稻壳清蒸任务、熟稻壳发料任务、熟稻壳发放任务、原辅料库存可视化、停产物料拉动需求计算、原辅料差异处理、原辅料库存管理、原辅料盘点、回酒液位查看、回酒需求提报、回酒发放记录\n" +
            "2、生产部分：交酒液位检测"),
    /**
     * SAP系统
     */
    linkToSap("1、计划部分：排程计划同步、月计划同步、原辅料过账、原辅料冲销；\n" +
            "2、原辅料部分：原辅料库存盘点、原辅料差异处理、高粱发料过账、稻壳发料过账、回酒发放过账、大曲接收过账；\n" +
            "3、生产部分：标准工艺接收、窖池订单创建、删除，物料投入同步，在制工时同步，订单工时同步，甑口同步，窖池订单状态同步；","1、计划部分：排程计划同步、月计划同步、原辅料过账、原辅料冲销；\n" +
            "2、原辅料部分：原辅料库存盘点、原辅料差异处理、高粱发料过账、稻壳发料过账、回酒发放过账、大曲接收过账；\n" +
            "3、生产部分：标准工艺接收、窖池订单创建、删除，物料投入同步，在制工时同步，订单工时同步，甑口同步，窖池订单状态同步；"),
    /**
     * 威斯派克系统
     */
    linkToVspec("质检功能","质检功能"),
    ;

    private String code;
    private String name;

    BusinessModulesInvolvedEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
