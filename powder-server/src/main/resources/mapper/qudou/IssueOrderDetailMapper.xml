<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.powder.qudou.dao.IssueOrderDetailMapper">

    <update id="acceptDetailByParameter">
        update brewage.t_wp_issue_order_detail
        set accept_state =1
        where deleted = false
          and barcode = #{barcode}
          and accept_state = 0
    </update>
    <delete id="deleteByItems">
        delete from t_wp_issue_order_detail where item_id in
        <foreach collection="itemIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <select id="checkDouInUse" resultType="java.lang.Boolean">
        select 1
        from brewage.t_wp_issue_order_detail
        where deleted = false
          and barcode = #{barcode}
          and accept_state in (0, 2)
        limit 1
    </select>
    <select id="checkIssueOrderFinished" resultType="java.lang.Boolean">
        select 1
        from brewage.t_wp_issue_order twio
                 inner join brewage.t_wp_issue_order_item twioi
                            on twio.id = twioi.order_id
                 inner join brewage.t_wp_issue_order_detail twiod
                            on twioi.id = twiod.item_id
        where twio.deleted = false
          and twioi.deleted = false
          and twiod.deleted = false
          and twiod.accept_state in (0, 2, 3)
          and twioi.id = #{issueId}
        limit 1
    </select>
    <select id="checkExistDetailInfo" resultType="java.lang.Boolean">
        select 1
        from brewage.t_wp_issue_order twio
                 inner join brewage.t_wp_issue_order_item twioi
                            on twio.id = twioi.order_id
                 inner join brewage.t_wp_issue_order_detail twiod
                            on twioi.id = twiod.item_id
        where twioi.deleted = false
          and twio.deleted = false
          and twiod.deleted = false
        limit 1
    </select>
    <select id="queryPagedIssueOrderDetail" resultType="com.hvisions.powder.qudou.vo.DouIssueVO">
        select twiod.barcode, twiod.batch, twiod.issuer, twiod.issue_time, accept_center, twiod.item_id,IF(twiod.type = '1' and accept_center
        is not null and accept_state = '1',1,IF(is_surplus = '1' and accept_center
        is not null,1,(SELECT IFNULL(SUM(quantity),0)FROM t_wp_item_detail WHERE issue_detail_id  = twiod.id))) AS acceptNumber,
        accept_location,accept_line, twiod.acceptor, twiod.accept_time, twiod.accept_state,
        twio.demand_order,twiod.id as detailId,twiod.material_id,twiod.type,twiod.is_surplus,twiod.issue_surplus_number,twiod.experiment_power,twiod.science_power,twiod.issue_number, twsp.state, twsp.certificate_number,
        twsp.certificate_year,IF(twiod.experiment_power = 1,'实验曲',IF(twiod.science_power = 1,'科研曲','生产用曲')) AS powder_type,twiod.urgent,twiod.powder_line,twiod.smash_batch,
        tvt.id as vehicle_transport_id,
        tvt.license_plate_number,
        tvt.state as transport_state,
        IF(twiod.type = 2,IF(twiod.is_surplus = 1,twiod.issue_surplus_number,ss.weight),twiod.bucket_weight) AS bucket_weight
        from brewage.t_wp_issue_order_detail twiod
        LEFT JOIN t_wp_sack_specification ss ON  ss.id = twiod.specification_id
        left join brewage.t_wp_issue_order_item twioi on twioi.id = twiod.item_id and twioi.deleted = false
        left join brewage.t_wp_issue_order twio on twio.id = twioi.order_id and twio.deleted = false
        left join (SELECT id,order_detail_id,state,certificate_number,certificate_year FROM t_wp_sap_post twsp WHERE twsp.deleted = false AND twsp.id IN (SELECT MAX(id) id FROM t_wp_sap_post GROUP BY order_detail_id)) twsp ON twsp.order_detail_id = twiod.id
        left join  (select * from brewage.vehicle_transport where deleted = 0) tvt on twiod.vehicle_transport_id = tvt.id
        where twiod.deleted = false
        <if test="req.barcode != null">
            and twiod.barcode like concat('%',#{req.barcode},'%')
        </if>
        <if test="req.powderLine != null and req.powderLine != ''">
            and twiod.powder_line = #{req.powderLine}
        </if>
        <if test="req.centerId != null and req.centerId != ''">
            and FIND_IN_SET(twio.center_id , #{req.centerId})
        </if>
        <if test="req.locationId != null and req.locationId != ''">
            and FIND_IN_SET(twio.location_id , #{req.locationId})
        </if>
        <if test="req.acceptState != null">
            and twiod.accept_state = #{req.acceptState}
        </if>
        <if test="req.bindState != null">
            and twiod.bind_state = #{req.bindState}
        </if>
        <if test="req.start != null and req.end != null">
            and twiod.create_time between #{req.start} and #{req.end}
        </if>
        <if test="req.acceptStart != null and req.acceptEnd != null">
            AND DATE_FORMAT(twiod.accept_time, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{req.acceptStart}, '%Y-%m-%d') AND DATE_FORMAT(#{req.acceptEnd}, '%Y-%m-%d')
        </if>
        <if test="req.materialId != null">
            AND twiod.material_id = #{req.materialId}
        </if>
        <if test="req.vehicleTransportId != null">
            AND tvt.id = #{req.vehicleTransportId}
        </if>
        <if test="req.type != null and req.type != ''">
            AND twiod.type = #{req.type}
        </if>
        <if test="req.hasBatch">
            AND twiod.smash_batch IS NOT NULL AND twiod.smash_batch != ''
        </if>
        <if test="req.smashBatch != null and req.smashBatch != ''">
            AND twiod.smash_batch like concat('%',#{req.smashBatch},'%')
        </if>
        <if test="req.batch != null and req.batch != ''">
            AND twiod.batch like concat('%',#{batch},'%')
        </if>
        order by twiod.create_time desc
        limit #{req.page},#{req.pageSize}
    </select>
    <select id="queryPagedIssueOrderDetailCount" resultType="java.lang.Long">
        select count(*)
        from brewage.t_wp_issue_order_detail twiod
        LEFT JOIN t_wp_sack_specification ss ON  ss.id = twiod.specification_id
        left join brewage.t_wp_issue_order_item twioi on twioi.id = twiod.item_id and twioi.deleted = false
        left join brewage.t_wp_issue_order twio on twio.id = twioi.order_id and twio.deleted = false
        left join (SELECT id,order_detail_id,state,certificate_number,certificate_year FROM t_wp_sap_post twsp WHERE twsp.deleted = false AND twsp.id IN (SELECT MAX(id) id FROM t_wp_sap_post GROUP BY order_detail_id)) twsp ON twsp.order_detail_id = twiod.id
        left join  (select * from brewage.vehicle_transport where deleted = 0) tvt on twiod.vehicle_transport_id = tvt.id
        where twiod.deleted = false
        <if test="req.barcode != null">
            and twiod.barcode like concat('%',#{req.barcode},'%')
        </if>
        <if test="req.powderLine != null and req.powderLine != ''">
            and twiod.powder_line = #{req.powderLine}
        </if>
        <if test="req.centerId != null and req.centerId != ''">
            and FIND_IN_SET(twio.center_id , #{req.centerId})
        </if>
        <if test="req.locationId != null and req.locationId != ''">
            and FIND_IN_SET(twio.location_id , #{req.locationId})
        </if>
        <if test="req.acceptState != null">
            and twiod.accept_state = #{req.acceptState}
        </if>
        <if test="req.bindState != null">
            and twiod.bind_state = #{req.bindState}
        </if>
        <if test="req.start != null and req.end != null">
            and twiod.create_time between #{req.start} and #{req.end}
        </if>
        <if test="req.acceptStart != null and req.acceptEnd != null">
            AND DATE_FORMAT(twiod.accept_time, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{req.acceptStart}, '%Y-%m-%d') AND DATE_FORMAT(#{req.acceptEnd}, '%Y-%m-%d')
        </if>
        <if test="req.materialId != null">
            AND twiod.material_id = #{req.materialId}
        </if>
        <if test="req.vehicleTransportId != null">
            AND tvt.id = #{req.vehicleTransportId}
        </if>
        <if test="req.type != null and req.type != ''">
            AND twiod.type = #{req.type}
        </if>
        <if test="req.hasBatch">
            AND twiod.smash_batch IS NOT NULL AND twiod.smash_batch != ''
        </if>
        <if test="req.smashBatch != null and req.type != ''">
            AND twiod.smash_batch like concat('%',#{req.smashBatch},'%')
        </if>
    </select>
    <select id="listTransferRecord" resultType="com.hvisions.powder.qudou.dto.TransferRecord">
        select twio.center_id,
        twio.center_name,
        twio.location_id,
        twio.location_name,
        twiod.material_id,
        twiod.bucket_weight
        from brewage.t_wp_issue_order_detail twiod
        inner join brewage.t_wp_issue_order_item twioi
        on twioi.id = twiod.item_id and twioi.deleted = false
        inner join brewage.t_wp_issue_order twio
        on twio.id = twioi.order_id and twio.deleted = false
        where twiod.deleted = false
        and twiod.accept_state = 1
        <if test="start != null and end != null">
            and twiod.issue_time between #{start} and #{end}
        </if>
    </select>
    <select id="checkParameterRaisingException" resultType="java.lang.Boolean">
        select 1
        from brewage.t_wp_issue_order_detail
        where barcode = #{barcode}
          and accept_state = 3
    </select>
    <select id="listMaterialsIssued" resultType="java.lang.Integer">
        select distinct material_id
        from brewage.t_wp_issue_order_detail
        where deleted = false
    </select>

    <select id="getByCode" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueOrderDetailDTO">
        SELECT p.empty_state,p.unbind_time,iod.*
        FROM t_wp_parameter p
        LEFT JOIN (
            SELECT * FROM t_wp_issue_order_detail iod WHERE id IN (SELECT MAX(id) FROM t_wp_issue_order_detail WHERE deleted = 0 GROUP BY barcode)

        ) iod ON p.barcode = iod.barcode

        WHERE p.deleted = 0 AND p.barcode like concat('%',#{barcode},'%')
    </select>

    <select id="getNotSyncSapIssueOrderDetailList" resultType="com.hvisions.powder.qudou.vo.IssueOrderDetailVO">
        SELECT iod.*
        FROM t_wp_issue_order_detail iod
        LEFT JOIN t_wp_sap_post sp ON sp.order_detail_id = iod.id AND sp.deleted = 0

        WHERE iod.deleted = 0 AND iod.accept_state = 1 AND sp.id IS NULL

        AND FIND_IN_SET(iod.accept_center,"709,713,718")
        <if test="startTime != null and endTime != null">
            AND iod.accept_time BETWEEN #{startTime} AND  #{endTime}
        </if>

    </select>

    <select id="getVehicleTransportDetail" resultType="com.hvisions.powder.qudou.dto.VehicleTransportDetailDTO">
        SELECT (od.issue_number - IFNULL((SELECT SUM(quantity) FROM t_wp_item_detail WHERE issue_detail_id = od.id),0)) AS issue_number,od.issue_number actual_number,od.*,IF(od.type = 2,ss.weight,od.bucket_weight) AS weight,vt.license_plate_number
        FROM t_wp_issue_order_detail od
                 LEFT JOIN t_wp_sack_specification ss ON  ss.id = od.specification_id
                 LEFT JOIN vehicle_transport vt ON vt.id = od.vehicle_transport_id
                 LEFT JOIN t_wp_issue_order_item oi ON oi.id = od.item_id
        WHERE od.vehicle_transport_id = #{vehicleId}
        AND od.accept_state = 0
        AND od.type = #{type}
        AND od.deleted = FALSE
        order by od.issue_time
    </select>

    <resultMap id="getVehicleIssueList" type="com.hvisions.powder.qudou.dto.IssueOrderDetailPageDTO">
        <result property="licensePlateNumber" column="license_plate_number"/>
        <result property="id" column="detailId"/>
        <collection property="issueOrderDetailVOList" ofType="com.hvisions.powder.qudou.vo.IssueOrderDetailVO" javaType="list"/>
    </resultMap>

    <select id="getVehicleIssueList" resultMap="getVehicleIssueList">

    </select>

    <select id="getIssueDetailSum" resultType="com.hvisions.powder.qudou.dto.IssueDetailSumPageDTO">
        SELECT a.accept_state,a.base_id,a.material_id,SUM(weight) AS weight FROM
        (SELECT iod.id,iod.accept_state,iod.base_id,iod.material_id,iod.accept_time,IFNULL(IF(iod.type =
        '1',(iod.issue_number*iod.bucket_weight),(iod.issue_number*ss.weight)),0) AS weight
        FROM brewage.`t_wp_issue_order_detail` iod
        LEFT JOIN brewage.`t_wp_sack_specification` ss ON iod.specification_id = ss.id
        <where>
            <if test="acceptTime != null">
                AND YEAR(iod.accept_time) = #{acceptTime}
            </if>
            <if test="acceptState != null">
                AND iod.accept_state = #{acceptState}
            </if>
            <if test="baseId != null">
                AND iod.base_id = #{baseId}
            </if>
            <if test="materialId != null">
                AND iod.material_id = #{materialId}
            </if>
        </where>) a
        GROUP BY a.accept_state,a.base_id,a.material_id
    </select>

    <resultMap id="powderIssueSchedulePageDTOS" type="com.hvisions.powder.qudou.dto.CenterIssueSchedulePageDTO">
        <result property="centerId" column="center_id"/>
        <result property="centerName" column="center_name"/>
        <collection property="powderIssueSchedulePageDTOS" ofType="com.hvisions.powder.qudou.dto.PowderIssueSchedulePageDTO">
            <result property="type" column="type"/>
            <result property="materialCode" column="material_code"/>
            <result property="materialName" column="material_name"/>
            <result property="demandNumber" column="demand_number"/>
            <result property="issueNumber" column="issue_number"/>
            <result property="actualNumber" column="actual_number"/>
            <result property="demandWeight" column="demand_weight"/>
            <result property="issueWeight" column="issue_weight"/>
            <result property="actualWeight" column="actual_weight"/>
        </collection>
    </resultMap>

    <resultMap id="getBaseIssueSchedule" type="com.hvisions.powder.qudou.dto.BaseIssueSchedulePageDTO">
        <result property="baseId" column="base_id"/>
        <result property="baseName" column="base_name"/>
        <collection property="centerIssueSchedulePageDTOS" resultMap="powderIssueSchedulePageDTOS"/>
    </resultMap>

    <select id="getBaseIssueSchedule" resultMap="getBaseIssueSchedule">
        SELECT a.base_id,
               a.base_name,
               a.center_id,
               a.center_name,
               a.type,
               a.material_code,
               a.material_name,
               IFNULL(SUM(a.demand_number),0) AS demand_number,
               IFNULL(SUM(a.issue_number),0) AS issue_number,
               IFNULL(SUM(a.actual_number),0) AS actual_number,
               IFNULL(SUM(a.demand_weight),0) AS demand_weight,
               IFNULL(SUM(a.issue_weight),0) AS issue_weight,
               IFNULL(SUM(a.actual_weight),0) AS actual_weight
        FROM
            (SELECT
        sd.base_id,
        sd.base_name,
        sd.center_id,
        sd.center_name,
        sdd.type,
        sdd.specification_id,
        sdd.material_code,
        sdd.material_name,
        sdd.demand_number,
        ioi.issue_number,
        ioi.actual_number,
        if(sdd.type = 2,if(sdd.specification_id is null and sdd.bucket_weight is null,sdd.surplus_number,sdd.demand_number *(SELECT weight FROM t_wp_sack_specification WHERE id = sdd.specification_id)),sdd.demand_number * sdd.bucket_weight) AS demand_weight,
        if(sdd.type = 2,if(sdd.specification_id is null and sdd.bucket_weight is null,ioi.issue_surplus_number,ioi.issue_number *(SELECT weight FROM t_wp_sack_specification WHERE id = sdd.specification_id)),ioi.issue_number * sdd.bucket_weight) AS issue_weight,
        if(sdd.type = 2,if(sdd.specification_id is null and sdd.bucket_weight is null,ioi.actual_surplus_number,ioi.actual_number *(SELECT weight FROM t_wp_sack_specification WHERE id = sdd.specification_id)),ioi.actual_number * sdd.bucket_weight) AS actual_weight
             FROM t_wp_single_demand_detail sdd
                      LEFT JOIN t_wp_single_demand sd ON sdd.demand_id = sd.id AND sd.deleted = FALSE
                      LEFT JOIN t_wp_issue_order io ON io.demand_id = sdd.id AND io.deleted = FALSE
                      LEFT JOIN t_wp_issue_order_item ioi ON ioi.order_id = io.id AND ioi.deleted = FALSE AND ioi.material_code = sdd.material_code
             WHERE sdd.deleted = FALSE
                 <if test="useTime != null">
                    AND sd.use_time = #{useTime}
                 </if>
                 <if test="baseId != null and baseId > 0">
                    AND sd.base_id = #{baseId}
                 </if>
                 <if test="baseName != null and baseName != ''">
                    AND sd.base_name = #{baseName}
                 </if>
                 <if test="materialName != null and materialName != ''">
                    AND sdd.material_name = #{materialName}
                 </if>
                 <if test="startTime != null and endTime != null">
                     AND DATE_FORMAT(sd.use_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{startTime},'%Y-%m-%d') AND DATE_FORMAT(#{endTime},'%Y-%m-%d')
                 </if>
             ORDER BY sd.id DESC) a
        GROUP BY a.base_id,a.base_name,a.center_id,a.center_name,a.type,a.material_code,a.material_name
    </select>

    <update id="receivePowderVehicle">
        UPDATE t_wp_issue_order_detail
        <set>
            <if test="issueOrderDetail.grossWeight != null">
                gross_weight = #{issueOrderDetail.grossWeight},
            </if>
            <if test="issueOrderDetail.appearanceWeight != null">
                appearance_weight = #{issueOrderDetail.appearanceWeight},
            </if>
            <if test="issueOrderDetail.leaveNetWeight != null">
                leave_net_weight = #{issueOrderDetail.leaveNetWeight},
            </if>
            <if test="issueOrderDetail.leaveNetWeight != null">
                net_weight = #{issueOrderDetail.netWeight},
            </if>
            <if test="issueOrderDetail.appearanceTime != null">
                appearance_time = #{issueOrderDetail.appearanceTime},
            </if>
            <if test="issueOrderDetail.admissionTime != null">
                admission_time = #{issueOrderDetail.admissionTime},
            </if>
            <if test="issueOrderDetail.vehicleState != null">
                vehicle_state = #{issueOrderDetail.vehicleState},
            </if>
            <if test="issueOrderDetail.demandNumber != null and issueOrderDetail.demandNumber != ''">
                demand_number = #{issueOrderDetail.demandNumber},
            </if>
        </set>
        WHERE demand_number = #{demandNumber}
    </update>

    <select id="getWeighingInformation" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueWeight">
        SELECT
        t.license_plate_number,
        count( 1 ) num_count,
        SUM( t.appearance_weight ) appearance_weight,
        SUM( t.gross_weight ) gross_weight,
        SUM( t.net_weight ) net_weight
        FROM
        (
        SELECT
        vt.license_plate_number,
        id.demand_number,
        id.appearance_weight,
        id.gross_weight,
        id.net_weight,
        id.appearance_time,
        id.powder_line,
        id.admission_time
        FROM
        t_wp_issue_order_detail id
        LEFT JOIN vehicle_transport vt ON vt.id = id.vehicle_transport_id
        AND vt.deleted = FALSE
        WHERE
        id.deleted = FALSE
        AND id.vehicle_transport_id IS NOT NULL
        AND id.demand_number IS NOT NULL
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
                AND vt.license_plate_number = #{licensePlateNumber}
            </if>
            <if test="startTime != null and endTime != null">
                AND date_format(id.issue_time,'%Y-%m-%d 00:00:00.0') BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="centerCode != null and centerCode != ''">
                AND id.accept_center = #{centerCode}
            </if>
        GROUP BY
        vt.license_plate_number,
        id.demand_number,
        id.appearance_weight,
        id.gross_weight,
        id.net_weight,
        id.appearance_time,
        id.powder_line,
        id.admission_time
        ) t
        GROUP BY t.license_plate_number
        ORDER BY t.license_plate_number DESC
    </select>

    <select id="getWeighingInformationDetail" resultType="Integer">
        SELECT SUM(issue_weight) from (
        SELECT
        vt.license_plate_number,
        IF
        ( id.type = 2, '袋装', '斗装' ) AS type,
        IF
        ( id.type = 2,( IF ( id.is_surplus = 1, id.issue_surplus_number, ss.weight )), id.bucket_weight ) AS specification,
        IF
        (
        id.type = 1,
        1,
        IF
        ( id.is_surplus = 1, 1, id.issue_number )) AS issue_number,
        IF
        (
        id.type = 2,
        IF
        (
        id.is_surplus = 1,
        id.issue_surplus_number,(
        id.issue_number * ss.weight
        )),
        id.bucket_weight
        ) AS issue_weight
        FROM
        t_wp_issue_order_detail id
        LEFT JOIN vehicle_transport vt ON vt.id = id.vehicle_transport_id
        LEFT JOIN materials.hv_bm_material bm ON bm.id = id.material_id
        LEFT JOIN t_wp_sack_specification ss ON ss.id = id.specification_id
        WHERE vt.license_plate_number = #{issueWeight.licensePlateNumber}
        <if test="startTime != null and endTime != null">
            AND date_format(id.issue_time,'%Y-%m-%d 00:00:00.0') BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="demandList != null and demandList.size > 0">
            AND id.demand_number not in
            <foreach collection="demandList" open="(" close=")" separator="," item="item">
                #{item}
        </foreach>
        </if>
        ) t
    </select>

    <select id="getAllocationDetail" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.AllocationDetailPageDTO">
        SELECT l.id AS center_id,l.code AS center_code,l.name AS center_name,sp.state AS post_state,sp.certificate_number AS post_number,sp.operating_time AS post_time,1 AS issue_number,sp.weight AS specification,sp.material_name,sp.material_code,sp.material_id
        FROM t_wp_sap_post sp
        LEFT JOIN (SELECT * FROM t_mp_inventory_location WHERE id IN  (
        SELECT MAX(id) FROM t_mp_inventory_location where deleted = FALSE GROUP BY code)) il ON il.code = sp.target_warehouse_code AND il.deleted = FALSE
        LEFT JOIN equipment.`hv_bm_location` l ON l.id = il.center_id
        LEFT JOIN equipment.`hv_bm_location` l2 ON l2.id = l.parent_id
        WHERE  FIND_IN_SET(material_name,'QF1,QF2,QF3,QF4') AND sp.state = 1
        <if test="beginDate != null and endDate != null">
            AND date_format(sp.certificate_date,'%Y-%m-%d')  BETWEEN #{beginDate} AND #{endDate}
        </if>
        <if test="centerId != null">
            AND l.id = #{centerId}
        </if>
        ORDER BY sp.id DESC
    </select>

    <select id="getIssueOrderDetailStatistics" resultType="com.hvisions.powder.qudou.dto.IssueOrderDetailStatistics">
        SELECT m.material_name,iod.type,if(iod.type = 1,1,if(bucket_weight is null and specification_id is null,1,issue_number)) AS issue_number,if(iod.type = 2,(if(iod.is_surplus = 1,iod.issue_surplus_number,ss.weight)),iod.bucket_weight) AS specification,iod.accept_state
        FROM t_wp_issue_order_detail iod
        LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id
        LEFT JOIN materials.`hv_bm_material` m ON m.id = iod.material_id
        LEFT JOIN equipment.`hv_bm_location` l ON l.code = iod.accept_center
        LEFT JOIN equipment.`hv_bm_location` l2 ON l2.id = l.parent_id
        WHERE (accept_state != 1 or accept_center is not null)
        <if test="query.useTime != null">
            AND date_format(iod.issue_time,'%Y-%m-%d') = date_format(#{query.useTime},'%Y-%m-%d')
        </if>
        <if test="query.baseId != null">
            AND l2.id = #{query.baseId}
        </if>
        <if test="query.baseName != null and query.baseName != ''">
            AND l2.name = #{query.baseName}
        </if>
        <if test="materialName != null and materialName != ''">
            AND m.material_name = #{materialName}
        </if>
        <if test="type != null and type != ''">
            AND iod.type = #{type}
        </if>
        <if test="acceptState != null and acceptState != ''">
            AND iod.accept_state = #{acceptState}
        </if>
    </select>

    <update id="postForLuoHan">
        UPDATE t_wp_issue_order_detail SET post_state = '1',post_year = #{postYear},post_number = #{postNumber},post_time = #{postTime}
        WHERE accept_center = '706'
        AND  accept_state = 1
        AND accept_center IS NOT NULL
        AND date_format(accept_time,'%Y-%m-%d')  BETWEEN #{beginDate} AND #{endDate}
        AND material_id = #{materialId}
    </update>

    <select id="getWriteList" resultType="java.util.Map">
        SELECT sp.certificate_number AS postNumber, sp.certificate_year AS postYear,sp.certificate_date AS postTime
        FROM t_wp_sap_post sp
                 LEFT JOIN (SELECT * FROM t_mp_inventory_location WHERE id IN  (
            SELECT MAX(id) FROM t_mp_inventory_location where deleted = FALSE GROUP BY code)) il ON il.code = sp.target_warehouse_code AND il.deleted = FALSE
                 LEFT JOIN equipment.`hv_bm_location` l ON l.id = il.center_id
                 LEFT JOIN equipment.`hv_bm_location` l2 ON l2.id = l.parent_id
        WHERE  FIND_IN_SET(material_name,'QF1,QF2,QF3,QF4')
        AND date_format(certificate_date,'%Y-%m-%d')  BETWEEN #{beginDate} AND #{endDate}
        AND sp.material_name = #{materialName}
        AND sp.state = '1'
        AND l.id = #{centerId}
        GROUP BY sp.certificate_number, sp.certificate_year,sp.certificate_date
    </select>

    <update id="writeForLuoHan">
        UPDATE t_wp_issue_order_detail SET post_year = null,post_number = null,post_state = '0',post_time = null
        WHERE post_year = #{postYear} AND post_number = #{postNumber}
    </update>

    <select id="listDetailByItemId" resultType="com.hvisions.powder.qudou.vo.IssueOrderDetailVO">
        SELECT id.id, id.accept_location_id, id.accept_center_id, id.accept_location, id.accept_center, id.acceptor, id.acceptor_id, id.accept_time, id.accept_line,
               if(iod.type = 1,iod.bucket_weight,
               if(iod.is_surplus = 1,iod.issue_surplus_number,ss.weight)) AS bucket_weight,
               if(iod.type =1 or iod.is_surplus = 1,1,(SELECT SUM(quantity) FROM t_wp_item_detail WHERE issue_item_id = #{itemId} AND issue_detail_id = iod.id)) AS issue_number,v.license_plate_number,iod.batch, iod.`issuer`, iod.issuer_id, iod.issue_time
        FROM t_wp_item_detail id
                 LEFT JOIN t_wp_issue_order_detail iod on id.issue_detail_id = iod.id
                 LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id
                 LEFT JOIN vehicle_transport v ON v.id = iod.vehicle_transport_id AND v.deleted = FALSE
        WHERE id.issue_item_id = #{itemId}
    </select>

    <select id="getPowderDistributionProductQuantityByTime"
            resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.PowderDistributionDetailDTO">
        SELECT SUM(if(od.type = '2',(if(is_surplus = '1',(issue_surplus_number),(issue_number * ss.weight))),(od.bucket_weight * issue_number))) AS weight,od.material_id ,bm.material_code,bm.material_name
        FROM brewage.t_wp_issue_order_detail od
        LEFT JOIN brewage.t_wp_sack_specification ss ON ss.id = od.specification_id
        LEFT JOIN materials.hv_bm_material bm ON bm.id = od.material_id
        WHERE od.deleted = 0 AND od.accept_state = 1
                AND DATE_FORMAT(accept_time, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m-%d')
                        AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        GROUP BY od.material_id,bm.material_code,bm.material_name
    </select>

    <select id="getPowderDistributionMonthProductQuantity"
            resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.PowderDistributionDetailDTO">
        SELECT SUM(if(od.type = '2',(if(is_surplus = '1',(issue_surplus_number),(issue_number * ss.weight))),(od.bucket_weight * issue_number))) AS weight,od.material_id ,bm.material_code,bm.material_name
        FROM brewage.t_wp_issue_order_detail od
        LEFT JOIN brewage.t_wp_sack_specification ss ON ss.id = od.specification_id
        LEFT JOIN materials.hv_bm_material bm ON bm.id = od.material_id
        WHERE od.deleted = 0 AND od.accept_state = 1
                AND DATE_FORMAT(accept_time, '%Y-%m-%d') BETWEEN
                        STR_TO_DATE(CONCAT(IF(#{month} =1,#{year} -1,#{year}), '-', IF(#{month} =1,12,#{month}-1), '-24'), '%Y-%m-%d')
                        AND STR_TO_DATE(CONCAT(#{year}, '-', #{month}, '-23'), '%Y-%m-%d')
        GROUP BY od.material_id,bm.material_code,bm.material_name
    </select>

    <select id="getBaseIssueWeight" resultType="com.hvisions.powder.dto.kanBan.issue.BaseIssueWeightDTO">
        SELECT sd.base_name,sd.base_id,m.id AS material_id,m.material_name,m.material_code,SUM(if(iod.type = 1,iod.bucket_weight,if(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number*ss.weight))) AS issue_weight
        FROM t_wp_issue_order_detail iod
        LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id
        LEFT JOIN t_wp_issue_order_item ioi ON ioi.id = iod.item_id AND ioi.deleted = FALSE
        LEFT JOIN t_wp_issue_order io ON io.id = ioi.order_id AND io.deleted = FALSE
        LEFT JOIN t_wp_single_demand_detail sdd ON sdd.id = io.demand_id AND sdd.deleted = FALSE
        LEFT JOIN t_wp_single_demand sd ON sd.id = sdd.demand_id AND sd.deleted = FALSE
        LEFT JOIN `materials`.hv_bm_material m ON m.id = iod.material_id
        WHERE iod.item_id IS NOT NULL
        <if test="startTime != null and endTime != null">
            AND date_format(iod.issue_time,'%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY sd.base_name,sd.base_id,m.id,m.material_name,m.material_code
    </select>

    <select id="getAcceptTotal" resultType="java.math.BigDecimal">
        SELECT SUM(d.bucket_weight)
        FROM t_wp_issue_order_detail d
        WHERE d.deleted = 0 AND d.accept_state = 1
        <if test="centerId != null">
            and d.center_id = #{centerId}
        </if>
        <if test="locationId != null">
            and d.location_id = #{locationId}
        </if>
        <if test="startTime != null and endTime != null">
            AND d.accept_time BETWEEN #{startTime} AND  #{endTime}
        </if>
    </select>

    <select id="getDemandByCP" resultType="string">
        SELECT MAX(demand_number)
        FROM t_wp_issue_order_detail iod
        LEFT JOIN vehicle_transport v ON v.id = iod.vehicle_transport_id AND v.deleted = FALSE
        WHERE iod.deleted = FALSE AND license_plate_number = #{licensePlateNumber}
        AND (iod.net_weight IS NULL || iod.net_weight = 0)
    </select>

    <select id="getIssueDetailSmash" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueDetailSmashDTO">
        SELECT iod.id,iod.type,IF(iod.type = 1,iod.bucket_weight,IFNULL(iod.issue_surplus_number,ss.weight)) AS specification,IFNULL(iod.issue_number,1) AS issue_quantity,IFNULL(iod.issue_surplus_number,0) AS issue_surplus_numbe, iod.batch,iod.powder_line,iod.issue_time,iod.issuer,IFNULL(if(type = 1,iod.bucket_weight,IF(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number * ss.weight)),0) AS issue_weight,iod.accept_time,iod.acceptor,iod.accept_center, IF(iod.accept_center IS NOT NULL,IFNULL(if(type = 1,iod.bucket_weight,IF(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number * ss.weight)),0),0) AS accept_weight
        FROM t_wp_issue_order_detail iod
        LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id AND ss.deleted = FALSE
        LEFT JOIN t_wp_issue_order_item oi ON oi.id = iod.item_id AND oi.deleted = FALSE
        WHERE  smash_batch = #{smashBatch}
        AND iod.deleted = FALSE
    </select>

    <select id="getIssueOrderDetailById"
            resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueDetailSmashDTO">
        SELECT iod.id,iod.type,IF(iod.type = 1,iod.bucket_weight,IFNULL(iod.issue_surplus_number,ss.weight)) AS specification, iod.accept_line, iod.smash_batch,
        IFNULL(iod.issue_number,1) AS issue_quantity,IFNULL(iod.issue_surplus_number,0) AS issue_surplus_numbe,
        iod.batch,iod.powder_line,iod.issue_time,iod.issuer,
        IFNULL(if(type = 1,iod.bucket_weight,IF(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number * ss.weight)),0) AS issue_weight,
        iod.accept_time,iod.acceptor,iod.accept_center, IF(iod.accept_center IS NOT NULL,
        IFNULL(if(type = 1,iod.bucket_weight,IF(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number * ss.weight)),0),0) AS accept_weight
        FROM t_wp_issue_order_detail iod
        LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id AND ss.deleted = FALSE
        LEFT JOIN t_wp_issue_order_item oi ON oi.id = iod.item_id AND oi.deleted = FALSE
        WHERE iod.id = #{id}
        AND iod.deleted = FALSE
    </select>

    <update id="IssueByVehicle">
        UPDATE t_wp_issue_order_detail SET update_time = NOW(),accept_state = '1'
        WHERE accept_state = '0'
        AND deleted = FALSE
        AND issue_time &lt; DATE_SUB(NOW(),INTERVAL 1 HOUR)
        AND type = '2'
        AND vehicle_transport_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getIssueWeightSummary" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueWeightSummary">
        SELECT
        MAX(CASE a.material_name WHEN 'QF1' THEN a.specification ELSE 0 END) qf1Weight,
        MAX(CASE a.material_name WHEN 'QF2' THEN a.specification ELSE 0 END) qf2Weight,
        MAX(CASE a.material_name WHEN 'QF3' THEN a.specification ELSE 0 END) qf3Weight,
        MAX(CASE a.material_name WHEN 'QF4' THEN a.specification ELSE 0 END) qf4Weight
        FROM (SELECT sum(sp.weight) AS specification,sp.material_name
        FROM t_wp_sap_post sp
        WHERE  FIND_IN_SET(material_name,'QF1,QF2,QF3,QF4') AND sp.state = 1
                 <choose>
                     <when test="startTime != null and endTime != null">
                         AND DATE_FORMAT(certificate_date,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{startTime},'%Y-%m-%d') AND DATE_FORMAT(#{endTime},'%Y-%m-%d')
                     </when>
                     <otherwise>
                         AND DATE_FORMAT(certificate_date,'%Y-%m-%d') BETWEEN CONCAT( DATE_FORMAT(DATE_SUB(now(),INTERVAL 1 month),'%Y-%m'),'-24') AND CONCAT(DATE_FORMAT(NOW(),'%Y-%m'),'-23')
                    </otherwise>
                 </choose>
        GROUP BY sp.material_name) a
    </select>

    <select id="getSalesWeightSummary" resultType="java.util.Map">
        SELECT
            MAX(CASE a.material_name WHEN 'QF1' THEN a.issue_weight ELSE 0 END) salesQf1Weight,
            MAX(CASE a.material_name WHEN 'QF2' THEN a.issue_weight ELSE 0 END) salesQf2Weight
        FROM (
                 SELECT som.material_name,SUM(dd.net_weight) AS issue_weight
                 FROM t_mp_demand_detail dd
                          LEFT JOIN t_wp_demand d ON d.id = dd.demand_id AND d.deleted = FALSE
                          LEFT JOIN t_wp_sales_order_material som ON som.id = d.sales_material_id
                 WHERE dd.deleted = FALSE
        <choose>
            <when test="startTime != null and endTime != null">
                AND DATE_FORMAT(admission_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{startTime},'%Y-%m-%d') AND DATE_FORMAT(#{endTime},'%Y-%m-%d')
            </when>
            <otherwise>
                AND DATE_FORMAT(admission_time,'%Y-%m-%d') BETWEEN CONCAT( DATE_FORMAT(DATE_SUB(now(),INTERVAL 1 month),'%Y-%m'),'-24') AND CONCAT(DATE_FORMAT(NOW(),'%Y-%m'),'-23')
            </otherwise>
        </choose>
                 GROUP BY som.material_name) a
    </select>

    <select id="getEveryDayWeight" resultType="com.hvisions.powder.dto.kanBan.issue.EveryDayWeightDTO">
        SELECT
            a.issue_time,
            MAX(CASE a.material_name WHEN 'QF1' THEN a.issue_weight ELSE 0 END) AS yj_weight,
            MAX(CASE a.material_name WHEN 'QF2' THEN a.issue_weight ELSE 0 END) AS fyj_weight,
            MAX(CASE a.material_name WHEN 'QF3' THEN a.issue_weight ELSE 0 END) qf3Weight,
            MAX(CASE a.material_name WHEN 'QF4' THEN a.issue_weight ELSE 0 END) qf4Weight
        FROM
            (SELECT DATE_FORMAT(iod.issue_time,'%Y-%m-%d') AS issue_time,m.material_name,SUM(FLOOR(IF(iod.type = 1,iod.bucket_weight,IF(iod.is_surplus = 1,iod.issue_surplus_number,iod.issue_number*ss.weight)))) AS issue_weight
             FROM t_wp_issue_order_detail iod
                      LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id AND ss.deleted = FALSE
                      LEFT JOIN materials.`hv_bm_material` m ON m.id = iod.material_id
             WHERE iod.issue_time >=  DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 7 day),'%Y-%m-%d')
               AND iod.deleted = FALSE
               AND (m.material_name = 'QF1' OR m.material_name = 'QF2' OR m.material_name = 'QF3' OR m.material_name = 'QF4')
             GROUP BY DATE_FORMAT(iod.issue_time,'%Y-%m-%d'),m.material_name)a
        GROUP BY a.issue_time
    </select>

    <select id="getDateIssueWeight" resultType="com.hvisions.powder.dto.kanBan.issue.DateIssueWeightDTO">
        SELECT DATE_FORMAT(b.issue_time,'%Y-%m-%d') AS issue_time,
               MAX(CASE b.material_name WHEN 'QF1' THEN b.issue_weight ELSE 0 END)	AS qf1_issue_weight,
               MAX(CASE b.material_name WHEN 'QF2' THEN b.issue_weight ELSE 0 END)	AS qf2_issue_weight,
               MAX(CASE b.material_name WHEN 'QF3' THEN b.issue_weight ELSE 0 END)	AS qf3_issue_weight,
               MAX(CASE b.material_name WHEN 'QF4' THEN b.issue_weight ELSE 0 END)	AS qf4_issue_weight
        FROM
            (SELECT DATE_FORMAT(a.issue_time,'%Y-%m-%d') AS issue_time,a.material_id,a.material_code,a.material_name,SUM(a.issue_weight) AS issue_weight
             FROM
                 (SELECT DATE_FORMAT(iod.issue_time,'%Y-%m-%d') AS issue_time,iod.material_id,m.material_name,m.material_code,IFNULL(IF(iod.type = 1,iod.bucket_weight,IFNULL(iod.issue_surplus_number,iod.issue_number*ss.weight)),0) AS issue_weight
                  FROM t_wp_issue_order_detail iod
                           LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id AND ss.deleted = FALSE
                           LEFT JOIN materials.`hv_bm_material` m ON m.id = iod.material_id
                  WHERE iod.deleted = FALSE
                  <if test="materialName != null and materialName != ''">
                      AND m.material_name = #{materialName}
                  </if>
                  <if test="startTime != null and endTime != null">
                      AND DATE_FORMAT(iod.issue_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{startTime},'%Y-%m-%d') AND DATE_FORMAT(#{endTime},'%Y-%m-%d')
                  </if>
                  )a
             GROUP BY DATE_FORMAT(a.issue_time,'%Y-%m-%d'),a.material_id,a.material_code,a.material_name)b
        GROUP BY DATE_FORMAT(b.issue_time,'%Y-%m-%d')
    </select>

    <select id="getAcceptDetail" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.AcceptDetailDTO">
        SELECT l.name AS accept_center,io.location_name AS accept_location,id.create_time AS accept_time,id.quantity AS accept_number,u.user_name AS acceptor
        FROM t_wp_item_detail id
        LEFT JOIN t_wp_issue_order_item ioi ON ioi.id = issue_item_id
        LEFT JOIN t_wp_issue_order io ON io.id = ioi.order_id
        LEFT JOIN authority.`sys_user`u ON u.id = id.creator_id
        LEFT JOIN equipment.`hv_bm_location` l ON l.id = io.center_id
        WHERE issue_detail_id = #{id}
    </select>
    <select id="getAcceptDetailDou" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.AcceptDetailDTO">
        SELECT l.`name` AS accept_center,l2.name AS accept_location,iod.acceptor,iod.accept_time,1 AS acceptNumber
        FROM t_wp_issue_order_detail iod
        LEFT JOIN authority.`sys_user`u ON u.id = iod.creator_id
        LEFT JOIN equipment.`hv_bm_location` l ON l.id = iod.center_id
        LEFT JOIN equipment.`hv_bm_location` l2 ON l2.id = iod.location_id
        WHERE iod.id = #{id}
        AND iod.accept_state = '1' AND accept_center IS NOT NULL
    </select>
    <select id="getDetailByItemsOfDai" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueDetailByItem">
        SELECT m.material_name,iod.batch
        FROM (SELECT issue_detail_id FROM t_wp_item_detail
              WHERE issue_item_id = #{itemId}
              GROUP BY issue_detail_id) id
        LEFT JOIN t_wp_issue_order_detail iod ON iod.id = id.issue_detail_id
        LEFT JOIN materials.`hv_bm_material` m ON m.id = iod.material_id
    </select>

    <select id="getDetailByItemsOfDou" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueDetailByItem">
        SELECT m.material_name,iod.batch,iod.barcode
        FROM t_wp_issue_order_item iot
        LEFT JOIN t_wp_issue_order_detail iod ON iod.item_id = iot.id
        LEFT JOIN materials.`hv_bm_material` m ON m.id = iod.material_id
        WHERE iot.id = #{itemId}
    </select>
    <select id="getWeightContrast"
            resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueWeight">
        SELECT vt.license_plate_number,id.demand_number,id.appearance_weight,id.gross_weight,net_weight, id.appearance_time, id.powder_line, id.admission_time
        FROM  t_wp_issue_order_detail id
        LEFT JOIN vehicle_transport vt ON vt.id = id.vehicle_transport_id AND vt.deleted = FALSE
        WHERE id.deleted = FALSE AND id.vehicle_transport_id IS NOT NULL AND id.demand_number IS NOT NULL
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND vt.license_plate_number = #{licensePlateNumber}
        </if>
        <if test="startTime != null and endTime != null">
            AND date_format(id.issue_time,'%Y-%m-%d 00:00:00.0') BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="centerCode != null and centerCode != ''">
            AND id.accept_center = #{centerCode}
        </if>
        GROUP BY vt.license_plate_number,id.demand_number,id.appearance_weight,id.gross_weight,id.net_weight, id.appearance_time, id.powder_line, id.admission_time
        ORDER BY id.demand_number DESC
    </select>
    <select id="getWeighingInformationDetailList" resultType="com.hvisions.powder.dto.qudou.demand.issue.order.detail.IssueWeightDetail">
        SELECT vt.license_plate_number,id.batch,if(id.type = 2,'袋装','斗装') AS type,id.barcode,id.material_id,bm.material_code,bm.material_name,
        if(id.type = 2,(if(id.is_surplus = 1,id.issue_surplus_number,ss.weight)),id.bucket_weight) AS specification,if(id.type = 1,1,if(id.is_surplus = 1,1,id.issue_number)) AS issue_number,if(id.type = 2,if(id.is_surplus = 1,id.issue_surplus_number,(id.issue_number * ss.weight)),id.bucket_weight) AS issue_weight,id.`issuer`,issue_time,id.demand_number,if(id.experiment_power = 1,'实验曲',if(id.science_power = 1,'科研曲','生产用曲')) AS powder_type
        FROM t_wp_issue_order_detail id
        LEFT JOIN vehicle_transport vt ON vt.id = id.vehicle_transport_id
        LEFT JOIN materials.hv_bm_material bm ON bm.id = id.material_id
        LEFT JOIN t_wp_sack_specification ss ON ss.id = id.specification_id
        WHERE vt.license_plate_number = #{issueWeight.licensePlateNumber}
        <if test="startTime != null and endTime != null">
            AND date_format(id.issue_time,'%Y-%m-%d 00:00:00.0') BETWEEN #{startTime} AND #{endTime}
        </if>
        <choose>
            <when test="issueWeight.demandNumber != null and issueWeight.demandNumber != ''">
                AND id.demand_number = #{issueWeight.demandNumber}
            </when>
            <when test="issueWeight.demandNumber == null or issueWeight.demandNumber == ''">
                AND id.demand_number IS NULL
            </when>
        </choose>
        <choose>
            <when test="issueWeight.netWeight != null">
                AND id.net_weight = #{issueWeight.netWeight}
            </when>
            <when test="issueWeight.netWeight == null">
                AND id.net_weight IS NULL
            </when>
        </choose>
    </select>
    <select id="getWeightContrastExportData"
            resultType="com.hvisions.powder.dto.kanBan.issue.WeightContrastExportDTO">

        SELECT vt.license_plate_number,id.demand_number,id.appearance_weight,id.gross_weight,net_weight, id.appearance_time, id.powder_line, id.admission_time,
               id.batch,if(id.type = 2,'袋装','斗装') AS type,id.barcode,bm.material_code,bm.material_name,
               if(id.type = 2,(if(id.is_surplus = 1,id.issue_surplus_number,ss.weight)),id.bucket_weight) AS specification,if(id.type = 1,1,if(id.is_surplus = 1,1,id.			      issue_number)) AS issue_number,if(id.type = 2,if(id.is_surplus = 1,id.issue_surplus_number,(id.issue_number * ss.weight)),id.bucket_weight) AS issue_weight,id.`issuer`,issue_time,if(id.experiment_power = 1,'实验曲',if(id.science_power = 1,'科研曲','生产用曲')) AS powder_type
        FROM  t_wp_issue_order_detail id
          LEFT JOIN vehicle_transport vt ON vt.id = id.vehicle_transport_id AND vt.deleted = FALSE
          LEFT JOIN materials.hv_bm_material bm ON bm.id = id.material_id
          LEFT JOIN t_wp_sack_specification ss ON ss.id = id.specification_id
        WHERE id.deleted = FALSE AND id.vehicle_transport_id IS NOT NULL AND id.demand_number IS NOT NULL
        <if test="licensePlateNumber != null and licensePlateNumber != ''">
            AND vt.license_plate_number = #{licensePlateNumber}
        </if>
        <if test="startTime != null and endTime != null">
            AND date_format(id.issue_time,'%Y-%m-%d 00:00:00.0') BETWEEN #{startTime} AND #{endTime}
        </if>
        ORDER BY id.demand_number DESC
    </select>
    <select id="listDetailByItemIdAndLocation" resultType="com.hvisions.powder.qudou.vo.IssueOrderDetailVO">
         SELECT if(iod.type = 1,iod.bucket_weight,
               if(iod.is_surplus = 1,iod.issue_surplus_number,ss.weight)) AS bucket_weight,
               if(iod.type =1 or iod.is_surplus = 1,1,(SELECT SUM(quantity) FROM t_wp_item_detail WHERE issue_item_id =  #{itemId} AND issue_detail_id = iod.id)) AS issue_number,
               v.license_plate_number, iod.barcode, iod.batch, iod.`issuer`, iod.issue_time, iod.accept_state,
                id.accept_center, id.accept_location, id.accept_time, id.quantity AS accept_number, id.acceptor
         FROM t_wp_issue_order_detail iod
         LEFT JOIN t_wp_sack_specification ss ON ss.id = iod.specification_id
         LEFT JOIN vehicle_transport v ON v.id = iod.vehicle_transport_id AND v.deleted = FALSE
         LEFT JOIN t_wp_item_detail id on id.issue_detail_id = iod.id
         WHERE (iod.id IN (SELECT distinct issue_detail_id FROM t_wp_item_detail WHERE issue_item_id = #{itemId}) OR item_id = #{itemId})
         AND iod.deleted = FALSE
         AND id.accept_location_id = #{locationId}
    </select>
</mapper>
