package com.hvisions.powder.qudou.service.impl;

import cn.hutool.core.stream.CollectorUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.brewage.feign.brewage.TaskTurnoverDemandClient;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.ExcelFillCellMergeStrategy;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.consts.MaterialConstant;
import com.hvisions.powder.dto.contactForLuoHa.QuFenOperateCompleteForLuoHan;
import com.hvisions.powder.dto.kanBan.demand.DemandDetailDTO;
import com.hvisions.powder.dto.product.order.sync.TurnOverDemandAddDTO;
import com.hvisions.powder.dto.qudou.demand.DemandDetailReceiveDTO;
import com.hvisions.powder.dto.qudou.demand.parameter.ParameterCenterDetailDTO;
import com.hvisions.powder.excel.ExcelCellByMultiHandler;
import com.hvisions.powder.excel.SingleDemandExcel;
import com.hvisions.powder.qudou.consts.DemandState;
import com.hvisions.powder.qudou.dao.*;
import com.hvisions.powder.qudou.dto.SingleDemandQueryReq;
import com.hvisions.powder.qudou.entity.IssueOrder;
import com.hvisions.powder.qudou.entity.IssueOrderItem;
import com.hvisions.powder.qudou.entity.SingleDemand;
import com.hvisions.powder.qudou.entity.SingleDemandDetail;
import com.hvisions.powder.qudou.service.IIssueOrderService;
import com.hvisions.powder.qudou.service.IParameterService;
import com.hvisions.powder.qudou.service.ISingleDemandDetailService;
import com.hvisions.powder.qudou.service.ISingleDemandService;
import com.hvisions.powder.qudou.vo.SingleDemandVO;
import com.hvisions.powder.sack.entity.SackSpecification;
import com.hvisions.powder.sack.mapper.SackSpecificationMapper;
import com.hvisions.powder.sack.service.SackSpecificationService;
import com.hvisions.powder.sap.RequestTms;
import com.hvisions.powder.sap.constant.SapConst;
import com.hvisions.powder.sap.dto.tms.TmsBaseDataDto;
import com.hvisions.powder.sap.dto.tms.TmsBaseDataItemDto;
import com.hvisions.powder.sap.dto.tms.TmsBaseResponseDto;
import com.hvisions.powder.sap.service.ContactForLuoHanService;
import com.hvisions.powder.utils.BaseWrapper;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.StringUtil;
import com.hvisions.product.OrderPlanClient;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 曲粉日需求单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Slf4j
@Service
public class SingleDemandServiceImpl extends ServiceImpl<SingleDemandMapper, SingleDemand> implements ISingleDemandService {
    @Autowired
    BaseWrapper baseWrapper;
    @Autowired
    ISingleDemandDetailService singleDemandDetailService;
    @Autowired
    IIssueOrderService issueOrderService;

    @Resource
    SingleDemandMapper singleDemandMapper;

    @Resource
    SackSpecificationMapper sackSpecificationMapper;

    @Resource
    SingleDemandDetailMapper singleDemandDetailMapper;

    @Resource
    private RequestTms requestTms;

    @Resource
    private IParameterService iParameterService;

    @Resource
    private ContactForLuoHanService contactForLuoHanService;

    @Resource
    private OrderPlanClient orderPlanClient;

    @Resource
    private SackSpecificationService sackSpecificationService;

    @Resource
    private IssueOrderMapper issueOrderMapper;

    @Resource
    private IssueOrderItemMapper issueOrderItemMapper;

    @Resource
    private IssueOrderDetailMapper issueOrderDetailMapper;

    @Resource
    private TaskTurnoverDemandClient taskTurnoverDemandClient;

    @Override
    public Page<SingleDemandVO> queryPagedSingleDemand(SingleDemandQueryReq req, PageInfo pageInfo) {
        Page<SingleDemand> page = new Page<>(pageInfo.getPage(), pageInfo.getPageSize());
        List<SingleDemand> singleDemands = singleDemandMapper.pageDemand(page, req);
        page.setRecords(singleDemands);
        Page<SingleDemandVO> singleDemandVOPage = baseWrapper.convertToPage(page, SingleDemandVO.class);
        singleDemandVOPage.getRecords().forEach(it -> {
            it.setSingleDemandDetailVOList(singleDemandDetailService.getDetailByDemandId(it.getId(), req.getType(), req.getPowderType()));
        });
        return singleDemandVOPage;
    }

    /***
     * @Description 接收曲粉需求，生成曲粉发放单，并将需求推送给tms系统
     *
     * <AUTHOR>
     * @Date 2022-11-28 14:30
     * @param demandDetailIds
     * @return void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeReceive(List<Integer> demandDetailIds) {

        //创建请购发放单
        LambdaQueryWrapper<SingleDemandDetail> queryWrapper = new LambdaQueryWrapper<SingleDemandDetail>()
                .in(SingleDemandDetail::getId, demandDetailIds);
        List<SingleDemandDetail> singleDemandDetails = singleDemandDetailService.list(queryWrapper);

        singleDemandDetails.stream()
                .filter(i -> i.getState().equals(DemandState.WAIT_RECEIVE))
                .forEach(item -> {
                    SingleDemand singleDemand = this.getById(item.getDemandId());
                    // 更新曲粉需求详情状态
                    item.setState(DemandState.ALREADY_RECEIVE);
                    item.setUpdateTime(new Date());
                    singleDemandDetailService.updateById(item);
                    //接收罗汉需求通知罗汉8中心
                    if ("708".equals(singleDemand.getCenterName())) {
                        QuFenOperateCompleteForLuoHan quFenOperateComplete = new QuFenOperateCompleteForLuoHan();
                        quFenOperateComplete.setDemandOrder(item.getDemandOrder());
                        quFenOperateComplete.setRowNo("2");
                        try {
                            contactForLuoHanService.operateComplete(quFenOperateComplete);

                        } catch (Exception e) {
                            log.info("通知罗汉8中心曲粉需求接收状态失败，参数：{}", quFenOperateComplete);
                        }
                    }
                    // 生成曲粉发放单
                    DemandDetailReceiveDTO demandDetailReceiveDTO = baseWrapper.convert(item, DemandDetailReceiveDTO.class);
                    demandDetailReceiveDTO.setCenterId(singleDemand.getCenterId());
                    demandDetailReceiveDTO.setCenterName(singleDemand.getCenterName());
                    demandDetailReceiveDTO.setUseTime(singleDemand.getUseTime());
                    demandDetailReceiveDTO.setBaseId(singleDemand.getBaseId());
                    demandDetailReceiveDTO.setBaseName(singleDemand.getBaseName());
                    demandDetailReceiveDTO.setType(item.getType());
                    demandDetailReceiveDTO.setSpecificationId(item.getSpecificationId());
                    demandDetailReceiveDTO.setBucketWeight(item.getBucketWeight());
                    demandDetailReceiveDTO.setExperimentPower(item.getExperimentPower());
                    demandDetailReceiveDTO.setSciencePower(item.getSciencePower());
                    demandDetailReceiveDTO.setUrgent(item.getUrgent());
                    issueOrderService.handleDemand(demandDetailReceiveDTO);
                    //数据发到制曲生产过账
                    try {
                        sendToProduction(item);
                    } catch (Exception e) {
                        log.error("调用制曲生产过账失败:" + e.getMessage());
                    }
                });
        sendToTms(demandDetailIds);
    }

    public void sendToTms(List<Integer> demandDetailIds) {
        List<TmsBaseDataDto> tmsBaseDataDtoList = new ArrayList<>();
        boolean isPush = false;
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        // 判断是否需要推送给tms系统（7：00 - 23：59）
        String strTime1 = "7:00";
        String strTime2 = "23:59";
        String now = sdf.format(new Date());
        try {
            Date nowTime = sdf.parse(now);
            Date startTime = sdf.parse(strTime1);
            Date endTime = sdf.parse(strTime2);
            if (DateUtil.isEffectiveDate(nowTime, startTime, endTime)) {
                isPush = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 接收后同步给TMS
        for (Integer detailId : demandDetailIds) {
            SingleDemandDetail singleDemandDetail = singleDemandDetailMapper.selectById(detailId);

            if ("1".equals(singleDemandDetail.getType())) { // 斗装
                SingleDemand singleDemand = singleDemandMapper.selectById(singleDemandDetail.getDemandId());
                TmsBaseDataDto tmsBaseDataDto = new TmsBaseDataDto();

                String bzbh = this.convertBzbh(singleDemand.getCenterName(), singleDemandDetail.getLocationName(), singleDemandDetail.getLineName());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDateTime = singleDemand.getUseTime().format(formatter);
                tmsBaseDataDto.setBzbh(bzbh);
                tmsBaseDataDto.setBglx("U");
                tmsBaseDataDto.setJhrq(formattedDateTime);
                tmsBaseDataDto.setType(singleDemandDetail.getType());

                // 获取跨下空斗数量
                List<ParameterCenterDetailDTO> parameterCenterDetailList = iParameterService.getParameterCenterDetailList(bzbh);
                if (StringUtil.isNotEmpty(parameterCenterDetailList) && parameterCenterDetailList.size() > 0) {
                    tmsBaseDataDto.setKds(parameterCenterDetailList.get(0).getEmptyNum().toString());
                } else {
                    tmsBaseDataDto.setKds("0");
                }
                if ("QF1".equals(singleDemandDetail.getMaterialName())) {
                    tmsBaseDataDto.setQf1(singleDemandDetail.getDemandNumber().stripTrailingZeros().toPlainString());
                } else if ("QF2".equals(singleDemandDetail.getMaterialName())) {
                    tmsBaseDataDto.setQf2(singleDemandDetail.getDemandNumber().stripTrailingZeros().toPlainString());
                } else if ("QF3".equals(singleDemandDetail.getMaterialName())) {
                    tmsBaseDataDto.setQf3(singleDemandDetail.getDemandNumber().stripTrailingZeros().toPlainString());
                }
                tmsBaseDataDtoList.add(tmsBaseDataDto);
            } else if ("2".equals(singleDemandDetail.getType())) {
                // 袋装
                SingleDemand singleDemand = singleDemandMapper.selectById(singleDemandDetail.getDemandId());
                TmsBaseDataDto tmsBaseDataDto = new TmsBaseDataDto();
                String bzbh = this.convertBzbh(singleDemand.getCenterName(), singleDemandDetail.getLocationName(), singleDemandDetail.getLineName());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDateTime = singleDemand.getUseTime().format(formatter);
                tmsBaseDataDto.setBzbh(bzbh);
                tmsBaseDataDto.setBglx("U");
                tmsBaseDataDto.setType(singleDemandDetail.getType());
                tmsBaseDataDto.setJhrq(formattedDateTime);
                TmsBaseDataItemDto itemDto = new TmsBaseDataItemDto();

                SackSpecification sackSpecification = sackSpecificationMapper.selectById(singleDemandDetail.getSpecificationId());
                if ("QF1".equals(singleDemandDetail.getMaterialName())) {
                    itemDto.setQf1((StringUtil.isNotEmpty(singleDemandDetail.getDemandNumber()) ? singleDemandDetail.getDemandNumber() : new BigDecimal("0")).toPlainString());
                    itemDto.setQf1gg(StringUtil.isEmpty(singleDemandDetail.getSurplusNumber()) || singleDemandDetail.getSurplusNumber().compareTo(BigDecimal.ZERO) == 0 ?
                            sackSpecification.getWeight().toString() :
                            singleDemandDetail.getSurplusNumber().stripTrailingZeros().toPlainString());
                } else if ("QF2".equals(singleDemandDetail.getMaterialName())) {
                    itemDto.setQf2((StringUtil.isNotEmpty(singleDemandDetail.getDemandNumber()) ? singleDemandDetail.getDemandNumber() : new BigDecimal("0")).toPlainString());
                    itemDto.setQf2gg(StringUtil.isEmpty(singleDemandDetail.getSurplusNumber()) || singleDemandDetail.getSurplusNumber().compareTo(BigDecimal.ZERO) == 0 ?
                            sackSpecification.getWeight().toString() :
                            singleDemandDetail.getSurplusNumber().stripTrailingZeros().toPlainString());
                } else if ("QF3".equals(singleDemandDetail.getMaterialName())) {
                    itemDto.setQf3((StringUtil.isNotEmpty(singleDemandDetail.getDemandNumber()) ? singleDemandDetail.getDemandNumber() : new BigDecimal("0")).toPlainString());
                    itemDto.setQf3gg(StringUtil.isEmpty(singleDemandDetail.getSurplusNumber()) || singleDemandDetail.getSurplusNumber().compareTo(BigDecimal.ZERO) == 0 ?
                            sackSpecification.getWeight().toString() :
                            singleDemandDetail.getSurplusNumber().stripTrailingZeros().toPlainString());
                }
                tmsBaseDataDto.setParmData(itemDto);
                tmsBaseDataDtoList.add(tmsBaseDataDto);
            }

        }
        // todo 往tms推送新增修改得需求
        if (isPush && tmsBaseDataDtoList.size() > 0) {
            for (TmsBaseDataDto tmsBaseDataDto : tmsBaseDataDtoList) {
                log.info("斗装日需求下发给tms==={}", JSONObject.toJSON(tmsBaseDataDto));
                if ("1".equals(tmsBaseDataDto.getType())) {
                    TmsBaseResponseDto tmsBaseResponseDto = requestTms.dockingTms(tmsBaseDataDto, SapConst.TMS_URI1);
                } else {
                    TmsBaseResponseDto tmsBaseResponseDto = requestTms.dockingTms(tmsBaseDataDto, SapConst.TMS_URI2);
                }
            }
        }
    }

    /**
     * 调用制曲生产进行过账
     * @param detail
     */
    private void sendToProduction(SingleDemandDetail detail) {
        if (MaterialConstant.QF3.equals(detail.getMaterialCode()) || MaterialConstant.QF4.equals(detail.getMaterialCode())) {
            TurnOverDemandAddDTO addDTO = new TurnOverDemandAddDTO();
            addDTO.setDemandDetailId(detail.getId());
            addDTO.setDemandWeight(computeDemandWeight(detail));
            addDTO.setMaterialId(detail.getMaterialId());
            addDTO.setMaterialCode(detail.getMaterialCode());
            addDTO.setMaterialName(detail.getMaterialName());
            log.info("翻沙曲过账调用");
            ResultVO<Boolean> turnOverDemand = orderPlanClient.createTurnOverDemand(addDTO);
            if (turnOverDemand == null || turnOverDemand.getCode() != 200) {
                log.info(JSONObject.toJSONString(turnOverDemand));
                log.error("翻沙曲调用制曲生产进行过账失败");
            }
        }
    }

    private BigDecimal computeDemandWeight(SingleDemandDetail detail) {
        if ("1".equals(detail.getType())) {
            //斗装
            int demandNumber = detail.getDemandNumber().intValue();
            return detail.getBucketWeight().multiply(new BigDecimal(demandNumber));
        } else {
            //袋装
            if (detail.getSurplusNumber() != null) {
                //散装
                return detail.getSurplusNumber();
            } else {
                SackSpecification sackSpecification = sackSpecificationService.getById(detail.getSpecificationId());
                return new BigDecimal(sackSpecification.getWeight()).multiply(detail.getDemandNumber());
            }
        }
    }

    /**
     * @Description 根据中心、车间、产线或者下发给tms的班组
     *
     * <AUTHOR>
     * @Date 2024-7-1 15:25
     * @param centerName
     * @param locationName
     * @param lineName
     * @return java.lang.String
     **/
    @Override
    public String convertBzbh(String centerName, String locationName, String lineName) {
        if (StringUtil.isNotEmpty(centerName) && StringUtil.isNotEmpty(lineName)) {
            return centerName + lineName;
        } else {
            if ("708".equals(centerName)) {
                return "70801";
            }
            if (StringUtil.isEmpty(locationName)) {
                return "";
            }
            switch (locationName) {
                case "7001":
                    return "70101";
                case "7002":
                    return "70102";
                case "7003":
                    return "70103";
                case "7004":
                    return "70104";
                case "7005":
                    return "70105";
                case "7006":
                    return "70106";
                case "7007":
                    return "70107";
                case "7008":
                    return "70108";
                case "7009":
                    return "70109";
                case "7010":
                    return "70110";
                case "7012":
                    return "70201";
                case "7013":
                    return "70202";
                case "7015":
                    return "70203";
                case "7016":
                    return "70204";
                case "7018":
                    return "70301";
                case "7019":
                    return "70302";
                case "7024":
                    return "70303";
                case "7025":
                    return "70304";
                case "7020":
                    return "70401";
                case "7021":
                    return "70402";
                case "7022":
                    return "70403";
                case "7023":
                    return "70404";
                case "7011":
                    return "70501";
                case "7014":
                    return "70502";
                case "7017":
                    return "70503";
                case "7026":
                    return "70601";
                case "70701":
                    return "70701";
                case "70702":
                    return "70702";

                default:
                    return "";
            }
        }
    }

    /**
      * @description: 曲粉日需求单导出excel
      * @author: wy liu
      * @date: 2024/7/15
      * @param req
     * @param response
      * @return void
    */
    @Override
    public ResultVO<ExcelExportDto> getSingleDemandExcel(SingleDemandQueryReq req, HttpServletResponse response) throws IOException {
        List<SingleDemandExcel> singleDemandExcels = singleDemandDetailMapper.getSingleDemandExcel(req);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        //需要合并的列
        int[] mergeColumeIndex = {0,1,2,3};
        // 从那一列开始合并
        int mergeRowIndex = 0;
        EasyExcel.write(outputStream, SingleDemandExcel.class)
                .sheet("曲粉日需求导出")
                .registerWriteHandler(new ExcelCellByMultiHandler(singleDemandExcels.size(),0,3))
                .doWrite(singleDemandExcels);
        byte[] bytes = outputStream.toByteArray();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachement", URLEncoder.encode("曲粉日需求导出.xlsx", "utf-8"));
        ResponseEntity<byte[]> responseEntity = new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName("曲粉日需求导出.xlsx");
        return ResultVO.success(excelExportDto);

    }

    /**
     * 驳回曲粉日需求
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Integer overrule(Integer id) {
        //判断是不是翻窖任务来的需求，如果是的话，需要驳回翻窖任务
        SingleDemandDetail query = singleDemandDetailMapper.selectById(id);
        SingleDemand singleDemand = singleDemandMapper.selectById(query.getDemandId());
        if (StringUtils.isNotBlank(query.getLineName()) && singleDemand != null && StringUtils.isNotBlank(singleDemand.getTaskNo())) {
            log.info("开始驳回翻窖需求,任务: {}, 跨号: {}", singleDemand.getTaskNo(), query.getLineName());
            ResultVO<String> resultVO = taskTurnoverDemandClient.rejectedTask(singleDemand.getTaskNo(), query.getLineName(), null, null);
            if (200 != resultVO.getCode()) {
                throw new BaseKnownException("驳回翻窖需求失败,任务: {}, 跨号: {}", singleDemand.getTaskNo(), query.getLineName());
            }
            //如果是翻沙曲的话,驳回应该驳回这个物料的所有需求
            List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>()
                    .eq(SingleDemandDetail::getDemandOrder, query.getDemandOrder()).eq(SingleDemandDetail::getDeleted, false));
            List<Integer> ids = singleDemandDetails.stream().map(SingleDemandDetail::getId).filter(iId -> !iId.equals(id)).collect(Collectors.toList());
            for (Integer i : ids) {
                overruleExtracted(i);
            }
        } else {
            overruleExtracted(id);
        }
        return 1;
    }

    private void overruleExtracted(Integer id) {
        SingleDemandDetail singleDemandDetail = singleDemandDetailMapper.selectById(id);
        singleDemandDetail.setState("1");
        singleDemandDetailMapper.updateById(singleDemandDetail);
        List<IssueOrder> issueOrders = issueOrderMapper.selectList(new LambdaUpdateWrapper<IssueOrder>().eq(IssueOrder::getDemandId, id));
        List<Integer> ids = issueOrders.stream().map(IssueOrder::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(ids)){
            List<IssueOrderItem> issueOrderItems = issueOrderItemMapper.selectList(new LambdaUpdateWrapper<IssueOrderItem>().in(IssueOrderItem::getOrderId, ids));
            issueOrderMapper.deleteAll(ids);
            if (!CollectionUtils.isEmpty(issueOrderItems)) {
                List<Integer> itemIds = issueOrderItems.stream().map(IssueOrderItem::getId).collect(Collectors.toList());
                issueOrderItemMapper.deleteAll(itemIds);
                issueOrderDetailMapper.deleteByItems(itemIds);
            }
        }
    }

    /**
     * 删除翻窖需求明细
     * @param taskNo
     * @param lineName
     * @return
     */
    @Override
    public Integer delTurnoverDemandDetail(String taskNo, String lineName) {

        return null;
    }
}
