/**
 * Copyright (c) 2018-2028, <PERSON>n <PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.powder.qudou.dto;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.powder.dto.product.QueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname IssueOrderDetailQueryReq
 * @description 日发放单详情数据传输查询参数实体类
 * @date 2022-04-13
 */
@Data
@ApiModel("IssueOrderDetail数据传输查询参数实体类")
public class IssueOrderDetailQueryReq extends PageInfo implements Serializable, QueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("曲斗条码")
    private String barcode;

    @ApiModelProperty("中心id")
    private String centerId;

    @ApiModelProperty("车间id")
    private String locationId;

    @ApiModelProperty("接收状态")
    private Integer acceptState;

    @ApiModelProperty("绑定状态")
    private Integer bindState;

    @ApiModelProperty("提报时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitStart;

    @ApiModelProperty("提报时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate submitEnd;

    @ApiModelProperty("提报时间开始")
    private LocalDateTime start;

    @ApiModelProperty("提报时间结束")
    private LocalDateTime end;

    @ApiModelProperty("曲粉ID")
    private Integer materialId;

    @ApiModelProperty("接收时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate acceptStart;

    @ApiModelProperty("接收时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate acceptEnd;

    @ApiModelProperty("车辆配置id")
    private Integer vehicleTransportId;

    @ApiModelProperty("包装方式 1-斗装 2-袋装")
    private String type;

    @ApiModelProperty("曲粉线")
    private String powderLine;

    @ApiModelProperty("是否过滤粉碎批次为空")
    private Boolean hasBatch;

    @ApiModelProperty("粉碎批次查询")
    private String smashBatch;

    @ApiModelProperty("批次查询")
    private String batch;
}
