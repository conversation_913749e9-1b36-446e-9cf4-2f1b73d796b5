package com.hvisions.powder.qudou.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialQueryDTO;
import com.hvisions.powder.advice.UserAuditorAware;
import com.hvisions.powder.consts.MaterialConstant;
import com.hvisions.powder.dto.product.order.sync.TurnOverDemandAddDTO;
import com.hvisions.powder.dto.product.order.sync.TurnOverDemandDelDTO;
import com.hvisions.powder.dto.qudou.demand.*;
import com.hvisions.powder.dto.qudou.demand.parameter.PackTaskDetailDTO;
import com.hvisions.powder.qudou.consts.DemandState;
import com.hvisions.powder.qudou.dao.*;
import com.hvisions.powder.qudou.dto.ReceiveBlQuFenDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordQueryDTO;
import com.hvisions.powder.qudou.entity.*;
import com.hvisions.powder.qudou.service.IParameterService;
import com.hvisions.powder.qudou.service.ISingleDemandService;
import com.hvisions.powder.qudou.service.QudouService;
import com.hvisions.powder.sack.entity.SackSpecification;
import com.hvisions.powder.sack.mapper.SackSpecificationMapper;
import com.hvisions.powder.sack.service.SackSpecificationService;
import com.hvisions.powder.sap.RequestTms;
import com.hvisions.powder.utils.CopyUtil;
import com.hvisions.powder.utils.DateUtil;
import com.hvisions.powder.utils.StringUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.utils.note.SendSms;
import com.hvisions.product.OrderPlanClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

//import com.hvisions.brewage.utils.DistinctKeyUtil;

/**
 * <AUTHOR>
 * @description:供应商
 * @date 2022/4/6 10:18
 */
@Slf4j
@Service
public class QudouServiceImpl implements QudouService {

    @Resource
    private SackSpecificationMapper sackSpecificationMapper;

    @Resource
    private SingleDemandMapper singleDemandMapper;

    @Resource
    private SingleDemandDetailMapper singleDemandDetailMapper;

    @Resource
    private SingleDemandDetailRecordMapper singleDemandDetailRecordMapper;

    @Resource
    private SendSms sendSms;

    @Resource
    UserAuditorAware userAuditorAware;

    @Resource
    private IssueOrderMapper issueOrderMapper;

    @Resource
    private IssueOrderItemMapper issueOrderItemMapper;

    @Resource
    private SackSpecificationService sackSpecificationService;

    @Resource
    private LocationExtendClient locationExtendClient;


    @Resource
    private MaterialClient materialClient;

    @Resource
    private OrderPlanClient orderPlanClient;

    @Resource
    private IssueItemDetailMapper issueItemDetailMapper;

    @Resource
    private ISingleDemandService singleDemandService;


    @Override
    public Page<DemandDetailPageDTO> getDemandDetailPageList(DemandDetailPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(singleDemandDetailMapper::getDemandDetailPageList, queryDTO, DemandDetailPageDTO.class);
    }

    /***
     * @Description 批量新增修改删除曲粉需求
     *
     * <AUTHOR>
     * @Date 2022-11-25 14:46
     * @param demandBatchOperateDTO
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer batchOperateQudouDemand(DemandBatchOperateDTO demandBatchOperateDTO) {

        int res = 0;
        SingleDemand singleDemand = new SingleDemand();
        if (StringUtil.isNotEmpty(demandBatchOperateDTO.getId())) {
            singleDemand = singleDemandMapper.selectById(demandBatchOperateDTO.getId());
            if (StringUtils.isNotBlank(demandBatchOperateDTO.getRemark())) {
                //增加修改备注
                singleDemand.setRemark(demandBatchOperateDTO.getRemark());
                singleDemandMapper.updateById(singleDemand);
            }
        } else {
            if (StringUtil.isNotEmpty(demandBatchOperateDTO.getCenterId())) {
                try {
                    //通过中心id获取中心信息
                    ResultVO<LocationDTO> center = locationExtendClient.getLocationById(demandBatchOperateDTO.getCenterId());
                    demandBatchOperateDTO.setCenterName(center.getData().getCode());
                    //通过基地id获取基地信息
                    ResultVO<LocationDTO> base = locationExtendClient.getLocationById(center.getData().getParentId());
                    demandBatchOperateDTO.setBaseId(base.getData().getId());
                    demandBatchOperateDTO.setBaseName(base.getData().getName());
                } catch (Exception e) {
                    log.info("通过中心id获取基地信息失败");
                }
            }
            List<SingleDemand> singleDemands = singleDemandMapper.selectList(Wrappers.<SingleDemand>lambdaQuery()
                    .eq(SingleDemand::getCenterId, demandBatchOperateDTO.getCenterId())
                    .eq(SingleDemand::getUseTime, demandBatchOperateDTO.getUseTime())
                    .eq(demandBatchOperateDTO.getBaseId()!= null,SingleDemand::getBaseId, demandBatchOperateDTO.getBaseId())
                    .eq(demandBatchOperateDTO.getBaseName()!= null,SingleDemand::getBaseName, demandBatchOperateDTO.getBaseName())
                    .eq(SingleDemand::getDeleted, "0"));

            if (singleDemands.size() > 0) {
                singleDemand = singleDemands.get(0);
                //判断 如果需求 同车间同跨 对应日期包装类型和规格已有对应物料类型已接收状态的曲粉需求则不允许新增，只允许修改
                List<DemandBatchOperateDetailDTO> detailDTOList = demandBatchOperateDTO.getDetailDTOS();
                List<String> materialCodes = detailDTOList.stream().map(DemandBatchOperateDetailDTO::getMaterialCode).collect(Collectors.toList());
                List<String> lineNames = detailDTOList.stream().filter(d -> d.getLineName() != null).map(DemandBatchOperateDetailDTO::getLineName).collect(Collectors.toList());
                List<Integer> locationIds = detailDTOList.stream().filter(d -> d.getLocationId() != null).map(DemandBatchOperateDetailDTO::getLocationId).collect(Collectors.toList());
                List<String> typeList = detailDTOList.stream().filter(d -> d.getType() != null).map(DemandBatchOperateDetailDTO::getType).collect(Collectors.toList());
                List<Integer> specificationIds = detailDTOList.stream().filter(d -> d.getSpecificationId() != null).map(DemandBatchOperateDetailDTO::getSpecificationId).collect(Collectors.toList());
                List<BigDecimal> bucketWeightList = detailDTOList.stream().filter(d -> d.getBucketWeight() != null).map(DemandBatchOperateDetailDTO::getBucketWeight).collect(Collectors.toList());
                List<SingleDemandDetail> check = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>().eq(SingleDemandDetail::getDemandId, singleDemand.getId())
                        .in(SingleDemandDetail::getMaterialCode, materialCodes)
                        .in(CollectionUtils.isNotEmpty(lineNames), SingleDemandDetail::getLineName, lineNames)
                        .in(CollectionUtils.isNotEmpty(locationIds), SingleDemandDetail::getLocationId, locationIds)
                        .in(CollectionUtils.isNotEmpty(typeList), SingleDemandDetail::getType, typeList)
                        .in(CollectionUtils.isNotEmpty(specificationIds), SingleDemandDetail::getSpecificationId, specificationIds)
                        .in(CollectionUtils.isNotEmpty(bucketWeightList), SingleDemandDetail::getBucketWeight, bucketWeightList)
                        .eq(SingleDemandDetail::getState, "2"));
                if (CollectionUtils.isNotEmpty(check)) {
                    SingleDemandDetail queryData = check.get(0);
                    String specification = null;
                    if ("1".equals(queryData.getType())) {
                        specification = queryData.getBucketWeight().toString();
                    } else {
                        if(StringUtil.isNotEmpty(queryData.getSpecificationId())){
                            SackSpecification sackSpecification = sackSpecificationMapper.selectById(queryData.getSpecificationId());
                            specification = sackSpecification.getSpecification();
                        }else {
                            specification = Optional.ofNullable(queryData.getSurplusNumber()).orElse(BigDecimal.ZERO).toString();
                        }
                    }
                    throw new BaseKnownException("曲粉需求" + queryData.getMaterialName() + "的规格" + specification + "已接收请联系经理助理或经理进行修改");
                }
            } else {
                singleDemand.setCenterId(demandBatchOperateDTO.getCenterId());
                singleDemand.setCenterName(demandBatchOperateDTO.getCenterName());
                singleDemand.setUseTime(demandBatchOperateDTO.getUseTime());
                singleDemand.setSubmissionTime(LocalDateTime.now());
                //新增基地Id
                singleDemand.setBaseId(demandBatchOperateDTO.getBaseId());
                singleDemand.setBaseName(demandBatchOperateDTO.getBaseName());
                singleDemand.setRemark(demandBatchOperateDTO.getRemark());
                singleDemand.setTaskNo(demandBatchOperateDTO.getTaskNo());
                res += singleDemandMapper.insert(singleDemand);
            }

        }
        List<DemandBatchOperateDetailDTO> detailDTOS = demandBatchOperateDTO.getDetailDTOS();
        if (StringUtil.isNotEmpty(detailDTOS)) {
            for (DemandBatchOperateDetailDTO detailDTO : detailDTOS) {
                try {
                    ResultVO<LocationDTO> location = locationExtendClient.getLocationById(detailDTO.getLocationId());
                    detailDTO.setLocationName(location.getData().getCode());
                } catch (Exception e) {
                    log.info("通过车间id获取车间信息失败，车间id:{}", detailDTO.getLocationId());
                }
                if (StringUtil.isEmpty(detailDTO.getType())) {
                    throw new BaseKnownException(415006, "请选择包装类型");
                }
                if ("0".equals(detailDTO.getOperateState())) {

                    //限制同车间有未接收的需求，不能提报新需求
                    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
                    boolean checkLine = pattern.matcher(singleDemand.getCenterName()).matches() && Integer.parseInt(singleDemand.getCenterName()) >= 709 && Integer.parseInt(singleDemand.getCenterName()) <= 718;
                    List<SingleDemandDetail> singleDemandDetails1 = singleDemandDetailMapper.getListByState(singleDemand.getCenterId(), detailDTO.getLocationId(), detailDTO.getLineName(), checkLine);
                    if (StringUtil.isNotEmpty(singleDemandDetails1) && !singleDemandDetails1.isEmpty()) {
                        throw new BaseKnownException("当天有未接收完的需求");
                    }
                    if (StringUtil.isNotEmpty(detailDTO.getSurplusNumber()) && detailDTO.getSurplusNumber().compareTo(new BigDecimal("0")) > 0) {
                        //新增散装需求
                        SingleDemandDetail singleDemandDetail = new SingleDemandDetail();
                        singleDemandDetail.setMaterialId(detailDTO.getMaterialId());
                        singleDemandDetail.setMaterialCode(detailDTO.getMaterialCode());
                        singleDemandDetail.setMaterialName(detailDTO.getMaterialName());
                        singleDemandDetail.setLocationId(detailDTO.getLocationId());
                        singleDemandDetail.setLineName(detailDTO.getLineName());
                        singleDemandDetail.setSubmitTime(LocalDateTime.now());
                        singleDemandDetail.setType("2");
                        singleDemandDetail.setSurplusNumber(detailDTO.getSurplusNumber());
                        singleDemandDetail.setExperimentPower(detailDTO.getExperimentPower());
                        singleDemandDetail.setSciencePower(detailDTO.getSciencePower());
                        singleDemandDetail.setUrgent(detailDTO.getUrgent());
                        singleDemandDetail.setLocationName(detailDTO.getLocationName());
                        singleDemandDetail.setDemandOrder(detailDTO.getDemandOrder());
                        singleDemandDetail.setLineNumber(detailDTO.getLineNumber());
                        if (StringUtil.isEmpty(singleDemandDetail.getDemandOrder())) {
                            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
                            String s = df.format(singleDemand.getUseTime());
                            s = "XQ" + s + singleDemand.getCenterName() + Optional.ofNullable(detailDTO.getLocationName()).orElse("") + Optional.ofNullable(detailDTO.getLineName()).orElse("");
                            singleDemandDetail.setDemandOrder(s);
                        }
                        singleDemandDetail.setDemandId(singleDemand.getId());
                        singleDemandDetail.setCreateTime(new Date());
                        if (StringUtil.isNotEmpty(demandBatchOperateDTO.getIsSubmit()) && demandBatchOperateDTO.getIsSubmit()) {
                            singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                            singleDemandDetail.setSubmitTime(LocalDateTime.now());
                        } else {
                            singleDemandDetail.setState(DemandState.WAIT_SUBMIT);
                        }
                        singleDemandDetailMapper.insert(singleDemandDetail);
                    }
                    if (StringUtil.isNotEmpty(detailDTO.getSpecificationId()) || StringUtil.isNotEmpty(detailDTO.getBucketWeight())) {
                        List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectList(Wrappers.<SingleDemandDetail>lambdaQuery()
                                .eq(SingleDemandDetail::getDeleted, "0")
                                .eq(SingleDemandDetail::getDemandId, singleDemand.getId())
                                .eq(SingleDemandDetail::getType, detailDTO.getType())
                                .eq(SingleDemandDetail::getExperimentPower, detailDTO.getExperimentPower())
                                .eq(SingleDemandDetail::getSciencePower, detailDTO.getSciencePower())
                                .eq(SingleDemandDetail::getUrgent, detailDTO.getUrgent())
                                .eq(
                                        "1".equals(detailDTO.getType()) ? SingleDemandDetail::getBucketWeight
                                                :
                                                SingleDemandDetail::getSpecificationId,
                                        "1".equals(detailDTO.getType()) ? detailDTO.getBucketWeight()
                                                : detailDTO.getSpecificationId())
                                .eq(StringUtil.isNotEmpty(detailDTO.getLocationId()), SingleDemandDetail::getLocationId, detailDTO.getLocationId())
                                .eq(StringUtil.isNotEmpty(detailDTO.getLineName()), SingleDemandDetail::getLineName, detailDTO.getLineName())
                                .eq(SingleDemandDetail::getMaterialId, detailDTO.getMaterialId()));
                        if (StringUtil.isNotEmpty(singleDemandDetails) && !singleDemandDetails.isEmpty()) {
                            SingleDemandDetail singleDemandDetail = singleDemandDetails.get(0);
                            singleDemandDetail.setRemark(detailDTO.getRemark());
                            //累加需求数
                            if (StringUtil.isNotEmpty(singleDemandDetail.getDemandNumber()) && StringUtil.isNotEmpty(detailDTO.getDemandNumber())) {
                                singleDemandDetail.setDemandNumber(singleDemandDetail.getDemandNumber().add(detailDTO.getDemandNumber()));
                            } else singleDemandDetail.setDemandNumber(detailDTO.getDemandNumber());
                            if (StringUtil.isNotEmpty(demandBatchOperateDTO.getIsSubmit()) && demandBatchOperateDTO.getIsSubmit()) {
                                singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                                singleDemandDetail.setSubmitTime(LocalDateTime.now());
                            }
                            int i;
                            if (singleDemandDetail.getState().equals(DemandState.ALREADY_RECEIVE)) {
                                // 存在当天该跨曲粉需求已经接收，则修改曲粉需求为待接收,且曲粉发放单需求数量对应修改修改
                                singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                                singleDemandDetail.setSubmitTime(LocalDateTime.now());
                                List<IssueOrder> issueOrders = issueOrderMapper.selectList(Wrappers.<IssueOrder>lambdaQuery()
                                        .eq(IssueOrder::getDemandId, singleDemandDetail.getId())
                                        .eq(IssueOrder::getBaseId, demandBatchOperateDTO.getBaseId())
                                        .eq(IssueOrder::getBaseName, demandBatchOperateDTO.getBaseName()));
                                if (issueOrders.size() > 0) {
                                    final IssueOrder issueOrder = issueOrders.get(0);
                                    List<IssueOrderItem> issueOrderItems = issueOrderItemMapper.selectList(Wrappers.<IssueOrderItem>lambdaQuery()
                                            .eq(IssueOrderItem::getOrderId, issueOrder.getId()));
                                    if (issueOrderItems.size() > 0) {
                                        issueOrderItems.get(0).setDemandNumber(singleDemandDetail.getDemandNumber());
                                        issueOrderItemMapper.updateById(issueOrderItems.get(0));
                                    }
                                    issueOrder.setRemark(detailDTO.getRemark());
                                    issueOrderMapper.updateById(issueOrder);
                                }

                                i = singleDemandDetailMapper.updateById(singleDemandDetail);

                            } else {
                                i = singleDemandDetailMapper.updateById(singleDemandDetail);
                                if (i > 0) {
                                    String[] msg = new String[]{
                                            singleDemandDetail.getLocationName(),
                                            singleDemandDetail.getLineName(),
                                            singleDemandDetail.getMaterialName()
                                    };
                                    sendDemandChangeNotice(false, msg);
                                }
                            }
                            res += i;
                            // 新增修改记录
                            UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                                    .orElse(new UserBaseDTO());
                            SingleDemandDetailRecord singleDemandDetailRecord = new SingleDemandDetailRecord();
                            singleDemandDetailRecord.setCreateTime(new Date());
                            singleDemandDetailRecord.setUpdatePeople(userBaseDTO.getUserName());
                            singleDemandDetailRecord.setUpdatePeopleId(userBaseDTO.getId());
                            singleDemandDetailRecord.setDemandDetailId(singleDemandDetail.getId());
                            singleDemandDetailRecord.setDemandNumber(detailDTO.getDemandNumber());
                            singleDemandDetailRecord.setRemark(detailDTO.getRemark());
                            res += singleDemandDetailRecordMapper.insert(singleDemandDetailRecord);
                        } else {
                            // 新增
                            SingleDemandDetail singleDemandDetail = DtoMapper.convert(detailDTO, SingleDemandDetail.class);
                            if ("1".equals(singleDemandDetail.getType())) {
                                singleDemandDetail.setSpecificationId(null);
                            } else {
                                singleDemandDetail.setBucketWeight(null);
                            }
                            singleDemandDetail.setSurplusNumber(null);
                            if (StringUtil.isEmpty(singleDemandDetail.getDemandOrder())) {
                                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
                                String s = df.format(singleDemand.getUseTime());
                                s = "XQ" + s + singleDemand.getCenterName() + Optional.ofNullable(detailDTO.getLocationName()).orElse("") + Optional.ofNullable(detailDTO.getLineName()).orElse("");
                                singleDemandDetail.setDemandOrder(s);
                            }
                            singleDemandDetail.setDemandId(singleDemand.getId());
                            singleDemandDetail.setCreateTime(new Date());
                            if (StringUtil.isNotEmpty(demandBatchOperateDTO.getIsSubmit()) && demandBatchOperateDTO.getIsSubmit()) {
                                singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                                singleDemandDetail.setSubmitTime(LocalDateTime.now());
                            } else {
                                singleDemandDetail.setState(DemandState.WAIT_SUBMIT);
                            }
                            int i = singleDemandDetailMapper.insert(singleDemandDetail);
                            log.info("新增曲粉需求：" + singleDemandDetail.getDemandOrder());
                            res += i;
                            if (i > 0) {
                                String[] msg = new String[]{
                                        singleDemandDetail.getLocationName(),
                                        singleDemandDetail.getLineName(),
                                        singleDemandDetail.getMaterialName()
                                };
                                sendDemandChangeNotice(true, msg);
                            }
                        }
                    } else if (StringUtil.isEmpty(detailDTO.getSurplusNumber()) || detailDTO.getSurplusNumber().compareTo(new BigDecimal("0")) <= 0) {
                        throw new BaseKnownException("规格不能为空");
                    }
                } else if ("1".equals(detailDTO.getOperateState())) {
                    // 修改
                    SingleDemandDetail singleDemandDetail = singleDemandDetailMapper.selectById(detailDTO.getId());
                    if (StringUtil.isEmpty(singleDemandDetail)) {
                        throw new BaseKnownException(1000, "为查询到相关需求");
                    }
                    BigDecimal subtract = new BigDecimal("0");
                    if (StringUtil.isNotEmpty(detailDTO.getDemandNumber()) && StringUtil.isNotEmpty(singleDemandDetail.getDemandNumber())) {
                        subtract = detailDTO.getDemandNumber().subtract(singleDemandDetail.getDemandNumber());
                    } else if (StringUtil.isNotEmpty(detailDTO.getSurplusNumber()) && StringUtil.isNotEmpty(singleDemandDetail.getSurplusNumber())) {
                        subtract = detailDTO.getSurplusNumber().subtract(singleDemandDetail.getSurplusNumber());
                    }
                    singleDemandDetail.setSpecificationId(detailDTO.getSpecificationId());
                    singleDemandDetail.setSurplusNumber(detailDTO.getSurplusNumber());
                    singleDemandDetail.setExperimentPower(detailDTO.getExperimentPower());
                    singleDemandDetail.setSciencePower(detailDTO.getSciencePower());
                    singleDemandDetail.setDemandNumber(detailDTO.getDemandNumber());
                    singleDemandDetail.setRemark(detailDTO.getRemark());
                    singleDemandDetail.setMaterialId(detailDTO.getMaterialId());
                    singleDemandDetail.setMaterialName(detailDTO.getMaterialName());
                    singleDemandDetail.setMaterialCode(detailDTO.getMaterialCode());
                    if (StringUtil.isEmpty(detailDTO.getDemandNumber()) && StringUtil.isEmpty(detailDTO.getSurplusNumber())) {
                        throw new BaseKnownException(10000, "需求数为0时，散装数不能为0");
                    }
                    int i;
                    if (singleDemandDetail.getState().equals(DemandState.ALREADY_RECEIVE)) {
                        // 存在当天该跨曲粉需求已经接收，则修改曲粉需求为待接收,且曲粉发放单需求数量对应修改修改
                        singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                        singleDemandDetail.setSubmitTime(LocalDateTime.now());
                        List<IssueOrder> issueOrders = issueOrderMapper.selectList(Wrappers.<IssueOrder>lambdaQuery()
                                .eq(IssueOrder::getDemandId, singleDemandDetail.getId()));
                        if (issueOrders.size() > 0) {
                            final IssueOrder issueOrder = issueOrders.get(0);
                            List<IssueOrderItem> issueOrderItems = issueOrderItemMapper.selectList(Wrappers.<IssueOrderItem>lambdaQuery()
                                    .eq(IssueOrderItem::getOrderId, issueOrder.getId()));
                            if (issueOrderItems.size() > 0) {
                                issueOrderItems.get(0).setDemandNumber(singleDemandDetail.getDemandNumber());
                                issueOrderItemMapper.updateById(issueOrderItems.get(0));
                            }
                            issueOrder.setType(singleDemandDetail.getType());
                            issueOrder.setSpecificationId(singleDemandDetail.getSpecificationId());
                            issueOrder.setExperimentPower(singleDemandDetail.getExperimentPower());
                            issueOrder.setSciencePower(singleDemandDetail.getSciencePower());
                            issueOrder.setUrgent(singleDemandDetail.getUrgent());
                            issueOrder.setBucketWeight(singleDemandDetail.getBucketWeight());
                            issueOrder.setRemark(detailDTO.getRemark());
                            issueOrderMapper.updateById(issueOrder);
                        }
                        i = singleDemandDetailMapper.updateById(singleDemandDetail);
                        //需求已接收需要取消制曲生产翻沙曲的数据同步
                        try {
                            cancelToProduction(singleDemandDetail);
                        } catch (Exception e) {
                            log.error("调用制曲生产过账失败:" + e.getMessage());
                        }
                    } else {
                        i = singleDemandDetailMapper.updateById(singleDemandDetail);
                        if (i > 0) {
                            String[] msg = new String[]{
                                    singleDemandDetail.getLocationName(),
                                    singleDemandDetail.getLineName(),
                                    singleDemandDetail.getMaterialName()
                            };
                            sendDemandChangeNotice(false, msg);
                        }
                    }
                    res += i;

                    //新增修改记录
                    UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                            .orElse(new UserBaseDTO());
                    SingleDemandDetailRecord singleDemandDetailRecord = new SingleDemandDetailRecord();
                    singleDemandDetailRecord.setCreateTime(new Date());
                    singleDemandDetailRecord.setUpdatePeople(userBaseDTO.getUserName());
                    singleDemandDetailRecord.setUpdatePeopleId(userBaseDTO.getId());
                    singleDemandDetailRecord.setDemandDetailId(singleDemandDetail.getId());
                    singleDemandDetailRecord.setDemandNumber(subtract);
                    singleDemandDetailRecord.setRemark(detailDTO.getRemark());
                    res += singleDemandDetailRecordMapper.insert(singleDemandDetailRecord);

                    SingleDemand singleDemand1 = singleDemandMapper.selectById(singleDemandDetail.getDemandId());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String formattedDateTime = singleDemand1.getUseTime().format(formatter);
                    String[] msg = new String[]{
                            singleDemandDetail.getLocationName() + "车间的",
                            formattedDateTime,
                    };
                    sendSms.sendRoleMessage(137, "2227497", msg);

                } else if ("2".equals(detailDTO.getOperateState())) {
                    // 删除
                    SingleDemandDetail singleDemandDetail = singleDemandDetailMapper.selectById(detailDTO.getId());

                    List<IssueOrder> issueOrders = issueOrderMapper.selectList(Wrappers.<IssueOrder>lambdaQuery()
                            .eq(IssueOrder::getDemandId, singleDemandDetail.getId()));
                    if (issueOrders.size() > 0) {
                        throw new BaseKnownException(10000, singleDemandDetail.getDemandOrder() + "存在曲粉发放单，不能删除");
                    } else {
                        res += singleDemandDetailMapper.deleteById(detailDTO.getId());

                    }
                } else {
                    continue;
                }
            }
        }
        return res;
    }

    /**
     * 取消翻沙曲需求
     * @param detail
     */
    private void cancelToProduction(SingleDemandDetail detail) {
        if (MaterialConstant.QF3.equals(detail.getMaterialCode()) || MaterialConstant.QF4.equals(detail.getMaterialCode())) {
            TurnOverDemandDelDTO delDTO = new TurnOverDemandDelDTO();
            delDTO.setDemandDetailId(detail.getId());
            log.info("翻沙曲过账取消调用");
            ResultVO<Boolean> turnOverDemand = orderPlanClient.cancelTurnOverDemand(delDTO);
            if (turnOverDemand == null || turnOverDemand.getCode() != 200) {
                log.info(JSONObject.toJSONString(turnOverDemand));
                log.error("翻沙曲调用制曲生产进行过账取消失败");
            }
        }
    }

    @Override
    public Integer batchOperateQudouDemand(List<DemandBatchOperateDTO> demandBatchOperateDTO) {
        AtomicInteger res = new AtomicInteger();
        demandBatchOperateDTO.forEach(item -> {
            res.addAndGet(batchOperateQudouDemand(item));
        });
        return res.get();
    }

    /***
     * @Description 曲粉需求新增修改通知
     *
     * <AUTHOR>
     * @Date 2023-2-8 16:41
     * @param isCreate
     * @param msg
     * @return void
     **/
    public void sendDemandChangeNotice(boolean isCreate, String[] msg) {
        try {
            if (Integer.parseInt(DateUtil.HourFormat(new Date())) > 17 || Integer.parseInt(DateUtil.HourFormat(new Date())) < 8) {
                // 8点前和 17点后不接收短信
                return;
            }
//            List<UserDTO> userDTOS = userClient.getUsersByRoleName("曲粉SAP").getData();
//            List<String> phones = new ArrayList<>();
//            for (UserDTO userDTO : userDTOS) {
//                if (StringUtil.isNotEmpty(userDTO.getMobilePhone())) {
//                    phones.add("+86" + userDTO.getMobilePhone());
//                }
//            }
//            if (isCreate) {
//
//                if (Integer.parseInt(DateUtil.HourFormat(new Date())) > 6) { // 7点-24点收短信
//                    return;
//                }
//                SendSms.sendNote("1699733", msg, phones.toArray(new String[phones.size()]));
//            } else {
//                SendSms.sendNote("1695184", msg, phones.toArray(new String[phones.size()]));
//            }
        } catch (Exception e) {
            log.info("曲粉新增修改短信通知失败");
            e.printStackTrace();
        }

    }


    /***
     * @Description 根据车间编码和跨获取tms跨编号
     *
     * <AUTHOR>
     * @Date 2022-12-12 11:04
     * @param locationName
     * @param lineName
     * @return java.lang.String
     **/
    public String getTmsLineName(String locationName, String lineName) {
        String tmsLineName = "";
        switch (locationName + lineName) {
            case "70901A":
                tmsLineName = "70901";
                break;
            case "70901B":
                tmsLineName = "70902";
                break;
            case "70902A":
                tmsLineName = "70903";
                break;
            case "70902B":
                tmsLineName = "70904";
                break;
            case "70903A":
                tmsLineName = "70905";
                break;
            case "70903B":
                tmsLineName = "70906";
                break;
            case "70904A":
                tmsLineName = "70907";
                break;
            case "70904B":
                tmsLineName = "70908";
                break;
            case "70905A":
                tmsLineName = "70909";
                break;
            case "70905B":
                tmsLineName = "70910";
                break;

            case "71301A":
                tmsLineName = "71301";
                break;
            case "71301B":
                tmsLineName = "71302";
                break;
            case "71302A":
                tmsLineName = "71303";
                break;
            case "71302B":
                tmsLineName = "71304";
                break;
            case "71303A":
                tmsLineName = "71305";
                break;
            case "71303B":
                tmsLineName = "71306";
                break;
            case "71304A":
                tmsLineName = "71307";
                break;
            case "71304B":
                tmsLineName = "71308";
                break;
            case "71305A":
                tmsLineName = "71309";
                break;
            case "71305B":
                tmsLineName = "71310";
                break;

            case "71801A":
                tmsLineName = "71801";
                break;
            case "71801B":
                tmsLineName = "71802";
                break;
            case "71802A":
                tmsLineName = "71803";
                break;
            case "71802B":
                tmsLineName = "71804";
                break;
            case "71803A":
                tmsLineName = "71805";
                break;
            case "71803B":
                tmsLineName = "71806";
                break;
            case "71804A":
                tmsLineName = "71807";
                break;
            case "71804B":
                tmsLineName = "71808";
                break;
            case "71805A":
                tmsLineName = "71809";
                break;
            case "71805B":
                tmsLineName = "71810";
                break;

            case "71001A":
                tmsLineName = "71001";
                break;
            case "71001B":
                tmsLineName = "71002";
                break;
            case "71002A":
                tmsLineName = "71003";
                break;
            case "71002B":
                tmsLineName = "71004";
                break;
            case "71003A":
                tmsLineName = "71005";
                break;
            case "71003B":
                tmsLineName = "71006";
                break;
            case "71004A":
                tmsLineName = "71007";
                break;
            case "71004B":
                tmsLineName = "71008";
                break;
            case "71005A":
                tmsLineName = "71009";
                break;
            case "71005B":
                tmsLineName = "71010";
                break;

            case "71101A":
                tmsLineName = "71101";
                break;
            case "71101B":
                tmsLineName = "71102";
                break;
            case "71102A":
                tmsLineName = "71103";
                break;
            case "71102B":
                tmsLineName = "71104";
                break;
            case "71103A":
                tmsLineName = "71105";
                break;
            case "71103B":
                tmsLineName = "71106";
                break;
            case "71104A":
                tmsLineName = "71107";
                break;
            case "71104B":
                tmsLineName = "71108";
                break;
            case "71105A":
                tmsLineName = "71109";
                break;
            case "71105B":
                tmsLineName = "71110";
                break;
            case "71201A":
                tmsLineName = "71201";
                break;
            case "71201B":
                tmsLineName = "71202";
                break;
            case "71202A":
                tmsLineName = "71203";
                break;
            case "71202B":
                tmsLineName = "71204";
                break;
            case "71203A":
                tmsLineName = "71205";
                break;
            case "71203B":
                tmsLineName = "71206";
                break;
            case "71204A":
                tmsLineName = "71207";
                break;
            case "71204B":
                tmsLineName = "71208";
                break;
            case "71205A":
                tmsLineName = "71209";
                break;
            case "71205B":
                tmsLineName = "71210";
                break;
            case "71401A":
                tmsLineName = "71401";
                break;
            case "71401B":
                tmsLineName = "71402";
                break;
            case "71402A":
                tmsLineName = "71403";
                break;
            case "71402B":
                tmsLineName = "71404";
                break;
            case "71403A":
                tmsLineName = "71405";
                break;
            case "71403B":
                tmsLineName = "71406";
                break;
            case "71404A":
                tmsLineName = "71407";
                break;
            case "71404B":
                tmsLineName = "71408";
                break;
            case "71405A":
                tmsLineName = "71409";
                break;
            case "71405B":
                tmsLineName = "71410";
                break;
            case "71501A":
                tmsLineName = "71501";
                break;
            case "71501B":
                tmsLineName = "71502";
                break;
            case "71502A":
                tmsLineName = "71503";
                break;
            case "71502B":
                tmsLineName = "71504";
                break;
            case "71503A":
                tmsLineName = "71505";
                break;
            case "71503B":
                tmsLineName = "71506";
                break;
            case "71504A":
                tmsLineName = "71507";
                break;
            case "71504B":
                tmsLineName = "71508";
                break;
            case "71505A":
                tmsLineName = "71509";
                break;
            case "71505B":
                tmsLineName = "71510";
                break;
            case "71601A":
                tmsLineName = "71601";
                break;
            case "71601B":
                tmsLineName = "71602";
                break;
            case "71602A":
                tmsLineName = "71603";
                break;
            case "71602B":
                tmsLineName = "71604";
                break;
            case "71603A":
                tmsLineName = "71605";
                break;
            case "71603B":
                tmsLineName = "71606";
                break;
            case "71604A":
                tmsLineName = "71607";
                break;
            case "71604B":
                tmsLineName = "71608";
                break;
            case "71605A":
                tmsLineName = "71609";
                break;
            case "71605B":
                tmsLineName = "71610";
                break;
            case "71701A":
                tmsLineName = "71701";
                break;
            case "71701B":
                tmsLineName = "71702";
                break;
            case "71702A":
                tmsLineName = "71703";
                break;
            case "71702B":
                tmsLineName = "71704";
                break;
            case "71703A":
                tmsLineName = "71705";
                break;
            case "71703B":
                tmsLineName = "71706";
                break;
            case "71704A":
                tmsLineName = "71707";
                break;
            case "71704B":
                tmsLineName = "71708";
                break;
            case "71705A":
                tmsLineName = "71709";
                break;
            case "71705B":
                tmsLineName = "71710";
                break;
            default:
                tmsLineName = "";
                break;

        }
        return tmsLineName;
    }


    /***
     * @Description 曲粉需求批量报送
     *
     * <AUTHOR>
     * @Date 2022-11-28 11:50
     * @param detailIds
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer batchSubmitQudouDemand(List<Integer> detailIds) {

        int res = 0;
        for (Integer detailId : detailIds) {
            SingleDemandDetail singleDemandDetail = singleDemandDetailMapper.selectById(detailId);
            if (DemandState.WAIT_SUBMIT.equals(singleDemandDetail.getState())) {
                singleDemandDetail.setState(DemandState.WAIT_RECEIVE);
                singleDemandDetail.setSubmitTime(LocalDateTime.now());
                res += singleDemandDetailMapper.updateById(singleDemandDetail);
            }
        }
        return res;
    }

    @Override
    @Transactional
    public Integer batchDeleteQudouDemand(List<Integer> detailIds) {
        int res = 0;
        List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectBatchIds(detailIds);
        for (SingleDemandDetail singleDemandDetail : singleDemandDetails) {
            if (DemandState.WAIT_SUBMIT.equals(singleDemandDetail.getState()) || DemandState.WAIT_RECEIVE.equals(singleDemandDetail.getState())) {
                //有相关发放单则不让删除
                List<IssueOrder> issueOrders = issueOrderMapper.selectList(new LambdaUpdateWrapper<IssueOrder>()
                        .eq(IssueOrder::getDemandId, singleDemandDetail.getId()));
                if (StringUtil.isNotEmpty(issueOrders) && !issueOrders.isEmpty()) {
                    throw new BaseKnownException(10000, "该需求已经生成发放单，不可删除");
                }
                res += singleDemandDetailMapper.deleteById(singleDemandDetail.getId());
            }
        }
        return res;
    }

    @Override
    public Page<SingleDemandDetailRecordDTO> getSingleDemandDetailRecordPageList(SingleDemandDetailRecordQueryDTO queryDTO) {
        return PageHelperUtil.getPage(singleDemandDetailRecordMapper::getSingleDemandDetailRecordPageList, queryDTO, SingleDemandDetailRecordDTO.class);
    }

    @Override
    public Integer receiveBlQuFen(ReceiveBlQuFenDTO receiveBlQuFenDTO) {
        log.info("布勒推送的曲粉数据==》{}", receiveBlQuFenDTO);
        return 0;
    }

    @Override
    public Integer operateQudouDemandForLuoHan(DemandBatchOperateFroLuoHanDTO demandBatchOperateFroLuoHanDTO) {
        DemandBatchOperateDTO demandBatchOperateDTO = CopyUtil.simpleCopy(demandBatchOperateFroLuoHanDTO, DemandBatchOperateDTO.class);
        demandBatchOperateDTO.setBaseId(179);
        demandBatchOperateDTO.setBaseName("罗汉");
        try {
            ResultVO<List<LocationDTO>> locationListByType = locationExtendClient.getLocationListByType(20);
            List<LocationDTO> data = locationListByType.getData();
            LocationDTO locationDTO1 = data.stream().filter(locationDTO -> locationDTO.getCode().contains("708")).findFirst().orElse(null);
            log.info("过滤出的708中心:{}", locationDTO1);
            demandBatchOperateDTO.setCenterId(locationDTO1.getId());
            demandBatchOperateDTO.setCenterName(locationDTO1.getName());
        } catch (Exception e) {

        }
        demandBatchOperateDTO.setIsSubmit(Boolean.TRUE);
        Instant instant = demandBatchOperateFroLuoHanDTO.getUseTime().toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        demandBatchOperateDTO.setUseTime(instant.atZone(zoneId).toLocalDateTime());
        MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        List<MaterialDTO> content = null;
        try {
            queryDTO.setMaterialType(30);
            queryDTO.setPageSize(99999);
            ResultVO<HvPage<MaterialDTO>> materialByNameOrCode = materialClient.getMaterialByNameOrCode(queryDTO);
            content = materialByNameOrCode.getData().getContent();
        } catch (Exception e) {
            log.info("获取曲粉信息失败");
        }
        List<DemandBatchOperateDetailDTO> demandBatchOperateDetailDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(demandBatchOperateFroLuoHanDTO.getDemandDetailReqList())) {
            List<MaterialDTO> finalContent = content;
            demandBatchOperateFroLuoHanDTO.getDemandDetailReqList().forEach(demandBatchOperateDetailForLuoHanDTO -> {
                DemandBatchOperateDetailDTO demandBatchOperateDetailDTO = CopyUtil.simpleCopy(demandBatchOperateDetailForLuoHanDTO, DemandBatchOperateDetailDTO.class);
                if (CollectionUtils.isNotEmpty(finalContent)) {
                    MaterialDTO materialDTO = finalContent.stream().filter(item -> StringUtil.isNotEmpty(item.getMaterialCode()) && item.getMaterialCode().equals(demandBatchOperateDetailDTO.getMaterialCode())).findAny().orElse(null);
                    if (StringUtil.isNotEmpty(materialDTO)) {
                        demandBatchOperateDetailDTO.setMaterialId(materialDTO.getId());
                    } else throw new BaseKnownException(10000, "发送的物料编码有误");
                }
                if ("1".equals(demandBatchOperateDetailForLuoHanDTO.getType())) {
                    //斗装封装
                    //根据曲粉获取当前生效大曲配送规格
                    BigDecimal bucketWeight = sackSpecificationMapper.getDistribution(demandBatchOperateDetailForLuoHanDTO.getMaterialId());
                    if (StringUtil.isNotEmpty(bucketWeight) && bucketWeight.compareTo(new BigDecimal(0)) != 0) {
                        demandBatchOperateDetailDTO.setBucketWeight(bucketWeight);
                    } else {
                        demandBatchOperateDetailDTO.setBucketWeight(BigDecimal.valueOf(demandBatchOperateDetailForLuoHanDTO.getBucketWeight()));
                    }
                } else if ("2".equals(demandBatchOperateDetailForLuoHanDTO.getType()) && StringUtil.isNotEmpty(demandBatchOperateDetailForLuoHanDTO.getBucketWeight())) {
                    //袋装封装
                    SackSpecification sackSpecification = sackSpecificationMapper.selectOne(new LambdaUpdateWrapper<SackSpecification>()
                            .eq(SackSpecification::getWeight, demandBatchOperateDetailForLuoHanDTO.getBucketWeight())
                            .eq(SackSpecification::getState, 1)
                            .eq(SackSpecification::getDeleted, false)
                            .last("LIMIT 1"));
                    if (StringUtil.isNotEmpty(sackSpecification))
                        demandBatchOperateDetailDTO.setSpecificationId(sackSpecification.getId());
                    else throw new BaseKnownException(10000, "袋装规格不存在");
                }
                demandBatchOperateDetailDTO.setOperateState(demandBatchOperateDetailForLuoHanDTO.getOperateState());
                if ("1".equals(demandBatchOperateDetailForLuoHanDTO.getOperateState())) {
                    //罗汉需求修改
                    //现根据行号查询出提报的需求
                    List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>()
                            .eq(SingleDemandDetail::getLineNumber, demandBatchOperateDetailForLuoHanDTO.getLineNumber()));
                    if (StringUtil.isNotEmpty(singleDemandDetails)) {
                        if (StringUtil.isNotEmpty(demandBatchOperateDetailForLuoHanDTO.getBucketWeight())) {
                            if ("1".equals(demandBatchOperateDetailForLuoHanDTO.getType())) {
                                //斗装需求修改匹配
                                SingleDemandDetail singleDemandDetail1 = singleDemandDetails
                                        .stream()
                                        .filter(singleDemandDetail -> StringUtil.isNotEmpty(singleDemandDetail.getBucketWeight()) && singleDemandDetail.getMaterialCode().equals(demandBatchOperateDetailForLuoHanDTO.getMaterialCode()) && singleDemandDetail.getType().equals(demandBatchOperateDetailForLuoHanDTO.getType()))
                                        .findFirst().orElse(new SingleDemandDetail());
                                demandBatchOperateDetailDTO.setId(singleDemandDetail1.getId());
                                demandBatchOperateDetailDTO.setDemandId(singleDemandDetail1.getDemandId());
                                demandBatchOperateDetailDTO.setSurplusNumber(null);
                            } else {
                                //整袋需求修改匹配
                                SingleDemandDetail singleDemandDetail1 = singleDemandDetails
                                        .stream()
                                        .filter(singleDemandDetail -> StringUtil.isNotEmpty(singleDemandDetail.getSpecificationId()) && singleDemandDetail.getMaterialCode().equals(demandBatchOperateDetailForLuoHanDTO.getMaterialCode()) && singleDemandDetail.getType().equals(demandBatchOperateDetailForLuoHanDTO.getType()))
                                        .findFirst().orElse(new SingleDemandDetail());
                                demandBatchOperateDetailDTO.setId(singleDemandDetail1.getId());
                                demandBatchOperateDetailDTO.setDemandId(singleDemandDetail1.getDemandId());
                                demandBatchOperateDetailDTO.setSurplusNumber(null);
                            }

                        }
                        //散装修改
                        if (StringUtil.isNotEmpty(demandBatchOperateDetailForLuoHanDTO.getSurplusNumber()) && demandBatchOperateDetailForLuoHanDTO.getSurplusNumber().compareTo(new BigDecimal("0")) != 0) {
                            SingleDemandDetail singleDemandDetail1 = singleDemandDetails.stream().filter(singleDemandDetail -> StringUtil.isEmpty(singleDemandDetail.getBucketWeight()) && StringUtil.isEmpty(singleDemandDetail.getSpecificationId()) && singleDemandDetail.getType().equals(demandBatchOperateDetailForLuoHanDTO.getType()) && singleDemandDetail.getMaterialCode().equals(demandBatchOperateDetailForLuoHanDTO.getMaterialCode())).findFirst().orElse(new SingleDemandDetail());
                            singleDemandDetail1.setSurplusNumber(demandBatchOperateDetailForLuoHanDTO.getSurplusNumber());
                            DemandBatchOperateDetailDTO demandBatchOperateDetailDTO1 = CopyUtil.simpleCopy(singleDemandDetail1, DemandBatchOperateDetailDTO.class);
                            demandBatchOperateDetailDTO1.setOperateState("1");
                            demandBatchOperateDetailDTOList.add(demandBatchOperateDetailDTO1);
                        }
                    }
                }
                demandBatchOperateDetailDTO.setExperimentPower(StringUtil.isNotEmpty(demandBatchOperateDetailForLuoHanDTO.getExperimentPower()) ? demandBatchOperateDetailForLuoHanDTO.getExperimentPower() : "0");
                demandBatchOperateDetailDTO.setSciencePower("0");
                demandBatchOperateDetailDTO.setUrgent("0");
                demandBatchOperateDetailDTO.setLocationName(demandBatchOperateDetailForLuoHanDTO.getLocationName());
                demandBatchOperateDetailDTO.setLocationId(demandBatchOperateDetailForLuoHanDTO.getLocationId());
                demandBatchOperateDetailDTO.setDemandNumber(demandBatchOperateDetailForLuoHanDTO.getDemandNumber());
                demandBatchOperateDetailDTOList.add(demandBatchOperateDetailDTO);
            });
        }
        demandBatchOperateDTO.setDetailDTOS(demandBatchOperateDetailDTOList);
        return this.batchOperateQudouDemand(demandBatchOperateDTO);
    }

    @Override
    public Page<PackTaskDTO> getPackTack(PackTaskQueryDTO packTaskQueryDTO) {
        Page<PackTaskDTO> page = PageHelperUtil.getPage(singleDemandDetailMapper::getPackTack, packTaskQueryDTO, PackTaskDTO.class);
        if (CollectionUtils.isNotEmpty(page.getContent())) {
            page.getContent().forEach(packTaskDTO -> {
                List<PackTaskDetailDTO> packTaskDetailDTOList = singleDemandDetailMapper.getPackTackDetail(packTaskDTO);
                packTaskDTO.setPackTaskDetailDTOList(packTaskDetailDTOList);
            });
        }
        return page;
    }

    @Override
    public Boolean completePackTack(List<Integer> ids) {
        return singleDemandDetailMapper.completePackTack(ids) > 0;
    }

    /**
      * @描述: 条件查询需求
      * @作者: 刘文勇
      * @日期: 2024/9/27 10:20
      * @参数: demandDetailQuery
      * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.DemandDetailPageDTO>
    */
    @Override
    public List<DemandDetailPageDTO> getDemandDetailQuery(DemandDetailQuery demandDetailQuery) {
        return Optional.ofNullable(singleDemandDetailMapper.getDemandDetailQuery(demandDetailQuery)).orElse(new ArrayList<>());
    }

    /**
     * 查询车间中心跨库存信息
     * @param demandDetailQuery
     * @return
     */
    @Override
    public LocationInventory getLocationInventory(DemandDetailQuery demandDetailQuery) {
        LocationInventory locationInventory = new LocationInventory();
        Map<Integer, SackSpecification> specificationMap = new HashMap<>();
        //获取车间库存
        List<IssueItemDetail> issueItemDetails = issueItemDetailMapper.selectList(new LambdaUpdateWrapper<IssueItemDetail>()
                .eq(IssueItemDetail::getAcceptLocationId, demandDetailQuery.getLocationId())
                .eq(IssueItemDetail::getAcceptCenterId, demandDetailQuery.getCenterId()));
        locationInventory.setLocationInventory(issueItemDetails.stream().map(IssueItemDetail::getRemainQuantity).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        //获取在途库存
        List<SingleDemandDetail> singleDemandDetails = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>()
                .eq(SingleDemandDetail::getLocationId, demandDetailQuery.getLocationId())
                .eq(SingleDemandDetail::getLineName, demandDetailQuery.getLineName())
                .eq(SingleDemandDetail::getState, 1));
        locationInventory.setOnTransitInventory(summaryQuantity(singleDemandDetails, specificationMap));
        //获取当日需求量
        List<Integer> demandIds = singleDemandMapper.selectList(new LambdaUpdateWrapper<SingleDemand>()
                        .eq(SingleDemand::getCenterId, demandDetailQuery.getCenterId())
                        .eq(SingleDemand::getUseTime, demandDetailQuery.getUseTime()))
                .stream().map(SingleDemand::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(demandIds)) {
            List<SingleDemandDetail> dayDemandDetails = singleDemandDetailMapper.selectList(new LambdaUpdateWrapper<SingleDemandDetail>()
                    .eq(SingleDemandDetail::getLocationId, demandDetailQuery.getLocationId())
                    .in(SingleDemandDetail::getDemandId, demandIds)
                    .eq(SingleDemandDetail::getState, 1));
            locationInventory.setDayDemand(summaryQuantity(dayDemandDetails, specificationMap));
        } else {
            locationInventory.setDayDemand(BigDecimal.ZERO);
        }
        return locationInventory;
    }

    /**
     * 删除翻窖需求明细
     * @param taskNo
     * @param lineName
     * @return
     */
    @Override
    public Integer delTurnoverDemandDetail(String taskNo, String lineName) {
        return singleDemandService.delTurnoverDemandDetail(taskNo, lineName);
    }

    /**
     * 统计数量
     * @param singleDemandDetails
     * @param specificationMap
     * @return
     */
    @NotNull
    private BigDecimal summaryQuantity(List<SingleDemandDetail> singleDemandDetails, Map<Integer, SackSpecification> specificationMap) {
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal nextNumber;
        for (SingleDemandDetail detail : singleDemandDetails) {
            if ("1".equals(detail.getType())) {
                //斗装
                Integer demandNumber = detail.getDemandNumber().intValue();
                nextNumber = detail.getBucketWeight().multiply(new BigDecimal(demandNumber));
            } else {
                //袋装
                if (detail.getSurplusNumber() != null) {
                    //散装
                    nextNumber = detail.getSurplusNumber();
                } else {
                    SackSpecification sackSpecification = specificationMap.get(detail.getSpecificationId());
                    if (sackSpecification == null) {
                        sackSpecification = sackSpecificationService.getById(detail.getSpecificationId());
                        specificationMap.put(detail.getSpecificationId(), sackSpecification);
                    }
                    nextNumber = new BigDecimal(sackSpecification.getWeight()).multiply(detail.getDemandNumber());
                }
            }
            sum = sum.add(nextNumber);
        }
        return sum;
    }
}
