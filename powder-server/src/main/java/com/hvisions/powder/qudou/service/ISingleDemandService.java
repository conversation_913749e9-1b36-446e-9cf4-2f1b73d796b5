package com.hvisions.powder.qudou.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.qudou.dto.SingleDemandQueryReq;
import com.hvisions.powder.qudou.entity.SingleDemand;
import com.hvisions.powder.qudou.vo.SingleDemandVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <p>
 * 曲粉日需求单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface ISingleDemandService extends IService<SingleDemand> {
    Page<SingleDemandVO> queryPagedSingleDemand(SingleDemandQueryReq req, PageInfo pageInfo);


    /***
     * @Description 接收曲粉需求单，生成曲粉发放记录
     *
     * <AUTHOR>
     * @Date 2022-11-28 14:22
     * @param demandDetailIds
     * @return void
     **/
    void changeReceive(List<Integer> demandDetailIds);

    /**
     * @Description 根据中心、车间、产线或者下发给tms的班组
     *
     * <AUTHOR>
     * @Date 2024-7-1 15:25
     * @param centerName
     * @param locationName
     * @param lineName
     * @return java.lang.String
     **/
    String convertBzbh(String centerName, String locationName, String lineName);

    /**
     * @description: 曲粉日需求单导出excel
     * @author: wy liu
     * @date: 2024/7/15
     * @param req
     * @param response
     * @return void
     */
    ResultVO<ExcelExportDto> getSingleDemandExcel(SingleDemandQueryReq req, HttpServletResponse response) throws IOException;

    /**
     * 驳回曲粉日需求
     * @param id
     * @return
     */
    Integer overrule(Integer id);

    /**
     * 删除翻窖需求明细
     * @param taskNo
     * @param lineName
     * @return
     */
    Integer delTurnoverDemandDetail(String taskNo, String lineName);
}
