/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.powder.qudou.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.powder.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * @classname SingleDemand
 * @description 曲粉日需求单实体类
 * <AUTHOR>
 * @date 2022-04-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_wp_single_demand")
@Table(name = "t_wp_single_demand")
@Entity
@ApiModel("SingleDemand对象")
public class SingleDemand extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("中心id")
    private Integer centerId;

    @ApiModelProperty("中心名称")
    private String centerName;

    @ApiModelProperty("基地Id")
    private Integer baseId;

    @ApiModelProperty("基地名称")
    private String baseName;

    @ApiModelProperty("提报时间")
    private LocalDateTime submissionTime;

    @ApiModelProperty("使用日期")
    private LocalDateTime useTime;

    /**
     * 冗余字段,日需求接受确认时返回
     */
    @ApiModelProperty("顺序号")
    private Integer rowNo;

    @ApiModelProperty("任务号--翻窖需求会有")
    private String taskNo;

    @ApiModelProperty("备注")
    private String remark;
}
