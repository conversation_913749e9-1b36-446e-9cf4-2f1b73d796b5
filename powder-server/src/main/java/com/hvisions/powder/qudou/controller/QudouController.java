package com.hvisions.powder.qudou.controller;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.powder.dto.qudou.demand.*;
import com.hvisions.powder.operlog.annotation.Log;
import com.hvisions.powder.operlog.enums.BusinessType;
import com.hvisions.powder.qudou.dto.ReceiveBlQuFenDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordQueryDTO;
import com.hvisions.powder.qudou.service.QudouService;
import com.hvisions.powder.sap.entity.LhPost;
import com.hvisions.powder.sap.mapper.LhPostMapper;
import com.hvisions.powder.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:曲粉（新）
 * @date 2022/11/24 10:18
 */
@RestController
@RequestMapping(value = "/qudou")
@Api(tags = "曲粉（新）")
@Slf4j
public class QudouController {
    @Resource
    private QudouService qudouService;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Autowired
    private LhPostMapper lhPostMapper;

    @ApiOperation(value = "分页查询曲粉需求详情")
    @RequestMapping(value = "demand/list/page/get", method = RequestMethod.POST)
    public Page<DemandDetailPageDTO> getDemandDetailPageList(@RequestBody DemandDetailPageQueryDTO queryDTO) {
        return qudouService.getDemandDetailPageList(queryDTO);
    }

    @ApiOperation(value = "曲粉需求多个中心批量新增操作")
    @RequestMapping(value = "/demand/operate/batch/center", method = RequestMethod.POST)
    public Integer batchOperateQudouDemand(@RequestBody List<DemandBatchOperateDTO> demandBatchOperateDTOList) {
        return qudouService.batchOperateQudouDemand(demandBatchOperateDTOList);
    }

    @ApiOperation(value = "曲粉需求新增修改删除操作")
    @RequestMapping(value = "/demand/operate/batch", method = RequestMethod.POST)
    @Log(title = "曲粉需求新增修改删除操作", businessType = BusinessType.INSERT)
    public Integer batchOperateQudouDemand(@RequestBody DemandBatchOperateDTO demandBatchOperateDTO) {
        return qudouService.batchOperateQudouDemand(demandBatchOperateDTO);
    }

    @ApiOperation(value = "删除翻窖需求明细")
    @RequestMapping(value = "/delTurnoverDemandDetail", method = RequestMethod.GET)
    @Log(title = "删除翻窖需求明细", businessType = BusinessType.DELETE)
    public Integer delTurnoverDemandDetail(@RequestParam String taskNo, @RequestParam String lineName) {
        return qudouService.delTurnoverDemandDetail(taskNo, lineName);
    }

    @ApiOperation(value = "罗汉曲粉需求新增修改删除操作")
    @RequestMapping(value = "/demand/operate/for/luoHan", method = RequestMethod.POST)
    public Integer batchOperateQudouDemand(@RequestBody DemandBatchOperateFroLuoHanDTO demandBatchOperateFroLuoHanDTO){
        log.info("接收罗汉曲粉需求提报：{}",demandBatchOperateFroLuoHanDTO);
        LogDto logDto = new LogDto();
        logDto.setControllerName("8中心调用mes");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular("大曲需求");
        logDto.setLogInvocation("8中心");
        logDto.setLocation("/qudou/demand/operate/for/luoHan");
        logDto.setMethodName("接收8中心曲粉需求");
        logDto.setLogParameter(JSONObject.toJSONString(demandBatchOperateFroLuoHanDTO));
        logDto.setLogType(1);
        LhPost lhPost = new LhPost();
        lhPost.setBaseName("罗汉");
        lhPost.setCenterName("708");
        lhPost.setPostTime(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        lhPost.setUrl("/qudou/demand/operate/for/luoHan");
        lhPost.setStatus("2");
        lhPost.setControllerName("8中心调用mes");
        lhPost.setBody(JSONObject.toJSONString(demandBatchOperateFroLuoHanDTO));
        lhPostMapper.insert(lhPost);
        try {
            logCaptureClient.logRecord(logDto);
        }catch (Exception e){
            e.printStackTrace();
        }
        Integer integer = qudouService.operateQudouDemandForLuoHan(demandBatchOperateFroLuoHanDTO);
        lhPost.setStatus("1");
        lhPostMapper.updateById(lhPost);
        return integer;
    }

    @ApiOperation(value = "查询打包任务")
    @RequestMapping(value = "/packTack/get", method = RequestMethod.POST)
    public Page<PackTaskDTO> getPackTack(@RequestBody PackTaskQueryDTO packTaskQueryDTO){
        return qudouService.getPackTack(packTaskQueryDTO);
    }

    @ApiOperation(value = "完成打包任务")
    @RequestMapping(value = "/packTack/complete", method = RequestMethod.POST)
    public Boolean completePackTack(@RequestBody List<Integer> ids){
        return qudouService.completePackTack(ids);
    }

    @ApiOperation(value = "曲粉需求批量报送")
    @RequestMapping(value = "/demand/submit/batch", method = RequestMethod.POST)
    @Log(title = "曲粉需求批量报送", businessType = BusinessType.UPDATE)
    public Integer batchSubmitQudouDemand(@RequestBody List<Integer> detailIds) {
        return qudouService.batchSubmitQudouDemand(detailIds);
    }

    @ApiOperation(value = "曲粉需求批量删除")
    @RequestMapping(value = "/demand/delete/batch", method = RequestMethod.POST)
    public Integer batchDeleteQudouDemand(@RequestBody List<Integer> detailIds) {
        return qudouService.batchDeleteQudouDemand(detailIds);
    }

    @ApiOperation(value = "分页查询曲斗详情修改记录")
    @RequestMapping(value = "/update/list/page/get", method = RequestMethod.POST)
    public Page<SingleDemandDetailRecordDTO> getSingleDemandDetailRecordPageList(@RequestBody SingleDemandDetailRecordQueryDTO queryDTO) {
        return qudouService.getSingleDemandDetailRecordPageList(queryDTO);
    }

    @ApiOperation(value = "接收布勒推送的曲粉筒仓数据")
    @RequestMapping(value = "/receiveBlQuFen", method = RequestMethod.POST)
    public Integer receiveBlQuFen(@RequestBody ReceiveBlQuFenDTO receiveBlQuFenDTO) {
        return qudouService.receiveBlQuFen(receiveBlQuFenDTO);
    }

    @ApiOperation(value = "条件查询需求")
    @RequestMapping(value = "/get/detail/query", method = RequestMethod.POST)
    public List<DemandDetailPageDTO> getDemandDetailQuery(@RequestBody DemandDetailQuery demandDetailQuery){
        return qudouService.getDemandDetailQuery(demandDetailQuery);
    }

    @ApiOperation(value = "查询车间中心跨库存信息")
    @RequestMapping(value = "/getLocationInventory", method = RequestMethod.POST)
    public LocationInventory getLocationInventory(@RequestBody DemandDetailQuery demandDetailQuery){
        return qudouService.getLocationInventory(demandDetailQuery);
    }
}
