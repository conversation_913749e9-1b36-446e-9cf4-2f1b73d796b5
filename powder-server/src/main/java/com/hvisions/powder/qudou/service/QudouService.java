package com.hvisions.powder.qudou.service;

import com.hvisions.powder.dto.qudou.demand.*;
import com.hvisions.powder.qudou.dto.ReceiveBlQuFenDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordDTO;
import com.hvisions.powder.qudou.dto.SingleDemandDetailRecordQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description:大曲
 * @date 2022/4/6 10:18
 */
public interface QudouService {

    /***
     * @Description 分页查询曲粉需求详情
     *
     * <AUTHOR>
     * @Date 2022-11-29 16:08
     * @param queryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.brewage.dto.qudou.demand.DemandDetailPageDTO>
     **/
    Page<DemandDetailPageDTO> getDemandDetailPageList(DemandDetailPageQueryDTO queryDTO);


    /***
     * @Description 曲粉需求新增修改删除操作
     *
     * <AUTHOR>
     * @Date 2022-11-25 13:45
     * @param demandBatchOperateDTO
     * @return java.lang.Integer
     **/
    Integer batchOperateQudouDemand(DemandBatchOperateDTO demandBatchOperateDTO);

    /**
      * @description: 曲粉需求多个中心批量新增操作
      * @author: wy liu
      * @date: 2024/7/12
      * @param demandBatchOperateDTO
      * @return java.lang.Integer
    */
    Integer batchOperateQudouDemand(List<DemandBatchOperateDTO> demandBatchOperateDTO);


    /***
     * @Description 曲粉需求批量报送
     *
     * <AUTHOR>
     * @Date 2022-11-28 11:50
     * @param detailIds
     * @return java.lang.Integer
     **/
    Integer batchSubmitQudouDemand(List<Integer> detailIds);

    /***
     * @Description 批量删除曲粉需求
     *
     * <AUTHOR>
     * @Date 2022-11-29 17:03
     * @param detailIds
     * @return java.lang.Integer
     **/
    Integer batchDeleteQudouDemand(List<Integer> detailIds);

    /***
     * @Description 分页查询曲粉需求详情记录
     *
     * <AUTHOR>
     * @Date 2022-12-8 16:59
     * @param queryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.brewage.qudou.dto.SingleDemandDetailRecordDTO>
     **/
    Page<SingleDemandDetailRecordDTO> getSingleDemandDetailRecordPageList(@RequestBody SingleDemandDetailRecordQueryDTO queryDTO);

    /***
     * @Description 接收布勒推送的曲粉筒仓数据
     *
     * <AUTHOR>
     * @Date 2023-8-31 13:47
     * @param receiveBlQuFenDTO
     * @return java.lang.Integer
     **/
    Integer receiveBlQuFen(ReceiveBlQuFenDTO receiveBlQuFenDTO);


    Integer operateQudouDemandForLuoHan(DemandBatchOperateFroLuoHanDTO demandBatchOperateFroLuoHanDTO);

    /**
      * @描述: 查询打包任务
      * @作者: 刘文勇
      * @日期: 2024/6/4 10:02
      * @参数: packTaskQueryDTO
      * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.PackTaskDTO>
    */
    Page<PackTaskDTO> getPackTack(PackTaskQueryDTO packTaskQueryDTO);

    /**
      * @描述: 完成打包任务
      * @作者: 刘文勇
      * @日期: 2024/6/4 10:13
      * @参数: packTaskDTOList
      * @返回值: java.lang.Boolean
    */
    Boolean completePackTack(List<Integer> ids);

    /**
      * @描述: 条件查询需求
      * @作者: 刘文勇
      * @日期: 2024/9/27 10:20
      * @参数: demandDetailQuery
      * @返回值: java.util.List<com.hvisions.powder.dto.qudou.demand.DemandDetailPageDTO>
    */
    List<DemandDetailPageDTO> getDemandDetailQuery(DemandDetailQuery demandDetailQuery);

    /**
     * 查询车间中心跨库存信息
     * @param demandDetailQuery
     * @return
     */
    LocationInventory getLocationInventory(DemandDetailQuery demandDetailQuery);

    /**
     * 删除翻窖需求明细
     * @param taskNo
     * @param lineName
     * @return
     */
    Integer delTurnoverDemandDetail(String taskNo, String lineName);
}
