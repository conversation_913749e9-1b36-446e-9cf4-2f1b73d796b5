package com.hvisions.powder;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <p>Title: DemoApplication</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *e
 * <AUTHOR>
 * @version :1.0.0
 */
@EnableConfigurationProperties
@EnableTransactionManagement
@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan({"com.hvisions.powder.dao","com.hvisions.brewage.esb.dao","com.hvisions.brewage.auth.mapper","com.hvisions.brewage.esb.dao"})
@EnableFeignClients(basePackages = {"com.hvisions.powder.feign",
        "com.hvisions.brewage.feign",
        "com.hvisions.brewage.client", "com.hvisions.purchase.client", "com.hvisions.log.capture.client", "com.hvisions.auth.client", "com.hvisions.equipmentmsd.client", "com.hvisions.materialsmsd.materials.client", "com.hvisions.schedule.client", "com.hvisions.timer.client", "com.hvisions.print.client","com.hvisions.product"})
@ComponentScan(basePackages = {"com.hvisions.powder",
        "com.hvisions.brewage",
        "com.hvisions.brewage.esb",
        "com.hvisions.brewage.auth",
        "com.hvisions.brewage.client", "com.hvisions.purchase.client", "com.hvisions.log.capture.client.fallback", "com.hvisions.auth.client", "com.hvisions.equipmentmsd.client.fallback", "com.hvisions.materialsmsd.materials.client", "com.hvisions.schedule.client", "com.hvisions.timer.client", "com.hvisions.print.client","com.hvisions.product"})
public class PowderApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(PowderApplication.class, args);
    }

    /**
     * 可以使得项目用war包部署
     * @param builder builder
     * @return builder
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(PowderApplication.class);
    }
}
