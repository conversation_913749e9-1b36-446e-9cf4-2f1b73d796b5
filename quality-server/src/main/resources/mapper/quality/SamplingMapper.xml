<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.quality.quality.dao.SamplingMapper">

    <!-- 分页查询取样任务列表 -->
    <select id="getSamplingPageList" resultType="com.hvisions.quality.dto.quality.sampling.SamplingPageDTO">
        select f.*
        from (
        select a.*, IF(d2.id, d2.id, d.id) top_department_id, d.department_name
        from (
        SELECT s.id, s.sampling_order, e.scene_name, s.state, s.material_name, s.reinspection,
        DATE_FORMAT(s.plan_date, '%Y-%m-%d') plan_date, DATE_FORMAT(s.actual_date, '%Y-%m-%d') actual_date,
        it.sampling_code AS material_code, s.sampling_location, it.inspection_order, it.inspection_type, it.sampling_group_id, sampling_group_code,
        it.lees_type, it.tasting_scene, sc.collect_value source_level, it.department_id, it.task_state,
        s.create_time
        FROM t_qa_sampling s
        LEFT JOIN t_qa_sampling_collect sc ON sc.sampling_id = s.id AND sc.deleted = 0 AND sc.item_name = "糟源层级"
        LEFT JOIN t_qa_specimen_standard ss ON s.standard_id = ss.id
        LEFT JOIN t_qa_scene e ON ss.scene_id = e.id
        INNER JOIN (
        SELECT it.sampling_id, GROUP_CONCAT(it.sampling_code) sampling_code,
        GROUP_CONCAT(it.inspection_order) inspection_order,
        GROUP_CONCAT(it.inspection_type) inspection_type,
        GROUP_CONCAT(it.sampling_group_id) sampling_group_id,
        GROUP_CONCAT(sg.`code`) sampling_group_code,
        GROUP_CONCAT(it.`lees_type`) lees_type,
        GROUP_CONCAT(ta.tasting_scene) tasting_scene,
        GROUP_CONCAT(it.state) task_state,
        GROUP_CONCAT(it.department_id) department_id
        FROM t_qa_inspection_task it
        LEFT JOIN t_qa_tasting ta ON ta.inspection_id = it.id AND ta.deleted = 0
        LEFT JOIN t_qa_sampling_group sg ON sg.id = it.sampling_group_id AND sg.deleted = 0
        WHERE it.deleted = 0
        GROUP BY it.sampling_id
        ) it ON it.sampling_id = s.id
        WHERE s.deleted = 0
        <if test="samplingOrder != null and samplingOrder != ''">
            AND s.sampling_order LIKE CONCAT('%', #{samplingOrder}, '%')
        </if>
        <if test="samplingCode != null and samplingCode != ''">
            AND it.sampling_code LIKE CONCAT('%', #{samplingCode}, '%')
        </if>
        <if test="inspectionOrder != null and inspectionOrder != ''">
            AND it.inspection_order LIKE CONCAT('%', #{inspectionOrder}, '%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND s.material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        <if test="inspectionType != null and inspectionType != ''">
            AND it.inspection_type LIKE CONCAT('%', #{inspectionType}, '%')
        </if>
        <if test="departmentId != null">
            AND it.department_id = #{departmentId}
        </if>
        <if test="sceneId != null">
            AND e.id = #{sceneId}
        </if>
        <if test="sceneCodeList != null and sceneCodeList != ''">
            AND FIND_IN_SET(e.scene_code,#{sceneCodeList})
        </if>
        <if test="state != null and state != ''">
            AND s.state = #{state}
        </if>
        <if test="planDateStart != null">
            AND s.plan_date <![CDATA[ >= ]]> #{planDateStart}
        </if>
        <if test="planDateEnd != null">
            AND s.plan_date <![CDATA[ <= ]]> #{planDateEnd}
        </if>
        <if test="actualDateStart != null">
            AND s.actual_date <![CDATA[ >= ]]> #{actualDateStart}
        </if>
        <if test="actualDateEnd != null">
            AND s.actual_date <![CDATA[ <= ]]> #{actualDateEnd}
        </if>
        <if test="samplingSite != null and samplingSite != ''">
            AND s.sampling_location LIKE CONCAT('%', #{samplingSite}, '%')
        </if>
        <if test="samplingSite == null and samplingSite == ''">
            ORDER BY s.plan_date DESC
        </if>
        ) a
        LEFT JOIN authority.sys_department d ON a.department_id = d.id
        LEFT JOIN authority.sys_department d2 ON d.parent_id = d2.id AND d.parent_id > 0
        ) f
        <where>
            <if test="topDepartmentId != null">
                f.top_department_id = #{topDepartmentId}
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

    <!-- 样品报表列表分页查询 -->
    <select id="getSamplingReportPageList" resultType="com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageDTO">
        SELECT it.id, s.id AS sampling_id, it.sampling_code, s.material_code, s.material_name,e.scene_name,
        mt.material_type_name, s.sampling_people, s.actual_date, it.inspection_name, it.actual_inspection_time,
        it.inspection_order, s.sampling_order
        FROM t_qa_inspection_task it
        LEFT JOIN t_qa_sampling s ON it.sampling_id = s.id
        LEFT JOIN t_qa_specimen_standard ss ON s.standard_id = ss.id
        LEFT JOIN t_qa_scene e ON ss.scene_id = e.id
        LEFT JOIN materials.hv_bm_material m ON s.material_id = m.id
        LEFT JOIN materials.hv_bm_material_type mt ON m.material_type = mt.id
        WHERE it.deleted = 0
        <if test="inspectionOrder != null and inspectionOrder != ''">
            AND it.inspection_order LIKE CONCAT('%', #{inspectionOrder}, '%')
        </if>
        <if test="samplingCode != null and samplingCode != ''">
            AND it.sampling_code LIKE CONCAT('%', #{samplingCode}, '%')
        </if>
        <if test="samplingStartDate != null and samplingEndDate != null">
            AND DATE_FORMAT(s.actual_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{samplingStartDate}, '%Y-%m-%d') AND DATE_FORMAT(#{samplingEndDate}, '%Y-%m-%d')
        </if>
        <if test="inspectionStartDate != null and inspectionEndDate != null">
            AND DATE_FORMAT(it.actual_inspection_time, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{inspectionStartDate}, '%Y-%m-%d') AND DATE_FORMAT(#{inspectionEndDate}, '%Y-%m-%d')
        </if>
        ORDER BY it.create_time DESC
    </select>

    <!-- 根据取样id获取详情数据 -->
    <select id="getSamplingDetail" resultType="com.hvisions.quality.dto.quality.sampling.SamplingDetailDTO">
        SELECT s.id, s.sampling_order, e.scene_name,s.sampling_location, s.state, s.material_code, s.material_name, d.department_name,s.sampling_people_id,s.sampling_people,
        GROUP_CONCAT( it.sampling_code ) sampling_code,
        GROUP_CONCAT( it.id ) inspection_id,
        GROUP_CONCAT( it.inspection_order ) inspection_order,
        GROUP_CONCAT( it.state ) inspect_state,
        GROUP_CONCAT( it.declaration_remark ) declaration_remark,
        GROUP_CONCAT( it.lees_type ) lees_type,
        DATE_FORMAT( s.plan_date, '%Y-%m-%d' ) plan_date,
        DATE_FORMAT( s.actual_date, '%Y-%m-%d' ) actual_date,
        ss.standard_count, ss.standard_weight, sp.packing_type_name, sl.specimen_location,
        ss.label_position, ss.id AS standard_id,
        (
            SELECT h.stage_num FROM t_po_workshop_handin_task h
            WHERE  FIND_IN_SET(h.handin_task_code,GROUP_CONCAT( it.associated_document ))
        ) stage_num,
        GROUP_CONCAT(ta.tasting_scene) tasting_scene,
        (SELECT GROUP_CONCAT(id.indicator_name) FROM t_qa_inspection_data id WHERE FIND_IN_SET(id.inspection_id,GROUP_CONCAT( it.id ))) indicators

        FROM t_qa_sampling s
        LEFT JOIN t_qa_specimen_standard ss ON s.standard_id = ss.id AND ss.deleted = 0
        LEFT JOIN t_qa_specimen_packing sp ON ss.packing_id = sp.id AND sp.deleted = 0
        LEFT JOIN t_qa_specimen_location sl ON ss.location_id = sl.id AND sl.deleted = 0
        LEFT JOIN t_qa_scene e ON ss.scene_id = e.id AND e.deleted = 0
        LEFT JOIN authority.sys_user u ON u.id = s.creator_id
        LEFT JOIN authority.sys_department d ON u.department_id = d.id
        LEFT JOIN t_qa_inspection_task it ON it.sampling_id = s.id AND it.deleted = 0
        LEFT JOIN t_qa_tasting ta ON ta.inspection_id = it.id AND ta.deleted = 0
        WHERE s.deleted = 0 AND s.id = #{id}
    </select>

    <!-- 获取样品打印列表 -->
    <select id="getLabelPrintCountList" resultType="com.hvisions.quality.dto.quality.sampling.LabelPrintCountListDTO">
        SELECT it.id, it.sampling_code, it.sampling_name, it.print_count
        FROM t_qa_sampling s
        INNER JOIN t_qa_inspection_task it ON it.sampling_id = s.id AND it.deleted = 0
        WHERE s.deleted = 0 AND s.id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 根据取样任务id，获取未打印条码的任务 -->
    <select id="getNotPrintTask" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM `t_qa_inspection_task`
        WHERE sampling_id = #{samplingId} AND deleted = 0 AND print_count = 0
    </select>
    <select id="getSamplingAppletSelect" resultType="com.hvisions.quality.sap.dto.applet.SamplingAppletDTO">
        SELECT t.id, ul1.user_account createBy, tc.temp_tank_category_name zaoyuanCategory, l.`code` workshopCode, tt.workshop_tmp_tank_code jarCode, t.`level` grading, t.end_time, t.location_id,
               it.sampling_code `code`, ul2.user_account samplingBy, s.actual_date samplingTime, qt.tasting_scene ratedType, t.fermentation_period fermentingTime, t.pit_age, t.handin_task_code handinTaskCode
        FROM `t_qa_inspection_task` it
                 left join t_qa_sampling s on s.id = it.sampling_id
                 left join t_qa_tasting qt on qt.inspection_id = it.id
                 left join brewage.t_po_workshop_handin_task t on t.qm_order_code = it.sampling_code
                 left join brewage.t_po_workshop_tmp_tank tt on t.tmptank_id = tt.id
                 left join brewage.t_po_workshop_tmp_tank_category tc on tc.id = tt.workshop_tmp_tank_category_id
                 left join authority.sys_user_login ul1 on ul1.user_id = t.send_userid
                 left join authority.sys_user_login ul2 on ul2.user_id = s.sampling_people_id
                 left join equipment.hv_bm_location l on l.id = tt.work_shop
        where t.is_delete = 0 and it.deleted = 0 and it.sampling_code = #{samplingCode}
        order by t.id desc, it.id desc
        limit 1
    </select>
    <select id="getPlanRowByDate" resultType="java.lang.String">
        select r.`name` rowName
        from brewage_plan.t_pp_month_plan m
        left join brewage_plan.t_pp_scheduling s on s.id = m.scheduling_id
        left join brewage_plan.t_pp_scheduling_row sr on s.id = sr.scheduling_id and s.deleted = 0
        left join brewage_plan.t_pp_row r on r.id = sr.row_id and r.deleted = 0
        where sr.deleted = 0 and sr.use_state = 1 and s.production_base_ids = 1 and m.location_id = #{locationId}
        and sr.begin_time <![CDATA[ <= ]]> #{dateTime} and sr.end_time <![CDATA[ >= ]]> #{dateTime}
        and m.month_begin_date <![CDATA[ <= ]]> #{dateTime} and m.month_end_date <![CDATA[ >= ]]> #{dateTime}
        limit 1
    </select>

</mapper>
