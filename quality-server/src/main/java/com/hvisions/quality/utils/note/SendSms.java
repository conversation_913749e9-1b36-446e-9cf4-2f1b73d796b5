package com.hvisions.quality.utils.note;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * Tencent Cloud Sms Sendsms
 */
@Slf4j
@Component
public class SendSms {

    @Resource
    private UserClient userClient;

    /**
     * 发送短信
     *
     * @param phoneNumberSet
     */
    public static void sendNote(String templateId, String[] templateParamSet, String[] phoneNumberSet) {
        try {
            String secretId = "AKIDCD3hCdwKYaQIxPm4DBwzoQC0Pgrz0pfV";
            String secretKey = "jNxMYMwJSN3XnjT3QpkmhR1TuBFCtCNJ";
            Credential cred = new Credential(secretId, secretKey);

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("POST");
            httpProfile.setConnTimeout(60);
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            ClientProfile clientProfile = new ClientProfile();

            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);

            SendSmsRequest req = new SendSmsRequest();

            String sdkAppId = "1400747897";
            req.setSmsSdkAppId(sdkAppId);

            String signName = "泸州老窖";
            req.setSignName(signName);

            req.setTemplateId(templateId);
            req.setTemplateParamSet(templateParamSet);
            req.setPhoneNumberSet(phoneNumberSet);

            String sessionContext = "";
            req.setSessionContext(sessionContext);

            String extendCode = "";
            req.setExtendCode(extendCode);

            String senderid = "";
            req.setSenderId(senderid);
            log.info("************send sms************** request: " + JSONObject.toJSONString(req));
            SendSmsResponse res = client.SendSms(req);

            // 输出json格式的字符串回包
            String response = SendSmsResponse.toJsonString(res);
            System.out.println(response);
            SmsVo smsVo = JSONObject.parseObject(response, SmsVo.class);
            Set<SmsVo.SendStatusSet> sets = smsVo.getSendStatusSet();
            for (SmsVo.SendStatusSet set : sets) {
                String code = set.getCode();
                log.info("************send sms end result : " + code + " message : " + set.getMessage());
            }
        } catch (TencentCloudSDKException e) {
            log.info("************send sms function error : " + e.getMessage());
        }
    }

    public void sendUserMessage(Integer userId, String template, String[] msg) {
        try {
            UserDTO userDTO = userClient.getUser(userId).getData();
            List<String> phones = new ArrayList<>();
            phones.add("+86" + userDTO.getMobilePhone());
            SendSms.sendNote(template, msg, phones.toArray(new String[phones.size()]));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        String[] templateParamSet = {"123456"};
        String[] phoneNumberSet = {"+8615950377162"};
        sendNote("", templateParamSet, phoneNumberSet);
    }
}
