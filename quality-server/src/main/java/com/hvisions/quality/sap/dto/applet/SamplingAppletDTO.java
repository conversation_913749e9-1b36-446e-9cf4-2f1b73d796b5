package com.hvisions.quality.sap.dto.applet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "同步交酒预填数据发送小程序DTO")
public class SamplingAppletDTO {

    @ApiModelProperty(value = "样品编码（年份-酒罐编码-流水码）")
    private String code;

    @ApiModelProperty(value = "酒罐编码")
    private String jarCode;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "发酵时间（天）")
    private Integer fermentingTime;

    @ApiModelProperty(value = "窖龄")
    private Integer pitAge;

    @ApiModelProperty(value = "糟源类别")
    private String zaoyuanCategory;

    @ApiModelProperty(value = "交酒提报人(工号)")
    private String createBy;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "定级方式:集中定级(Concentrated)or现场定级(OnSite)")
    private String ratedType;

    @ApiModelProperty(value = "采样批次 固定传1")
    private String batchNum = "1";

    @ApiModelProperty(value = "采样时间")
    private String samplingTime;

    @ApiModelProperty(value = "采样人员(工号)")
    private String samplingBy;

    @ApiModelProperty(value = "计划年份(样品编码前面部分)")
    private String planYear;

    @ApiModelProperty(value = "原度重量")
    private String originalWeight;

    @ApiModelProperty(value = "交酒任务编号")
    private String handinTaskCode;

    @ApiModelProperty(value = "定级数据--现场定级时传输")
    private String grading;

    @ApiModelProperty(value = "交酒任务编号")
    private Date endTime;

    @ApiModelProperty(value = "车间id")
    private String locationId;

    @ApiModelProperty(value = "排次信息")
    private String orderTrans;
}
