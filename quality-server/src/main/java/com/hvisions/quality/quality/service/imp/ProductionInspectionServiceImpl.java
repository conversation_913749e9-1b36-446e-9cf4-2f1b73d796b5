package com.hvisions.quality.quality.service.imp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.auth.client.MessageClient;
import com.hvisions.auth.dto.message.MessageCreateDTO;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.brewage.client.WineHandInClient;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.quality.advice.UserAuditorAware;
import com.hvisions.quality.configuration.ThreadPoolExecutorFactory;
import com.hvisions.quality.dto.quality.inspection.data.InspectionDataDetailDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.IncomingInspectionTaskDetailDTO;
import com.hvisions.quality.dto.quality.inspection.production.*;
import com.hvisions.quality.dto.quality.inspection.production.infrared.*;
import com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckDetailDTO;
import com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckPageDTO;
import com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckPageQueryDTO;
import com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckResultDTO;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectListDTO;
import com.hvisions.quality.quality.consts.*;
import com.hvisions.quality.quality.dao.*;
import com.hvisions.quality.quality.entity.*;
import com.hvisions.quality.quality.service.IncomingInspectionService;
import com.hvisions.quality.quality.service.MessageService;
import com.hvisions.quality.quality.service.ProductionInspectionService;
import com.hvisions.quality.sap.RequestApplet;
import com.hvisions.quality.sap.constant.EsbConst;
import com.hvisions.quality.sap.dto.applet.AppletBaseDataDTO;
import com.hvisions.quality.sap.dto.applet.AppletBaseResponseDto;
import com.hvisions.quality.sap.dto.applet.SamplingAppletResultDTO;
import com.hvisions.quality.sap.dto.applet.SamplingAppletSendDTO;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.StringUtil;
import com.hvisions.quality.utils.note.SendSms;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.hvisions.quality.consts.CommonConsts.LOGIN_HINT;

/**
 * <AUTHOR>
 * @description: 生产过程检验
 * @date 2022/3/15 9:26
 */
@Slf4j
@Service
public class ProductionInspectionServiceImpl implements ProductionInspectionService {

    @Resource
    private ProductionInspectionMapper productionInspectionMapper;

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;

    @Resource
    private ReceiveGroupInspectionMapper receiveGroupInspectionMapper;

    @Resource
    private IncomingInspectionService incomingInspectionService;

    @Resource
    private InspectionDataMapper inspectionDataMapper;

    @Resource
    private InspectionDataRecordMapper inspectionDataRecordMapper;

    @Resource
    private InspectionDataItemMapper inspectionDataItemMapper;

    @Resource
    private SamplingCollectMapper samplingCollectMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private MessageClient messageClient;

    @Resource
    private SamplingMapper samplingMapper;


    @Resource
    private CollectItemMapper collectItemMapper;

    @Resource
    private SendSms sendSms;

    @Resource
    private WineHandInClient wineHandInClient;

    @Resource
    private RequestApplet requestApplet;


//    @Resource
//    private SapService sapService;
//
//    @Resource
//    private TPoWorkshopHandinTaskMapper tPoWorkshopHandinTaskMapper;


    /*
     * @description: 分页查询生产过程检验任务列表
     * <AUTHOR>
     * @date 2022/3/15 10:46
     * @param pageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.inspection.production.ProductionInspectionPageDTO>
     */
    @Override
    public Page<ProductionInspectionPageDTO> getProductionInspectionPageList(ProductionInspectionPageQueryDTO pageQueryDTO) {
        Page<ProductionInspectionPageDTO> result = PageHelperUtil.getPage(productionInspectionMapper::getProductionInspectionPageList, pageQueryDTO, ProductionInspectionPageDTO.class);
        for (ProductionInspectionPageDTO dto : result.getContent()) {
            dto.setGroupCodes(productionInspectionMapper.selectGroupCodeById(dto.getId()));
        }
        return result;
    }

    /*
     * @description: 分页查询生产过程检验复检任务列表
     * <AUTHOR>
     * @date 2022/3/25 10:29
     * @param pageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckPageDTO>
     */
    @Override
    public Page<ProductionRecheckPageDTO> getProductionRecheckPageList(ProductionRecheckPageQueryDTO pageQueryDTO) {
        return PageHelperUtil.getPage(productionInspectionMapper::getProductionRecheckPageList, pageQueryDTO, ProductionRecheckPageDTO.class);
    }

    /*
     * @description: 获取生产过程检验单数据
     * <AUTHOR>
     * @date 2022/3/15 13:12
     * @param inspectionId
     * @return com.hvisions.quality.dto.quality.inspection.production.ProductionInspectionDetailDTO
     */
    @Override
    public ProductionInspectionDetailDTO getProductionInspectionDetail(Integer inspectionId) {
        ProductionInspectionDetailDTO productionInspectionDetailDTO = new ProductionInspectionDetailDTO();

        ProductionInspectionTaskDTO productionInspectionTaskDTO = productionInspectionMapper.getProductionInspectionTask(inspectionId);

        List<InspectionDataDetailDTO> inspectionDataDetailDTOList = productionInspectionMapper.getProductionInspectionDataRecheckByInspectionIdCopy(inspectionId);
        if (StringUtil.isEmpty(inspectionDataDetailDTOList) || inspectionDataDetailDTOList.size() == 0) {
//            如果没有复检数据，获取检验单数据
            inspectionDataDetailDTOList = productionInspectionMapper.getProductionInspectionDataByInspectionIdCopy(inspectionId);
        }

//        if (productionInspectionTaskDTO.getSceneName().contains("微生物")) {
//            inspectionDataDetailDTOList.forEach(inspectionData -> {
//
//                if (StringUtil.isNotEmpty(inspectionData.getValue())) {
//                    double bigNumber = Double.parseDouble(inspectionData.getValue());
//                    // 科学计数
//                    inspectionData.setValue(ScientificNotationFormatter.formatToScientificNotation(bigNumber));
//                }
//            });
//        }

        if (productionInspectionTaskDTO.getSamplingId() != null && productionInspectionTaskDTO.getSamplingId() > 0) {
            // 根据取样id获取取样采集数据列表
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, productionInspectionTaskDTO.getSamplingId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            productionInspectionDetailDTO.setSamplingCollectListDTOS(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));

        }
        productionInspectionDetailDTO.setProductionInspectionTaskDTO(productionInspectionTaskDTO);
        productionInspectionDetailDTO.setInspectionDataDetailDTOList(inspectionDataDetailDTOList);
        return productionInspectionDetailDTO;
    }

    @Override
    public List<ProductionInspectionDetailDTO> getProductionInspectionDetails(List<Integer> inspectionIds) {

        List<ProductionInspectionDetailDTO> productionInspectionDetailDTOS = new ArrayList<>();
        for (Integer inspectionId : inspectionIds) {
            ProductionInspectionDetailDTO productionInspectionDetail = this.getProductionInspectionDetail(inspectionId);
            productionInspectionDetailDTOS.add(productionInspectionDetail);
        }
        return productionInspectionDetailDTOS;
    }

    /*
     * @description: 根据样品编码获取生产过程复检详情
     * <AUTHOR>
     * @date 2022/3/25 11:25
     * @param inspectionId
     * @return com.hvisions.quality.dto.quality.inspection.production.recheck.ProductionRecheckDetailDTO
     */
    @Override
    public ProductionRecheckDetailDTO getProductionRecheckDetail(String sampleCode) {
        ProductionRecheckDetailDTO productionRecheckDetailDTO = new ProductionRecheckDetailDTO();

        ProductionInspectionTaskDTO productionInspectionTaskDTO = productionInspectionMapper.getProductionInspectionTaskBySampleCode(sampleCode);

        if (StringUtil.isEmpty(productionInspectionTaskDTO)) {
            throw new BaseKnownException(10000, "条码错误！");
        }
        if (StringUtil.isNotEmpty(productionInspectionTaskDTO) && productionInspectionTaskDTO.getId() > 0) {
            List<InspectionDataDetailDTO> recheckInspectionDataDetailDTOList = productionInspectionMapper.getProductionInspectionDataRecheckByInspectionId(productionInspectionTaskDTO.getId());
            List<InspectionDataDetailDTO> inspectionDataDetailDTOList = productionInspectionMapper.getProductionInspectionDataByInspectionId(productionInspectionTaskDTO.getId());
            productionRecheckDetailDTO.setInspectionDataDetailDTOList(inspectionDataDetailDTOList);
            productionRecheckDetailDTO.setRecheckInspectionDataDetailDTOList(recheckInspectionDataDetailDTOList);
        }

        if (productionInspectionTaskDTO.getSamplingId() != null && productionInspectionTaskDTO.getSamplingId() > 0) {
            // 根据取样id获取取样采集数据列表
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, productionInspectionTaskDTO.getSamplingId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            productionRecheckDetailDTO.setSamplingCollectListDTOS(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));

        }
        productionRecheckDetailDTO.setProductionInspectionTaskDTO(productionInspectionTaskDTO);

        return productionRecheckDetailDTO;
    }

    /**
     * @Description 大曲理化指标值保留位数 四舍六入五成双
     *
     * <AUTHOR>
     * @Date 2024-7-26 15:14
     * @param indicatorName 指标名称
     * @param value 指标值
     * @return java.lang.String
     **/
    @Override
    public String powderIndicatorFormat(String indicatorName, String value) {
        if (StringUtil.isEmpty(value)) {
            return value;
        }
        BigDecimal v = new BigDecimal(value);
        if ("糖化力".equals(indicatorName) || "酯化力".equals(indicatorName)) {
            return v.setScale(0, RoundingMode.HALF_EVEN).toPlainString();
        } else if ("液化力".equals(indicatorName) || "发酵力".equals(indicatorName)) {
            return v.setScale(2, RoundingMode.HALF_EVEN).toPlainString();

        } else if ("淀粉".equals(indicatorName) || "水分".equals(indicatorName) || "酸度".equals(indicatorName)) {
            return v.setScale(1, RoundingMode.HALF_EVEN).toPlainString();
        }
        return value;

    }

    /*
     * @description: 生产过程检验数据录入
     * <AUTHOR>
     * @date 2022/3/15 14:02
     * @param productionInspectionResultDTO
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer enterInspectionResult(ProductionInspectionResultDTO productionInspectionResultDTO) {
        int flag = 0;
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(productionInspectionResultDTO.getId());
        inspectionTask.setInspectionRemark(productionInspectionResultDTO.getInspectionRemark());
        inspectionTask.setCheckoutEquipmentId(productionInspectionResultDTO.getCheckoutEquipmentId());
        inspectionTask.setSampleNo(productionInspectionResultDTO.getSampleNo());
        inspectionTask.setActualInspectionTime(new Date());
        //新增标识为三方样
        if("基酒".equals(inspectionTask.getSamplingName())){
            inspectionTask.setIsTripartite(1);
        }

        List<InspectionDataDetailDTO> inspectionDataDTOList = productionInspectionResultDTO.getInspectionDataDetailDTOList();
        if (inspectionDataDTOList != null && inspectionDataDTOList.size() > 0) {

            List<TQaInspectionData> inspectionDataList=new ArrayList<>();
            for (InspectionDataDetailDTO inspectionDataDetailDTO : inspectionDataDTOList) {
                TQaInspectionData inspectionData = inspectionDataMapper.selectById(inspectionDataDetailDTO.getId());
                inspectionData.setUpdateTime(new Date());
                if ("大曲理化".equals(inspectionData.getIndicatorType())) {
                    inspectionData.setValue(powderIndicatorFormat(inspectionData.getIndicatorName(), inspectionDataDetailDTO.getValue()));
                } else {
                    inspectionData.setValue(inspectionDataDetailDTO.getValue());
                }
                flag += inspectionDataMapper.updateById(inspectionData);

                inspectionDataList.add(inspectionData);

            }

            if("基酒".equals(inspectionTask.getSamplingName())){
                //留存记录
                log.info("开始新增基酒记录---手工录入数据");
                LambdaQueryWrapper<TQaInspectionDataRecord> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TQaInspectionDataRecord::getSampleCode,inspectionTask.getSamplingCode())
                        .eq(TQaInspectionDataRecord::getType,2)
                        .eq(TQaInspectionDataRecord::getDeleted,0);
                TQaInspectionDataRecord inspectionDataRecord = inspectionDataRecordMapper.selectOne(wrapper);
                if(null==inspectionDataRecord){
                    inspectionDataRecord=new TQaInspectionDataRecord();
                    inspectionDataRecord.setSampleCode(inspectionTask.getSamplingCode());
                    inspectionDataRecord.setInspectionId(inspectionTask.getId());
                    inspectionDataRecord.setType("2");
                }

                //新增记录数据
                for (TQaInspectionData inspectionData : inspectionDataList) {
                    assignMidValue(inspectionDataRecord,inspectionData);
                }

                if(null==inspectionDataRecord.getId()){
                    inspectionDataRecordMapper.insert(inspectionDataRecord);
                }else {
                    inspectionDataRecordMapper.updateById(inspectionDataRecord);
                }

//                log.info("开始更改基酒预处理记录---手工录入数据");
//                inspectionDataList.forEach(v->{
//                    LambdaQueryWrapper<TQaInspectionData> wrapper1 = new LambdaQueryWrapper<>();
//                    wrapper1.eq(TQaInspectionData::getInspectionId,v.getInspectionId())
//                            .eq(TQaInspectionData::getIndicatorCode,v.getIndicatorCode())
//                            .eq(TQaInspectionData::getDeleted,0)
//                            .eq(TQaInspectionData::getType,InspectionDataState.PRETREATMENT);
//                    TQaInspectionData tQaInspectionData = inspectionDataMapper.selectOne(wrapper1);
//                    if(null!=tQaInspectionData){
//                        tQaInspectionData.setValue(v.getValue());
//                        tQaInspectionData.setUpdateTime(new Date());
//                        inspectionDataMapper.updateById(tQaInspectionData);
//                    }
//                });
            }

            // 判断检验单的指标是否全部录入，如果全部录了，
            List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                    .eq(TQaInspectionData::getDeleted, 0)
                    .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                    .eq(TQaInspectionData::getType, 0)
            );
            long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
            if (count == 0) {
                inspectionTask.setIndicatorIsFull(true);
            } else {
                inspectionTask.setIndicatorIsFull(false);
            }

            //数据录入状态
            if (inspectionDatas.stream().filter(i -> StringUtils.isNotBlank(i.getValue())).count() == inspectionDatas.size()) {
                //已经全部录入完了
                inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.ENTRY_FINISH);
            } else if (inspectionDatas.stream().filter(i -> "色谱".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                //色谱未录入完整
                inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.CHROMATOGRAPHY_NOT_ENTRY);
            } else if (inspectionDatas.stream().filter(i -> "基酒理化".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                //色谱未录入完整
                inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.PHYSICOCHEMICAL_NOT_ENTRY);
            }
        }
        if (productionInspectionResultDTO.getFlag()) { // 提交
            inspectionTask.setInspectionName(productionInspectionResultDTO.getInspectionName());
            inspectionTask.setState(InspectionTaskState.AWAIT_AUDIT);

            LambdaQueryWrapper<TQaReceiveGroupInspection> wrapper = Wrappers.<TQaReceiveGroupInspection>lambdaQuery()
                    .eq(TQaReceiveGroupInspection::getInspectionId, inspectionTask.getId())
//                    .eq(TQaReceiveGroupInspection::getGroupId, inspectionTask.getGroupId())
                    .eq(TQaReceiveGroupInspection::getDeleted, "0");

            // 取消检验任务绑定的组合
            receiveGroupInspectionMapper.delete(wrapper);

            // 检验提交，发送消息给对应检验组长
            SendMessageDTO sendMessageDTO = new SendMessageDTO();
            sendMessageDTO.setTitle("检验完成");
            sendMessageDTO.setContent(inspectionTask.getSamplingCode() + "检验完成，待审核");
            if (inspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.FERMENTED_GRAIN_INSPECTION)) {
                sendMessageDTO.setRoleCode(MessageRole.Role.FermentedLeader);

            } else if (inspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.BASE_WINE_INSPECTION)) {
                sendMessageDTO.setRoleCode(MessageRole.Role.PCALeader);
            }
            messageService.sendMessage(sendMessageDTO);

        } else { // 保存
            inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
        }
        inspectionTask.setUpdateTime(new Date());
        flag += inspectionTaskMapper.updateById(inspectionTask);

        return flag;
    }

    @Override
    public Integer batchSubmit(ProductionInspectionBatchSubmitDTO batchSubmitDTO) {
        int flag = 0;
        for (Integer id : batchSubmitDTO.getIds()) {
            TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(id);
            inspectionTask.setInspectionName(batchSubmitDTO.getInspectionName());
            inspectionTask.setState(InspectionTaskState.AWAIT_AUDIT);
            inspectionTask.setUpdateTime(new Date());

            LambdaQueryWrapper<TQaReceiveGroupInspection> wrapper = Wrappers.<TQaReceiveGroupInspection>lambdaQuery()
                    .eq(TQaReceiveGroupInspection::getInspectionId, inspectionTask.getId())
//                    .eq(TQaReceiveGroupInspection::getGroupId, inspectionTask.getGroupId())
                    .eq(TQaReceiveGroupInspection::getDeleted, "0");

            // 取消检验任务绑定的组合
            flag += receiveGroupInspectionMapper.delete(wrapper);

            flag += inspectionTaskMapper.updateById(inspectionTask);

        }

        return flag;
    }

    @Override
    @Transactional
    public Integer enterRecheckResult(ProductionRecheckResultDTO recheckResultDTO) {
        int flag = 0;
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(recheckResultDTO.getId());
        inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);
        inspectionTask.setInspectionResult(InspectionTaskState.Result.QUALIFIED);
        inspectionTask.setRecheck(InspectionTaskState.IsRecheck.RECHECK);
        inspectionTask.setUpdateTime(new Date());
        flag += inspectionTaskMapper.updateById(inspectionTask);
        List<InspectionDataDetailDTO> inspectionDataDTOList = recheckResultDTO.getInspectionDataDetailDTOList();
        if (inspectionDataDTOList != null && inspectionDataDTOList.size() > 0) {
            // 新增一份复检数据
            for (InspectionDataDetailDTO inspectionDataDetailDTO : inspectionDataDTOList) {
                TQaInspectionData inspectionData = inspectionDataMapper.selectById(inspectionDataDetailDTO.getId());
                inspectionData.setValue(inspectionDataDetailDTO.getValue());
                inspectionData.setType(InspectionDataState.RECHECK_DATA);
                inspectionData.setCreateTime(new Date());
                flag += inspectionDataMapper.insert(inspectionData);
                List<String> itemList = inspectionDataDetailDTO.getItemList();
                if (StringUtil.isNotEmpty(itemList) && itemList.size() > 0) {
                    for (String s : itemList) {
                        TQaInspectionDataItem inspectionDataItem = new TQaInspectionDataItem();
                        inspectionDataItem.setCreateTime(new Date());
                        inspectionDataItem.setInspectionDataId(inspectionData.getId());
                        inspectionDataItem.setItemName(s);
                        flag += inspectionDataItemMapper.insert(inspectionDataItem);
                    }
                }
            }
        }

        return flag;
    }

    /*
     * @description: 审核检验任务
     * <AUTHOR>
     * @date 2022/3/16 9:19
     * @param productionInspectionReviewDTO
     * @return java.lang.Integer
     */
    @Override
    public Integer review(List<ProductionInspectionReviewDTO> productionInspectionReviewDTOs) {
        int flag = 0;
        if (!productionInspectionReviewDTOs.isEmpty()) {
            log.info("开始批量审批检验任务，提交总数:" + productionInspectionReviewDTOs.size());
            AtomicInteger atomicInteger = new AtomicInteger(0);
            for (ProductionInspectionReviewDTO productionInspectionReviewDTO : productionInspectionReviewDTOs) {
                ThreadPoolExecutorFactory.getInstance().execute(() -> {
                    approveExtracted(productionInspectionReviewDTO, atomicInteger);
                    //通常提交数量在100以上，阻塞增加耗时，防止创建线程过多被拒绝或者导致OOM
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        log.info("批量审批，线程休眠出错:" + e.getMessage());
                    }
                });
            }
        }
        return flag;
    }

    /**
     * 执行审批
     *
     * @param productionInspectionReviewDTO
     * @param atomicInteger
     */
    private void approveExtracted(ProductionInspectionReviewDTO productionInspectionReviewDTO, AtomicInteger atomicInteger) {
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(productionInspectionReviewDTO.getId());
        inspectionTask.setActualInspectionTime(new Date());
        if (!productionInspectionReviewDTO.getFlag()) { // 组长审核
            inspectionTask.setLeaderReviewResult(productionInspectionReviewDTO.getResult());
            inspectionTask.setLeaderReviewComments(productionInspectionReviewDTO.getComments());
            if (productionInspectionReviewDTO.getResult().equals(InspectionTaskState.ReviewResult.PASS)) {
                inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);
                inspectionTask.setInspectionResult(InspectionTaskState.Result.QUALIFIED);

                // 给取样人发送检验完成通知
                try {
                    String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(inspectionTask.getId());

                    if (SceneNameConst.POWDER_MONTH_LH.equals(sceneName)
                            || SceneNameConst.POWDER_WSW.equals(sceneName)
                            || SceneNameConst.POWDER_SY_LH.equals(sceneName)
                            || SceneNameConst.POWDER_P_SY_WSW.equals(sceneName)
                            || SceneNameConst.POWDER_P_SY_LH.equals(sceneName)) {
                        TQaSampling sampling = samplingMapper.selectById(inspectionTask.getSamplingId());

                        String[] msg = new String[]{
                                sampling.getSamplingPeople(),
                                DateUtil.format(inspectionTask.getReceiveData(), "yyyy-MM-dd"),
                                inspectionTask.getSamplingCode(),
                                sceneName
                        };
                        // 发送检验完成通知，要根据检验场景的
                        sendSms.sendUserMessage(sampling.getSamplingPeopleId(), "2466427", msg);
                    }
                } catch (Exception e) {
                    log.info("给取样人发送检验完成通知失败：{}", e.getMessage());
                }
                samplingGroupSync(inspectionTask);
            } else {
                inspectionTask.setState(InspectionTaskState.IN_INSPECTION);

                if (InspectionTaskState.InspectionType.POWDER_INSPECTION.equals(inspectionTask.getInspectionType())) {
                    // 曲粉检验，组长审核驳回， 发送小铃铛消息：角色编码 DQLHJYZY DQLHJYZZ DQWSWJYY DQWSWJYZZ 发送组长驳回消息
                    MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
                    messageCreateDTO.setRoleIds(Arrays.asList(128, 129, 130, 131));
                    messageCreateDTO.setTitle("组长审核驳回");
                    messageCreateDTO.setContent(inspectionTask.getSamplingCode() + "被组长审核驳回，请查看！");
                    messageClient.createMessage(messageCreateDTO);
                }
            }

        } else { // 经理审核
            if (productionInspectionReviewDTO.getResult().equals(InspectionTaskState.ReviewResult.PASS)) {
                inspectionTask.setInspectionResult(InspectionTaskState.Result.QUALIFIED);
                inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);

                // 给取样人发送检验完成通知
                try {
                    String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(inspectionTask.getId());

                    if (SceneNameConst.POWDER_MONTH_LH.equals(sceneName)
                            || SceneNameConst.POWDER_WSW.equals(sceneName)
                            || SceneNameConst.POWDER_SY_LH.equals(sceneName)
                            || SceneNameConst.POWDER_P_SY_WSW.equals(sceneName)
                            || SceneNameConst.POWDER_P_SY_LH.equals(sceneName)) {
                        TQaSampling sampling = samplingMapper.selectById(inspectionTask.getSamplingId());

                        String[] msg = new String[]{
                                sampling.getSamplingPeople(),
                                DateUtil.format(inspectionTask.getReceiveData(), "yyyy-MM-dd"),
                                inspectionTask.getSamplingCode(),
                                sceneName
                        };
                        // 发送检验完成通知
                        sendSms.sendUserMessage(sampling.getSamplingPeopleId(), "2466427", msg);
                    }
                } catch (Exception e) {
                    log.info("给取样人发送检验完成通知失败：{}", e.getMessage());
                }
                samplingGroupSync(inspectionTask);

            } else {
                inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
            }
            inspectionTask.setManagerReviewResult(productionInspectionReviewDTO.getResult());
            inspectionTask.setManagerReviewComments(productionInspectionReviewDTO.getComments());
        }
        productionInspectionMapper.updateById(inspectionTask);
        atomicInteger.addAndGet(1);
        log.info("批量审批检验任务执行结束，当前审核总数:" + atomicInteger.get());
        if ("2".equals(inspectionTask.getInspectionType()) && productionInspectionReviewDTO.getResult().equals(InspectionTaskState.ReviewResult.PASS)) {
            //如果是基酒检验，审核通过，将结果发送给小程序
            sendInspectionToSamplingApplet(inspectionTask.getId());
        }
    }

    /**
     * 数据推送到小程序
     * @param taskId
     */
    @Override
    public AppletBaseResponseDto sendInspectionToSamplingApplet(Integer taskId) {
        TQaInspectionTask task = inspectionTaskMapper.selectById(taskId);
        List<InspectionDataDetailDTO> inspectionDataDetailList = inspectionTaskMapper.getInspectionDataByInspectionId(taskId);
        if (!CollectionUtils.isEmpty(inspectionDataDetailList)) {
            SamplingAppletResultDTO samplingAppletResultDTO = new SamplingAppletResultDTO();
            samplingAppletResultDTO.setCode(task.getSamplingCode());
            List<String> jisuanYizhiList = inspectionDataDetailList.stream().filter(d -> "己酸乙酯".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(jisuanYizhiList)) {
                log.info("发送理化数据到小程序,己酸乙酯数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setJisuanYizhi(jisuanYizhiList.get(0));
            }
            List<String> rusuanYizhiList = inspectionDataDetailList.stream().filter(d -> "乳酸乙酯".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rusuanYizhiList)) {
                log.info("发送理化数据到小程序,乳酸乙酯数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setRusuanYizhi(rusuanYizhiList.get(0));
            }
            List<String> yisuanYizhiList = inspectionDataDetailList.stream().filter(d -> "乙酸乙酯".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(yisuanYizhiList)) {
                log.info("发送理化数据到小程序,乙酸乙酯数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setYisuanYizhi(yisuanYizhiList.get(0));
            }
            List<String> dingsuanYizhiList = inspectionDataDetailList.stream().filter(d -> "丁酸乙酯".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dingsuanYizhiList)) {
                log.info("发送理化数据到小程序,丁酸乙酯数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setDingsuanYizhi(dingsuanYizhiList.get(0));
            }
            List<String> zongSuanList = inspectionDataDetailList.stream().filter(d -> "总酸".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(zongSuanList)) {
                log.info("发送理化数据到小程序,总酸数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setZongSuan(zongSuanList.get(0));
            }
            List<String> zongZhiList = inspectionDataDetailList.stream().filter(d -> "总酯".equals(d.getIndicatorName()) && StringUtils.isNotBlank(d.getValue())).map(InspectionDataDetailDTO::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(zongZhiList)) {
                log.info("发送理化数据到小程序,总酯数据不存在,样品编码:{}", task.getSamplingCode());
            } else {
                samplingAppletResultDTO.setZongZhi(zongZhiList.get(0));
            }
            AppletBaseDataDTO baseDataDTO = new AppletBaseDataDTO();
            BeanUtils.copyProperties(samplingAppletResultDTO, baseDataDTO);
            //获取交酒任务数据
            SamplingAppletResultDTO handInTaskDto = inspectionTaskMapper.selectHandInTaskByInspectionTask(task.getId());
            baseDataDTO.setAlcoholContent(handInTaskDto.getAlcoholContent());
            baseDataDTO.setEqualsWeight(handInTaskDto.getEqualsWeight());
            return requestApplet.dockingApplet(baseDataDTO, EsbConst.APPLET_SAMPLING_RESULT_SEND_URI, "同步理化数据");
        }
        return null;
    }

    /***
     * @Description 取样组合下数据同步修改
     *
     * <AUTHOR>
     * @Date 2022-10-22 13:41
     * @param finishTask 检验完成数据
     * @return void
     **/
    public void samplingGroupSync(TQaInspectionTask finishTask) {
        /**
         * 1、更新检验任务状态为完成，检验时间一样
         * 2、同步检验单检验数据
         */

        List<TQaInspectionTask> inspectionTasks = inspectionTaskMapper.selectList(Wrappers.<TQaInspectionTask>lambdaQuery()
                .eq(TQaInspectionTask::getSamplingGroupId, finishTask.getSamplingGroupId())
                .eq(TQaInspectionTask::getDeleted, "0")
                .ne(TQaInspectionTask::getId, finishTask.getId()));
        if (StringUtil.isNotEmpty(inspectionTasks) && inspectionTasks.size() > 0) {
            for (TQaInspectionTask task : inspectionTasks) {

                String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(task.getId());
                if (!"大曲微生物检验".equals(sceneName) && !"制曲试验微生物检验".equals(sceneName) && !"成品大曲理化检验".equals(sceneName) && !"制曲试验理化检验".equals(sceneName) && !"实验大曲理化检验".equals(sceneName) ) {
                    task.setState(InspectionTaskState.INSPECTION_COMPLETE);
                    task.setInspectionResult(InspectionTaskState.Result.QUALIFIED);
                    task.setActualInspectionTime(finishTask.getActualInspectionTime());
                    task.setUpdateTime(new Date());
                    inspectionTaskMapper.updateById(task);
                    List<TQaInspectionData> finishInspectionDataList = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getInspectionId, finishTask.getId())
                            .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET)
                    );

                    List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getInspectionId, task.getId())
                            .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET)
                    );

                    inspectionDataList.forEach(inspectionData -> {
                        for (TQaInspectionData finishInspectionData : finishInspectionDataList) {
                            if (inspectionData.getIndicatorId() == finishInspectionData.getIndicatorId()) {
                                inspectionData.setValue(finishInspectionData.getValue());
                                inspectionDataMapper.updateById(inspectionData);
                                break;
                            }
                        }
                    });
                }

            }
        }
    }

    /*
     * @description: 根据收样组合id获取，检验任务、检验数据、过程数据详情
     * <AUTHOR>
     * @date 2022/3/18 9:22
     * @param groupId
     * @return java.util.List<com.hvisions.quality.dto.quality.inspection.incoming.IncomingInspectionTaskDetailDTO>
     */
    @Override
    public List<IncomingInspectionTaskDetailDTO> getProductionTaskDetailListByGroupCode(Integer groupId) {

        List<IncomingInspectionTaskDetailDTO> list = new ArrayList<>();


        LambdaQueryWrapper<TQaReceiveGroupInspection> wrapper = Wrappers.<TQaReceiveGroupInspection>lambdaQuery()
                .eq(TQaReceiveGroupInspection::getGroupId, groupId)
                .eq(TQaReceiveGroupInspection::getDeleted, "0");
        List<TQaReceiveGroupInspection> receiveGroupInspections = receiveGroupInspectionMapper.selectList(wrapper);

        if (receiveGroupInspections.size() > 0) {
            List<Integer> inspectionIds = receiveGroupInspections.stream().map(TQaReceiveGroupInspection::getInspectionId).collect(Collectors.toList());
            List<TQaInspectionTask> inspectionTasks = new ArrayList<>();
            for (Integer inspectionId : inspectionIds) {
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionId);
                if (StringUtil.isNotEmpty(inspectionTask)) {
                    inspectionTasks.add(inspectionTask);
                }
            }
            if (StringUtil.isNotEmpty(inspectionTasks) && inspectionTasks.size() > 0) {
                for (TQaInspectionTask inspectionTask : inspectionTasks) {
                    IncomingInspectionTaskDetailDTO taskAndProcessDataBySamplingCode = incomingInspectionService.getIncomingInspectionTaskAndProcessDataBySamplingCode(inspectionTask.getSamplingCode());
                    if (StringUtil.isNotEmpty(taskAndProcessDataBySamplingCode)) {
                        list.add(taskAndProcessDataBySamplingCode);
                    }
                }
            }
        }
        return list;
    }

    /*
     * @description: 糟醅检验近红外数据模板下载
     * <AUTHOR>
     * @date 2022/3/18 11:12
     * @param
     * @return com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
     */
    @Override
    public ResultVO<ExcelExportDto> getNearInfraredImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> workPlanImportTemplate = ExcelUtil.generateImportFile(NearInfraredImportDTO.class, "近红外数据导入模板");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(workPlanImportTemplate.getBody());
        excelExportDto.setFileName("近红外数据导入模板.xls");
        return ResultVO.success(excelExportDto);
    }

    /*
     * @description: 基酒检验中红外数据模板下载
     * <AUTHOR>
     * @date 2022/3/18 11:13
     * @param
     * @return com.hvisions.common.vo.ResultVO<com.hvisions.common.dto.ExcelExportDto>
     */
    @Override
    public ResultVO<ExcelExportDto> getMidInfraredImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> workPlanImportTemplate = ExcelUtil.generateImportFile(MidInfraredImportDTO.class, "中红外数据导入模板");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(workPlanImportTemplate.getBody());
        excelExportDto.setFileName("中红外数据导入模板.xls");
        return ResultVO.success(excelExportDto);
    }

    /*
     * @description: 导入近红外数据
     * <AUTHOR>
     * @date 2022/3/21 11:31
     * @param nearInfraredImportDTOS
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer importNearInfrared(Boolean isSubmit, List<NearInfraredImportDTO> nearInfraredImportDTOS) {

        int flag = 0;
        if (StringUtil.isNotEmpty(nearInfraredImportDTOS) && nearInfraredImportDTOS.size() > 0) {
            for (NearInfraredImportDTO nearInfraredImportDTO : nearInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, nearInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
//                    throw new BaseKnownException(10000, nearInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                    continue;

                }
                if (InspectionTaskState.INSPECTION_COMPLETE.equals(inspectionTask.getState())) {
                    continue;
                }
                UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                        .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));

                inspectionTask.setInspectionName(userBaseDTO.getUserName());
                inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
                inspectionTask.setActualInspectionTime(new Date());
                inspectionTaskMapper.updateById(inspectionTask);

                /**
                 * 1、获取近红外检验数据，如果有，修改，如果没有，复制一份
                 * 2、保存，修改近红外检验数据，
                 * 3、提交，保存近红外数据和检验单数据
                 */

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (isSubmit) { // 同步到检验单
                    if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                        // 更新检验单类型检验数据
                        for (TQaInspectionData inspectionData : inspectionDataList) {
                            assignNearValue(nearInfraredImportDTO, inspectionData);
                            flag += inspectionDataMapper.updateById(inspectionData);
                        }
                        // 判断检验单的指标是否全部录入，如果全部录了，
                        List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                                .eq(TQaInspectionData::getDeleted, 0)
                                .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                                .eq(TQaInspectionData::getType, 0)
                        );
                        long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                        if (count == 0) {
                            inspectionTask.setIndicatorIsFull(true);
                        } else {
                            inspectionTask.setIndicatorIsFull(false);
                        }
                        inspectionTaskMapper.updateById(inspectionTask);

                    }
                }

                // 获取检验任务下的 近红外 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.NEAR_INFRARED);
                List<TQaInspectionData> nearDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper);
                // 存在近红外数据
                if (StringUtil.isNotEmpty(nearDataList) && nearDataList.size() > 0) {
                    for (TQaInspectionData inspectionData : nearDataList) {
                        assignNearValue(nearInfraredImportDTO, inspectionData);
                        inspectionData.setUpdateTime(new Date());
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                } else {
                    // 不存在近红外数据
                    if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                        // 复制一份近红外数据
                        for (TQaInspectionData inspectionData : inspectionDataList) {
                            inspectionData.setType(InspectionDataState.NEAR_INFRARED);
                            inspectionData.setCreateTime(new Date());
                            assignNearValue(nearInfraredImportDTO, inspectionData);
                            flag += inspectionDataMapper.insert(inspectionData);
                        }
                    }
                }
            }
        }
        return flag;
    }


    @Override
    public Integer batchAuditNearInfrared(List<NearInfraredImportDTO> nearInfraredImportDTOS) {
        /**
         * 同步检验单，然后检验完成
         */
        int res = 0;
        res += this.importNearInfrared(Boolean.TRUE, nearInfraredImportDTOS);

        res += reviewCompareNearData(nearInfraredImportDTOS,Boolean.FALSE);
        return res;
    }

    @Override
    public Integer batchAuditMidInfrared(List<MidInfraredImportDTO> midInfraredImportDTOS) {
        /**
         * 同步检验单，然后检验完成
         */
        int res = 0;
        res += this.importMidInfrared(Boolean.TRUE, midInfraredImportDTOS);

        res += reviewCompareMidData(midInfraredImportDTOS);
        return res;
    }

    /*
     * @description: 给近红外数据赋值
     * <AUTHOR>
     * @date 2022/3/21 14:02
     * @param nearInfraredImportDTO
     * @param inspectionData
     * @return void
     */
    private void assignNearValue(NearInfraredImportDTO nearInfraredImportDTO, TQaInspectionData inspectionData) {
        switch (inspectionData.getIndicatorName()) {
            case InfraredContent.Near.ACIDITY:
                inspectionData.setValue(nearInfraredImportDTO.getAcidity());
                inspectionData.setUnit("mmol/10g");
                break;
            case InfraredContent.Near.STARCH:
                inspectionData.setValue(nearInfraredImportDTO.getStarch());
                inspectionData.setUnit("%");
                break;
            case InfraredContent.Near.WATER:
                inspectionData.setValue(nearInfraredImportDTO.getWater());
                inspectionData.setUnit("%");
                break;
            case InfraredContent.Near.ALCOHOL:
                inspectionData.setValue(nearInfraredImportDTO.getAlcohol());
                inspectionData.setUnit("%vol");
                break;
        }
    }


    /*
     * @description: 导入中红外数据
     * <AUTHOR>
     * @date 2022/3/21 14:18
     * @param isSubmit
     * @param midInfraredImportDTOS
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer importMidInfrared(Boolean isSubmit, List<MidInfraredImportDTO> midInfraredImportDTOS) {
        int flag = 0;
        if (StringUtil.isNotEmpty(midInfraredImportDTOS) && midInfraredImportDTOS.size() > 0) {
            for (MidInfraredImportDTO midInfraredImportDTO : midInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, midInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
//                    throw new BaseKnownException(10000, midInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                    continue;

                }

                if (InspectionTaskState.INSPECTION_COMPLETE.equals(inspectionTask.getState())) {
                    continue;
                }


                //判断标准酒度(%vol)是否有值，有值则需要更新交酒数据
                if(null!=midInfraredImportDTO.getModifyNormStandardVol()){
                    log.info("开始调用刷新标准酒度信息，传入参数------》关联单据：{},标准酒度:{}",inspectionTask.getAssociatedDocument(),midInfraredImportDTO.getModifyNormStandardVol());
                    //刷新交酒数据
                    ResultVO resultVO = wineHandInClient.updateModifyNormStandardVol(inspectionTask.getAssociatedDocument(),midInfraredImportDTO.getModifyNormStandardVol());
                    log.info("调用完成刷新标准酒度信息，传入参数------》关联单据：{},标准酒度:{}，返回结果:{}",inspectionTask.getAssociatedDocument(),midInfraredImportDTO.getModifyNormStandardVol(), JSONObject.toJSONString(resultVO));
                    if(resultVO.getCode()!=200){
                        throw new BaseKnownException(resultVO.getCode(),"刷新交酒数据数据失败，失败原因："+resultVO.getMessage());
                    }
                }

                UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                        .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));

                inspectionTask.setInspectionName(userBaseDTO.getUserName());
                inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
                inspectionTask.setActualInspectionTime(new Date());


//                //已经标识为三方样，不允许提交检验单
//                if(isSubmit && "基酒".equals(inspectionTask.getSamplingName()) && null!=inspectionTask.getIsTripartite() && inspectionTask.getIsTripartite()==1){
//                    throw new BaseKnownException(110,"样品编码："+midInfraredImportDTO.getSamplingCode()+"已是三方样，不允许同步至检验单");
//                }

                //新增标识为三方样
                if(!isSubmit){
                    inspectionTask.setIsTripartite(1);
                }else {
                    inspectionTask.setIsTripartite(0);
                }

                inspectionTaskMapper.updateById(inspectionTask);

                /**
                 * 1、获取中红外检验数据，如果有，修改，如果没有，复制一份
                 * 2、保存，修改中红外检验数据，
                 * 3、提交，保存中红外数据和检验单数据
                 */

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (isSubmit) { // 同步到检验单
                    if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                        // 更新检验单类型检验数据
                        for (TQaInspectionData inspectionData : inspectionDataList) {
                            assignMidValue(midInfraredImportDTO, inspectionData);
                            flag += inspectionDataMapper.updateById(inspectionData);
                        }
                        // 判断检验单的指标是否全部录入，如果全部录了，
                        List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                                .eq(TQaInspectionData::getDeleted, 0)
                                .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                                .eq(TQaInspectionData::getType, 0)
                        );
                        long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                        if (count == 0) {
                            inspectionTask.setIndicatorIsFull(true);
                        } else {
                            inspectionTask.setIndicatorIsFull(false);
                        }
                        //数据录入状态
                        if (inspectionDataList.stream().filter(i -> StringUtils.isNotBlank(i.getValue())).count() == inspectionDataList.size()) {
                            //已经全部录入完了
                            inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.ENTRY_FINISH);
                        } else if (inspectionDataList.stream().filter(i -> "色谱".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                            //色谱未录入完整
                            inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.CHROMATOGRAPHY_NOT_ENTRY);
                        } else if (inspectionDataList.stream().filter(i -> "基酒理化".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                            //色谱未录入完整
                            inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.PHYSICOCHEMICAL_NOT_ENTRY);
                        }
                        inspectionTaskMapper.updateById(inspectionTask);
                    }
                }

                // 获取检验任务下的 中红外 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.MID_INFRARED);
                List<TQaInspectionData> nearDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper);
                // 存在中红外数据
                if (StringUtil.isNotEmpty(nearDataList) && nearDataList.size() > 0) {
                    for (TQaInspectionData inspectionData : nearDataList) {
                        assignMidValue(midInfraredImportDTO, inspectionData);
                        inspectionData.setUpdateTime(new Date());
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                } else {
                    // 不存在中红外数据
                    if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                        // 复制一份近红外数据
                        for (TQaInspectionData inspectionData : inspectionDataList) {
                            inspectionData.setType(InspectionDataState.MID_INFRARED);
                            inspectionData.setCreateTime(new Date());
                            assignMidValue(midInfraredImportDTO, inspectionData);
                            flag += inspectionDataMapper.insert(inspectionData);
                        }
                    }
                }


                //新增导入记录数据---基酒
                if("基酒".equals(inspectionTask.getSamplingName())){
                    log.info("开始新增基酒记录---快检设备数据");
                    LambdaQueryWrapper<TQaInspectionDataRecord> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(TQaInspectionDataRecord::getSampleCode,midInfraredImportDTO.getSamplingCode())
                            .eq(TQaInspectionDataRecord::getType,1)
                            .eq(TQaInspectionDataRecord::getDeleted,0)
                            .orderByDesc(TQaInspectionDataRecord::getUpdateTime)
                            .last("limit 1");
                    TQaInspectionDataRecord inspectionDataRecord = inspectionDataRecordMapper.selectOne(wrapper);
                    if(null==inspectionDataRecord){
                        inspectionDataRecord=new TQaInspectionDataRecord();
                        inspectionDataRecord.setInspectionId(inspectionTask.getId());
                        inspectionDataRecord.setType("1");
                    }

                    inspectionDataRecord.setSampleCode(midInfraredImportDTO.getSamplingCode());
                    if(null!=midInfraredImportDTO.getModifyNormStandardVol()){
                        inspectionDataRecord.setModifyNormStandardVol(BigDecimal.valueOf(midInfraredImportDTO.getModifyNormStandardVol()));
                    }
                    inspectionDataRecord.setEthylAcetateContent(midInfraredImportDTO.getEthylAcetateContent());
                    inspectionDataRecord.setEthylButyrateContent(midInfraredImportDTO.getEthylButyrateContent());
                    inspectionDataRecord.setEthylCaproateContent(midInfraredImportDTO.getEthylCaproateContent());
                    inspectionDataRecord.setEthylLactateContent(midInfraredImportDTO.getEthylLactateContent());
                    inspectionDataRecord.setTotalAcidContent(midInfraredImportDTO.getTotalAcidContent());
                    inspectionDataRecord.setTotalEsterContent(midInfraredImportDTO.getTotalEsterContent());

                    if(null==inspectionDataRecord.getId()){
                        inspectionDataRecordMapper.insert(inspectionDataRecord);
                    }else {
                        inspectionDataRecordMapper.updateById(inspectionDataRecord);
                    }
                }
            }
        }
        return flag;
    }

    /*
     * @description: 给中红外检验数据赋值
     * <AUTHOR>
     * @date 2022/3/21 14:18
     * @param midInfraredImportDTO
     * @param inspectionData
     * @return void
     */
    private void assignMidValue(MidInfraredImportDTO midInfraredImportDTO, TQaInspectionData inspectionData) {
        inspectionData.setUnit("g/L");
        switch (inspectionData.getIndicatorName()) {
            case InfraredContent.Min.TOTAL_ACID_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getTotalAcidContent());
                break;
            case InfraredContent.Min.TOTAL_ESTER_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getTotalEsterContent());
                break;
            case InfraredContent.Min.ETHYL_ACETATE_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getEthylAcetateContent());
                break;
            case InfraredContent.Min.ETHYL_BUTYRATE_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getEthylButyrateContent());
                break;
            case InfraredContent.Min.ETHYL_LACTATE_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getEthylLactateContent());
                break;
            case InfraredContent.Min.ETHYL_CAPROATE_CONTENT:
                inspectionData.setValue(midInfraredImportDTO.getEthylCaproateContent());
                break;
        }
    }

    /*
     * @description: 给记录数据赋值
     */
    private void assignMidValue(TQaInspectionDataRecord record, TQaInspectionData inspectionData) {
        switch (inspectionData.getIndicatorName()) {
            case InfraredContent.Min.TOTAL_ACID_CONTENT:
                record.setTotalAcidContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.TOTAL_ESTER_CONTENT:
                record.setTotalEsterContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_ACETATE_CONTENT:
                record.setEthylAcetateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_BUTYRATE_CONTENT:
                record.setEthylButyrateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_LACTATE_CONTENT:
                record.setEthylLactateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_CAPROATE_CONTENT:
                record.setEthylCaproateContent(inspectionData.getValue());
                break;
        }
    }


    /*
     * @description: 基酒比对列表查询
     * <AUTHOR>
     * @date 2022/3/22 10:00
     * @param compareQueryDTO
     * @return com.hvisions.quality.dto.quality.inspection.production.infrared.BaseWineCompareDTO
     */
    @Override
    public BaseWineCompareDTO getMidInfraredCompareList(CompareQueryDTO compareQueryDTO) {
        BaseWineCompareDTO baseWineCompareDTO = new BaseWineCompareDTO();
        if (InspectionTaskState.InspectionType.BASE_WINE_INSPECTION.equals(compareQueryDTO.getInspectionType())) {

            compareQueryDTO.setType(InspectionDataState.MID_INFRARED);
            // 设备中红外数据
            List<MidInfraredCompareDTO> midInfraredData = getMidInfraredData(compareQueryDTO);
            Collections.sort(midInfraredData, Comparator.comparingInt(MidInfraredCompareDTO::getId));

            for (MidInfraredCompareDTO midInfraredCompareDTO : midInfraredData) {
                try {
                    BigDecimal total = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getTotalEsterContent()) ? "0" : midInfraredCompareDTO.getTotalEsterContent());
                    BigDecimal bigDecimal1 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylAcetateContent()) ? "0" : midInfraredCompareDTO.getEthylAcetateContent());
                    BigDecimal bigDecimal2 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylButyrateContent()) ? "0" : midInfraredCompareDTO.getEthylButyrateContent());
                    BigDecimal bigDecimal3 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylLactateContent()) ? "0" : midInfraredCompareDTO.getEthylLactateContent());
                    BigDecimal bigDecimal4 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylCaproateContent()) ? "0" : midInfraredCompareDTO.getEthylCaproateContent());

                    BigDecimal colorValue = bigDecimal1.add(bigDecimal2.multiply(new BigDecimal(0.76)));
                    colorValue = colorValue.add(bigDecimal3.multiply(new BigDecimal(0.75)));
                    colorValue = colorValue.add(bigDecimal4.multiply(new BigDecimal(0.61)));

                    BigDecimal difference = total.subtract(colorValue);
                    DecimalFormat df = new DecimalFormat("0.00");

                    BigDecimal withManual = BigDecimal.ZERO;
                    if (total.compareTo(BigDecimal.ZERO) == 1) {
                        withManual = difference.multiply(new BigDecimal(100)).divide(total, BigDecimal.ROUND_CEILING).abs();
                    }
                    midInfraredCompareDTO.setColorTotalEster(df.format(colorValue));
                    midInfraredCompareDTO.setDifference(df.format(difference));
                    midInfraredCompareDTO.setWithManual(df.format(withManual));

                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }

            baseWineCompareDTO.setMidInfraredData(midInfraredData);

            // 手工预处理数据
            compareQueryDTO.setType(InspectionDataState.PRETREATMENT);
            List<MidInfraredCompareDTO> pretreatmentData = getMidInfraredData(compareQueryDTO);
            Collections.sort(pretreatmentData, Comparator.comparingInt(MidInfraredCompareDTO::getId));

            /**
             * 计算：
             * 1. 色谱总酯=乙酸乙酯+丁酸乙酯×0.76+乳酸乙酯×0.75+己酸乙酯×0.61
             * 2. 差值=理化总酯-色谱总酯
             * 3. 与手工=（差值/理化总酯×100）的绝对值
             */
            for (MidInfraredCompareDTO midInfraredCompareDTO : pretreatmentData) {
                try {
                    BigDecimal total = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getTotalEsterContent()) ? "0" : midInfraredCompareDTO.getTotalEsterContent());
                    BigDecimal bigDecimal1 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylAcetateContent()) ? "0" : midInfraredCompareDTO.getEthylAcetateContent());
                    BigDecimal bigDecimal2 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylButyrateContent()) ? "0" : midInfraredCompareDTO.getEthylButyrateContent());
                    BigDecimal bigDecimal3 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylLactateContent()) ? "0" : midInfraredCompareDTO.getEthylLactateContent());
                    BigDecimal bigDecimal4 = new BigDecimal(StringUtil.isEmpty(midInfraredCompareDTO.getEthylCaproateContent()) ? "0" : midInfraredCompareDTO.getEthylCaproateContent());

                    BigDecimal colorValue = bigDecimal1.add(bigDecimal2.multiply(new BigDecimal(0.76)));
                    colorValue = colorValue.add(bigDecimal3.multiply(new BigDecimal(0.75)));
                    colorValue = colorValue.add(bigDecimal4.multiply(new BigDecimal(0.61)));

                    BigDecimal difference = total.subtract(colorValue);
                    DecimalFormat df = new DecimalFormat("0.00");

                    BigDecimal withManual = BigDecimal.ZERO;
                    if (total.compareTo(BigDecimal.ZERO) == 1) {
                        withManual = difference.multiply(new BigDecimal(100)).divide(total, BigDecimal.ROUND_CEILING).abs();
                    }
                    midInfraredCompareDTO.setColorTotalEster(df.format(colorValue));
                    midInfraredCompareDTO.setDifference(df.format(difference));
                    midInfraredCompareDTO.setWithManual(df.format(withManual));

                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
//                    throw new BaseKnownException(10000, "计算错误！" + e.getMessage());
                }
            }
            baseWineCompareDTO.setPretreatmentData(pretreatmentData);
        }

        return baseWineCompareDTO;
    }


    /*
     * @description: 获取基酒检验检验数据（预处理或中红外）
     * <AUTHOR>
     * @date 2022/3/22 10:59
     * @param compareQueryDTO
     * @return java.util.List<com.hvisions.quality.dto.quality.inspection.production.infrared.MidInfraredCompareDTO>
     */
    private List<MidInfraredCompareDTO> getMidInfraredData(CompareQueryDTO compareQueryDTO) {
        List<MidInfraredCompareDTO> midInfraredData = new ArrayList<>();
        List<InspectionTaskDTO> midInfraredTaskData = productionInspectionMapper.getProductionInspectionTaskData(compareQueryDTO);

        if (StringUtil.isNotEmpty(midInfraredTaskData) && midInfraredTaskData.size() > 0) {
            for (InspectionTaskDTO inspectionTaskDTO : midInfraredTaskData) {
                MidInfraredCompareDTO midInfraredCompareDTO = new MidInfraredCompareDTO();

                midInfraredCompareDTO.setId(inspectionTaskDTO.getId());
                midInfraredCompareDTO.setInspectionOrder(inspectionTaskDTO.getInspectionOrder());
                midInfraredCompareDTO.setSamplingCode(inspectionTaskDTO.getSamplingCode());
                midInfraredCompareDTO.setState(inspectionTaskDTO.getState());
                midInfraredCompareDTO.setLevel(inspectionTaskDTO.getLevel());
                midInfraredCompareDTO.setLeesType(inspectionTaskDTO.getLeesType());
                midInfraredCompareDTO.setTastingDate(inspectionTaskDTO.getTastingDate());
                midInfraredCompareDTO.setPitCode(inspectionTaskDTO.getPitCode());
                midInfraredCompareDTO.setYear(inspectionTaskDTO.getYear());
                midInfraredCompareDTO.setFermentationDay(inspectionTaskDTO.getFermentationDay());
                midInfraredCompareDTO.setStandardVol(inspectionTaskDTO.getStandardVol());
                midInfraredCompareDTO.setQuantity(inspectionTaskDTO.getQuantity());
                midInfraredCompareDTO.setTo60Quantity(inspectionTaskDTO.getTo60Quantity());
                midInfraredCompareDTO.setStageNum(inspectionTaskDTO.getStageNum());
                List<InspectionDataDTO> inspectionDataDTOS = inspectionTaskDTO.getInspectionDataDTOS();
                if (StringUtil.isNotEmpty(inspectionDataDTOS) && inspectionDataDTOS.size() > 0) {
                    for (InspectionDataDTO inspectionDataDTO : inspectionDataDTOS) {
                        switch (inspectionDataDTO.getIndicatorName()) {
                            case InfraredContent.Min.TOTAL_ACID_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getTotalAcidContent())) {
                                    midInfraredCompareDTO.setTotalAcidContent(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Min.TOTAL_ESTER_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getTotalEsterContent())) {
                                    midInfraredCompareDTO.setTotalEsterContent(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Min.ETHYL_ACETATE_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getEthylAcetateContent())) {
                                    midInfraredCompareDTO.setEthylAcetateContent(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Min.ETHYL_BUTYRATE_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getEthylButyrateContent())) {

                                    midInfraredCompareDTO.setEthylButyrateContent(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Min.ETHYL_LACTATE_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getEthylLactateContent())) {

                                    midInfraredCompareDTO.setEthylLactateContent(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Min.ETHYL_CAPROATE_CONTENT:
                                if (StringUtil.isEmpty(midInfraredCompareDTO.getEthylCaproateContent())) {

                                    midInfraredCompareDTO.setEthylCaproateContent(inspectionDataDTO.getValue());
                                }
                                break;
                        }
                    }
                }
                midInfraredData.add(midInfraredCompareDTO);
            }
        }
        return midInfraredData;
    }


    /*
     * @description: 糟培比对列表查询
     * <AUTHOR>
     * @date 2022/3/22 13:28
     * @param compareQueryDTO
     * @return com.hvisions.quality.dto.quality.inspection.production.infrared.FermentedGrainsCompareDTO
     */
    @Override
    public FermentedGrainsCompareDTO getNearInfraredCompareList(CompareQueryDTO compareQueryDTO) {
        FermentedGrainsCompareDTO fermentedGrainsCompareDTO = new FermentedGrainsCompareDTO();
        if (InspectionTaskState.InspectionType.FERMENTED_GRAIN_INSPECTION.equals(compareQueryDTO.getInspectionType())) {

            compareQueryDTO.setType(InspectionDataState.NEAR_INFRARED);
            // 设备近红外数据
            List<NearInfraredCompareDTO> nearInfraredData = getNearInfraredData(compareQueryDTO);
            Collections.sort(nearInfraredData, Comparator.comparingInt(NearInfraredCompareDTO::getId));
            fermentedGrainsCompareDTO.setNearInfraredData(nearInfraredData);

            // 手工预处理数据
            compareQueryDTO.setType(InspectionDataState.PRETREATMENT);
            List<NearInfraredCompareDTO> pretreatmentData = getNearInfraredData(compareQueryDTO);
            Collections.sort(pretreatmentData, Comparator.comparingInt(NearInfraredCompareDTO::getId));


            /**
             * 计算：
             * 绝对差值=ABS（设备值  - 手工值）
             * 相对误差=绝对差值/手工值
             */
            for (NearInfraredCompareDTO pretreatmentDatum : pretreatmentData) {

                for (NearInfraredCompareDTO nearInfraredDatum : nearInfraredData) {
                    if (pretreatmentDatum.getInspectionOrder().equals(nearInfraredDatum.getInspectionOrder()) && pretreatmentDatum.getSamplingCode().equals(nearInfraredDatum.getSamplingCode())) {
                        try {
                            BigDecimal acidity = new BigDecimal(StringUtil.isEmpty(nearInfraredDatum.getAcidity()) ? "0" : nearInfraredDatum.getAcidity()); // 酸度
                            BigDecimal starch = new BigDecimal(StringUtil.isEmpty(nearInfraredDatum.getStarch()) ? "0" : nearInfraredDatum.getStarch()); // 淀粉
                            BigDecimal water = new BigDecimal(StringUtil.isEmpty(nearInfraredDatum.getWater()) ? "0" : nearInfraredDatum.getWater()); // 水分
                            BigDecimal alcohol = new BigDecimal(StringUtil.isEmpty(nearInfraredDatum.getAlcohol()) ? "0" : nearInfraredDatum.getAlcohol()); // 酒精度


                            BigDecimal acidity1 = new BigDecimal(StringUtil.isEmpty(pretreatmentDatum.getAcidity()) ? "0" : pretreatmentDatum.getAcidity()); // 酸度
                            BigDecimal starch1 = new BigDecimal(StringUtil.isEmpty(pretreatmentDatum.getStarch()) ? "0" : pretreatmentDatum.getStarch()); // 淀粉
                            BigDecimal water1 = new BigDecimal(StringUtil.isEmpty(pretreatmentDatum.getWater()) ? "0" : pretreatmentDatum.getWater()); // 水分
                            BigDecimal alcohol1 = new BigDecimal(StringUtil.isEmpty(pretreatmentDatum.getAlcohol()) ? "0" : pretreatmentDatum.getAlcohol()); // 酒精度

                            BigDecimal acidityAbs = acidity.subtract(acidity1).abs();

                            BigDecimal acidityRel = BigDecimal.ZERO;
                            if (acidity1.compareTo(BigDecimal.ZERO) == 1) {
                                acidityRel = acidityAbs.divide(acidity1, BigDecimal.ROUND_CEILING);
                            }


                            BigDecimal starchAbs = starch.subtract(starch1).abs();
                            BigDecimal starchRel = BigDecimal.ZERO;
                            if (starch1.compareTo(BigDecimal.ZERO) == 1) {
                                starchRel = starchAbs.divide(starch1, BigDecimal.ROUND_CEILING);
                            }

                            BigDecimal waterAbs = water.subtract(water1).abs();

                            BigDecimal waterRel = BigDecimal.ZERO;
                            if (water1.compareTo(BigDecimal.ZERO) == 1) {
                                waterRel = waterAbs.divide(water1, BigDecimal.ROUND_CEILING);
                            }

                            BigDecimal alcoholAbs = alcohol.subtract(alcohol1).abs();
                            BigDecimal alcoholRel = BigDecimal.ZERO;
                            if (alcohol1.compareTo(BigDecimal.ZERO) == 1) {
                                alcoholRel = alcoholAbs.divide(alcohol1, BigDecimal.ROUND_CEILING);
                            }

                            pretreatmentDatum.setAcidityAbs(acidityAbs.toString());
                            pretreatmentDatum.setAcidityRel(acidityRel.toString());
                            pretreatmentDatum.setStarchAbs(starchAbs.toString());
                            pretreatmentDatum.setStarchRel(starchRel.toString());
                            pretreatmentDatum.setWaterAbs(waterAbs.toString());
                            pretreatmentDatum.setWaterRel(waterRel.toString());
                            pretreatmentDatum.setAlcoholAbs(alcoholAbs.toString());
                            pretreatmentDatum.setAlcoholRel(alcoholRel.toString());

                            continue;
                        } catch (Exception e) {
                            e.printStackTrace();
                            continue;
                        }
                    }
                }

            }

            fermentedGrainsCompareDTO.setPretreatmentData(pretreatmentData);

        }
        return fermentedGrainsCompareDTO;
    }

    /*
     * @description: 获取糟培检验检验数据（预处理或近红外）
     * <AUTHOR>
     * @date 2022/3/22 13:34
     * @param compareQueryDTO
     * @return java.util.List<com.hvisions.quality.dto.quality.inspection.production.infrared.NearInfraredCompareDTO>
     */
    private List<NearInfraredCompareDTO> getNearInfraredData(CompareQueryDTO compareQueryDTO) {
        List<NearInfraredCompareDTO> nearInfraredData = new ArrayList<>();
        List<InspectionTaskDTO> nearInfraredTaskData = productionInspectionMapper.getProductionInspectionTaskData(compareQueryDTO);
        if (StringUtil.isNotEmpty(nearInfraredTaskData) && nearInfraredTaskData.size() > 0) {
            for (InspectionTaskDTO inspectionTaskDTO : nearInfraredTaskData) {
                NearInfraredCompareDTO nearInfraredCompareDTO = new NearInfraredCompareDTO();

                nearInfraredCompareDTO.setId(inspectionTaskDTO.getId());
                nearInfraredCompareDTO.setInspectionOrder(inspectionTaskDTO.getInspectionOrder());
                nearInfraredCompareDTO.setSamplingCode(inspectionTaskDTO.getSamplingCode());
                nearInfraredCompareDTO.setState(inspectionTaskDTO.getState());
                nearInfraredCompareDTO.setDepartmentName(inspectionTaskDTO.getDepartmentName());
                nearInfraredCompareDTO.setSourceLevel(inspectionTaskDTO.getSourceLevel());
                nearInfraredCompareDTO.setCenterName(inspectionTaskDTO.getCenterName());
                nearInfraredCompareDTO.setFullPitId(inspectionTaskDTO.getFullPitId());

                List<InspectionDataDTO> inspectionDataDTOS = inspectionTaskDTO.getInspectionDataDTOS();
                if (StringUtil.isNotEmpty(inspectionDataDTOS) && inspectionDataDTOS.size() > 0) {
                    for (InspectionDataDTO inspectionDataDTO : inspectionDataDTOS) {
                        switch (inspectionDataDTO.getIndicatorName()) {
                            case InfraredContent.Near.ACIDITY:
                                if (StringUtil.isEmpty(nearInfraredCompareDTO.getAcidity())) {
                                    nearInfraredCompareDTO.setAcidity(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Near.STARCH:
                                if (StringUtil.isEmpty(nearInfraredCompareDTO.getStarch())) {
                                    nearInfraredCompareDTO.setStarch(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Near.WATER:
                                if (StringUtil.isEmpty(nearInfraredCompareDTO.getWater())) {
                                    nearInfraredCompareDTO.setWater(inspectionDataDTO.getValue());
                                }
                                break;
                            case InfraredContent.Near.ALCOHOL:
                                if (StringUtil.isEmpty(nearInfraredCompareDTO.getAlcohol())) {
                                    nearInfraredCompareDTO.setAlcohol(inspectionDataDTO.getValue());
                                }
                                break;

                        }
                    }
                }
                nearInfraredData.add(nearInfraredCompareDTO);
            }
        }
        return nearInfraredData;
    }


    /*
     * @description: 近红外比对数据同步到检验单
     * <AUTHOR>
     * @date 2022/3/22 14:50
     * @param infraredImportDTOS
     * @return java.lang.Integer
     */
    @Transactional
    @Override
    public Integer syncCompareNearData(List<NearInfraredImportDTO> nearInfraredImportDTOS) {
        int flag = 0;
        if (StringUtil.isNotEmpty(nearInfraredImportDTOS) && nearInfraredImportDTOS.size() > 0) {
            for (NearInfraredImportDTO nearInfraredImportDTO : nearInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, nearInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
                    throw new BaseKnownException(10000, nearInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                }

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                    // 更新检验单类型检验数据
                    for (TQaInspectionData inspectionData : inspectionDataList) {
                        assignNearValue(nearInfraredImportDTO, inspectionData);
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                    // 判断检验单的指标是否全部录入，如果全部录了，
                    List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getDeleted, 0)
                            .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                            .eq(TQaInspectionData::getType, 0)
                    );
                    long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                    if (count == 0) {
                        inspectionTask.setIndicatorIsFull(true);
                    } else {
                        inspectionTask.setIndicatorIsFull(false);
                    }
                    inspectionTaskMapper.updateById(inspectionTask);

                }
            }
        }
        return flag;
    }

    @Override
    @Transactional
    public Integer reviewCompareNearData(List<NearInfraredImportDTO> nearInfraredImportDTOS,Boolean manualData) {
        int flag = 0;
        if (StringUtil.isNotEmpty(nearInfraredImportDTOS) && nearInfraredImportDTOS.size() > 0) {
            for (NearInfraredImportDTO nearInfraredImportDTO : nearInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, nearInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
                    throw new BaseKnownException(10000, nearInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                }

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                    // 更新检验单类型检验数据
                    for (TQaInspectionData inspectionData : inspectionDataList) {
                        assignNearValue(nearInfraredImportDTO, inspectionData);
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                    // 判断检验单的指标是否全部录入，如果全部录了，
                    List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getDeleted, 0)
                            .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                            .eq(TQaInspectionData::getType, 0)
                    );
                    long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                    if (count == 0) {
                        inspectionTask.setIndicatorIsFull(true);
                    } else {
                        inspectionTask.setIndicatorIsFull(false);
                    }
                }
                //不通过后，样品状态应该由“待审核”变成“检验中”，且不用更新检验结果,20250418
                if(manualData){
                    inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
                    //inspectionTask.setInspectionResult(StringUtils.isNotBlank(nearInfraredImportDTO.getInspectionResult())?nearInfraredImportDTO.getInspectionResult():InspectionTaskState.Result.QUALIFIED);
                }else{
                    inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);
                    inspectionTask.setInspectionResult(StringUtils.isNotBlank(nearInfraredImportDTO.getInspectionResult())?nearInfraredImportDTO.getInspectionResult():InspectionTaskState.Result.QUALIFIED);
                }

                inspectionTask.setReviewComments(nearInfraredImportDTO.getReviewComments());
                inspectionTask.setUpdateTime(new Date());
                inspectionTaskMapper.updateById(inspectionTask);
                samplingGroupSync(inspectionTask);

            }
        }
        return flag;
    }

    /*
     * @description: 中红外比对数据同步到检验单
     * <AUTHOR>
     * @date 2022/3/22 14:51
     * @param midInfraredImportDTOS
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer syncCompareMidData(List<MidInfraredImportDTO> midInfraredImportDTOS) {
        int flag = 0;
        if (StringUtil.isNotEmpty(midInfraredImportDTOS) && midInfraredImportDTOS.size() > 0) {
            for (MidInfraredImportDTO midInfraredImportDTO : midInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, midInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
                    throw new BaseKnownException(10000, midInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                }

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                    // 更新检验单类型检验数据
                    for (TQaInspectionData inspectionData : inspectionDataList) {
                        assignMidValue(midInfraredImportDTO, inspectionData);
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                    // 判断检验单的指标是否全部录入，如果全部录了，
                    List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getDeleted, 0)
                            .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                            .eq(TQaInspectionData::getType, 0)
                    );
                    long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                    if (count == 0) {
                        inspectionTask.setIndicatorIsFull(true);
                    } else {
                        inspectionTask.setIndicatorIsFull(false);
                    }
                    inspectionTaskMapper.updateById(inspectionTask);
                }
            }
        }
        return flag;
    }

    @Override
    @Transactional
    public Integer reviewCompareMidData(List<MidInfraredImportDTO> midInfraredImportDTOS) {
        int flag = 0;
        if (StringUtil.isNotEmpty(midInfraredImportDTOS) && midInfraredImportDTOS.size() > 0) {
            for (MidInfraredImportDTO midInfraredImportDTO : midInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, midInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
                    throw new BaseKnownException(10000, midInfraredImportDTO.getSamplingCode() + "没有找到对应检验任务");
                }

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                    // 更新检验单类型检验数据
                    for (TQaInspectionData inspectionData : inspectionDataList) {
                        assignMidValue(midInfraredImportDTO, inspectionData);
                        flag += inspectionDataMapper.updateById(inspectionData);
                    }
                    // 判断检验单的指标是否全部录入，如果全部录了，
                    List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getDeleted, 0)
                            .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                            .eq(TQaInspectionData::getType, 0)
                    );
                    long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                    if (count == 0) {
                        inspectionTask.setIndicatorIsFull(true);
                    } else {
                        inspectionTask.setIndicatorIsFull(false);
                    }
                }

                inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);
                inspectionTask.setInspectionResult(StringUtils.isNotBlank(midInfraredImportDTO.getInspectionResult())?midInfraredImportDTO.getInspectionResult():InspectionTaskState.Result.QUALIFIED);
                inspectionTask.setReviewComments(midInfraredImportDTO.getReviewComments());
                inspectionTask.setUpdateTime(new Date());
                inspectionTaskMapper.updateById(inspectionTask);
                samplingGroupSync(inspectionTask);
            }
        }
        return flag;
    }


    @Override
    public Page<SapSyncTaskDTO> getSapSyncTask(SapSyncTaskPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(productionInspectionMapper::getSapSyncTask, queryDTO, SapSyncTaskDTO.class);
    }

    /**
     * 判断酒精度(%vol)是否有值，有值则需要更新取样数据采集的数据,无值则更新
     * @param acoholContent
     * @param samplingId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  void addOrUpDateTQaSamplingCollect(String acoholContent,Integer samplingId,String associatedDocument){
        if(StringUtil.isNotEmpty(acoholContent)){
            TQaSampling tQaSampling = samplingMapper.selectById(samplingId);
            if(null!=tQaSampling){

                LambdaQueryWrapper<TQaCollectItem> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TQaCollectItem::getStandardId,tQaSampling.getStandardId())
                        .eq(TQaCollectItem::getItemName,"酒精度");
                TQaCollectItem tQaCollectItem = collectItemMapper.selectOne(wrapper);
                if(null!=tQaCollectItem){
                    LambdaQueryWrapper<TQaSamplingCollect> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(TQaSamplingCollect::getSamplingId,samplingId).eq(TQaSamplingCollect::getItemName,tQaCollectItem.getItemName());
                    TQaSamplingCollect tQaSamplingCollect = samplingCollectMapper.selectOne(queryWrapper);
                    if(null!=tQaSamplingCollect){
                        tQaSamplingCollect.setCollectValue(acoholContent);
                        samplingCollectMapper.updateById(tQaSamplingCollect);
                    }else {
                        TQaSamplingCollect add = new TQaSamplingCollect();
                        add.setSamplingId(samplingId);
                        add.setItemName(tQaCollectItem.getItemName());
                        add.setEntryMode(tQaCollectItem.getEntryMode());
                        add.setUnit(tQaCollectItem.getUnit());
                        add.setOptionOne(tQaCollectItem.getOptionOne());
                        add.setOptionTwo(tQaCollectItem.getOptionTwo());
                        add.setOptionThree(tQaCollectItem.getOptionThree());
                        add.setCollectValue(acoholContent);
                        samplingCollectMapper.insert(add);
                    }
                }

                //刷新交酒数据数据
                ResultVO resultVO = wineHandInClient.updateTPoWorkshopHandinTask(associatedDocument, null, Double.valueOf(acoholContent));
                if(resultVO.getCode()!=200){
                    throw new BaseKnownException(resultVO.getCode(),"刷新交酒数据失败，失败原因："+resultVO.getMessage());
                }

            }
        }
    }

    @Override
    public Integer sendInspectionListToSamplingApplet(SamplingAppletSendDTO samplingAppletSendDTO) {
        Integer add = 0;
        List<Integer> taskIdList = samplingAppletSendDTO.getTaskIdList();
        for (Integer taskId : taskIdList) {
            log.info("准备向小程序发送理化数据,检验任务id:" + taskId);
            sendInspectionToSamplingApplet(taskId);
            add++;
        }
        return add;
    }

    /**
     * 查询基酒样品对比
     * @return
     */
    @Override
    public Page<MidInfraredContrastDTO> getMidInfraredContrast(MidInfraredContrastQueryDTO queryDTO) {
        Page<MidInfraredContrastDTO> page = PageHelperUtil.getPage(productionInspectionMapper::getMidInfraredContrast, queryDTO, MidInfraredContrastDTO.class);
        if(!CollectionUtils.isEmpty(page.getContent())){
            List<MidInfraredContrastDTO> content =page.getContent() ;
            List<String> sampleCodeList = content.stream().map(MidInfraredContrastDTO::getSamplingCode).collect(Collectors.toList());

            LambdaQueryWrapper<TQaInspectionDataRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(TQaInspectionDataRecord::getSampleCode,sampleCodeList)
                    .eq(TQaInspectionDataRecord::getDeleted,0);
            List<TQaInspectionDataRecord> recordList = inspectionDataRecordMapper.selectList(wrapper);

            content.forEach(v->{
                List<TQaInspectionDataRecord> dataRecordList = recordList.stream().filter(item -> item.getSampleCode().equals(v.getSamplingCode())).collect(Collectors.toList());
                List<MidInfraredContrastRecordDTO> contrastRecordDTOList = DtoMapper.convertList(dataRecordList, MidInfraredContrastRecordDTO.class);
                v.setRecordList(contrastRecordDTOList);
            });

        }

        return page;
    }

    /**
     * 基酒样品对比-保存
     * @param contrastAddDTOList
     * @return
     */
    @Override
    public String addMidInfraredContrast(List<MidInfraredContrastAddDTO> contrastAddDTOList) {
        List<MidInfraredImportDTO> midInfraredImportDTOS = DtoMapper.convertList(contrastAddDTOList, MidInfraredImportDTO.class);
        if (StringUtil.isNotEmpty(midInfraredImportDTOS) && midInfraredImportDTOS.size() > 0) {
            for (MidInfraredImportDTO midInfraredImportDTO : midInfraredImportDTOS) {
                // 根据检验任务单号和样品编码，获取检验任务
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, midInfraredImportDTO.getSamplingCode())
                        .eq(TQaInspectionTask::getDeleted, "0");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (inspectionTask == null) {
                    continue;

                }

                if (InspectionTaskState.INSPECTION_COMPLETE.equals(inspectionTask.getState())) {
                    continue;
                }


                UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                        .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));

                inspectionTask.setInspectionName(userBaseDTO.getUserName());
                inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
                inspectionTask.setActualInspectionTime(new Date());
                inspectionTaskMapper.updateById(inspectionTask);

                // 获取检验任务下的 检验单 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                // 同步到检验单
                if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                    // 更新检验单类型检验数据
                    for (TQaInspectionData inspectionData : inspectionDataList) {
                        assignMidValue(midInfraredImportDTO, inspectionData);
                        inspectionDataMapper.updateById(inspectionData);
                    }
                    // 判断检验单的指标是否全部录入，如果全部录了，
                    List<TQaInspectionData> inspectionDatas = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getDeleted, 0)
                            .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                            .eq(TQaInspectionData::getType, 0)
                    );
                    long count = inspectionDatas.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                    if (count == 0) {
                        inspectionTask.setIndicatorIsFull(true);
                    } else {
                        inspectionTask.setIndicatorIsFull(false);
                    }
                    //数据录入状态
                    if (inspectionDataList.stream().filter(i -> StringUtils.isNotBlank(i.getValue())).count() == inspectionDataList.size()) {
                        //已经全部录入完了
                        inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.ENTRY_FINISH);
                    } else if (inspectionDataList.stream().filter(i -> "色谱".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                        //色谱未录入完整
                        inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.CHROMATOGRAPHY_NOT_ENTRY);
                    } else if (inspectionDataList.stream().filter(i -> "基酒理化".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                        //色谱未录入完整
                        inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.PHYSICOCHEMICAL_NOT_ENTRY);
                    }
                    inspectionTaskMapper.updateById(inspectionTask);
                }


                // 获取检验任务下的 中红外 检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.MID_INFRARED);
                List<TQaInspectionData> nearDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper);
                // 存在中红外数据
                if (StringUtil.isNotEmpty(nearDataList) && nearDataList.size() > 0) {
                    for (TQaInspectionData inspectionData : nearDataList) {
                        assignMidValue(midInfraredImportDTO, inspectionData);
                        inspectionData.setUpdateTime(new Date());
                        inspectionDataMapper.updateById(inspectionData);
                    }
                } else {
                    // 不存在中红外数据
                    if (StringUtil.isNotEmpty(inspectionDataList) && inspectionDataList.size() > 0) {
                        // 复制一份近红外数据
                        for (TQaInspectionData inspectionData : inspectionDataList) {
                            inspectionData.setType(InspectionDataState.MID_INFRARED);
                            inspectionData.setCreateTime(new Date());
                            assignMidValue(midInfraredImportDTO, inspectionData);
                            inspectionDataMapper.insert(inspectionData);
                        }
                    }
                }
            }
        }
        return "同步成功";
    }

    /**
     * 批量录入检验结果
     * @param list
     * @return
     */
    @Override
    public String enterInspectionResultList(List<ProductionInspectionResultDTO> list) {
        list.forEach(v->{
            List<InspectionDataDetailDTO> inspectionDataDTOList = v.getInspectionDataDetailDTOList();
            if (inspectionDataDTOList != null && inspectionDataDTOList.size() > 0) {
                for (InspectionDataDetailDTO inspectionDataDetailDTO : inspectionDataDTOList) {
                    TQaInspectionData inspectionData = inspectionDataMapper.selectById(inspectionDataDetailDTO.getId());
                    inspectionData.setUpdateTime(new Date());
                    if ("大曲理化".equals(inspectionData.getIndicatorType())) {
                        inspectionData.setValue(powderIndicatorFormat(inspectionData.getIndicatorName(), inspectionDataDetailDTO.getValue()));
                    } else {
                        inspectionData.setValue(inspectionDataDetailDTO.getValue());
                    }
                    inspectionDataMapper.updateById(inspectionData);
                }
            }
        });
        return "批量录入检验结果成功";
    }

}
