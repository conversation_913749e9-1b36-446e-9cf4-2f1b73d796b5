package com.hvisions.quality.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.quality.dto.quality.sampling.LabelPrintCountListDTO;
import com.hvisions.quality.dto.quality.sampling.SamplingDetailDTO;
import com.hvisions.quality.dto.quality.sampling.SamplingPageDTO;
import com.hvisions.quality.dto.quality.sampling.SamplingPageQueryDTO;
import com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageDTO;
import com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageQueryDTO;
import com.hvisions.quality.quality.entity.TQaSampling;
import com.hvisions.quality.sap.dto.applet.SamplingAppletDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Description: 取样
 *
 * @author: Jcao
 * @time: 2022/3/7 16:14
 */
@Mapper
public interface SamplingMapper extends BaseMapper<TQaSampling> {

    /*
     * @Description: 分页查询取样任务列表
     * <AUTHOR>
     */
    List<SamplingPageDTO> getSamplingPageList(SamplingPageQueryDTO samplingPageQueryDTO);

    /*
     * @Description: 分页查询样品报表列表
     * <AUTHOR>
     */
    List<SamplingReportPageDTO> getSamplingReportPageList(SamplingReportPageQueryDTO samplingReportPageQueryDTO);

    /*
     * @Description: 根据取样id获取详情数据
     * <AUTHOR>
     */
    SamplingDetailDTO getSamplingDetail(Integer id);

    /*
     * @Description: 获取样品打印列表
     * <AUTHOR>
     */
    List<LabelPrintCountListDTO> getLabelPrintCountList(List<Integer> ids);

    /*
     * @Description: 根据取样任务id，获取未打印条码的任务
     * <AUTHOR>
     */
    Integer getNotPrintTask(Integer samplingId);

    /**
     * 小程序获取数据查询
     * @param samplingCode
     * @return
     */
    SamplingAppletDTO getSamplingAppletSelect(String samplingCode);

    /**
     * 根据时间查询排次信息
     *
     * @param dateTime
     * @param locationId
     * @return
     */
    String getPlanRowByDate(@Param("dateTime") Date dateTime, @Param("locationId") String locationId);
}
