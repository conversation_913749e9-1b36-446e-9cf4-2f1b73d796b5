package com.hvisions.quality.quality.service.imp;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.client.MeasureVolToStandardVolClient;
import com.hvisions.brewage.client.WineHandInClient;
import com.hvisions.brewage.dto.mkwine.dto.AlcoholConfirmUpdateDTO;
import com.hvisions.brewage.dto.mkwine.dto.MeasureVolToStandardVolDTO;
import com.hvisions.brewage.dto.mkwine.dto.QualityTaskUpdateDTO;
import com.hvisions.brewage.dto.mkwine.dto.UpdateQuantityForQmDTO;
import com.hvisions.brewage.dto.mkwine.vo.MeasureVolToStandardVolVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectListDTO;
import com.hvisions.quality.dto.quality.tasting.*;
import com.hvisions.quality.quality.consts.InspectionTaskState;
import com.hvisions.quality.quality.consts.TastingState;
import com.hvisions.quality.quality.dao.*;
import com.hvisions.quality.quality.entity.*;
import com.hvisions.quality.quality.service.MessageService;
import com.hvisions.quality.quality.service.SamplingService;
import com.hvisions.quality.quality.service.TastingService;
import com.hvisions.quality.sap.RequestCpxt;
import com.hvisions.quality.sap.dto.CpxtBaseDataDto;
import com.hvisions.quality.sap.dto.CpxtBaseResponseDto;
import com.hvisions.quality.sap.dto.applet.SamplingAppletSendDTO;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 尝评任务
 * @author: Jcao
 * @time: 2022/3/8 14:40
 */
@Slf4j
@Service
public class TastingServiceImpl implements TastingService {

    @Resource
    private TastingMapper tastingMapper;

    @Resource
    private RequestCpxt requestCpxt;

    @Resource
    private BaseWineMapper baseWineMapper;

    @Resource
    private BaseWineEvaluateMapper baseWineEvaluateMapper;

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;

    @Resource
    private SamplingMapper samplingMapper;

    @Resource
    private SamplingCollectMapper samplingCollectMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private WineHandInClient wineHandInClient;

    @Resource
    private MeasureVolToStandardVolClient measureVolToStandardVolClient;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Resource
    private SamplingService samplingService;

    /*
     * @Description: 分页查询尝评任务列表
     *
     * <AUTHOR>
     * @param tastingPageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.tasting.TastingPageDTO>
     */
    @Override
    public Page<TastingPageDTO> getTastingPageList(TastingPageQueryDTO tastingPageQueryDTO) {
        Page<TastingPageDTO> page = PageHelperUtil.getPage(tastingMapper::getTastingPageList, tastingPageQueryDTO, TastingPageDTO.class);
        for (TastingPageDTO tastingPageDTO : page.getContent()) {
            TastingTaskDetailDTO taskDetailDTO = tastingMapper.getTastingTaskDetailById(tastingPageDTO.getId());
            // 获取酿酒重量
            ResultVO<Double> resultVO = wineHandInClient.getQmQuality(taskDetailDTO.getAssociatedDocument());
            if (resultVO.getCode() == 200) {
                Double qmQuality = resultVO.getData();
                if (qmQuality != null) {
                    tastingPageDTO.setQmQuality(new BigDecimal(qmQuality));
                }
            }
        }
        return page;
    }

    /*
     * @description: 批量酒精度确定
     * <AUTHOR>
     * @date 2022/3/16 16:17
     * @param confirmDTO
     * @return int
     */
    @Override
    @Transactional
    public int confirmVol(TastingVolConfirmDTO confirmDTO) {
        int flag = 0;
        List<TastingVolConfirmTaskDTO> taskDTOS = confirmDTO.getTaskDTOS();

        if (!confirmDTO.getConfirmResult().equals(TastingState.ConfirmResult.PASS)) {

            LambdaQueryWrapper<TQaInspectionTask> wrapper1 = Wrappers.<TQaInspectionTask>lambdaQuery()
                    .eq(TQaInspectionTask::getId, taskDTOS.get(0).getId());
            TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(wrapper1);
            TQaSampling sampling = samplingMapper.selectById(inspectionTask.getSamplingId());

            // 酒精度确定不合格，给取样人员发送酒精度确认不通过的通知
            SendMessageDTO sendMessageDTO = new SendMessageDTO();
            sendMessageDTO.setTitle("酒精度不合格");
            sendMessageDTO.setContent(sampling.getSamplingOrder() + "酒精度不合格");
            sendMessageDTO.setUserId(sampling.getSamplingPeopleId());
            sendMessageDTO.setIsSendUser(true);
            messageService.sendMessage(sendMessageDTO);
            return flag;
        }
        if (taskDTOS.size() > 0) {
            //校验批量信息数据是否合规
//            for (TastingVolConfirmTaskDTO taskDTO : taskDTOS) {
//                if (taskDTO.getTemperature() == null || taskDTO.getVol() == null) {
//                    log.info("酒精度确定交酒数据错误==>" + "酒精度或酒液温度不能为空");
//                    throw new BaseKnownException(9999, "酒精度确定交酒数据错误==>" + "酒精度或酒液温度不能为空");
//                }
//            }

            for (TastingVolConfirmTaskDTO taskDTO : taskDTOS) {
                TQaTasting tasting = tastingMapper.selectById(taskDTO.getId());
                tasting.setConfirmResult(confirmDTO.getConfirmResult());
                tasting.setConfirmPeople(confirmDTO.getConfirmPeople());
                tasting.setConfirmRemark(confirmDTO.getConfirmRemark());
                tasting.setConfirmDate(new Date());
                tasting.setUpdateTime(new Date());
                tasting.setTastingState(TastingState.CONFIRM_ALCOHOL);
                flag += tastingMapper.updateById(tasting);
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(taskDTO.getInspectionId());
                if (Integer.parseInt(inspectionTask.getState()) < 3) {
                    inspectionTask.setState(InspectionTaskState.CONFIRM_ALCOHOL);
                    inspectionTask.setUpdateTime(new Date());
                    flag += inspectionTaskMapper.updateById(inspectionTask);
                }
                if (taskDTO.getTemperature() == null || taskDTO.getVol() == null) {
                    //如果没有酒精度，不再调用酿酒计算更新折60度量
                    continue;
                }
                // 调用基酒接口
                AlcoholConfirmUpdateDTO alcoholConfirmUpdateDTO = new AlcoholConfirmUpdateDTO();
                alcoholConfirmUpdateDTO.setHandinTaskCode(inspectionTask.getAssociatedDocument());
                alcoholConfirmUpdateDTO.setTemperature(taskDTO.getTemperature());
                alcoholConfirmUpdateDTO.setVol(taskDTO.getVol());
                ResultVO<Integer> resultVO = wineHandInClient.alcoholConfirmUpdate(alcoholConfirmUpdateDTO);
                if (resultVO.getCode() != 200) {
                    throw new BaseKnownException(10000, "基酒数据同步失败");
                }

                // 通过交酒编号，温度，酒度，更新60度折算重量
                UpdateQuantityForQmDTO updateQuantityForQmDTO = new UpdateQuantityForQmDTO();
                updateQuantityForQmDTO.setHandInTaskCode(inspectionTask.getAssociatedDocument());
                updateQuantityForQmDTO.setModifyStandardVol(new Double(taskDTO.getVol()));
                updateQuantityForQmDTO.setWineTemperature(new Double(taskDTO.getTemperature()));
                wineHandInClient.updateTo60QuantityForQm(updateQuantityForQmDTO);
            }
        }

        return flag;
    }

    @Override
    @Transactional
    public int confirmAppVol(List<TastingVolConfirmAppDTO> confirmAppDTOS) {
        int flag = 0;

        if (!confirmAppDTOS.isEmpty()) {
            //校验批量信息数据是否合规
            for (TastingVolConfirmAppDTO taskDTO : confirmAppDTOS) {
                if (taskDTO.getTemperature() == null || taskDTO.getVol() == null) {
                    log.info("酒精度确定交酒数据错误==>" + "酒精度或酒液温度不能为空");
                    throw new BaseKnownException(9999, "酒精度或酒液温度不能为空");
                }
            }

            for (TastingVolConfirmAppDTO taskDTO : confirmAppDTOS) {
                TQaTasting tasting = tastingMapper.selectById(taskDTO.getId());
                tasting.setConfirmResult(taskDTO.getConfirmResult());
                tasting.setConfirmPeople(taskDTO.getConfirmPeople());
                tasting.setConfirmRemark(taskDTO.getConfirmRemark());
                tasting.setConfirmDate(new Date());
                tasting.setUpdateTime(new Date());
                tasting.setTastingState(TastingState.CONFIRM_ALCOHOL);
                flag += tastingMapper.updateById(tasting);
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(taskDTO.getInspectionId());
                if (Integer.parseInt(inspectionTask.getState()) < 3) {
                    inspectionTask.setState(InspectionTaskState.CONFIRM_ALCOHOL);
                    inspectionTask.setUpdateTime(new Date());
                    flag += inspectionTaskMapper.updateById(inspectionTask);
                }

                // 调用基酒接口
                AlcoholConfirmUpdateDTO alcoholConfirmUpdateDTO = new AlcoholConfirmUpdateDTO();
                alcoholConfirmUpdateDTO.setHandinTaskCode(inspectionTask.getAssociatedDocument());
                alcoholConfirmUpdateDTO.setTemperature(taskDTO.getTemperature());
                alcoholConfirmUpdateDTO.setVol(taskDTO.getVol());
                ResultVO<Integer> resultVO = wineHandInClient.alcoholConfirmUpdate(alcoholConfirmUpdateDTO);
                if (resultVO.getCode() != 200) {
                    throw new BaseKnownException(10000, "基酒数据同步失败");
                }
            }
        }
        return flag;
    }

    /*
     * @description: 基酒定级复评
     * <AUTHOR>
     * @date 2022/3/16 17:42
     * @param gradingDTOs
     * @return int
     */
    @Override
    @Transactional
    public int grading(List<TastingGradingDTO> gradingDTOS) {
        int flag = 0;
        if (gradingDTOS.size() > 0) {
            for (TastingGradingDTO gradingDTO : gradingDTOS) {
                if (StringUtil.isEmpty(gradingDTO.getLevel()) || StringUtil.isEmpty(gradingDTO.getLeesType())) {
                    throw new BaseKnownException(10000, "存在级别或糟源类别为空，不能执行定级操作");
                }
                TQaTasting tasting = tastingMapper.selectById(gradingDTO.getId());
                tasting.setTastingPeople(gradingDTO.getTastingPeople());
                tasting.setTastingDate(new Date());
                tasting.setFinalLevel(gradingDTO.getLevel());
                tasting.setUpdateTime(new Date());
                tasting.setCompleteState("1");
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(tasting.getInspectionId());
                if (gradingDTO.getFlag()) {// 复评
                    tasting.setTastingDate(gradingDTO.getTastingDate());
                    tasting.setReviewLeesType(gradingDTO.getLeesType());
                    tasting.setReviewLevel(gradingDTO.getLevel());
                    tasting.setReviewBaseWineIds(gradingDTO.getBaseWineIds());
                    tasting.setReviewEvaluateIds(gradingDTO.getEvaluateIds());
                    if (TastingState.TASTING_COMPLETE.equals(tasting.getTastingState())) {
                        tasting.setTastingState(TastingState.REPEAT_COMPLETE);
                    } else {
                        throw new BaseKnownException(10000, "存在未定级的记录，不能执行复评操作");
                    }
//                    if (!inspectionTask.getLeesType().equals(gradingDTO.getLeesType())) {
//                        inspectionTask.setLeesType(gradingDTO.getLeesType());
//                        flag += inspectionTaskMapper.updateById(inspectionTask);
//                    }
                } else { // 定级
                    tasting.setGradingLeesType(gradingDTO.getLeesType());
                    tasting.setLevel(gradingDTO.getLevel());
                    tasting.setBaseWineIds(gradingDTO.getBaseWineIds());
                    tasting.setGradingEvaluateIds(gradingDTO.getEvaluateIds());
//                    if (TastingState.CONFIRM_ALCOHOL.equals(tasting.getTastingState())) {
//                        tasting.setTastingState(TastingState.TASTING_COMPLETE);
//                    } else {
//                        throw new BaseKnownException(10000, "存在未酒精度确定的记录，不能执行定级操作");
//                    }
                    tasting.setTastingState(TastingState.TASTING_COMPLETE);
                    if (Integer.parseInt(inspectionTask.getState()) < 4) {
                        inspectionTask.setState(InspectionTaskState.ALREADY_GRADING);

                    }
                    inspectionTask.setReceiveState(InspectionTaskState.ReceiveState.CAN_RECEIVE);
                    inspectionTask.setUpdateTime(new Date());
                    flag += inspectionTaskMapper.updateById(inspectionTask);
                }
                flag += tastingMapper.updateById(tasting);

                // 调用酿酒接口
                QualityTaskUpdateDTO qualityTaskUpdateDTO = new QualityTaskUpdateDTO();
                qualityTaskUpdateDTO.setHandinTaskCode(inspectionTask.getAssociatedDocument());
                qualityTaskUpdateDTO.setGradingValue(gradingDTO.getLevel());
                qualityTaskUpdateDTO.setFlag(gradingDTO.getFlag());
                qualityTaskUpdateDTO.setTastingDate(tasting.getTastingDate());
                qualityTaskUpdateDTO.setGradingEvaluateIds(gradingDTO.getEvaluateIds());
                ResultVO<Integer> resultVO = wineHandInClient.qualityTaskUpdate(qualityTaskUpdateDTO);
                if (resultVO.getCode() != 200) {
                    log.info(resultVO.getMessage());
                } else {
                    //成功后发送交酒预填数据到小程序
                    SamplingAppletSendDTO sendDTO = new SamplingAppletSendDTO();
                    List<String> samplingCodeList = new ArrayList<>();
                    samplingCodeList.add(inspectionTask.getSamplingCode());
                    sendDTO.setSamplingCodeList(samplingCodeList);
                    try {
                        samplingService.sendSamplingListDataToApplet(sendDTO);
                    } catch (Exception e) {
                        log.error("发送交酒预调数据失败" + e.getMessage());
                    }
                }
            }
        }

        return flag;
    }

    @Override
    @Transactional
    public int saveGrading(List<TastingGradingDTO> gradingDTOS) {
        int flag = 0;
        if (gradingDTOS.size() > 0) {
            for (TastingGradingDTO gradingDTO : gradingDTOS) {
                TQaTasting tasting = tastingMapper.selectById(gradingDTO.getId());
                tasting.setTastingPeople(gradingDTO.getTastingPeople());
                tasting.setTastingDate(new Date());
                tasting.setFinalLevel(gradingDTO.getLevel());
                tasting.setUpdateTime(new Date());
                tasting.setGradingLeesType(gradingDTO.getLeesType());
                tasting.setLevel(gradingDTO.getLevel());
                tasting.setBaseWineIds(gradingDTO.getBaseWineIds());
                tasting.setGradingEvaluateIds(gradingDTO.getEvaluateIds());
                flag += tastingMapper.updateById(tasting);
            }
        }
        return flag;
    }

    /*
     * @description: 根据尝评任务id获取尝评详情页面数据
     * <AUTHOR>
     * @date 2022/3/17 9:19
     * @param id
     * @return com.hvisions.quality.dto.quality.tasting.TastingDetailDataDTO
     */
    @Override
    public TastingDetailDataDTO getTastingDetailDataById(Integer id) {
        TastingDetailDataDTO tastingDetailDataDTO = new TastingDetailDataDTO();
        TastingTaskDetailDTO tastingTaskDetailById = tastingMapper.getTastingTaskDetailById(id);

        if ((tastingTaskDetailById.getSamplingId() != null && tastingTaskDetailById.getSamplingId() > 0)) {
            // 根据取样id获取取样采集数据列表
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, tastingTaskDetailById.getSamplingId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            tastingDetailDataDTO.setSamplingCollectListDTOS(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));

        }
        tastingDetailDataDTO.setTastingTaskDetailDTO(tastingTaskDetailById);
        return tastingDetailDataDTO;

    }

    /*
     * @description: 根据样品编码获取尝评任务详情
     * <AUTHOR>
     * @date 2022/3/23 14:22
     * @param samplingCode
     * @return com.hvisions.quality.dto.quality.tasting.TastingDetailDataDTO
     */
    @Override
    public TastingDetailDataDTO getTastingDetailDataBySamplingCode(String samplingCode) {
        TastingDetailDataDTO tastingDetailDataDTO = new TastingDetailDataDTO();
        TastingTaskDetailDTO tastingTaskDetailById = tastingMapper.getTastingTaskDetailBySamplingCode(samplingCode);

        if (tastingTaskDetailById != null && tastingTaskDetailById.getSamplingId() != null && tastingTaskDetailById.getSamplingId() > 0) {
            // 根据取样id获取取样采集数据列表
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, tastingTaskDetailById.getSamplingId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            tastingDetailDataDTO.setSamplingCollectListDTOS(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));
        }
        tastingDetailDataDTO.setTastingTaskDetailDTO(tastingTaskDetailById);
        return tastingDetailDataDTO;
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("mes调用中控");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("sys");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }

    /***
     * @Description 接收尝评系统定级数据
     *
     * <AUTHOR>
     * @Date 2022-12-15 11:47
     * @param tastingGradingPushDTOS
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer getGradeData(List<TastingGradingPushDTO> tastingGradingPushDTOS) throws ParseException, UnsupportedEncodingException {
        final LogDto logadd = getBaseLog("尝品系统", "/tasting/grade_data",
                "接收尝评系统定级数据");
        logadd.setLogType(1);
        logadd.setLogParameter(JSONArray.toJSONString(tastingGradingPushDTOS));
        logCaptureClient.logRecord(logadd);

        int res = 0;
        for (TastingGradingPushDTO tastingGradingPushDTO : tastingGradingPushDTOS) {
            List<TQaInspectionTask> inspectionTasks = inspectionTaskMapper.selectList(Wrappers.<TQaInspectionTask>lambdaQuery().eq(TQaInspectionTask::getSamplingCode, tastingGradingPushDTO.getSpecimenNo()));
            if (inspectionTasks.size() > 0) {
                TQaInspectionTask inspectionTask = inspectionTasks.get(0);
                List<TQaTasting> tQaTastings = tastingMapper.selectList(Wrappers.<TQaTasting>lambdaQuery().eq(TQaTasting::getInspectionId, inspectionTask.getId()));
                if (tQaTastings.size() > 0) {
                    TQaTasting tasting = tQaTastings.get(0);
                    QualityTaskUpdateDTO qualityTaskUpdateDTO = new QualityTaskUpdateDTO();

                    TastingEvaluteDTO tastingEvaluteDTO = this.evaluateOperate(tastingGradingPushDTO.getEvaluate());
                    String typeIds = null;
                    String valueIds = null;
                    String cpEvaluate = null;

                    if (StringUtil.isNotEmpty(tastingEvaluteDTO)) {
                        typeIds = tastingEvaluteDTO.getTypeIds();
                        valueIds = tastingEvaluteDTO.getValueIds();
                        cpEvaluate = tastingEvaluteDTO.getCpEvaluate();
                    }
                    tasting.setTastingDate(DateUtil.toDateDay(tastingGradingPushDTO.getTastingDate()));
                    tasting.setFinalLevel(tastingGradingPushDTO.getLevel());
                    tasting.setUpdateTime(new Date());
                    tasting.setAcceptDate(new Date());
                    tasting.setCompleteState("1");
                    if (TastingState.TASTING_COMPLETE.equals(tasting.getTastingState())) {
                        // 第二次接收
                        qualityTaskUpdateDTO.setFlag(Boolean.TRUE);

                        tasting.setReviewLeesType(tastingGradingPushDTO.getVinasseCode());
                        tasting.setReviewLevel(tastingGradingPushDTO.getLevel());
                        tasting.setReviewBaseWineIds(typeIds);
                        tasting.setReviewEvaluateIds(valueIds);
                        tasting.setCpReviewEvaluate(cpEvaluate);
                        tasting.setTastingState(TastingState.REPEAT_COMPLETE);
                    } else {
                        // 第一次接收，定级
                        qualityTaskUpdateDTO.setFlag(Boolean.FALSE);

                        tasting.setGradingLeesType(tastingGradingPushDTO.getVinasseCode());
                        tasting.setLevel(tastingGradingPushDTO.getLevel());
                        tasting.setBaseWineIds(typeIds);
                        tasting.setGradingEvaluateIds(valueIds);
                        tasting.setCpEvaluate(cpEvaluate);
                        tasting.setTastingState(TastingState.TASTING_COMPLETE);
                        if (Integer.parseInt(inspectionTask.getState()) < 4) {
                            inspectionTask.setState(InspectionTaskState.ALREADY_GRADING);

                        }
                        inspectionTask.setReceiveState(InspectionTaskState.ReceiveState.CAN_RECEIVE);
                        inspectionTask.setUpdateTime(new Date());
                        inspectionTaskMapper.updateById(inspectionTask);
                    }
                    tasting.setDockState(TastingState.DockState.COMPLETE);
                    res += tastingMapper.updateById(tasting);
                    log.info("评语类型id==>" + typeIds);
                    log.info("评语值id==>" + valueIds);
                    log.info("定级系统数据接收成功===>" + inspectionTask.getSamplingCode());

                    qualityTaskUpdateDTO.setHandinTaskCode(inspectionTask.getAssociatedDocument());
                    qualityTaskUpdateDTO.setGradingValue(tastingGradingPushDTO.getLevel());
                    qualityTaskUpdateDTO.setTastingDate(tasting.getTastingDate());
                    qualityTaskUpdateDTO.setGradingEvaluateIds(valueIds);
                    // 将定级数据推送给酿酒
                    ResultVO<Integer> resultVO = wineHandInClient.qualityTaskUpdate(qualityTaskUpdateDTO);
                    if (resultVO.getCode() != 200) {
                        log.info(resultVO.getMessage());
                    }
                }
            }
        }
        return res;
    }

    public TastingEvaluteDTO evaluateOperate(String evaluate) throws UnsupportedEncodingException {
        if (StringUtil.isNotEmpty(evaluate)) {
            evaluate = URLDecoder.decode(evaluate, "UTF-8");
            TastingEvaluteDTO tastingEvaluteDTO = new TastingEvaluteDTO();
            String[] evaluateList = evaluate.split(",");
            List<Integer> typeIds = new ArrayList<>();
            List<Integer> valueIds = new ArrayList<>();
            List<String> cpEvaluateList = new ArrayList<>();
            for (String s : evaluateList) {
                List<TQaBaseWineEvaluate> baseWineEvaluates = baseWineEvaluateMapper.selectList(Wrappers.<TQaBaseWineEvaluate>lambdaQuery()
                        .eq(TQaBaseWineEvaluate::getEvaluate, s));
                if (baseWineEvaluates.size() > 0) {
                    valueIds.add(baseWineEvaluates.get(0).getId());
                    TQaBaseWine baseWine = baseWineMapper.selectById(baseWineEvaluates.get(0).getBaseWineId());
                    if (StringUtil.isNotEmpty(baseWine)) {
                        typeIds.add(baseWine.getId());
                    }
                } else {
                    cpEvaluateList.add(s);
                }
            }
            tastingEvaluteDTO.setTypeIds(StringUtils.join(typeIds, ","));
            tastingEvaluteDTO.setValueIds(StringUtils.join(valueIds, ","));
            tastingEvaluteDTO.setCpEvaluate(StringUtils.join(cpEvaluateList, ","));
            return tastingEvaluteDTO;

        }
        return null;
    }

    /***
     * @Description 定级场景选择
     *
     * <AUTHOR>
     * @Date 2022-12-5 11:24
     * @param tastingSceneSelectDTO
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer selectScene(TastingSceneSelectDTO tastingSceneSelectDTO) {
        AtomicInteger res = new AtomicInteger();
        tastingSceneSelectDTO.getIds().forEach(id -> {
            TQaTasting tasting = tastingMapper.selectById(id);
            tasting.setUpdateTime(new Date());
            tasting.setSceneDate(new Date());
            tasting.setScenePeople(tastingSceneSelectDTO.getScenePeople());
            tasting.setSceneRemark(tastingSceneSelectDTO.getSceneRemark());
            tasting.setTastingScene(tastingSceneSelectDTO.getTastingScene());
            if (TastingState.NEWLY_BUILD.equals(tasting.getTastingState()) || TastingState.SELECT_SCENE.equals(tasting.getTastingState())) {
                tasting.setTastingState(TastingState.SELECT_SCENE);
            } else {
                throw new BaseKnownException(10000, "存在不是新建状态的任务，不能进行场景选择");
            }
            if (tastingSceneSelectDTO.getTastingScene().equals("2")) {
                // 如果是集中定级，对接状态未 待推送
                tasting.setDockState(TastingState.DockState.WAIT_PUSH);
            } else if (tastingSceneSelectDTO.getTastingScene().equals("1")) {
//                if (TastingState.DockState.WAIT_PUSH.compareTo(tasting.getDockState()) == 1) {
//                    throw new BaseKnownException(10000, tasting.getTastingOrder() + "已经推送给定级系统，不能修改场景");
//                }
            }
            res.addAndGet(tastingMapper.updateById(tasting));
        });
        return res.get();
    }

    /***
     * @Description 推送尝评任务
     *
     * <AUTHOR>
     * @Date 2022-12-5 15:43
     * @param ids
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer pushTasting(List<Integer> ids) {
        int res = 0;
        List<CpxtBaseDataDto> list = new ArrayList<>();

        for (Integer id : ids) {
            TQaTasting tasting = tastingMapper.selectById(id);
//            if (!TastingState.CONFIRM_ALCOHOL.equals(tasting.getTastingState())) {
//                throw new BaseKnownException(10000, tasting.getTastingOrder() + "未确定酒精度");
//            }
            if (TastingState.DockState.WAIT_PUSH.equals(tasting.getDockState())) {
                tasting.setDockState(TastingState.DockState.EXECUTING);
                tasting.setPushDate(new Date());
                res += tastingMapper.updateById(tasting);
            }
            TastingTaskDetailDTO taskDetailDTO = tastingMapper.getTastingTaskDetailById(id);

            // 获取酿酒重量
            ResultVO<Double> resultVO = wineHandInClient.getQmQuality(taskDetailDTO.getAssociatedDocument());
            if (resultVO.getCode() != 200) {
                throw new BaseKnownException(10000, "获取酿酒重量失败!");
            }
            Double qmQuality = resultVO.getData();

//            String standardVol = "";
//            // 获取标准酒度
//            if (StringUtil.isNotEmpty(taskDetailDTO.getVol()) && StringUtil.isNotEmpty(taskDetailDTO.getTemperature())) {
//                MeasureVolToStandardVolDTO measureVolToStandardVolDTO = new MeasureVolToStandardVolDTO();
//                measureVolToStandardVolDTO.setMeasureTemperature(new BigDecimal(taskDetailDTO.getTemperature()));
//                measureVolToStandardVolDTO.setMeasureVol(new BigDecimal(taskDetailDTO.getVol()));
//                ResultVO<MeasureVolToStandardVolVO> resultVO1 = measureVolToStandardVolClient.getMeasureVolToStandardVol(measureVolToStandardVolDTO);
//                if (resultVO1.getCode() != 200) {
//                    throw new BaseKnownException(10000, "获取标准酒度失败!");
//                }
//                MeasureVolToStandardVolVO measureVolToStandardVol = resultVO1.getData();
//                standardVol = measureVolToStandardVol.getStandardVol().setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString();
//            }

            CpxtBaseDataDto cpxtBaseDataDto = new CpxtBaseDataDto();
            cpxtBaseDataDto.setSn(taskDetailDTO.getSamplingCode());
            cpxtBaseDataDto.setSid(taskDetailDTO.getLeesType() + (StringUtil.isNotEmpty(taskDetailDTO.getStageNum()) ? taskDetailDTO.getStageNum() : ""));
            //cpxtBaseDataDto.setSdid(standardVol);
            cpxtBaseDataDto.setWight((StringUtil.isEmpty(qmQuality) || qmQuality.intValue() == 0) ? "4000" : Integer.toString(qmQuality.intValue()));
            list.add(cpxtBaseDataDto);

        }
        // 调用定级系统接口推送定级任务
        CpxtBaseResponseDto cpxtBaseResponseDto = requestCpxt.dockingCpxt(list, "");
        if (!"200".equals(cpxtBaseResponseDto.getCode())) {
            throw new BaseKnownException(10000, "尝评任务推送失败，原因是" + cpxtBaseResponseDto.getMessage());
        }
        return res;
    }

    /***
     * @Description 获取定级任务进度
     *
     * <AUTHOR>
     * @Date 2022-12-5 16:11
     * @param queryDTO
     * @return com.hvisions.quality.dto.quality.tasting.TastingPushRateDTO
     **/
    @Override
    public TastingPushRateDTO getPushRate(TastingPageQueryDTO queryDTO) {
        List<TastingPageDTO> tastingPageList = tastingMapper.getTastingPageList(queryDTO);
        TastingPushRateDTO tastingPushRateDTO = new TastingPushRateDTO();
        tastingPushRateDTO.setTotal(tastingPageList.size());
        int a = 0, b = 0, c = 0;
        for (TastingPageDTO tastingPageDTO : tastingPageList) {
            if (TastingState.DockState.WAIT_PUSH.equals(tastingPageDTO.getDockState())) {
                a++;
            } else if (TastingState.DockState.EXECUTING.equals(tastingPageDTO.getDockState())) {
                b++;
            } else if (TastingState.DockState.COMPLETE.equals(tastingPageDTO.getDockState())) {
                c++;
            }
        }

        tastingPushRateDTO.setWaitPush(a);
        tastingPushRateDTO.setExecute(b);
        tastingPushRateDTO.setComplete(c);
        return tastingPushRateDTO;
    }
}
