package com.hvisions.quality.quality.service.imp;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.auth.client.MessageClient;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.message.MessageCreateDTO;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.quality.dto.quality.review.ReviewDTO;
import com.hvisions.quality.dto.quality.review.ReviewInsertDTO;
import com.hvisions.quality.dto.quality.review.ReviewPageDTO;
import com.hvisions.quality.dto.quality.review.ReviewPageQueryDTO;
import com.hvisions.quality.quality.consts.InspectionTaskState;
import com.hvisions.quality.quality.dao.*;
import com.hvisions.quality.quality.entity.TQaInspectionTask;
import com.hvisions.quality.quality.entity.TQaReview;
import com.hvisions.quality.quality.entity.TQaSampling;
import com.hvisions.quality.quality.service.ReviewService;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.StringUtil;
import com.hvisions.quality.utils.note.SendSms;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 曲粉异常评审
 * @date 2022/2/28 18:37
 */
@Slf4j
@Service
public class ReviewServiceImpl implements ReviewService {

    @Resource
    private ReviewMapper reviewMapper;

    @Resource
    private PowderInspectionMapper powderInspectionMapper;

    @Resource
    private SamplingMapper samplingMapper;

    @Resource
    private DisqualifiedContentMapper disqualifiedContentMapper;

    @Resource
    private UserClient userClient;


    @Resource
    private SerialNumberMapper serialNumberMapper;

    @Resource
    private MessageClient messageClient;

    @Override
    public Page<ReviewPageDTO> getReviewPageList(ReviewPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(reviewMapper::getReviewPageList, queryDTO, ReviewPageDTO.class);
    }


    @Override
    @Transactional
    public Integer addReview(ReviewInsertDTO reviewInsertDTO) {
        int res = 0;
        TQaReview review = DtoMapper.convert(reviewInsertDTO, TQaReview.class);
        // 曲粉评审编码生成规则：PS+流水号（0001递增）
        String serialNumber = serialNumberMapper.getSerialNumber("t_qa_review", 4, false);
        review.setCode("PS" + serialNumber);
        // 检验任务变成待审核

        TQaInspectionTask inspectionTask = powderInspectionMapper.selectOne(Wrappers.<TQaInspectionTask>lambdaQuery().
                eq(TQaInspectionTask::getInspectionOrder, reviewInsertDTO.getInspectionOrder())
                .last("limit 1")
        );

        inspectionTask.setState(InspectionTaskState.AWAIT_REVIEW);
        inspectionTask.setCreateTime(new Date());
        inspectionTask.setUpdateTime(new Date());
        review.setInspectionId(inspectionTask.getId());
        res += reviewMapper.insert(review);
        res += powderInspectionMapper.updateById(inspectionTask);


        TQaSampling sampling = samplingMapper.selectById(inspectionTask.getSamplingId());
        String disqualifiedContents = disqualifiedContentMapper.getDisqualifiedContents(reviewInsertDTO.getDisqualifiedCountIds());
        String[] msg = new String[]{
                DateUtil.format(sampling.getActualDate(), "yyyy-MM-dd"),
                inspectionTask.getSamplingName(),
                inspectionTask.getSamplingCode(),
                disqualifiedContents
        };
        // 发送指标异常短信给 数据接收人 QFZJJGJSR --- id : 147
        this.sendSampleRejectionNotice(147, "2192208", msg);

        return res;
    }

    /**
     * 触发时间： 检验员发起异常指标评审时，触发此短信
     * 短信名称：指标异常通知
     * 接收人: 送样人 + 数据接收人
     * 内容: 在MES系统中，您于{送样时间}送往微生物分析组的{样品种类} 样品，编码为：{样品编码}，检验出异常指标：{异常类型}，请前往系统中进行异常评审
     */
    public void sendSampleRejectionNotice(Integer roleId, String template, String[] msg) {
        try {
            List<UserDTO> userDTOS = userClient.getUsersByRole(roleId).getData();
            List<String> phones = new ArrayList<>();
            for (UserDTO userDTO : userDTOS) {
                if (StringUtil.isNotEmpty(userDTO.getMobilePhone())) {
                    phones.add("+86" + userDTO.getMobilePhone());
                }
            }
            SendSms.sendNote(template, msg, phones.toArray(new String[phones.size()]));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendReviewResultNotice(Integer userId, Integer roleId, String template, String[] msg) {
        try {
            UserDTO userDTO1 = userClient.getUser(userId).getData();
            List<UserDTO> userDTOS = userClient.getUsersByRole(roleId).getData();
            List<String> phones = new ArrayList<>();
            phones.add("+86" + userDTO1.getMobilePhone());
            for (UserDTO userDTO : userDTOS) {
                if (StringUtil.isNotEmpty(userDTO.getMobilePhone())) {
                    phones.add("+86" + userDTO.getMobilePhone());
                }
            }
            SendSms.sendNote(template, msg, phones.toArray(new String[phones.size()]));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Integer review(ReviewDTO reviewDTO) {
        int res = 0;
        TQaReview review = reviewMapper.selectById(reviewDTO.getId());
        review.setOpinion(reviewDTO.getOpinion());
        review.setResult(reviewDTO.getResult());
        review.setReviewTime(new Date());
        review.setReviewPeople(reviewDTO.getReviewPeople());
        res += reviewMapper.updateById(review);
        TQaInspectionTask inspectionTask = powderInspectionMapper.selectById(review.getInspectionId());
        inspectionTask.setUpdateTime(new Date());
        inspectionTask.setActualInspectionTime(new Date());

        if ("1".equals(reviewDTO.getResult())) {
            inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
        } else if ("2".equals(reviewDTO.getResult())) {
            inspectionTask.setState(InspectionTaskState.ALREADY_CLOSE);
        }
        res += powderInspectionMapper.updateById(inspectionTask);

        try {
            // 触发时间: 大曲理化指标不合格评审完成时，触发此短信
            // 短信名称：大曲理化指标异常评审完成通知
            // 接收人: 不合格评审发起人 + 大曲理化检验组长（DQLHJYZZ）
            // 内容: 在制曲MES系统中，于{异常评审发起时间}发起的大曲异常指标异常，样品编码为：{样品编码}，请登录手机APP或电脑端查看并处理理化指标异常评审单 (https://mes.lzlj.com/)
            String[] msg = new String[]{
                    DateUtil.format(review.getCreateTime(), "yyyy-MM-dd"),
                    inspectionTask.getSamplingCode(),
            };
            //   DQLHJYZZ --- id : 129
            this.sendReviewResultNotice(review.getCreatorId(), 129, "2466425", msg);

            // 小铃铛消息：角色编码 DQLHJYZY DQLHJYZZ DQWSWJYY DQWSWJYZZ 发送异常评审消息
            MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
            messageCreateDTO.setRoleIds(Arrays.asList(128, 129, 130, 131));
            messageCreateDTO.setTitle("异常评审完成");
            messageCreateDTO.setContent(inspectionTask.getSamplingCode() + "异常评审完成，请查看！");
            messageClient.createMessage(messageCreateDTO);


        } catch (Exception e) {
            log.info("大曲理化指标不合格评审完成短信发送失败：{}", e.getMessage());
        }

        return res;
    }

    @Override
    public Integer deleteReview(Integer id) {
        return reviewMapper.deleteById(id);
    }


}