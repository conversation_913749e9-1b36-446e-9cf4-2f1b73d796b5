package com.hvisions.quality.quality.service.imp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.client.WineHandInClient;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.purchase.client.ReceivingClient;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.VehicleInspectStateUpdateDTO;
import com.hvisions.quality.dto.quality.inspection.data.InspectionDataDetailDTO;
import com.hvisions.quality.dto.quality.inspection.production.ZPLabelShowDataDTO;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import com.hvisions.quality.dto.quality.sampling.*;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectListDTO;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectValueDTO;
import com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageDTO;
import com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageQueryDTO;
import com.hvisions.quality.quality.consts.InspectionTaskState;
import com.hvisions.quality.quality.consts.SamplingState;
import com.hvisions.quality.quality.dao.*;
import com.hvisions.quality.quality.entity.*;
import com.hvisions.quality.quality.service.ExamineService;
import com.hvisions.quality.quality.service.MessageService;
import com.hvisions.quality.quality.service.PrintService;
import com.hvisions.quality.quality.service.SamplingService;
import com.hvisions.quality.sap.RequestApplet;
import com.hvisions.quality.sap.constant.EsbConst;
import com.hvisions.quality.sap.dto.applet.*;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.GenerateCodeUtil;
import com.hvisions.quality.utils.IntervalTaskUtil;
import com.hvisions.quality.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description: 取样任务
 * @author: Jcao
 * @time: 2022/3/8 16:07
 */
@Service
@Slf4j
public class SamplingServiceImpl implements SamplingService {

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private SamplingMapper samplingMapper;

    @Resource
    private SamplingCollectMapper samplingCollectMapper;

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;

    @Resource
    IntervalTaskUtil intervalTaskUtil;

    @Resource
    private MessageService messageService;

    @Resource
    private ReceivingClient receivingClient;

    @Resource
    private PrintService printService;

    @Resource
    private ExamineService examineService;

    @Resource
    private TastingMapper tastingMapper;

    @Resource
    private SamplingGroupMapper samplingGroupMapper;

    @Resource
    private SerialNumberMapper serialNumberMapper;

    @Resource
    private ProductionInspectionMapper productionInspectionMapper;

    @Resource
    private InspectionDataMapper inspectionDataMapper;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Resource
    private IncomingInspectionMapper incomingInspectionMapper;

    @Resource
    private WineHandInClient wineHandInClient;

    @Resource
    private RequestApplet requestApplet;


    /*
     * @Description: 分页查询取样任务列表
     *
     * <AUTHOR>
     * @param samplingPageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.sampling.SamplingPageDTO>
     */
    @Override
    public Page<SamplingPageDTO> getSamplingPageList(SamplingPageQueryDTO samplingPageQueryDTO) {
        return PageHelperUtil.getPage(samplingMapper::getSamplingPageList, samplingPageQueryDTO, SamplingPageDTO.class);
    }

    /*
     * @Description: 分页查询样品报表列表
     *
     * <AUTHOR>
     * @param samplingReportPageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.sampling.report.SamplingReportPageDTO>
     */
    @Override
    public Page<SamplingReportPageDTO> getSamplingReportPageList(SamplingReportPageQueryDTO samplingReportPageQueryDTO) {
        return PageHelperUtil.getPage(samplingMapper::getSamplingReportPageList, samplingReportPageQueryDTO, SamplingReportPageDTO.class);
    }

    /*
     * @Description: 根据id获取取样任务详情
     *
     * <AUTHOR>
     * @param id: 取样任务id
     * @return com.hvisions.quality.dto.quality.sampling.SamplingDetailDTO
     */
    @Override
    public SamplingDetailDTO getSamplingDetail(Integer id) {
        SamplingDetailDTO samplingDetail = samplingMapper.getSamplingDetail(id);
        if (StringUtil.isNotEmpty(samplingDetail.getId())) {
            // 根据取样id获取取样采集数据列表
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, samplingDetail.getId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            samplingDetail.setSamplingCollects(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));
        }
        // 样品打印列表
        List<Integer> ids = new ArrayList<>();
        ids.add(samplingDetail.getId());
        List<LabelPrintCountListDTO> labelPrintCounts = samplingMapper.getLabelPrintCountList(ids);
        samplingDetail.setLabelPrintCounts(labelPrintCounts);
        return samplingDetail;
    }

    /*
     * @Description: 插入采集数据
     *
     * <AUTHOR>
     * @param id:
     * @return java.lang.Integer
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveSamplingCollectValue(List<SamplingCollectValueDTO> samplingCollects) {
        Integer res = 0;

        Double wineTemperature=null;
        Double standardVol=null;
        Integer samplingId=0;
        for (SamplingCollectValueDTO samplingCollect : samplingCollects) {
            TQaSamplingCollect collect = new TQaSamplingCollect();
            collect.setId(samplingCollect.getId());
            collect.setCollectValue(samplingCollect.getCollectValue());
            collect.setUpdateTime(new Date());
            collect.setUpdaterId(samplingCollect.getUpdaterId());
            TQaSamplingCollect tQaSamplingCollect = samplingCollectMapper.selectById(samplingCollect.getId());
            if(null!=tQaSamplingCollect){
                if("酒精度".equals(tQaSamplingCollect.getItemName()) && StringUtil.isNotEmpty(samplingCollect.getCollectValue())){
                    standardVol= Double.valueOf(samplingCollect.getCollectValue());
                }else if("酒液温度".equals(tQaSamplingCollect.getItemName()) && StringUtil.isNotEmpty(samplingCollect.getCollectValue())){
                    wineTemperature= Double.valueOf(samplingCollect.getCollectValue());
                }
                samplingId=tQaSamplingCollect.getSamplingId();
            }
            res += samplingCollectMapper.updateById(collect);
        }
        LambdaQueryWrapper<TQaInspectionTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TQaInspectionTask::getSamplingId,samplingId);
        TQaInspectionTask tQaInspectionTask = incomingInspectionMapper.selectOne(wrapper);
        if(null!=tQaInspectionTask){
            //刷新交酒数据数据
            ResultVO resultVO = wineHandInClient.updateTPoWorkshopHandinTask(tQaInspectionTask.getAssociatedDocument(), wineTemperature, standardVol);
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(),"刷新交酒数据失败，失败原因："+resultVO.getMessage());
            }
        }

        return res;
    }


    /*
     * @Description: 根据关联单据打印样品
     *
     * <AUTHOR>
     * @param associatedDocument: 关联单据
     * @return java.lang.Boolean
     */
    @Override
    public Boolean print(String associatedDocument) {
        List<TQaInspectionTask> inspectionTasks = inspectionTaskMapper.selectList(new QueryWrapper<TQaInspectionTask>()
                .eq("associated_document", associatedDocument)
                .orderByDesc("create_time"));
        if (inspectionTasks != null && inspectionTasks.size() > 0) {
            TQaInspectionTask inspectionTask = inspectionTasks.get(0);
            Map<String, List<String>> map = new HashMap<>();
            List<String> codes = new ArrayList<>();
            codes.add(inspectionTask.getSamplingCode());

            map.put(inspectionTask.getInspectionType(), codes);
            // 打印机 0-高粱稻壳打印、1-糟培检验打印、2-基酒检验打印、3-熟糠打印、4-小麦打印、5-试验检验打印
            printService.printLabel(map);
        } else {
            throw new BaseKnownException(10000, "打印异常，根据当前单据未获取到样品信息！");
        }
        return true;
    }

    /**
     * @param samplingPrintDTO:
     * @return java.lang.Integer
     * @Description: 组合取样
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Integer addSamplingGroup(SamplingPrintDTO samplingPrintDTO) {

        // 1、创建取样组合，
        // 2、执行取样

        Integer res = 0;

        String number = serialNumberMapper.getSerialNumber("t_qa_sampling_group", 4, true);
        TQaSamplingGroup samplingGroup = new TQaSamplingGroup();
        samplingGroup.setCode("QYZH" + DateUtil.dateFormat(new Date(), "yyMMdd") + number);
        samplingGroup.setCreateTime(new Date());
        samplingGroup.setUpdateTime(new Date());
        // 插入组合
        res += samplingGroupMapper.insert(samplingGroup);

        if (samplingPrintDTO.getIds() != null && samplingPrintDTO.getIds().size() > 0) {
            Map<String, List<String>> map = new HashMap<>();
            List<String> llCodes = new ArrayList<>(); // 高粱稻壳
            List<String> xmCodes = new ArrayList<>(); // 小麦和成品曲
            List<String> skCodes = new ArrayList<>(); // 熟糠
            List<String> jjCodes = new ArrayList<>(); // 基酒
            List<String> zpCodes = new ArrayList<>(); // 糟培
            List<String> syCodes = new ArrayList<>(); // 试验曲和微生物曲
            // 根据取样ids获取取样任务列表
            List<TQaSampling> tQaSamplings = samplingMapper.selectBatchIds(samplingPrintDTO.getIds());
            for (TQaSampling tQaSampling : tQaSamplings) {
                // 根据取样任务获取检验任务列表
                LambdaQueryWrapper<TQaInspectionTask> wrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(StringUtil.isNotEmpty(samplingPrintDTO.getTaskId()), TQaInspectionTask::getId, samplingPrintDTO.getTaskId())
                        .eq(TQaInspectionTask::getSamplingId, tQaSampling.getId())
                        .eq(TQaInspectionTask::getDeleted, 0);
                List<TQaInspectionTask> tQaInspectionTasks = inspectionTaskMapper.selectList(wrapper);
                for (TQaInspectionTask tQaInspectionTask : tQaInspectionTasks) {
                    if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.GRAIN_INSPECTION)) {
                        // 根据关联单号校验是否为送货车辆，校验送货车辆是否入场，修改车辆状态为质检中
                        VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO = new VehicleInspectStateUpdateDTO();
                        vehicleInspectStateUpdateDTO.setDeliveryNumber(tQaInspectionTask.getAssociatedDocument());
                        vehicleInspectStateUpdateDTO.setInspectionOrder(tQaInspectionTask.getInspectionOrder());
                        vehicleInspectStateUpdateDTO.setVehicleInspectState("1");
                        ResultVO<Integer> resultVO = receivingClient.updateVehicleInspectState(vehicleInspectStateUpdateDTO);
                        if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
                            throw new BaseKnownException(10000, tQaInspectionTask.getAssociatedDocument() + "送货单号修改失败!");
                        }
                        String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(tQaInspectionTask.getId());
                        log.info("打印的场景名称：" + sceneName);
                        if ("熟糠检验".equals(sceneName)) {
                            skCodes.add(tQaInspectionTask.getSamplingCode());
                        } else if ("小麦来料检验".equals(sceneName)) {
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        } else if ("有机小麦来料检验".equals(sceneName)) {
                            //2025-01-15 拆分小麦检验
                            //打印地址和小麦来料检验一样
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        } else {
                            // 高粱稻壳
                            llCodes.add(tQaInspectionTask.getSamplingCode());
                        }
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.FERMENTED_GRAIN_INSPECTION)) {
                        zpCodes.add(tQaInspectionTask.getSamplingCode());
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.BASE_WINE_INSPECTION)) {
                        jjCodes.add(tQaInspectionTask.getSamplingCode());
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.POWDER_INSPECTION)) {
                        String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(tQaInspectionTask.getId());
                        log.info("打印条码的场景为：{}==={}", tQaInspectionTask.getSamplingCode(), sceneName);
                        if ("成品大曲理化检验".equals(sceneName) || "制曲试验微生物检验".equals(sceneName) || "制曲试验理化检验".equals(sceneName)) {
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        } else {
                            // 试验大曲和大曲微生物
                            syCodes.add(tQaInspectionTask.getSamplingCode());
                        }
                    }
                    Integer qty = StringUtil.isNotEmpty(tQaInspectionTask.getPrintCount()) ? tQaInspectionTask.getPrintCount() + 1 : 1;
                    TQaInspectionTask inspectionTask = new TQaInspectionTask();
                    inspectionTask.setId(tQaInspectionTask.getId());
                    inspectionTask.setPrintCount(qty);
                    inspectionTask.setSamplingGroupId(samplingGroup.getId());
                    // 第一次打印，修改检验状态为已取样
                    if (qty == 1) {
                        if (!InspectionTaskState.InspectionType.BASE_WINE_INSPECTION.equals(inspectionTask.getInspectionType())) {
                            // 如果不是基酒检验，则收样状态变成可收养
                            inspectionTask.setReceiveState(InspectionTaskState.ReceiveState.CAN_RECEIVE);
                        } else {
                            // 基酒任务取样，修改尝评任务修改时间
                            TQaTasting tasting = tastingMapper.selectOne(Wrappers.<TQaTasting>lambdaQuery()
                                    .eq(TQaTasting::getInspectionId, inspectionTask.getId())
                                    .eq(TQaTasting::getDeleted, "0")
                                    .eq(TQaTasting::getTastingState, "0"));
                            if (StringUtil.isNotEmpty(tasting)) {
                                tasting.setUpdateTime(new Date());
                                tastingMapper.updateById(tasting);
                            }
                        }
                        inspectionTask.setState(InspectionTaskState.ALREADY_SAMPLED);
                        if (InspectionTaskState.InspectionType.POWDER_INSPECTION.equals(tQaInspectionTask.getInspectionType())) {
                            // 如果是曲粉检验，试验审批
                            examineService.updateExamineSampleNumber(tQaInspectionTask.getAssociatedDocument(), tQaInspectionTask.getSamplingName());
                        }

                        try {
                            // 一天（24h）后未进行接收时,给报检人所属角色发送消息通知
                            intervalTaskUtil.postDelay(() -> {
                                TQaInspectionTask newInspectionTask = inspectionTaskMapper.selectById(inspectionTask.getId());
                                if (newInspectionTask.getState().equals(InspectionTaskState.ALREADY_SAMPLED)) {
                                    SendMessageDTO sendMessageDTO = new SendMessageDTO();
                                    sendMessageDTO.setTitle("样品超时未接收");
                                    sendMessageDTO.setContent(newInspectionTask.getSamplingCode() + "样品超时未接收");
                                    sendMessageDTO.setUserId(newInspectionTask.getCreatorId());
                                    sendMessageDTO.setIsSendUser(false);
                                    messageService.sendMessage(sendMessageDTO);
                                }
                            }, 24 * 60 * 60 * 1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    inspectionTask.setUpdateTime(new Date());
                    res += inspectionTaskMapper.updateById(inspectionTask);
                }
                // 判断所有检验任务是否已全部打印，修改检验任务为已收料
                Integer printCount = samplingMapper.getNotPrintTask(tQaSampling.getId());
                if (SamplingState.WAIT_SAMPLING.equals(tQaSampling.getState()) && printCount <= 0) {
                    TQaSampling sampling = new TQaSampling();
                    sampling.setId(tQaSampling.getId());
                    sampling.setState(SamplingState.COMPLETE_SAMPLING);
                    sampling.setSamplingPeople(samplingPrintDTO.getSamplingPeople());
                    sampling.setSamplingPeopleId(samplingPrintDTO.getCreatorId());
                    sampling.setActualDate(new Date());
                    samplingMapper.updateById(sampling);
                }
            }

            // 打印机 0-高粱稻壳打印、1-糟培检验打印、2-基酒检验打印、3-熟糠打印、4-小麦打印、5-试验检验打印
            map.put("0", llCodes);
            map.put("1", zpCodes);
            map.put("2", jjCodes);
            map.put("3", skCodes);
            map.put("4", xmCodes);
            map.put("5", syCodes);
            printService.printLabel(map); // 上线打开服务
        } else {
            throw new BaseKnownException(10000, "取样任务id不能为空！");
        }
        return res;

    }

    /*
     * @Description: 取样标签打印
     *
     * <AUTHOR>
     * @param ids: 取样任务id集合
     * @return java.lang.Integer
     */
    @Override
    public Integer labelPrint(SamplingPrintDTO samplingPrintDTO) {
        Integer res = 0;
        // 判断检验任务是否为空，进行单个任务打印或批量打印
        if (samplingPrintDTO.getIds() != null && samplingPrintDTO.getIds().size() > 0) {
            Map<String, List<String>> map = new HashMap<>();
            List<String> llCodes = new ArrayList<>(); // 高粱稻壳
            List<String> xmCodes = new ArrayList<>(); // 小麦和成品曲
            List<String> skCodes = new ArrayList<>(); // 熟糠
            List<String> jjCodes = new ArrayList<>(); // 基酒
            List<String> zpCodes = new ArrayList<>(); // 糟培
            List<String> syCodes = new ArrayList<>(); // 试验曲和微生物曲
            // 根据取样ids获取取样任务列表
            List<TQaSampling> tQaSamplings = samplingMapper.selectBatchIds(samplingPrintDTO.getIds());
            for (TQaSampling tQaSampling : tQaSamplings) {
                // 根据取样任务获取检验任务列表
                LambdaQueryWrapper<TQaInspectionTask> wrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(StringUtil.isNotEmpty(samplingPrintDTO.getTaskId()), TQaInspectionTask::getId, samplingPrintDTO.getTaskId())
                        .eq(TQaInspectionTask::getSamplingId, tQaSampling.getId())
                        .eq(TQaInspectionTask::getDeleted, 0);
                List<TQaInspectionTask> tQaInspectionTasks = inspectionTaskMapper.selectList(wrapper);
                for (TQaInspectionTask tQaInspectionTask : tQaInspectionTasks) {
                    if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.GRAIN_INSPECTION)) {
                        String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(tQaInspectionTask.getId());
                        // 粮食检验中的熟糠检验不用更改车辆
                        if (!"熟糠检验".equals(sceneName)) {
                            // 粮食检验 根据关联单号校验是否为送货车辆，校验送货车辆是否入场，修改车辆状态为质检中
                            VehicleInspectStateUpdateDTO vehicleInspectStateUpdateDTO = new VehicleInspectStateUpdateDTO();
                            vehicleInspectStateUpdateDTO.setDeliveryNumber(tQaInspectionTask.getAssociatedDocument());
                            vehicleInspectStateUpdateDTO.setInspectionOrder(tQaInspectionTask.getInspectionOrder());
                            vehicleInspectStateUpdateDTO.setVehicleInspectState("1");
                            ResultVO<Integer> resultVO = receivingClient.updateVehicleInspectState(vehicleInspectStateUpdateDTO);
                            if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
                                throw new BaseKnownException(10000, tQaInspectionTask.getAssociatedDocument() + "送货单号修改车辆检验状态失败!");
                            }
                        }
                        log.info("打印的场景名称：" + sceneName);
                        if ("熟糠检验".equals(sceneName)) {
                            skCodes.add(tQaInspectionTask.getSamplingCode());
                        } else if ("小麦来料检验".equals(sceneName)) {
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        }  else if ("有机小麦来料检验".equals(sceneName)) {
                            //2025-01-15 拆分小麦检验
                            //打印地址和小麦来料检验一样
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        } else {
                            // 高粱稻壳
                            llCodes.add(tQaInspectionTask.getSamplingCode());
                        }
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.FERMENTED_GRAIN_INSPECTION)) {
                        zpCodes.add(tQaInspectionTask.getSamplingCode());
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.BASE_WINE_INSPECTION)) {
                        jjCodes.add(tQaInspectionTask.getSamplingCode());
                    } else if (tQaInspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.POWDER_INSPECTION)) {
                        String sceneName = inspectionTaskMapper.getSceneNameByInspectionId(tQaInspectionTask.getId());
                        log.info("打印条码的场景为：{}==={}", tQaInspectionTask.getSamplingCode(), sceneName);

                        // todo 成品大曲理化检验的编码要重新生成，加上年度流水号
                        if ("成品大曲理化检验".equals(sceneName)) {
                            // 第一次取样（打印次数为 0）
                            if (tQaInspectionTask.getPrintCount() == 0){
                                String yearSerialNumber = generateCodeUtil.generateYearPowderInspectionCode("C");
                                String newSampleCode = tQaInspectionTask.getSamplingCode() + "-" + yearSerialNumber;
                                // 修改库中的编码
                                log.info("修改库中的编码为{}", newSampleCode);
                                tQaInspectionTask.setSamplingCode(newSampleCode);
                                inspectionTaskMapper.updateById(tQaInspectionTask);
                                xmCodes.add(newSampleCode);
                            } else {
                                xmCodes.add(tQaInspectionTask.getSamplingCode());
                            }

                        }
                        if ("制曲试验微生物检验".equals(sceneName) || "制曲试验理化检验".equals(sceneName)) {
                            xmCodes.add(tQaInspectionTask.getSamplingCode());
                        } else {
                            // 试验大曲和大曲微生物
                            syCodes.add(tQaInspectionTask.getSamplingCode());
                        }
                    }
                    Integer qty = StringUtil.isNotEmpty(tQaInspectionTask.getPrintCount()) ? tQaInspectionTask.getPrintCount() + 1 : 1;
                    TQaInspectionTask inspectionTask = new TQaInspectionTask();
                    inspectionTask.setId(tQaInspectionTask.getId());
                    inspectionTask.setPrintCount(qty);
                    // 第一次打印，修改检验状态为已取样
                    if (qty == 1) {
                        if (!InspectionTaskState.InspectionType.BASE_WINE_INSPECTION.equals(inspectionTask.getInspectionType())) {
                            // 如果不是基酒检验，则收样状态变成可收养
                            inspectionTask.setReceiveState(InspectionTaskState.ReceiveState.CAN_RECEIVE);
                        } else {
                            // 基酒任务取样，修改尝评任务修改时间
                            TQaTasting tasting = tastingMapper.selectOne(Wrappers.<TQaTasting>lambdaQuery()
                                    .eq(TQaTasting::getInspectionId, inspectionTask.getId())
                                    .eq(TQaTasting::getDeleted, "0")
                                    .eq(TQaTasting::getTastingState, "0"));
                            if (StringUtil.isNotEmpty(tasting)) {
                                tasting.setUpdateTime(new Date());
                                tastingMapper.updateById(tasting);
                            }
                        }
                        inspectionTask.setState(InspectionTaskState.ALREADY_SAMPLED);
                        log.info("第一次打印=={}", JSONObject.toJSON(inspectionTask));
                        if (InspectionTaskState.InspectionType.POWDER_INSPECTION.equals(tQaInspectionTask.getInspectionType())) {
                            // 如果是曲粉检验，试验审批
                            log.info("yyy=={}", JSONObject.toJSON(inspectionTask));
                            examineService.updateExamineSampleNumber(tQaInspectionTask.getAssociatedDocument(), tQaInspectionTask.getSamplingName());
                        }
                        try {
                            // 一天（24h）后未进行接收时,给报检人所属角色发送消息通知
                            intervalTaskUtil.postDelay(() -> {
                                TQaInspectionTask newInspectionTask = inspectionTaskMapper.selectById(inspectionTask.getId());
                                if (newInspectionTask.getState().equals(InspectionTaskState.ALREADY_SAMPLED)) {
                                    SendMessageDTO sendMessageDTO = new SendMessageDTO();
                                    sendMessageDTO.setTitle("样品超时未接收");
                                    sendMessageDTO.setContent(newInspectionTask.getSamplingCode() + "样品超时未接收");
                                    sendMessageDTO.setUserId(newInspectionTask.getCreatorId());
                                    sendMessageDTO.setIsSendUser(false);
                                    messageService.sendMessage(sendMessageDTO);
                                }
                            }, 24 * 60 * 60 * 1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    inspectionTask.setUpdateTime(new Date());
                    res += inspectionTaskMapper.updateById(inspectionTask);
                }
                // 判断所有检验任务是否已全部打印，修改检验任务为已收料
                Integer printCount = samplingMapper.getNotPrintTask(tQaSampling.getId());
                if (SamplingState.WAIT_SAMPLING.equals(tQaSampling.getState()) && printCount <= 0) {
                    TQaSampling sampling = new TQaSampling();
                    sampling.setId(tQaSampling.getId());
                    sampling.setState(SamplingState.COMPLETE_SAMPLING);
                    sampling.setSamplingPeople(samplingPrintDTO.getSamplingPeople());
                    sampling.setSamplingPeopleId(samplingPrintDTO.getCreatorId());
                    sampling.setActualDate(new Date());
                    samplingMapper.updateById(sampling);
                }
            }
            // 打印机 0-高粱稻壳打印、1-糟培检验打印、2-基酒检验打印、3-熟糠打印、4-小麦打印、5-试验检验打印
            map.put("0", llCodes);
            map.put("1", zpCodes);
            map.put("2", jjCodes);
            map.put("3", skCodes);
            map.put("4", xmCodes);
            map.put("5", syCodes);
            if (!CollectionUtils.isEmpty(jjCodes)) {
                //基酒检验取样打印推送数据到小程序
                for (String jjCode : jjCodes) {
                    //发送数据给小程序
                    try {
                        sendDataToSamplingApplet(jjCode);
                    } catch (Exception e) {
                        log.error(jjCode + "推送小程序出错" + e.getMessage(), e);
                    }
                }
            }
            printService.printLabel(map);   // 上线打开服务
        } else {
            throw new BaseKnownException(10000, "取样任务id不能为空！");
        }
        return res;
    }

    /**
     * 同步交酒预填数据发送小程序
     * @param samplingCode
     */
    public void sendDataToSamplingApplet(String samplingCode) {
        //增加推送原度重量
        SamplingAppletDTO samplingAppletDTO = samplingMapper.getSamplingAppletSelect(samplingCode);
        if ("尾子罐".equals(samplingAppletDTO.getZaoyuanCategory())) {
            log.info("当前交酒类别为尾子罐，不发送数据, code:" + samplingCode);
            return;
        }
        //根据样品编码获取取样人的工号
        String[] split = samplingCode.split("-");
        samplingAppletDTO.setPlanYear("20" + split[0]);
        //定级场景
        if ("1".equals(samplingAppletDTO.getRatedType())) {
            samplingAppletDTO.setRatedType("OnSite");
            //log.info("现场定级，不发送数据, code:" + samplingCode);
            //return;
        } else if ("2".equals(samplingAppletDTO.getRatedType())){
            samplingAppletDTO.setRatedType("Concentrated");
        } else {
            log.info("当前定级场景不做推送,定级场景:" + samplingAppletDTO.getRatedType() + ", code:" + samplingCode);
            return;
        }
        // 设置根据交酒完成时间设置排次信息
        String planRowByDate = samplingMapper.getPlanRowByDate(samplingAppletDTO.getEndTime(), samplingAppletDTO.getLocationId());
        log.info("根据车间和时间查询排次信息，车间:{}, 时间:{}, 查询到的排次信息:{}", samplingAppletDTO.getLocationId(), samplingAppletDTO.getEndTime(), planRowByDate);
        samplingAppletDTO.setOrderTrans(planRowByDate);
        // 获取酿酒重量
        ResultVO<Double> resultVO = wineHandInClient.getQmQuality(samplingAppletDTO.getHandinTaskCode());
        if (resultVO.getCode() != 200) {
            throw new BaseKnownException(10000, "获取酿酒重量失败!");
        }
        Double qmQuality = resultVO.getData();
        samplingAppletDTO.setOriginalWeight((StringUtil.isEmpty(qmQuality) || qmQuality.intValue() == 0) ? "4000" : Integer.toString(qmQuality.intValue()));
        sendSamplingDataToApplet(samplingAppletDTO);
    }

    /**
     * 发送数据接口
     * @param samplingAppletDTO
     */
    @Override
    public AppletBaseResponseDto sendSamplingDataToApplet(SamplingAppletDTO samplingAppletDTO) {
        AppletBaseDataDTO baseDataDTO = new AppletBaseDataDTO();
        BeanUtils.copyProperties(samplingAppletDTO, baseDataDTO);
        //更改推送批次
        baseDataDTO.setBatchNum(DateUtil.dateFormat(new Date(), "yyyyMMdd") + baseDataDTO.getBatchNum());
        return requestApplet.dockingApplet(baseDataDTO, EsbConst.APPLET_SAMPLING_SEND_URI, "同步交酒预填数据");
    }

    /**
     * 批量发送小程序数据
     * @param sendDTO
     * @return
     */
    @Override
    public Integer sendSamplingListDataToApplet(SamplingAppletSendDTO sendDTO) {
        Integer add = 0;
        List<String> samplingCodeList = sendDTO.getSamplingCodeList();
        for (String samplingCode : samplingCodeList) {
            log.info("准备发送小程序交酒预填写数据:" + samplingCode);
            sendDataToSamplingApplet(samplingCode);
            add ++;
        }
        return add;
    }

    @Override
    public Integer updateSampling(SamplingDetailDTO samplingDetailDTO) {
        TQaSampling sampling = samplingMapper.selectById(samplingDetailDTO.getId());
        sampling.setSamplingLocation(samplingDetailDTO.getSamplingLocation());
        return samplingMapper.updateById(sampling);
    }

    @Override
    public Integer updateSamplingPeople(SamplingPeopleDTO samplingPeopleDTO) {
        TQaSampling sampling = samplingMapper.selectById(samplingPeopleDTO.getId());
        sampling.setSamplingPeople(samplingPeopleDTO.getSamplingPeople());
        sampling.setSamplingPeopleId(samplingPeopleDTO.getSamplingPeopleId());
        return samplingMapper.updateById(sampling);
    }

    @Override
    public ZPLabelShowDataDTO vspecQueryBySampleCode(SamplingPeopleDTO samplingPeopleDTO) {
        if(StringUtil.isEmpty(samplingPeopleDTO.getSampleCode())){
            throw new BaseKnownException(10000, "样品编码不能为空！");
        }
        ZPLabelShowDataDTO labelShowData = productionInspectionMapper.getZPLabelShowData(samplingPeopleDTO.getSampleCode());
        return labelShowData;
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public Integer vspecDataPush(List<VspecDataPushDTO> vspecDataPushDTOs) {
        log.info("=========="+vspecDataPushDTOs.toString());
        if(vspecDataPushDTOs.size()<=0 || vspecDataPushDTOs == null){
            throw new BaseKnownException(10000, "推送数据不能为空！");
        }
        AtomicReference<Integer> count = new AtomicReference<>(0);
        Set<String> set = new HashSet<>();
        if(vspecDataPushDTOs.size()>0){
            vspecDataPushDTOs.forEach(item->{
                LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                        .eq(TQaInspectionTask::getSamplingCode, item.getSampleCode())
                        .eq(TQaInspectionTask::getDeleted, 0);
                TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
                if (StringUtil.isEmpty(inspectionTask)) {
                    throw new BaseKnownException(10004, "样品编码错误！");
                }else{
                    inspectionTask.setActualInspectionTime(DateUtil.toDate(item.getAnalysisDate()));
                    inspectionTask.setInspectionName(item.getAnalyst());
                    //inspectionTask.setInspectionResult("合格");
                    inspectionTask.setState("8");
                    inspectionTaskMapper.updateById(inspectionTask);
                }
                List<InspectionDataDetailDTO> inspectionDataDetailDTOList = productionInspectionMapper.getProductionInspectionDataBySamplingCode(item.getSampleCode());
                if (StringUtil.isEmpty(inspectionDataDetailDTOList) || inspectionDataDetailDTOList.size() == 0) {
                    throw new BaseKnownException(10001, item.getSampleCode()+"样品编码没查询到生产过程检验任务检验数据！");
                }else{
                    item.getVspecDataPushItemDTOs().forEach(itme2->{
                        set.add(itme2.getIndicatorCode()+"_"+itme2.getIndicatorName());
                    });
                    List<Integer> ids = inspectionDataDetailDTOList.stream().map(InspectionDataDetailDTO::getId).collect(Collectors.toList());
                    List<TQaInspectionData> list = inspectionDataMapper.selectBatchIds(ids);
                    if(list.size()>0){
                        list.forEach(item1->{
                            item.getVspecDataPushItemDTOs().forEach(itme2->{

                                if(itme2.getIndicatorCode().equals(item1.getIndicatorCode())){
                                    item1.setValue(itme2.getValue());
                                    item1.setSampleCode(item.getSampleCode());
                                    item1.setAnalysisDate(DateUtil.toDate(item.getAnalysisDate()));
                                    item1.setAnalyst(item.getAnalyst());
                                    item1.setDetection(item.getDetection());
                                    int i = inspectionDataMapper.updateById(item1);
                                    count.updateAndGet(v -> v + i);
                                    set.remove(itme2.getIndicatorCode()+"_"+itme2.getIndicatorName());
                                }
                            });
                        });
                    }else{
                        throw new BaseKnownException(10002, "没查询到指标数据！");
                    }
                }
            });
        }
        if(set.size()>0){
            throw new BaseKnownException(10003, set.toString()+"指标编码错误");
        }
        final LogDto log = getBaseLog("传输威斯派克检验数据", vspecDataPushDTOs.get(0).getSampleCode(),
                "接收威斯派克检验数据");
        log.setLogParameter(JSONObject.toJSONString(vspecDataPushDTOs));
        log.setLogType(1);
        logCaptureClient.logRecord(log);
        return count.get();
    }

    private LogDto getBaseLog(String logModular, String location, String methodName) {
        LogDto logDto = new LogDto();
        logDto.setControllerName("VSPEC传输MES");
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logModular);
        logDto.setLogInvocation("SYS");
        logDto.setLocation(location);
        logDto.setMethodName(methodName);
        return logDto;
    }
}
