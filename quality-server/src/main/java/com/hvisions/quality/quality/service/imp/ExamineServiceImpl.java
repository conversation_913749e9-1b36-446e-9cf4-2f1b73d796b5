package com.hvisions.quality.quality.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.auth.client.UserClient;
import com.hvisions.auth.dto.user.UserDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.quality.dto.quality.examine.ExamineAuditDTO;
import com.hvisions.quality.dto.quality.examine.ExamineInsertDTO;
import com.hvisions.quality.dto.quality.examine.ExaminePageDTO;
import com.hvisions.quality.dto.quality.examine.ExaminePageQueryDTO;
import com.hvisions.quality.dto.quality.examine.detail.ExamineDetailDTO;
import com.hvisions.quality.quality.consts.ExamineState;
import com.hvisions.quality.quality.dao.ExamineDetailMapper;
import com.hvisions.quality.quality.dao.ExamineMapper;
import com.hvisions.quality.quality.entity.TQaExamine;
import com.hvisions.quality.quality.entity.TQaExamineDetail;
import com.hvisions.quality.quality.service.ExamineService;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.GenerateCodeUtil;
import com.hvisions.quality.utils.StringUtil;
import com.hvisions.quality.utils.note.SendSms;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 试验审批单据
 * @author: yyy
 * @time: 2022/3/8 14:40
 */
@Slf4j
@Service
public class ExamineServiceImpl implements ExamineService {

    @Resource
    private ExamineMapper examineMapper;

    @Resource
    private ExamineDetailMapper examineDetailMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    /*
     * @Description: 分页查询试验审批单据列表
     *
     * <AUTHOR>
     * @param examinePageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.examine.ExaminePageDTO>
     */
    @Override
    public Page<ExaminePageDTO> getExaminePageList(ExaminePageQueryDTO examinePageQueryDTO) {
        return PageHelperUtil.getPage(examineMapper::getExaminePageList, examinePageQueryDTO, ExaminePageDTO.class);
    }

    /**
     * @Description 修改试验审批已取样数量
     *
     * <AUTHOR>
     * @Date 2024-7-5 16:46
     * @param orderNo
     * @param samplingName
     * @return java.lang.Integer
     **/
    @Override
    public Integer updateExamineSampleNumber(String orderNo, String samplingName) {
        log.info("yyy=={}=={}", orderNo, samplingName);

        int res = 0;
        TQaExamine examine = examineMapper.selectOne(Wrappers.<TQaExamine>lambdaQuery()
                .eq(TQaExamine::getOrderNo, orderNo)
                .last("limit 1")
        );
        if (StringUtil.isNotEmpty(examine)) {
            TQaExamineDetail examineDetail = examineDetailMapper.selectOne(Wrappers.<TQaExamineDetail>lambdaQuery()
                    .eq(TQaExamineDetail::getExamineId, examine.getId())
                    .eq(TQaExamineDetail::getMaterialName, samplingName)
                    .last("limit 1")
            );
            if (StringUtil.isNotEmpty(examineDetail)) {
                examineDetail.setSamplingNumber(examineDetail.getSamplingNumber() + 1);
                res += examineDetailMapper.updateById(examineDetail);
            }
        }
        return res;
    }

    /**
     * @Description 新增实验审批单据
     *
     * <AUTHOR>
     * @Date 2023-12-25 14:56
     * @param examineInsertDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer addExamine(ExamineInsertDTO examineInsertDTO) {
        int res = 0;
        // 新增审批单据
        TQaExamine examine = DtoMapper.convert(examineInsertDTO, TQaExamine.class);
        examine.setOrderNo(generateCodeUtil.generateCode("SP", 4));
        examine.setTestCode(generateCodeUtil.generateCode("A", 3));
        examine.setCreateTime(new Date());
        examine.setUpdateTime(new Date());
        examine.setState(ExamineState.AUDITING);
        res += examineMapper.insert(examine);
        // 新增审批详情单据
        List<ExamineDetailDTO> examineDetailDTOList = examineInsertDTO.getExamineDetailDTOList();
        if (examineDetailDTOList.size() > 0) {
            for (ExamineDetailDTO examineDetailDTO : examineDetailDTOList) {
                TQaExamineDetail examineDetail = DtoMapper.convert(examineDetailDTO, TQaExamineDetail.class);
                examineDetail.setExamineId(examine.getId());
                examineDetail.setCreateTime(new Date());
                examineDetail.setUpdateTime(new Date());
                res += examineDetailMapper.insert(examineDetail);
            }
        }

        try {
            ResultVO<UserDTO> resultVO = userClient.getUser(examine.getCreatorId());
            String launchUser = resultVO.getData().getUserName();

            // 给一级审批人发信息
//            短信名称：试验审批通知
//            接收人:  审批人
//            内容: 在制曲MES系统中，由{审批发起人}，于{发起时间}发起的试验需要您审批，实验名称为：{实验名称}，请登录手机 APP 或在网页端进行查看并审批: (https://mes.lzlj.com/)
            String[] msg = new String[]{
                    launchUser,
                    DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"),
                    examine.getName()
            };
            this.sendExamineAuditingNotice(examine.getAuditOneId(), "2466424", msg);
        } catch (Exception e) {
            log.info("试验审批短信发送失败：{}", e.getMessage());
        }

        return res;
    }

    public void sendExamineAuditingNotice(Integer userId, String template, String[] msg) {
        try {
            UserDTO user = userClient.getUser(userId).getData();
            List<String> phones = new ArrayList<>();
            phones.add("+86" + user.getMobilePhone());
            SendSms.sendNote(template, msg, phones.toArray(new String[phones.size()]));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Integer updateExamine(ExamineInsertDTO examineInsertDTO) {
        int res = 0;
        // 修改审批单据
        TQaExamine examine = DtoMapper.convert(examineInsertDTO, TQaExamine.class);
        examine.setUpdateTime(new Date());
        res += examineMapper.updateById(examine);
        // 新增修改审批详情单据
        List<ExamineDetailDTO> examineDetailDTOList = examineInsertDTO.getExamineDetailDTOList();
        if (examineDetailDTOList.size() > 0) {
            for (ExamineDetailDTO examineDetailDTO : examineDetailDTOList) {
                TQaExamineDetail examineDetail = DtoMapper.convert(examineDetailDTO, TQaExamineDetail.class);
                if (StringUtil.isNotEmpty(examineDetail.getId())) {
                    examineDetail.setUpdateTime(new Date());
                    examineDetail.setExamineId(examine.getId());
                    res += examineDetailMapper.updateById(examineDetail);
                } else {
                    examineDetail.setExamineId(examine.getId());
                    examineDetail.setCreateTime(new Date());
                    examineDetail.setUpdateTime(new Date());
                    res += examineDetailMapper.insert(examineDetail);
                }
            }
        }
        return res;
    }

    /**
     * @Description 审批试验审批单据
     *
     * <AUTHOR>
     * @Date 2023-12-25 15:52
     * @param examineAuditDTO
     * @return java.lang.Integer
     **/
    @Override
    public Integer auditExamine(ExamineAuditDTO examineAuditDTO) {
        TQaExamine examine = examineMapper.selectById(examineAuditDTO.getId());
        if (1 == examineAuditDTO.getLevel()) {
            examine.setAuditOneRemark(examineAuditDTO.getAuditRemark());
            examine.setAuditOneResult(examineAuditDTO.getAuditResult());
            examine.setAuditOneTime(new Date());
            if (StringUtil.isEmpty(examine.getAuditTwoId())) {
                if (ExamineState.AuditResult.PASS.equals(examineAuditDTO.getAuditResult())) {
                    examine.setState(ExamineState.PASS);
                }
            } else {
                // 存在二级审批人
                try {
                    ResultVO<UserDTO> resultVO = userClient.getUser(examine.getCreatorId());
                    String launchUser = resultVO.getData().getUserName();

                    // 给二级审批人发信息
                    // 短信名称：试验审批通知
                    // 接收人:  审批人 KYYJSPR
                    // 内容: 在制曲MES系统中，由{审批发起人}，于{发起时间}发起的试验需要您审批，实验名称为：{实验名称}，请登录手机 APP 或在网页端进行查看并审批: (https://mes.lzlj.com/)
                    String[] msg = new String[]{
                            launchUser,
                            DateUtil.format(examine.getCreateTime(), "yyyy-MM-dd HH:mm:ss"),
                            examine.getName()
                    };
                    this.sendExamineAuditingNotice(examine.getAuditTwoId(), "2466424", msg);
                } catch (Exception e) {
                    log.info("试验审批短信发送失败：{}", e.getMessage());
                }
            }
        } else if (2 == examineAuditDTO.getLevel()) {
            examine.setAuditTwoRemark(examineAuditDTO.getAuditRemark());
            examine.setAuditTwoResult(examineAuditDTO.getAuditResult());
            examine.setAuditTwoTime(new Date());
            if (ExamineState.AuditResult.PASS.equals(examineAuditDTO.getAuditResult())) {
                // 2级评审且评审通过，状态变成已通过
                examine.setState(ExamineState.PASS);
            }
        }
        examine.setAuditResult(examineAuditDTO.getAuditResult());

        if (ExamineState.AuditResult.FAIL.equals(examineAuditDTO.getAuditResult())) {
            // 任意一个审核不通过，状态变成驳回
            examine.setState(ExamineState.REJECT);
        }
        return examineMapper.updateById(examine);
    }

    /**
     * @Description 关闭试验审批单据
     *
     * <AUTHOR>
     * @Date 2023-12-25 16:21
     * @param id
     * @return java.lang.Integer
     **/
    @Override
    public Integer closeExamine(Integer id) {
        TQaExamine examine = examineMapper.selectById(id);
        examine.setState(ExamineState.CLOSING);
        examine.setUpdateTime(new Date());
        return examineMapper.updateById(examine);
    }

    /**
     * @Description 判定是否用完
     *
     * <AUTHOR>
     * @Date 2023-12-25 16:25
     * @param id
     * @return java.lang.Boolean true-用完，false-没用完
     **/
    @Override
    public Boolean judgeIsUsed(Integer id) {
        TQaExamine examine = examineMapper.selectById(id);
        LambdaQueryWrapper<TQaExamineDetail> queryWrapper = new LambdaQueryWrapper<TQaExamineDetail>()
                .eq(TQaExamineDetail::getDeleted, 0)
                .eq(TQaExamineDetail::getExamineId, id);
        List<TQaExamineDetail> examineDetailList = examineDetailMapper.selectList(queryWrapper);
        int i = 0;
        for (TQaExamineDetail examineDetail : examineDetailList) {
            if (!examineDetail.getNumber().equals(examineDetail.getUseNumber())) {
                // 报检数量不等于样品数量
                i++;
            }
        }
        if (i == 0) {
            examine.setState(ExamineState.USE_UP);
            examine.setUpdateTime(new Date());
            examineMapper.updateById(examine);
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;

        }
    }

    /**
     * @Description 根据审批单id获取详情列表
     *
     * <AUTHOR>
     * @Date 2023-12-25 17:48
     * @param examineId
     * @return java.util.List<com.hvisions.quality.dto.quality.examine.detail.ExamineDetailDTO>
     **/
    @Override
    public List<ExamineDetailDTO> getExamineDetailList(Integer examineId) {
        return examineDetailMapper.getExamineDetailList(examineId);
    }

    /**
     * @Description 试验审批单据失效，每天0点执行，将到期单据失效
     *
     * <AUTHOR>
     * @Date 2023-12-25 16:10
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "expireExamineLock")
    public void expireExamine() {
        log.info("试验审批单据失效，每天0点执行，将到期单据失效,开始执行");
        // 获取未删除，状态不是已过期，试验结束日期小于当前时间的，将这些单据失效
        LambdaQueryWrapper<TQaExamine> queryWrapper = new LambdaQueryWrapper<TQaExamine>()
                .eq(TQaExamine::getDeleted, 0)
                .lt(TQaExamine::getEndTime, new Date())
                .ne(TQaExamine::getState, ExamineState.EXPIRE);
        List<TQaExamine> examineList = examineMapper.selectList(queryWrapper);
        if (examineList.size() > 0) {
            for (TQaExamine examine : examineList) {
                examine.setState(ExamineState.EXPIRE);
                examine.setUpdateTime(new Date());
                examineMapper.updateById(examine);
            }
        }
        log.info("试验审批单据失效，每天0点执行，将到期单据失效,开始执行");
    }
}
