package com.hvisions.quality.quality.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.purchase.client.ReceivingClient;
import com.hvisions.purchase.dto.purchase.daily.delivery.detail.VehicleStateUpdateDTO;
import com.hvisions.quality.dto.quality.disqualified.DisqualifiedRecordDTO;
import com.hvisions.quality.dto.quality.inspection.data.InspectionDataDetailDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.*;
import com.hvisions.quality.dto.quality.inspection.incoming.batch.IncomingInspectionBatchEnterDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.batch.InspectionDataBathDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.batch.InspectionProcessDataBatchDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.batch.InspectionTaskBatchDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.data.IncomingBuckleWeightDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.data.IncomingInspectionDataDTO;
import com.hvisions.quality.dto.quality.inspection.incoming.process.IncomingInspectionProcessDataDTO;
import com.hvisions.quality.dto.quality.inspection.production.ProductionInspectionPageDTO;
import com.hvisions.quality.dto.quality.inspection.production.ProductionInspectionPageQueryDTO;
import com.hvisions.quality.dto.quality.inspection.production.infrared.MidInfraredImportDTO;
import com.hvisions.quality.dto.quality.inspection.task.InspectionTaskDataDTO;
import com.hvisions.quality.dto.quality.message.SendMessageDTO;
import com.hvisions.quality.dto.quality.sampling.collect.SamplingCollectListDTO;
import com.hvisions.quality.quality.consts.*;
import com.hvisions.quality.quality.dao.*;
import com.hvisions.quality.quality.entity.*;
import com.hvisions.quality.quality.service.IncomingInspectionService;
import com.hvisions.quality.quality.service.MessageService;
import com.hvisions.quality.quality.service.ProductionInspectionService;
import com.hvisions.quality.utils.DateUtil;
import com.hvisions.quality.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 来料检验
 * @date 2022/3/11 9:26
 */
@Slf4j
@Service
public class IncomingInspectionServiceImpl implements IncomingInspectionService {

    @Resource
    private IncomingInspectionMapper incomingInspectionMapper;

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;

    @Resource
    private DisqualifiedMapper disqualifiedMapper;

    @Resource
    private InspectionDataMapper inspectionDataMapper;

    @Resource
    private InspectionProcessDataMapper inspectionProcessDataMapper;

    @Resource
    private InspectionDataItemMapper inspectionDataItemMapper;

    @Resource
    private InspectionDataRecordMapper inspectionDataRecordMapper;

    @Resource
    private StandardMapper standardMapper;
    @Resource
    private StandardMaterialMapper standardMaterialMapper;

    @Resource
    private StandardScenesMapper standardScenesMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private StandardScenesIndicatorMapper standardScenesIndicatorMapper;

    @Resource
    private SsIndicatorItemMapper ssIndicatorItemMapper;

    @Resource
    private IndicatorPointMapper indicatorPointMapper;

    @Resource
    private ProcessPointMapper processPointMapper;

    @Resource
    private SamplingCollectMapper samplingCollectMapper;

    @Resource
    private ReceiveGroupMapper receiveGroupMapper;

    @Resource
    private ReceiveGroupInspectionMapper receiveGroupInspectionMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private ReceivingClient receivingClient;

    @Resource
    private ProductionInspectionService productionInspectionService;

    @Resource
    private IndicatorMapper indicatorMapper;

    @Resource
    private  ProductionInspectionMapper productionInspectionMapper;

    @Resource
    private SamplingMapper samplingMapper;


    /*
     * @description: 分页查询来料检验任务列表
     * <AUTHOR>
     * @date 2022/3/11 9:42
     * @param pageQueryDTO
     * @return org.springframework.data.domain.Page<com.hvisions.quality.dto.quality.inspection.incoming.IncomingInspectionPageDTO>
     */
    @Override
    public Page<IncomingInspectionPageDTO> getIncomingInspectionPageList(IncomingInspectionPageQueryDTO pageQueryDTO) {
        return PageHelperUtil.getPage(incomingInspectionMapper::getIncomingInspectionPageList, pageQueryDTO, IncomingInspectionPageDTO.class);
    }

    /*
     * @description: 根据检验任务id获取来料检验单详情
     * <AUTHOR>
     * @date 2022/3/11 13:12
     * @param inspectionId
     * @return com.hvisions.quality.dto.quality.inspection.incoming.IncomingInspectionDetailDTO
     */
    @Override
    public IncomingInspectionDetailDTO getIncomingInspectionDetail(Integer inspectionId) {

        IncomingInspectionDetailDTO incomingInspectionDetailDTO = new IncomingInspectionDetailDTO();
        InspectionTaskDataDTO inspectionTaskData = inspectionTaskMapper.getInspectionTaskData(inspectionId);
        List<InspectionDataDetailDTO> inspectionDataDetailDTOList = inspectionTaskMapper.getInspectionDataByInspectionId(inspectionId);
        // 设置指标小数位
        List<Integer> indicatorIds = inspectionDataDetailDTOList.stream().map(InspectionDataDetailDTO::getIndicatorId).collect(Collectors.toList());
        List<TQaIndicator> tQaIndicators = indicatorMapper.selectList(new LambdaUpdateWrapper<TQaIndicator>().in(TQaIndicator::getId, indicatorIds));
        for (InspectionDataDetailDTO inspectionDataDetailDTO : inspectionDataDetailDTOList) {
            List<TQaIndicator> getlist = tQaIndicators.stream().filter(i -> i.getId().equals(inspectionDataDetailDTO.getIndicatorId()) && i.getDecimalPlace() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(getlist)) {
                inspectionDataDetailDTO.setDecimalPlace(getlist.get(0).getDecimalPlace());
            }
        }
        List<DisqualifiedRecordDTO> disqualifiedRecordList = disqualifiedMapper.getDisqualifiedRecord(inspectionId);
        incomingInspectionDetailDTO.setInspectionTaskDataDTO(inspectionTaskData);
        incomingInspectionDetailDTO.setInspectionDataDetailDTOList(inspectionDataDetailDTOList);
        incomingInspectionDetailDTO.setDisqualifiedRecordDTOList(disqualifiedRecordList);
        return incomingInspectionDetailDTO;
    }

    /*
     * @description: 校验检验标准是否是最新生效
     * <AUTHOR>
     * @date 2022/3/11 14:51
     * @param inspectionId
     * @return java.lang.Boolean
     */
    @Override
    public Boolean checkStandard(Integer inspectionId) {
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionId);

        if (inspectionTask != null && inspectionTask.getStandardScenesId() > 0) {
            // 根据物料id获取生效标准
            TQaStandardMaterial effectStandardMaterial = standardMaterialMapper.getEffectStandardMaterialByMaterial(inspectionTask.getMaterialId());
            if (StringUtil.isEmpty(effectStandardMaterial) || effectStandardMaterial.getStandardId() == 0) {
                return true; // 没有找到该物料生效的检验标准
            }

            TQaStandardScene standardScene = standardScenesMapper.selectById(inspectionTask.getStandardScenesId());
            if (standardScene != null && standardScene.getStandardId() > 0) {
                TQaStandard standard = standardMapper.selectById(standardScene.getStandardId());
                if (standard != null) {
                    if (standard.getStandardStatus().equals(StandardState.EFFECT)) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
//        throw new BaseKnownException(10000, "检验任务不存在,或者该检验任务不存在检验标准");
        return true;
    }

    /*
     * @description:同步检验标准
     * <AUTHOR>
     * @date 2022/3/11 14:47
     * @param syncInspectionStandardDTOList
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer syncStandard(List<SyncInspectionStandardDTO> syncInspectionStandardDTOList) {

        int flag = 0;
        for (SyncInspectionStandardDTO syncInspectionStandardDTO : syncInspectionStandardDTOList) {
            Integer inspectionId = syncInspectionStandardDTO.getId();
            // 根据物料id获取生效标准
            TQaStandardMaterial effectStandardMaterial = standardMaterialMapper.getEffectStandardMaterialByMaterial(syncInspectionStandardDTO.getMaterialId());
            if (StringUtil.isEmpty(effectStandardMaterial) || effectStandardMaterial.getStandardId() == 0) {
                throw new BaseKnownException(10000, "没有找到该物料的检验标准");
            }

            int standardId = effectStandardMaterial.getStandardId();
            log.info("生效的检验标准", standardId);

            LambdaQueryWrapper<TQaStandardScene> standardSceneLambdaQueryWrapper = Wrappers.<TQaStandardScene>lambdaQuery()
                    .eq(TQaStandardScene::getStandardId, standardId)
                    .eq(TQaStandardScene::getScenesId, syncInspectionStandardDTO.getSceneId())
                    .eq(TQaStandardScene::getDeleted, "0");
            List<TQaStandardScene> standardScenes = standardScenesMapper.selectList(standardSceneLambdaQueryWrapper);

            if (standardScenes == null || standardScenes.size() != 1) {
                throw new BaseKnownException(10000, "没有找该任务的检验场景");
            }
            TQaStandardScene newStandardScene = standardScenes.get(0);
            log.info("生效的标准场景{}", newStandardScene);

            TQaScene scene = sceneMapper.selectById(newStandardScene.getScenesId());

            /**
             * 获取后删除 删除检验数据， 删除检验子项数据，删除检验过程数据
             */
            LambdaQueryWrapper<TQaInspectionData> inspectionDataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                    .eq(TQaInspectionData::getInspectionId, inspectionId);
            List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(inspectionDataLambdaQueryWrapper);
            List<Integer> indicatorIdList = new ArrayList<>();
            if (inspectionDataList != null && inspectionDataList.size() > 0) {
                for (TQaInspectionData inspectionData : inspectionDataList) {
                    indicatorIdList.add(inspectionData.getIndicatorId());
                    LambdaQueryWrapper<TQaInspectionDataItem> inspectionDataItemLambdaQueryWrapper = Wrappers.<TQaInspectionDataItem>lambdaQuery()
                            .eq(TQaInspectionDataItem::getInspectionDataId, inspectionData.getId());
                    inspectionDataItemMapper.delete(inspectionDataItemLambdaQueryWrapper);
                    LambdaQueryWrapper<TQaInspectionProcessData> inspectionProcessDataLambdaQueryWrapper = Wrappers.<TQaInspectionProcessData>lambdaQuery()
                            .eq(TQaInspectionProcessData::getInspectionDataId, inspectionData.getId());
                    inspectionProcessDataMapper.delete(inspectionProcessDataLambdaQueryWrapper);
                }
            }
            inspectionDataMapper.delete(inspectionDataLambdaQueryWrapper);

            /**
             * 跟新检验相关数据数据
             */

            // 更新检验任务标准场景id
            TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionId);
            inspectionTask.setStandardScenesId(newStandardScene.getId());
            inspectionTask.setUpdateTime(new Date());
            flag += inspectionTaskMapper.updateById(inspectionTask);

            // 更新检验数据
            LambdaQueryWrapper<TQaStandardSceneIndicator> standardSceneIndicatorLambdaQueryWrapper = Wrappers.<TQaStandardSceneIndicator>lambdaQuery()
                    .eq(TQaStandardSceneIndicator::getStandardSceneId, newStandardScene.getId())
                    .eq(TQaStandardSceneIndicator::getDeleted, "0");
            List<TQaStandardSceneIndicator> standardSceneIndicators = standardScenesIndicatorMapper.selectList(standardSceneIndicatorLambdaQueryWrapper);
            log.info("标准场景下的指标集合:{}", standardSceneIndicators);
            if (standardSceneIndicators != null && standardSceneIndicators.size() > 0) {
                if (SceneNameConst.POWDER_P_SY_WSW.equals(scene.getSceneName())
                        || SceneNameConst.POWDER_P_SY_LH.equals(scene.getSceneName())
                        || SceneNameConst.POWDER_WSW.equals(scene.getSceneName())
                        || SceneNameConst.POWDER_SY_LH.equals(scene.getSceneName())
                ) {
                    for (TQaStandardSceneIndicator standardSceneIndicator : standardSceneIndicators) {
                        // 判定是否是之前选中的指标
                        if (indicatorIdList.contains(standardSceneIndicator.getIndicatorId())) {
                            TQaInspectionData inspectionData = DtoMapper.convert(standardSceneIndicator, TQaInspectionData.class);
                            inspectionData.setInspectionId(inspectionTask.getId());
                            inspectionData.setType(InspectionDataState.INSPECTION_SHEET);
                            inspectionData.setCreateTime(new Date());
                            inspectionData.setUpdateTime(new Date());
                            flag += inspectionDataMapper.insert(inspectionData);
                            // 获取指标下的过程项点，即检验过程数据
                            LambdaQueryWrapper<TQaIndicatorPoint> indicatorPointLambdaQueryWrapper = Wrappers.<TQaIndicatorPoint>lambdaQuery()
                                    .eq(TQaIndicatorPoint::getIndicatorId, standardSceneIndicator.getIndicatorId())
                                    .eq(TQaIndicatorPoint::getDeleted, "0");
                            List<TQaIndicatorPoint> indicatorPoints = indicatorPointMapper.selectList(indicatorPointLambdaQueryWrapper);
                            log.info("项点过程数据：{}", indicatorPoints);
                            // 新增检验过程数据
                            if (indicatorPoints != null && indicatorPoints.size() > 0) {
                                for (TQaIndicatorPoint indicatorPoint : indicatorPoints) {
                                    TQaProcessPoint processPoint = processPointMapper.selectById(indicatorPoint.getProcessPointId());
                                    TQaInspectionProcessData inspectionProcessData = new TQaInspectionProcessData();
                                    inspectionProcessData.setInspectionDataId(inspectionData.getId());
                                    inspectionProcessData.setPointCode(processPoint.getPointCode());
                                    inspectionProcessData.setPointName(processPoint.getPointName());
                                    inspectionProcessData.setPointType(processPoint.getPointType());
                                    inspectionProcessData.setUnit(processPoint.getUnit());
                                    inspectionProcessData.setCreateTime(new Date());
                                    inspectionProcessData.setDefaultValue(processPoint.getDefaultValue());
                                    flag += inspectionProcessDataMapper.insert(inspectionProcessData);
                                }
                            }

                            // 获取标准场景指标子项
                            LambdaQueryWrapper<TQaSsIndicatorItem> itemLambdaQueryWrapper = Wrappers.<TQaSsIndicatorItem>lambdaQuery()
                                    .eq(TQaSsIndicatorItem::getSsIndicatorId, standardSceneIndicator.getId())
                                    .eq(TQaSsIndicatorItem::getDeleted, 0);
                            List<TQaSsIndicatorItem> ssIndicatorItems = ssIndicatorItemMapper.selectList(itemLambdaQueryWrapper);

                            // 生成检验检验数据子项
                            if (ssIndicatorItems != null && ssIndicatorItems.size() > 0) {
                                for (TQaSsIndicatorItem ssIndicatorItem : ssIndicatorItems) {
                                    TQaInspectionDataItem inspectionDataItem = new TQaInspectionDataItem();
                                    inspectionDataItem.setInspectionDataId(inspectionData.getId());
                                    inspectionDataItem.setItemName(ssIndicatorItem.getItemName());
                                    inspectionDataItem.setCreateTime(new Date());
                                    flag += inspectionDataItemMapper.insert(inspectionDataItem);
                                }
                            }
                        }

                    }
                } else {
                    for (TQaStandardSceneIndicator standardSceneIndicator : standardSceneIndicators) {
                        TQaInspectionData inspectionData = DtoMapper.convert(standardSceneIndicator, TQaInspectionData.class);
                        inspectionData.setInspectionId(inspectionTask.getId());
                        inspectionData.setType(InspectionDataState.INSPECTION_SHEET);
                        inspectionData.setCreateTime(new Date());
                        inspectionData.setUpdateTime(new Date());
                        flag += inspectionDataMapper.insert(inspectionData);
                        // 获取指标下的过程项点，即检验过程数据
                        LambdaQueryWrapper<TQaIndicatorPoint> indicatorPointLambdaQueryWrapper = Wrappers.<TQaIndicatorPoint>lambdaQuery()
                                .eq(TQaIndicatorPoint::getIndicatorId, standardSceneIndicator.getIndicatorId())
                                .eq(TQaIndicatorPoint::getDeleted, "0");
                        List<TQaIndicatorPoint> indicatorPoints = indicatorPointMapper.selectList(indicatorPointLambdaQueryWrapper);
                        log.info("项点过程数据：{}", indicatorPoints);
                        // 新增检验过程数据
                        if (indicatorPoints != null && indicatorPoints.size() > 0) {
                            for (TQaIndicatorPoint indicatorPoint : indicatorPoints) {
                                TQaProcessPoint processPoint = processPointMapper.selectById(indicatorPoint.getProcessPointId());
                                TQaInspectionProcessData inspectionProcessData = new TQaInspectionProcessData();
                                inspectionProcessData.setInspectionDataId(inspectionData.getId());
                                inspectionProcessData.setPointCode(processPoint.getPointCode());
                                inspectionProcessData.setPointName(processPoint.getPointName());
                                inspectionProcessData.setPointType(processPoint.getPointType());
                                inspectionProcessData.setUnit(processPoint.getUnit());
                                inspectionProcessData.setCreateTime(new Date());
                                inspectionProcessData.setDefaultValue(processPoint.getDefaultValue());
                                flag += inspectionProcessDataMapper.insert(inspectionProcessData);
                            }
                        }

                        // 获取标准场景指标子项
                        LambdaQueryWrapper<TQaSsIndicatorItem> itemLambdaQueryWrapper = Wrappers.<TQaSsIndicatorItem>lambdaQuery()
                                .eq(TQaSsIndicatorItem::getSsIndicatorId, standardSceneIndicator.getId())
                                .eq(TQaSsIndicatorItem::getDeleted, 0);
                        List<TQaSsIndicatorItem> ssIndicatorItems = ssIndicatorItemMapper.selectList(itemLambdaQueryWrapper);

                        // 生成检验检验数据子项
                        if (ssIndicatorItems != null && ssIndicatorItems.size() > 0) {
                            for (TQaSsIndicatorItem ssIndicatorItem : ssIndicatorItems) {
                                TQaInspectionDataItem inspectionDataItem = new TQaInspectionDataItem();
                                inspectionDataItem.setInspectionDataId(inspectionData.getId());
                                inspectionDataItem.setItemName(ssIndicatorItem.getItemName());
                                inspectionDataItem.setCreateTime(new Date());
                                flag += inspectionDataItemMapper.insert(inspectionDataItem);
                            }
                        }
                    }
                }
            }

        }
        return flag;
    }

    /*
     * @description: 检验结果录入
     * <AUTHOR>
     * @date 2022/3/14 10:53
     * @param incomingInspectionResultDTO
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer enterInspectionResult(IncomingInspectionResultDTO incomingInspectionResultDTO) {
        int flag = 0;
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(incomingInspectionResultDTO.getId());
        inspectionTask.setInspectionRemark(incomingInspectionResultDTO.getInspectionRemark());
        inspectionTask.setInspectionResult(incomingInspectionResultDTO.getInspectionResult());
        inspectionTask.setActualInspectionTime(new Date());

        //获取样品信息
        TQaSampling tQaSampling = samplingMapper.selectById(inspectionTask.getSamplingId());
        List<InspectionDataDetailDTO> inspectionDataDTOList = incomingInspectionResultDTO.getInspectionDataDetailDTOList();
        if (inspectionDataDTOList != null && inspectionDataDTOList.size() > 0) {
            List<TQaInspectionData> inspectionDataList = DtoMapper.convertList(inspectionDataDTOList, TQaInspectionData.class);
            for (TQaInspectionData tQaInspectionData : inspectionDataList) {
                //处理小数位数
                double dm = 0;
                try {
                    List<String> list = Arrays.asList("LGL1", "LGL2", "LGL4", "DK", "LQM1", "LQM2");
                    if ("LGL3".equals(tQaSampling.getMaterialName())) {
                        //保留两位小数的物料
                        dm = Double.parseDouble(tQaInspectionData.getValue());
                        BigDecimal newValue = BigDecimal.valueOf(dm).setScale(2, RoundingMode.HALF_UP);
                        tQaInspectionData.setValue(newValue.toPlainString());
                    } else if (list.contains(tQaSampling.getMaterialName())) {
                        //保留一位小数的物料
                        dm = Double.parseDouble(tQaInspectionData.getValue());
                        BigDecimal newValue = BigDecimal.valueOf(dm).setScale(1, RoundingMode.HALF_UP);
                        tQaInspectionData.setValue(newValue.toPlainString());
                    }
                } catch (NumberFormatException e) {
                    log.error("转换小数出错，放弃转换");
                }
                tQaInspectionData.setUpdateTime(new Date());
                tQaInspectionData.setType(InspectionDataState.INSPECTION_SHEET);
                flag += inspectionDataMapper.updateById(tQaInspectionData);
            }
        }
        // 判断检验单的指标是否全部录入，如果全部录了，
        List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                .eq(TQaInspectionData::getDeleted, 0)
                .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                .eq(TQaInspectionData::getType, 0)
        );
        long count = inspectionDataList.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
        if (count == 0) {
            inspectionTask.setIndicatorIsFull(true);
        } else {
            inspectionTask.setIndicatorIsFull(false);
        }

        if (incomingInspectionResultDTO.getFlag()) { // 提交
            inspectionTask.setInspectionName(incomingInspectionResultDTO.getInspectionName());

            if (incomingInspectionResultDTO.getInspectionResult().equals(InspectionTaskState.Result.QUALIFIED)) {
                inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);

                // 根据关联单号校验是否为送货车辆，修改状态：可卸货
                VehicleStateUpdateDTO vehicleStateUpdateDTO = new VehicleStateUpdateDTO();
                vehicleStateUpdateDTO.setDeliveryNumber(inspectionTask.getAssociatedDocument());
                vehicleStateUpdateDTO.setInspectionOrder(inspectionTask.getInspectionOrder());
                vehicleStateUpdateDTO.setVehicleState("2");
                ResultVO<Integer> resultVO = receivingClient.updateVehicleState(vehicleStateUpdateDTO);
                if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
                    throw new BaseKnownException(10000, inspectionTask.getAssociatedDocument() + "送货单号修改成可卸货失败!");
                }

                // 检验提交，结果合格，发送消息给报检人所属角色
                SendMessageDTO sendMessageDTO = new SendMessageDTO();
                sendMessageDTO.setTitle("检验完成");
                sendMessageDTO.setContent(inspectionTask.getSamplingCode() + "检验合格");
                sendMessageDTO.setUserId(inspectionTask.getCreatorId());
                sendMessageDTO.setIsSendUser(false);
                messageService.sendMessage(sendMessageDTO);

            } else if (incomingInspectionResultDTO.getInspectionResult().equals(InspectionTaskState.Result.UNQUALIFIED)) {
                inspectionTask.setState(InspectionTaskState.AWAIT_AUDIT);

                // 如果检验不合格，车辆状态变成待处理，检验状态变成不合格
                VehicleStateUpdateDTO vehicleStateUpdateDTO = new VehicleStateUpdateDTO();
                vehicleStateUpdateDTO.setDeliveryNumber(inspectionTask.getAssociatedDocument());
                vehicleStateUpdateDTO.setInspectionOrder(inspectionTask.getInspectionOrder());
                vehicleStateUpdateDTO.setVehicleState("5");
                ResultVO<Integer> resultVO = receivingClient.updateVehicleState(vehicleStateUpdateDTO);
                if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
                    throw new BaseKnownException(10000, inspectionTask.getAssociatedDocument() + "送货单号修改成待处理失败!");
                }
            }
        } else { // 保存
            inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
        }
        inspectionTask.setUpdateTime(new Date());
        flag += inspectionTaskMapper.updateById(inspectionTask);

        return flag;
    }



    /*
     * @description: 根据样品编码，获取检验任务、检验数据、过程数据详情
     * <AUTHOR>
     * @date 2022/3/14 14:54
     * @param samplingCode
     * @return com.hvisions.quality.dto.quality.inspection.incoming.IncomingInspectionTaskDetailDTO
     */

    @Override
    public IncomingInspectionTaskDetailDTO getIncomingInspectionTaskAndProcessDataBySamplingCode(String samplingCode) {

        LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                .like(TQaInspectionTask::getSamplingCode, samplingCode)
                .eq(TQaInspectionTask::getDeleted, 0)
                .orderByDesc(TQaInspectionTask::getCreateTime)
                .last("limit 1");
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
        if (StringUtil.isEmpty(inspectionTask)) {
            throw new BaseKnownException(10000, "条码错误！");
        }
//        if (!(inspectionTask.getState().equals(InspectionTaskState.ALREADY_RECEIVE) || inspectionTask.getState().equals(InspectionTaskState.IN_INSPECTION))) {
//            throw new BaseKnownException(10000, "检验任务状态不是已收样，或检验中，不能录入数据！");
//        }
        if (inspectionTask != null) {
//            // 将样品添加到临时组合中
//            if (inspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.BASE_WINE_INSPECTION)) {
//                LambdaQueryWrapper<TQaReceiveGroup> wrapper = Wrappers.<TQaReceiveGroup>lambdaQuery()
//                        .like(TQaReceiveGroup::getGroupCode, "JJJY")
//                        .eq(TQaReceiveGroup::getState, ReceiveGroupState.EFFECT);
//                TQaReceiveGroup receiveGroupJJ = receiveGroupMapper.selectOne(wrapper);
//                if (StringUtil.isNotEmpty(receiveGroupJJ)) {
//                    inspectionTask.setGroupId(receiveGroupJJ.getId());
//                }
//            } else if (inspectionTask.getInspectionType().equals(InspectionTaskState.InspectionType.FERMENTED_GRAIN_INSPECTION)) {
//                LambdaQueryWrapper<TQaReceiveGroup> wrapper = Wrappers.<TQaReceiveGroup>lambdaQuery()
//                        .like(TQaReceiveGroup::getGroupCode, "ZPJY")
//                        .eq(TQaReceiveGroup::getState, ReceiveGroupState.EFFECT);
//                TQaReceiveGroup receiveGroupZP = receiveGroupMapper.selectOne(wrapper);
//                if (StringUtil.isNotEmpty(receiveGroupZP)) {
//                    inspectionTask.setGroupId(receiveGroupZP.getId());
//                }
//            }
//            inspectionTask.setUpdateTime(new Date());
//            inspectionTaskMapper.updateById(inspectionTask);

            IncomingInspectionTaskDetailDTO incomingInspectionTaskDetailDTO = DtoMapper.convert(inspectionTask, IncomingInspectionTaskDetailDTO.class);
            //查询交酒类别
            ProductionInspectionPageQueryDTO pageQueryDTO = new ProductionInspectionPageQueryDTO();
            pageQueryDTO.setInspectionOrder(inspectionTask.getInspectionOrder());
            List<ProductionInspectionPageDTO> productionInspectionList = productionInspectionMapper.getProductionInspectionPageList(pageQueryDTO);
            if (!CollectionUtils.isEmpty(productionInspectionList)) {
                incomingInspectionTaskDetailDTO.setTempTankCategoryName(productionInspectionList.get(0).getTempTankCategoryName());
            }
            // 获取样品采集项点
            LambdaQueryWrapper<TQaSamplingCollect> wrapper = Wrappers.<TQaSamplingCollect>lambdaQuery()
                    .eq(TQaSamplingCollect::getSamplingId, inspectionTask.getSamplingId())
                    .eq(TQaSamplingCollect::getDeleted, 0);
            List<TQaSamplingCollect> samplingCollects = samplingCollectMapper.selectList(wrapper);
            incomingInspectionTaskDetailDTO.setSamplingCollectList(DtoMapper.convertList(samplingCollects, SamplingCollectListDTO.class));
            // 获取检验数据
            LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                    .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                    .eq(TQaInspectionData::getType, InspectionDataState.PRETREATMENT)
                    .eq(TQaInspectionData::getDeleted, 0);

            List<TQaInspectionData> inspectionDataList;
            inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper);

            //代澳旗说取消这个逻辑---2025年05月21日
            if (inspectionDataList == null || inspectionDataList.size() == 0) {
                // 如果检验数据类型为过程数据的为空，则获取检验数据类型为检验单的检验数据
                LambdaQueryWrapper<TQaInspectionData> dataLambdaQueryWrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET)
                        .eq(TQaInspectionData::getDeleted, "0");
                inspectionDataList = inspectionDataMapper.selectList(dataLambdaQueryWrapper1);

                //取消了前端会报错，所以设置为空值
                if(!CollectionUtils.isEmpty(inspectionDataList) && "基酒".equals(inspectionTask.getSamplingName())){
                    inspectionDataList.forEach(v->{
                        v.setValue(null);
                    });
                }
            }

            List<IncomingInspectionDataDTO> incomingInspectionDataDTOS = DtoMapper.convertList(inspectionDataList, IncomingInspectionDataDTO.class);

            // 设置指标小数位
            List<Integer> indicatorIds = inspectionDataList.stream().map(TQaInspectionData::getIndicatorId).collect(Collectors.toList());
            List<TQaIndicator> tQaIndicators = indicatorMapper.selectList(new LambdaUpdateWrapper<TQaIndicator>().in(TQaIndicator::getId, indicatorIds));
            for (IncomingInspectionDataDTO tQaInspectionDataDto : incomingInspectionDataDTOS) {
                List<TQaIndicator> getlist = tQaIndicators.stream().filter(i -> i.getId().equals(tQaInspectionDataDto.getIndicatorId()) && i.getDecimalPlace() != null).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(getlist)) {
                    tQaInspectionDataDto.setDecimalPlace(getlist.get(0).getDecimalPlace());
                }
            }

            if (incomingInspectionDataDTOS != null && incomingInspectionDataDTOS.size() > 0) {
                for (IncomingInspectionDataDTO incomingInspectionDataDTO : incomingInspectionDataDTOS) {
                    // 获取检验数据下的子项和过程数据
                    LambdaQueryWrapper<TQaInspectionDataItem> itemLambdaQueryWrapper = Wrappers.<TQaInspectionDataItem>lambdaQuery()
                            .eq(TQaInspectionDataItem::getInspectionDataId, incomingInspectionDataDTO.getId())
                            .eq(TQaInspectionDataItem::getDeleted, 0);
                    List<TQaInspectionDataItem> tQaInspectionDataItems = inspectionDataItemMapper.selectList(itemLambdaQueryWrapper);
                    if (tQaInspectionDataItems != null && tQaInspectionDataItems.size() > 0) {
                        List<String> items = new ArrayList<>();
                        for (TQaInspectionDataItem tQaInspectionDataItem : tQaInspectionDataItems) {
                            items.add(tQaInspectionDataItem.getItemName());
                        }
                        incomingInspectionDataDTO.setItemList(items);
                    }
                    LambdaQueryWrapper<TQaInspectionProcessData> processDataLambdaQueryWrapper = Wrappers.<TQaInspectionProcessData>lambdaQuery()
                            .eq(TQaInspectionProcessData::getInspectionDataId, incomingInspectionDataDTO.getId())
                            .eq(TQaInspectionProcessData::getDeleted, 0);
                    List<TQaInspectionProcessData> inspectionProcessDataList = inspectionProcessDataMapper.selectList(processDataLambdaQueryWrapper);
                    List<IncomingInspectionProcessDataDTO> incomingInspectionProcessDataDTOS = DtoMapper.convertList(inspectionProcessDataList, IncomingInspectionProcessDataDTO.class);
                    incomingInspectionDataDTO.setIncomingInspectionProcessDataDTOS(incomingInspectionProcessDataDTOS);
                }
            }
            incomingInspectionTaskDetailDTO.setIncomingInspectionDataDTOS(incomingInspectionDataDTOS);
            return incomingInspectionTaskDetailDTO;
        } else {
            throw new BaseKnownException(1000, "样品条码错误！");
        }
    }


    /*
     * @description: 批量录入检验数据
     * <AUTHOR>
     * @date 2022/3/14 16:08
     * @param incomingInspectionBatchEnterDTOS
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer batchEnterIncomingInspectionResult(IncomingInspectionBatchEnterDTO inspectionBatchEnterDTO) {
        int flag = 0;
        //获取检验任务集合
        List<InspectionTaskBatchDTO> inspectionTaskBatchDTOS = inspectionBatchEnterDTO.getInspectionTaskBatchDTOS();
        for (InspectionTaskBatchDTO inspectionTaskBatchDTO : inspectionTaskBatchDTOS) {
            LambdaQueryWrapper<TQaInspectionData> wrapper1 = Wrappers.<TQaInspectionData>lambdaQuery()
                    .eq(TQaInspectionData::getInspectionId, inspectionTaskBatchDTO.getId())
                    .eq(TQaInspectionData::getType, InspectionDataState.PRETREATMENT)
                    .eq(TQaInspectionData::getDeleted, "0");
            //获取单个任务的预处理录入数据集合
            List<TQaInspectionData> inspectionData1 = inspectionDataMapper.selectList(wrapper1);
            Boolean hasData = false;
            if (inspectionData1.size() > 0) {
                // 判断是否存在预处理数据
                hasData = true;
            }

            //获取单个任务的所有数据结合
            List<InspectionDataBathDTO> inspectionDataBathDTOS = inspectionTaskBatchDTO.getInspectionDataBathDTOS();

            TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(inspectionTaskBatchDTO.getId());

            boolean isAddRecord=false;
            if("基酒".equals(inspectionTask.getSamplingName())){
                isAddRecord=true;
            }

            List<TQaInspectionData> inspectionDataAddList=new ArrayList<>();

            // 新增修改预处理检验数据
            flag += addAdvanceInspectionData(inspectionDataBathDTOS, hasData,isAddRecord,inspectionDataAddList);

            //新增标识为三方样
            if("基酒".equals(inspectionTask.getSamplingName())){
                inspectionTask.setIsTripartite(1);
            }

            inspectionTask.setState(InspectionTaskState.IN_INSPECTION);
            inspectionTask.setInspectionRemark(inspectionTaskBatchDTO.getInspectionRemark());
            inspectionTask.setCheckoutEquipmentId(inspectionTaskBatchDTO.getCheckoutEquipmentId());
            inspectionTask.setSampleNo(inspectionTaskBatchDTO.getSampleNo());
            inspectionTask.setActualInspectionTime(new Date());
            //判断酒精度(%vol)是否有值，有值则需要更新取样数据采集的数据,无值则更新
            productionInspectionService.addOrUpDateTQaSamplingCollect(inspectionTaskBatchDTO.getAcoholContent(),inspectionTask.getSamplingId(),inspectionTask.getAssociatedDocument());


            //留存记录
            if(isAddRecord && inspectionDataAddList.size()>0){
                log.info("开始新增基酒记录---手工录入数据");
                LambdaQueryWrapper<TQaInspectionDataRecord> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TQaInspectionDataRecord::getSampleCode,inspectionTask.getSamplingCode())
                        .eq(TQaInspectionDataRecord::getType,2)
                        .eq(TQaInspectionDataRecord::getDeleted,0);
                TQaInspectionDataRecord inspectionDataRecord = inspectionDataRecordMapper.selectOne(wrapper);
                if(null==inspectionDataRecord){
                    inspectionDataRecord=new TQaInspectionDataRecord();
                    inspectionDataRecord.setSampleCode(inspectionTask.getSamplingCode());
                    inspectionDataRecord.setInspectionId(inspectionTask.getId());
                    inspectionDataRecord.setType("2");
                }

                //新增记录数据
                for (TQaInspectionData inspectionData : inspectionDataAddList) {
                    assignMidValue(inspectionDataRecord,inspectionData);
                }

                //查询标准酒度(%vol)
                String modifyNormStandardVol=inspectionDataMapper.selectModifyNormStandardVol(inspectionTask.getSamplingId());
                inspectionDataRecord.setModifyNormStandardVol(StringUtils.isEmpty(modifyNormStandardVol)?null:new BigDecimal(modifyNormStandardVol));


                if(null==inspectionDataRecord.getId()){
                    inspectionDataRecordMapper.insert(inspectionDataRecord);
                }else {
                    inspectionDataRecordMapper.updateById(inspectionDataRecord);
                }
            }

            if (inspectionBatchEnterDTO.getFlag()) { // 同步到检验单

                // 设置组合完成
                if (StringUtil.isNotEmpty(inspectionBatchEnterDTO.getGroupId()) && inspectionBatchEnterDTO.getGroupId() > 0) {
                    LambdaQueryWrapper<TQaReceiveGroupInspection> wrapper = Wrappers.<TQaReceiveGroupInspection>lambdaQuery()
                            .eq(TQaReceiveGroupInspection::getInspectionId, inspectionTask.getId())
                            .eq(TQaReceiveGroupInspection::getGroupId, inspectionBatchEnterDTO.getGroupId())
                            .eq(TQaReceiveGroupInspection::getDeleted, "0");

                    // 取消检验任务绑定的组合
                    receiveGroupInspectionMapper.delete(wrapper);

                    TQaReceiveGroup receiveGroup = receiveGroupMapper.selectById(inspectionBatchEnterDTO.getGroupId());

                    int i = DateUtil.subtractTwoDates(receiveGroup.getCreateTime(), new Date());
                    if (i >= 2) {// 组合超过两天
                        receiveGroup.setState(ReceiveGroupState.OVERDUE);
                    }
                    receiveGroup.setFinishState(ReceiveGroupState.FINISH);
                    flag += receiveGroupMapper.updateById(receiveGroup);

                }

//                TQaReceiveGroup receiveGroup = receiveGroupMapper.selectById(inspectionBatchEnterDTO.getGroupId());
//                receiveGroup.setState(ReceiveGroupState.OVERDUE);
//                flag += receiveGroupMapper.updateById(receiveGroup);

                // 更新检验任务数据
                inspectionTask.setInspectionName(inspectionBatchEnterDTO.getInspectionName());
                inspectionTask.setInspectionResult(inspectionBatchEnterDTO.getInspectionResult());

                // 更新检验数据（状态为检验单）
                for (InspectionDataBathDTO inspectionDataBathDTO : inspectionDataBathDTOS) {
                    LambdaQueryWrapper<TQaInspectionData> inspectionDataLambdaQueryWrapper = Wrappers.<TQaInspectionData>lambdaQuery()
                            .eq(TQaInspectionData::getInspectionId, inspectionTaskBatchDTO.getId())
                            .eq(TQaInspectionData::getIndicatorId, inspectionDataBathDTO.getIndicatorId())
                            .eq(TQaInspectionData::getType, InspectionDataState.INSPECTION_SHEET);
                    TQaInspectionData inspectionData = inspectionDataMapper.selectOne(inspectionDataLambdaQueryWrapper);
                    //数据存入根据类型不同进行精度的舍入
                    if ("大曲理化".equals(inspectionData.getIndicatorType())) {
                        inspectionData.setValue(productionInspectionService.powderIndicatorFormat(inspectionData.getIndicatorName(), inspectionDataBathDTO.getValue()));
                    } else {
                        inspectionData.setValueByRounding(inspectionDataBathDTO.getValue());
                    }
                    if (StringUtil.isEmpty(inspectionDataBathDTO.getValue())) {
                        // 如果检验指标数据为空，不同步，继续下一个检验指标同步
                        continue;
                    }
                    flag += inspectionDataMapper.updateById(inspectionData);

                    // 更新检验过程数据：（状态为检验单下的）
                    List<InspectionProcessDataBatchDTO> inspectionProcessDataBatchDTOS = inspectionDataBathDTO.getInspectionProcessDataBatchDTOS();
                    if (StringUtil.isNotEmpty(inspectionProcessDataBatchDTOS) && inspectionProcessDataBatchDTOS.size() > 0) {
                        if (inspectionData.getType().equals(InspectionDataState.INSPECTION_SHEET)) {
                            // 如果检验数据类型是检验单，直接修改
                            for (InspectionProcessDataBatchDTO inspectionProcessDataBatchDTO : inspectionProcessDataBatchDTOS) {
                                TQaInspectionProcessData inspectionProcessData = inspectionProcessDataMapper.selectById(inspectionProcessDataBatchDTO.getId());
                                inspectionProcessData.setProcessValue(inspectionProcessDataBatchDTO.getProcessValue());
                                if (StringUtil.isEmpty(inspectionProcessDataBatchDTO.getProcessValue())) {
                                    continue;
                                }
                                flag += inspectionProcessDataMapper.updateById(inspectionProcessData);
                            }
                        } else if (inspectionData.getType().equals(InspectionDataState.PRETREATMENT)) {
                            // 如果检验数据类型是预处理，修改检验单数据
                            // 先删除检验过程数据，再新增

                            LambdaQueryWrapper<TQaInspectionProcessData> processDataLambdaQueryWrapper = Wrappers.<TQaInspectionProcessData>lambdaQuery()
                                    .eq(TQaInspectionProcessData::getInspectionDataId, inspectionData.getId());
                            inspectionProcessDataMapper.delete(processDataLambdaQueryWrapper);
                            for (InspectionProcessDataBatchDTO inspectionProcessDataBatchDTO : inspectionProcessDataBatchDTOS) {
                                TQaInspectionProcessData inspectionProcessData = new TQaInspectionProcessData();
                                inspectionProcessData.setInspectionDataId(inspectionData.getId());
                                inspectionProcessData.setPointCode(inspectionProcessDataBatchDTO.getPointCode());
                                inspectionProcessData.setPointName(inspectionProcessDataBatchDTO.getPointName());
                                inspectionProcessData.setPointType(inspectionProcessDataBatchDTO.getPointType());
                                inspectionProcessData.setProcessValue(inspectionProcessDataBatchDTO.getProcessValue());
                                inspectionProcessData.setUnit(inspectionProcessDataBatchDTO.getUnit());
                                flag += inspectionProcessDataMapper.insert(inspectionProcessData);
                            }
                        }
                    }
                }

                // 判断检验单的指标是否全部录入，如果全部录了，
                List<TQaInspectionData> inspectionDataList = inspectionDataMapper.selectList(Wrappers.<TQaInspectionData>lambdaQuery()
                        .eq(TQaInspectionData::getDeleted, 0)
                        .eq(TQaInspectionData::getInspectionId, inspectionTask.getId())
                        .eq(TQaInspectionData::getType, 0)
                );
                long count = inspectionDataList.stream().filter(item -> StringUtil.isEmpty(item.getValue())).count();
                if (count == 0) {
                    inspectionTask.setIndicatorIsFull(true);
                } else {
                    inspectionTask.setIndicatorIsFull(false);
                }
                //数据录入状态
                if (inspectionDataList.stream().filter(i -> StringUtils.isNotBlank(i.getValue())).count() == inspectionDataList.size()) {
                    //已经全部录入完了
                    inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.ENTRY_FINISH);
                } else if (inspectionDataList.stream().filter(i -> "色谱".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                    //色谱未录入完整
                    inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.CHROMATOGRAPHY_NOT_ENTRY);
                } else if (inspectionDataList.stream().filter(i -> "基酒理化".equals(i.getIndicatorType()) && StringUtils.isBlank(i.getValue())).count() > 0) {
                    //色谱未录入完整
                    inspectionTask.setEntryStatus(InspectionTaskState.EntryStatus.PHYSICOCHEMICAL_NOT_ENTRY);
                }
            }
            inspectionTask.setUpdateTime(new Date());
            flag += inspectionTaskMapper.updateById(inspectionTask);
        }
        return flag;
    }

    /*
     * @description: 新增修改预处理检验数据
     * <AUTHOR>
     * @date 2022/3/18 15:52
     * @param list
     * @return java.lang.Integer
     */
    private Integer addAdvanceInspectionData(List<InspectionDataBathDTO> list, Boolean hasData,boolean isAddRecord,List<TQaInspectionData> inspectionDataList) {
        int flag = 0;

        if (StringUtil.isNotEmpty(list) && list.size() > 0) {

            for (InspectionDataBathDTO inspectionDataBathDTO : list) {
                if (!hasData) {
                    // 不存在预处理数据
                    // 新增预处理检验数据
                    TQaInspectionData inspectionData = inspectionDataMapper.selectById(inspectionDataBathDTO.getId());
                    inspectionData.setType(InspectionDataState.PRETREATMENT);
                    //存入数据时根据类型要求进行精度舍入（四舍五入）
                    if ("大曲理化".equals(inspectionData.getIndicatorType())) {
                        inspectionData.setValue(productionInspectionService.powderIndicatorFormat(inspectionData.getIndicatorName(), inspectionDataBathDTO.getValue()));
                    } else {
                        inspectionData.setValueByRounding(inspectionDataBathDTO.getValue());
                    }
                    inspectionData.setCreateTime(new Date());
                    flag += inspectionDataMapper.insert(inspectionData);

                    //是否需要留存记录
                    if(isAddRecord){
                        inspectionDataList.add(inspectionData);
                    }

                    // 复制检验数据子项
                    LambdaQueryWrapper<TQaInspectionDataItem> itemLambdaQueryWrapper = Wrappers.<TQaInspectionDataItem>lambdaQuery()
                            .eq(TQaInspectionDataItem::getInspectionDataId, inspectionDataBathDTO.getId());
                    List<TQaInspectionDataItem> inspectionDataItems = inspectionDataItemMapper.selectList(itemLambdaQueryWrapper);
                    if (StringUtil.isNotEmpty(inspectionDataItems) && inspectionDataItems.size() > 0) {
                        for (TQaInspectionDataItem inspectionDataItem : inspectionDataItems) {
                            inspectionDataItem.setInspectionDataId(inspectionData.getId());
                            inspectionDataItem.setCreateTime(new Date());
                            flag += inspectionDataItemMapper.insert(inspectionDataItem);
                        }
                    }

                    // 复制检验过程数据，并给值
                    List<InspectionProcessDataBatchDTO> inspectionProcessDataBatchDTOS = inspectionDataBathDTO.getInspectionProcessDataBatchDTOS();
                    if (StringUtil.isNotEmpty(inspectionProcessDataBatchDTOS) && inspectionProcessDataBatchDTOS.size() > 0) {
                        for (InspectionProcessDataBatchDTO inspectionProcessDataBatchDTO : inspectionProcessDataBatchDTOS) {
                            TQaInspectionProcessData inspectionProcessData = inspectionProcessDataMapper.selectById(inspectionProcessDataBatchDTO.getId());
                            inspectionProcessData.setProcessValue(inspectionProcessDataBatchDTO.getProcessValue());
                            inspectionProcessData.setInspectionDataId(inspectionData.getId());
                            flag += inspectionProcessDataMapper.insert(inspectionProcessData);
                        }
                    }

                } else if (hasData) {
                    // 存在预处理数据
                    // 修改预处理检验数据
                    TQaInspectionData inspectionData = inspectionDataMapper.selectById(inspectionDataBathDTO.getId());
                    if (StringUtil.isEmpty(inspectionDataBathDTO.getValue())) {
                        continue;
                    }
                    //存入数据时根据类型要求进行精度舍入（四舍五入）
                    if ("大曲理化".equals(inspectionData.getIndicatorType())) {
                        inspectionData.setValue(productionInspectionService.powderIndicatorFormat(inspectionData.getIndicatorName(), inspectionDataBathDTO.getValue()));
                    } else {
                        inspectionData.setValueByRounding(inspectionDataBathDTO.getValue());
                    }
                    flag += inspectionDataMapper.updateById(inspectionData);

                    //是否需要留存记录
                    if(isAddRecord){
                        inspectionDataList.add(inspectionData);
                    }

                    // 修改过程数据值
                    List<InspectionProcessDataBatchDTO> inspectionProcessDataBatchDTOS = inspectionDataBathDTO.getInspectionProcessDataBatchDTOS();
                    if (StringUtil.isNotEmpty(inspectionProcessDataBatchDTOS) && inspectionProcessDataBatchDTOS.size() > 0) {
                        for (InspectionProcessDataBatchDTO inspectionProcessDataBatchDTO : inspectionProcessDataBatchDTOS) {
                            TQaInspectionProcessData inspectionProcessData = inspectionProcessDataMapper.selectById(inspectionProcessDataBatchDTO.getId());
                            inspectionProcessData.setProcessValue(inspectionProcessDataBatchDTO.getProcessValue());
                            flag += inspectionProcessDataMapper.updateById(inspectionProcessData);
                        }
                    }

                }
            }
        }

        return flag;
    }

    /*
     * @description: 给记录数据赋值
     */
    private void assignMidValue(TQaInspectionDataRecord record, TQaInspectionData inspectionData) {
        switch (inspectionData.getIndicatorName()) {
            case InfraredContent.Min.TOTAL_ACID_CONTENT:
                record.setTotalAcidContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.TOTAL_ESTER_CONTENT:
                record.setTotalEsterContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_ACETATE_CONTENT:
                record.setEthylAcetateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_BUTYRATE_CONTENT:
                record.setEthylButyrateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_LACTATE_CONTENT:
                record.setEthylLactateContent(inspectionData.getValue());
                break;
            case InfraredContent.Min.ETHYL_CAPROATE_CONTENT:
                record.setEthylCaproateContent(inspectionData.getValue());
                break;
        }
    }

    /*
     * @description: 批量提交检验结果
     * <AUTHOR>
     * @date 2022/3/14 16:46
     * @param ids
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer batchSubmitIncomingInspectionResult(Integer[] ids) {
        int flag = 0;
        for (Integer id : ids) {
            TQaInspectionTask inspectionTask = inspectionTaskMapper.selectById(id);

            inspectionTask.setState(InspectionTaskState.INSPECTION_COMPLETE);
            inspectionTask.setUpdateTime(new Date());

            // 根据关联单号校验是否为送货车辆，修改状态：可卸货
            VehicleStateUpdateDTO vehicleStateUpdateDTO = new VehicleStateUpdateDTO();
            vehicleStateUpdateDTO.setDeliveryNumber(inspectionTask.getAssociatedDocument());
            vehicleStateUpdateDTO.setInspectionOrder(inspectionTask.getInspectionOrder());
            vehicleStateUpdateDTO.setVehicleState("2");
            ResultVO<Integer> resultVO = receivingClient.updateVehicleState(vehicleStateUpdateDTO);
            if (resultVO.getCode() != 200 || resultVO.getData() == 0) {
                throw new BaseKnownException(10000, inspectionTask.getAssociatedDocument() + "送货单号修改成可卸货失败!");
            }
            // 检验提交，结果合格，发送消息给报检人所属角色
            SendMessageDTO sendMessageDTO = new SendMessageDTO();
            sendMessageDTO.setTitle("检验完成");
            sendMessageDTO.setContent(inspectionTask.getSamplingCode() + "检验合格");
            sendMessageDTO.setUserId(inspectionTask.getCreatorId());
            sendMessageDTO.setIsSendUser(false);
            messageService.sendMessage(sendMessageDTO);

            flag += inspectionTaskMapper.updateById(inspectionTask);
        }
        return flag;
    }

    /*
     * @description: 根据送货单号，获取水分和不完善粒指标值
     * <AUTHOR>
     * @date 2022/4/19 16:23
     * @param associatedDocument
     * @return com.hvisions.quality.dto.quality.inspection.incoming.data.IncomingBuckleWeightDTO
     */
    @Override
    public IncomingBuckleWeightDTO getIncomingBuckleWeightData(String associatedDocument) {
        return incomingInspectionMapper.getIncomingBuckleWeightData(associatedDocument);
    }

    /**
     * 根据流水码，获取检验任务、检验数据、过程数据详情
     * @param samplingCode
     * @return
     */
    @Override
    public IncomingInspectionTaskDetailDTO taskProcessDataBySamplingCode(String samplingCode) {
        LambdaQueryWrapper<TQaInspectionTask> taskLambdaQueryWrapper = Wrappers.<TQaInspectionTask>lambdaQuery()
                .like(TQaInspectionTask::getSamplingCode, samplingCode)
                .eq(TQaInspectionTask::getDeleted, 0)
                .orderByDesc(TQaInspectionTask::getCreateTime)
                .last("limit 1");
        TQaInspectionTask inspectionTask = inspectionTaskMapper.selectOne(taskLambdaQueryWrapper);
        return getIncomingInspectionTaskAndProcessDataBySamplingCode(inspectionTask.getSamplingCode());
    }
}
