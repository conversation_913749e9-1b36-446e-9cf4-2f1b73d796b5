package com.hvisions.purchase.dto.purchase.daily.delivery;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.purchase.daily.delivery.item.DeliveryItemPageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 日送货计划分页返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "日送货计划分页返回dto")
public class DailyDeliveryPageDTO {

    @ApiModelProperty(value = "日送货计划id")
    private Integer id;

    @ApiModelProperty(value = "要货单id")
    private Integer demandId;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseNo;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "要货数量")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "已到货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @ApiModelProperty(value = "供应商Id")
    private Integer vendorId;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "日计划状态 待执行-0、执行中-1、已完成-2")
    private String state;

    @ApiModelProperty(value = "未完成备注")
    private String notFinishRemark;

    @ApiModelProperty(value = "要货需求备注")
    private String remark;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

    @ApiModelProperty(value = "基地 HY：黄舣基地，LH：罗汉基地、ZQ：制曲中心、XS：小市中心、GJ：国窖、ZJX：皂角巷")
    private String baseName;

    @ApiModelProperty(value = "送货计划子项集合")
    List<DeliveryItemPageDTO> deliveryItemPageDTOList;

}
