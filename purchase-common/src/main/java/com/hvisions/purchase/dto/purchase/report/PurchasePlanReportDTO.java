package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 采购计划报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "采购计划报表dto")
public class PurchasePlanReportDTO {

    @ApiModelProperty(value = "采购计划编号")
    private String planCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "采购数量(t)")
    private BigDecimal procurementNumber;

    @ApiModelProperty(value = "收货数量(t)")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "剩余数量(t)")
    private BigDecimal remainQuantity;

    @ApiModelProperty(value = "已过账数量(t)")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "供货地点")
    private String locationName;

    @ApiModelProperty(value = "采购计划开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "采购计划结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "采购计划备注")
    private String remark;

    @ApiModelProperty(value = "采购日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;

}
