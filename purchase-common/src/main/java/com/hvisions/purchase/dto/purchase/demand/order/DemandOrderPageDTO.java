package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 要货需求单分页返回dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "要货需求单分页返回dto")
public class DemandOrderPageDTO extends SysBaseDTO {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrder;

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "要货数量")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal harvestQuantity;

    @ApiModelProperty(value = "要货需求状态 待执行-0、执行中-1、已完成-2")
    private String state;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改备注")
    private String updateRemark;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    @ApiModelProperty(value = "入库地点id")
    private Integer locationId;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "基地 HY：黄舣基地，LH：罗汉基地、ZQ：制曲中心、XS：小市中心、GJ：国窖、ZJX：皂角巷")
    private String baseName;
}
