package com.hvisions.purchase.dto.purchase.receiving;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.LastModifiedBy;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "车辆信息修改dto")
public class VehicleDataUpdateDTO {

    @ApiModelProperty(value = "送货车id")
    @NotNull(message = "送货车id不能为空")
    private Integer id;

    @ApiModelProperty(value = "毛重(入场重量)")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "皮重(出厂重量)")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "净重(离厂净重)")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "更新用户")
    protected Integer updaterId;

}
