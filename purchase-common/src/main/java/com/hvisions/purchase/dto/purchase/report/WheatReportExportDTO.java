package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 高粱报表导出dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "高粱报表导出dto")
public class WheatReportExportDTO {

    @ApiModelProperty(value = "物料品种")
    private String materialName;

    @ApiModelProperty(value = "取样日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDate;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "检验次数")
    private BigDecimal inspectioNumber;

    @ApiModelProperty(value = "供货单位")
    private String vendorName;

    @ApiModelProperty(value = "容重（g/L）")
    private BigDecimal density;

    @ApiModelProperty(value = "水分（%）")
    private BigDecimal water;

    @ApiModelProperty(value = "杂质（%）")
    private BigDecimal impurity;

    @ApiModelProperty(value = "无机杂质（%）")
    private BigDecimal inorganicImpurity;

    @ApiModelProperty(value = "不完善粒（%）")
    private BigDecimal imperfect;

    @ApiModelProperty(value = "赤霉病粒（%）")
    private BigDecimal moldRate;

    @ApiModelProperty(value = "生芽粒（%）")
    private BigDecimal sproutedKernel;

    @ApiModelProperty(value = "感观")
    private String perception;

    @ApiModelProperty(value = "重量（kg）")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "质量判定")
    private String qualityResult;

    @ApiModelProperty(value = "质检备注")
    private String qualityRemark;

    @ApiModelProperty(value = "检验员")
    private String inspectionName;

    @ApiModelProperty(value = "取样人")
    private String samplingPeople;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

}
