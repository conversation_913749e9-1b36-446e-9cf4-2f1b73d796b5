package com.hvisions.purchase.dto.purchase.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 采购计划新增or修改
 * @author: Jcao
 * @time: 2022/4/2 9:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购计划新增or修改")
public class ProcurementPlanDTO extends SysBaseDTO {

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "周期开始时间不能为空！")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "周期结束时间不能为空！")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "采购数量")
    @Min(value = 1, message = "采购数量必须大于0！")
    private BigDecimal procurementNumber;

    @ApiModelProperty(value = "供货入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStorageTime;

    @ApiModelProperty(value = "使用时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date useTimeStart;

    @ApiModelProperty(value = "使用时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date useTimeEnd;

    @ApiModelProperty(value = "库存地点id")
    private Integer locationId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "计划明细数据")
    List<ProcurementPlanDetailDTO> planDetailList;
}
