package com.hvisions.purchase.dto.purchase.daily.delivery;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 日送货计划分页条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "日送货计划分页条件dto")
public class DailyDeliveryPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "日送货计划状态：0-待执行、1-执行中、2-已完成")
    private String state;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseNo;

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "要货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "要货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "是否查询今日之后数据")
    private Boolean afterDay;

    @ApiModelProperty(value = "基地 黄舣/制曲/罗汉/小市/皂角巷/国窖")
    private List<String> baseNameList;
}
