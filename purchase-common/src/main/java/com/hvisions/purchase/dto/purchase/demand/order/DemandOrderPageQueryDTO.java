package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 要货需求单分页条件dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "要货需求单分页条件dto")
public class DemandOrderPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "要货单号")
    private String orderNo;

    @ApiModelProperty(value = "采购单号")
    private String purchaseOrder;

    @ApiModelProperty(value = "物料类型编码")
    private List<String> materialTypeCodeList;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "要货状态：0-待执行、1-执行中、2-已完成")
    private List<String> states;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "基地 黄舣/制曲/罗汉/小市/皂角巷/国窖")
    private String baseName;

    @ApiModelProperty(value = "基地 黄舣/制曲/罗汉/小市/皂角巷/国窖")
    private List<String> baseNameList;
}
