package com.hvisions.purchase.dto.purchase.srm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "接收日送货计划dto")
public class DeliveryDetailDTO {

    @ApiModelProperty(value = "月需求单编号(MES)")
    private String mouthDemandNo;

    @ApiModelProperty(value = "日需求单编号(MES)")
    private String dayDemandNo;

    @ApiModelProperty(value = "日要货计划号(SRM)")
    private String deliveryPlanNo;

    @ApiModelProperty(value = "采购订单编号")
    private String sapOrder;

    @ApiModelProperty(value = "采购订单行号")
    private String itemKey;

    @ApiModelProperty(value = "送货单号(唯一)")
    private String deliveryNumber;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "品种(SRM系统)")
    private String srmVariety;

    @ApiModelProperty(value = "产地(SRM系统)")
    private String srmPlaceProduction;

    @ApiModelProperty(value = "批次(SRM系统)")
    private String srmBatch;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作类型 :10  新增;20  修改;30  删除")
    private String operationType;

    //非SRM传递，校验和设置使用
    @ApiModelProperty(value = "采购单id -- 通过采购订单查询")
    private Integer purchaseId;

    @ApiModelProperty(value = "要货需求id -- 通过日需求单编号查询")
    private Integer demandId;

    @ApiModelProperty(value = "采购单详情id -- 通过采购订单行号查询")
    private Integer purchaseDetailId;

    @ApiModelProperty(value = "车辆id -- 通过送货单号查询")
    private Integer dailyDeliveryDetailId;

    @ApiModelProperty(value = "日计划id -- 通过日要货计划号查询设置")
    private Integer deliveryId;

    @ApiModelProperty(value = "日计划子项id -- 通过日要货计划号查询设置")
    private Integer deliveryItemId;

    @ApiModelProperty(value = "月需求id -- 通过月需求单编号查询")
    private Integer monthDemandId;

    @ApiModelProperty(value = "基地 -- 通过月需求单编号查询 HY：黄舣基地，LH：罗汉基地、ZQ：制曲中心、XS：小市中心、GJ：国窖、ZJX：皂角巷")
    private String baseName;
}
