<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.plan.dao.PlanDifferenceReminderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.plan.entity.PlanDifferenceReminder">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
        <result column="difference_field" property="differenceField"/>
        <result column="percentage_difference_natural_month" property="percentageDifferenceNaturalMonth"/>
        <result column="percentage_difference_bookkeeping_month" property="percentageDifferenceBookkeepingMonth"/>
        <result column="notify_role" property="notifyRole"/>
        <result column="state" property="state"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="natural_moon_symbol" property="naturalMoonSymbol"/>
        <result column="bookkeeping_moon_symbol" property="bookkeepingMoonSymbol"/>
        <result column="natural_month_time" property="naturalMonthTime"/>
        <result column="bookkeeping_month_time" property="bookkeepingMonthTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        creator_id, updater_id, site_num, deleted, difference_field, percentage_difference_natural_month, percentage_difference_bookkeeping_month, notify_role, state, effective_time, natural_moon_symbol, bookkeeping_moon_symbol, natural_month_time, bookkeeping_month_time
    </sql>

    <select id="listPlanDifferenceReminder" resultType="com.hvisions.plan.vo.PlanDifferenceReminderVO">
        select pdr.*,user.user_name
        from brewage_plan.t_pp_plan_difference_reminder pdr
        left join authority.sys_user user on user.id = pdr.creator_id

        where pdr.deleted = 0

        <if test="req.state != null">
            and pdr.state = #{req.state}
        </if>
        order by pdr.state asc
    </select>

</mapper>
