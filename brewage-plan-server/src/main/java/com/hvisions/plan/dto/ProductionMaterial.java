package com.hvisions.plan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/9/11 14:50
 */
@Data
public class ProductionMaterial {

    @ApiModelProperty("用料物料编码")
    private String material;

    @ApiModelProperty("用料物料名称")
    private String materialName;

    @ApiModelProperty("物料投入量")
    private String feeding;

    @ApiModelProperty("物料耗用量")
    private BigDecimal consumption;

    @ApiModelProperty("1-增量、2-减量")
    private String status;

}
