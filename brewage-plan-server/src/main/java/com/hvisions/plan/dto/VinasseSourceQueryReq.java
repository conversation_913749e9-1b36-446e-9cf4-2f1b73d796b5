/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname VinasseSourceQueryReq
 * @description 糟源信息数据传输查询参数实体类
 * @date 2022-03-23
 */
@Data
@ApiModel("VinasseSource数据传输查询参数实体类")
public class VinasseSourceQueryReq {

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("糟源名称")
    private String name;

    @ApiModelProperty("糟源代码")
    private String code;

    @ApiModelProperty("糟源类别代码")
    private String typeCode;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间范围开始")
    private LocalDateTime beginDate;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间范围结束")
    private LocalDateTime endDate;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;
}