package com.hvisions.plan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/9/11 14:50
 */
@Data
public class BaseLiquor {

    @ApiModelProperty("基酒编码 计划下发的物料编码（尾号867）")
    private String product;

    @ApiModelProperty("基酒名称 物料编码对应的物料名称")
    private String productName;

    @ApiModelProperty("基酒等级 系统编码对应的等级编码")
    private String beerGrade;

    @ApiModelProperty("段次")
    private String paragraph;

    @ApiModelProperty("基酒产出数量")
    private BigDecimal quantity;

    @ApiModelProperty("1-增量、2-减量（交酒指令单的入库方向 ）")
    private String status;

}
