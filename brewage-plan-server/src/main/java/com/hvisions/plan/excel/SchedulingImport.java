package com.hvisions.plan.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: SchedulingImport</p>
 * <p>Description: 计划排程导入</p>
 * <p>Company: www.h-visions.com</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Data
public class SchedulingImport{
    /**
     * 排次
     */
    @ApiModelProperty(value = "排次")
    @ExcelProperty(value = "排次")
    private String rowName;

    @ApiModelProperty("排程ID")
    private Integer scheduleId;

    @ApiModelProperty("排次ID")
    private Integer rowId;

    @ApiModelProperty("生产模式id")
    private Integer modeId;
    /**
     * 排次
     */
    @ApiModelProperty("排次顺序")
    private Integer sort;
    /**
     * 生产模式
     */
    @ApiModelProperty(value = "生产模式")
    @ExcelProperty(value = "生产模式")
    private String productionMode;

    @ApiModelProperty(value = "年份")
    @ExcelProperty("年份")
    private String year;

    @ApiModelProperty(value = "时间安排")
    @ExcelProperty("时间安排")
    private String time;

    @ApiModelProperty(value = "开始时间")
    @ExcelProperty("开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    @ExcelProperty("结束时间")
    private String endTime;

    /**
     * 是否热季检修
     */
    @ApiModelProperty(value = "是否热季检修")
    @ExcelProperty("是否热季检修")
    private Boolean checkState;

    /**
     * 是否跨月
     */
    @ApiModelProperty(value = "是否跨月")
    @ExcelProperty(value = "是否跨月")
    private Boolean jumpMonth;

    /**
     * 工作天数
     */
    @ApiModelProperty(value = "工作天数")
    @ExcelProperty(value = "工作天数")
    private BigDecimal workDays;

    /**
     * 生产天数
     */
    @ApiModelProperty(value = "生产天数")
    @ExcelProperty(value = "生产天数")
    private BigDecimal productionDays;

    /**
     * 检修天数
     */
    @ApiModelProperty(value = "检修天数")
    @ExcelProperty(value = "检修天数")
    private BigDecimal overhaulDays;

    /**
     * 翻窖天数
     */
    @ApiModelProperty(value = "翻窖天数")
    @ExcelProperty(value = "翻窖天数")
    private BigDecimal cellarDays;

    /**
     * 打扫天数
     */
    @ApiModelProperty(value = "打扫天数")
    @ExcelProperty(value = "打扫天数")
    private BigDecimal sweepDays;

    /**
     * 节假日
     */
    @ApiModelProperty(value = "节假日")
    @ExcelProperty(value = "节假日")
    private String holidays;

    /**
     * 粮食糟
     */
    @ApiModelProperty(value = "粮食糟")
    @ExcelProperty(value = "CJ")
    private BigDecimal CJ;

    /**
     * 储备糟
     */
    @ApiModelProperty(value = "储备糟")
    @ExcelProperty(value = "CZ")
    private BigDecimal CZ;

    /**
     * 减粮糟
     */
    @ApiModelProperty(value = "减粮糟")
    @ExcelProperty(value = "HZ")
    private BigDecimal HZ;

    /**
     * 质量糟
     */
    @ApiModelProperty(value = "质量糟")
    @ExcelProperty(value = "ZL")
    private BigDecimal ZL;

    /**
     * 底糟
     */
    @ApiModelProperty(value = "底糟")
    @ExcelProperty(value = "DZ")
    private BigDecimal DZ;

    /**
     * 粮糟
     */
    @ApiModelProperty(value = "粮糟")
    @ExcelProperty(value = "CT")
    private BigDecimal CT;

    /**
     * 底糟
     */
    @ApiModelProperty(value = "底糟")
    @ExcelProperty(value = "KDZ")
    private BigDecimal KDZ;

    /**
     * 减粮糟
     */
    @ApiModelProperty(value = "减粮糟")
    @ExcelProperty(value = "KHZ")
    private BigDecimal KHZ;

    /**
     * 开翻沙糟
     */
    @ApiModelProperty(value = "开翻沙糟")
    @ExcelProperty(value = "KZL")
    private BigDecimal KZL;

    /**
     * 大转排母糟
     */
    @ApiModelProperty(value = "大转排母糟")
    @ExcelProperty(value = "CB")
    private BigDecimal CB;

    /**
     * 不投粮母糟
     */
    @ApiModelProperty(value = "不投粮母糟")
    @ExcelProperty(value = "MZ")
    private BigDecimal MZ;

    @ApiModelProperty(value = "扩展糟源类型1")
    private BigDecimal extend1;

    @ApiModelProperty(value = "扩展糟源类型2")
    private BigDecimal extend2;

    @ApiModelProperty(value = "扩展糟源类型3")
    private BigDecimal extend3;

    @ApiModelProperty(value = "扩展糟源类型4")
    private BigDecimal extend4;

    @ApiModelProperty(value = "扩展糟源类型5")
    private BigDecimal extend5;

    /**
     * 需要时间
     */
    @ApiModelProperty(value = "需要时间")
    @ExcelProperty(value = "需要时间")
    private BigDecimal needDays;

}
