package com.hvisions.plan.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.ExcelListener;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Author：JIA WANG
 * @Package：com.hvisions.plan.utils
 * @Project：酿酒二期
 * @name：ExcelRemoveUtils
 * @Date：2025/9/16 19:03
 * @Filename：ExcelRemoveUtils
 */
public class ExcelRemoveUtils {
    public static <T> List<T> getImport(MultipartFile file, Class<T> clazz) {
        ExcelReader excelReader = null;

        List var5;
        try {
            excelReader = EasyExcel.read(file.getInputStream()).build();
            ExcelListener<T> listener = new ExcelListener();
            ReadSheet mouldGroupSheet = ((ExcelReaderSheetBuilder)((ExcelReaderSheetBuilder)EasyExcel.readSheet(0).head(clazz).headRowNumber(2)).headRowNumber(2).registerReadListener(listener)).build();
            excelReader.read(new ReadSheet[]{mouldGroupSheet});
            var5 = listener.getAll();
        } catch (IOException var9) {
            throw new BaseKnownException(1000, "读取数据异常:" + var9.getMessage(), new Object[0]);
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }

        }

        return var5;
    }
}
