/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @classname PlanDifferenceReminderVO
 * @description 计划差异提醒视图实体类
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("PlanDifferenceReminderVO对象")
public class PlanDifferenceReminderVO extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("创建人")
    private String userName;

    @ApiModelProperty("差异字段")
    private String differenceField;

    @ApiModelProperty("差异字段ids")
    private List<String> differenceFieldList;

    @ApiModelProperty("差异字段名称")
    private String differenceFieldName;

    @ApiModelProperty("自然月符号")
    private String naturalMoonSymbol;

    @ApiModelProperty("扎帐月符号")
    private String bookkeepingMoonSymbol;

    @ApiModelProperty("自然月差异百分比")
    private String percentageDifferenceNaturalMonth;

    @ApiModelProperty("扎帐月差异百分比")
    private String percentageDifferenceBookkeepingMonth;

    @ApiModelProperty("自然月校验日期")
    private Date naturalMonthTime;

    @ApiModelProperty("扎帐月校验日期")
    private Date bookkeepingMonthTime;

    @ApiModelProperty("通知角色")
    private String notifyRole;

    @ApiModelProperty("通知角色ids")
    private List<String> notifyRoleList;

    @ApiModelProperty("通知角色名称")
    private String notifyRoleName;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

    @ApiModelProperty("生效时间")
    private Date effectiveTime;

}
