/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.vo;

import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @classname VinasseSourceVO
 * @description 糟源信息视图实体类
 * @date 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("VinasseSourceVO对象")
public class VinasseSourceVO extends SysBase {

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("糟源名称")
    private String name;

    @ApiModelProperty("糟源代码")
    private String code;

    @ApiModelProperty("糟源类别代码")
    private String typeCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

}
