
package com.hvisions.plan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @classname PlanDifferenceReminder
 * @description 计划差异提醒实体类
 * @date 2025-05-26
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_pp_plan_difference_reminder")
@Accessors(chain = true)
@ApiModel("PlanDifferenceReminder对象")
public class PlanDifferenceReminder extends SysBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("差异字段")
    private String differenceField;

    @ApiModelProperty("自然月符号")
    private String naturalMoonSymbol;

    @ApiModelProperty("扎帐月符号")
    private String bookkeepingMoonSymbol;

    @ApiModelProperty("自然月差异百分比")
    private String percentageDifferenceNaturalMonth;

    @ApiModelProperty("扎帐月差异百分比")
    private String percentageDifferenceBookkeepingMonth;

    @ApiModelProperty("通知角色")
    private String notifyRole;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

    @ApiModelProperty("生效时间")
    private Date effectiveTime;

    @ApiModelProperty("自然月校验日期")
    private Date naturalMonthTime;

    @ApiModelProperty("扎帐月校验日期")
    private Date bookkeepingMonthTime;

}