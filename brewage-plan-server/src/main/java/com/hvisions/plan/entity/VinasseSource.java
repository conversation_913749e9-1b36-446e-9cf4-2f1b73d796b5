package com.hvisions.plan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.brewage.mkwine.req.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @classname VinasseSource
 * @description 糟源信息实体类
 * @date 2022-03-23
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_pp_vinasse_source")
@ApiModel("VinasseSource对象")
public class VinasseSource extends SysBase {

    @ApiModelProperty("生产基地id")
    private Integer productionBaseId;

    @ApiModelProperty("生产基地名称")
    private String productionBaseName;

    @ApiModelProperty("糟源名称")
    private String name;

    @ApiModelProperty("糟源代码")
    private String code;

    @ApiModelProperty("糟源类别代码")
    private String typeCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态;0-新建、1-生效、2-存档")
    private String state;

}