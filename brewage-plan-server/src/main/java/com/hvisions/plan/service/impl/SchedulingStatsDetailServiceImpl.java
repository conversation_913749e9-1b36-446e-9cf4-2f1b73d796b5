package com.hvisions.plan.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.plan.dao.*;
import com.hvisions.plan.dto.SchedulingArrangeReq;
import com.hvisions.plan.dto.SchedulingStatsDetailReq;
import com.hvisions.plan.dto.SchedulingVinasseReq;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.enums.StatsTypeEnum;
import com.hvisions.plan.operlog.utils.StringUtils;
import com.hvisions.plan.service.IFormulaService;
import com.hvisions.plan.service.ISchedulingStatsDetailService;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.plan.vo.FormulaDetailByDay;
import com.hvisions.plan.vo.FormulaDetailVO;
import com.hvisions.plan.vo.SchedulingStatsDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 排程统计详情数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
@Slf4j
public class SchedulingStatsDetailServiceImpl extends ServiceImpl<SchedulingStatsDetailMapper, SchedulingStatsDetail> implements ISchedulingStatsDetailService {
    @Autowired
    SchedulingStatsDetailMapper schedulingStatsDetailMapper;
    @Autowired
    SchedulingMapper schedulingMapper;
    @Autowired
    IFormulaService formulaService;
    @Autowired
    RetortContainerMapper retortContainerMapper;
    @Autowired
    SchedulingTemplateMapper schedulingTemplateMapper;
    @Autowired
    RowMapper rowMapper;

    @Override
    public void handleNewDetails(List<SchedulingStatsDetailReq> statsDetailReqList) {
        List<SchedulingStatsDetail> statsDetailList = statsDetailReqList.stream()
                .map(it -> CopyUtil.simpleCopy(it, SchedulingStatsDetail.class))
                .collect(Collectors.toList());
        this.saveBatch(statsDetailList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByStatsIds(List<Integer> statsId) {
        schedulingStatsDetailMapper.physicsDeleteByStatsIds(statsId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleNewDetails(Integer statsId, List<SchedulingArrangeReq> schedulingArrangeReqList, List<FormulaDetailByDay> formulaDetailByDayList) {
        //查询基地  当前只处理单个基地
        Integer scheduleId = schedulingArrangeReqList.get(0).getScheduleId();
        Scheduling scheduling = Optional.ofNullable(schedulingMapper.selectById(scheduleId))
                .orElseThrow(() -> new BaseKnownException(330001, "未查到相关排程计划"));
        Integer plantId = Optional.of(scheduling)
                .map(it -> it.getProductionBaseIds())
                .map(Integer::parseInt)
                .orElseThrow(() -> new BaseKnownException(329001, "未查到相关基地"));
        //任取一个中心用于查找配方
        String centerId = scheduling.getCenterIds().split(",")[0];
        List<FormulaDetailVO> formulaDetails = new ArrayList<FormulaDetailVO>();
        schedulingArrangeReqList.forEach(it -> {
            //生产天数为0的不参与计算
            if (Objects.isNull(it.getProductionDays())
                    || Objects.isNull(it.getWorkDays())
                    || Objects.isNull(it.getSchedulingVinasseReqList())
                    || 0 == BigDecimal.ZERO.compareTo(it.getProductionDays())) {
                return;
            }
            String rowName = Optional.ofNullable(rowMapper.selectById(it.getRowId()))
                    .map(Row::getName)
                    .orElseThrow(() -> new BaseKnownException(901001, "获取排次信息失败"));
            int workDays = it.getWorkDays().intValue();
//            Map<String, BigDecimal> vinasseMap = it.getSchedulingVinasseReqList().stream()
//                    .collect(Collectors.toMap(SchedulingVinasseReq::getVinasseCode, SchedulingVinasseReq::getVinasseData, (a, b) -> b));
            for (SchedulingVinasseReq schedulingVinasseReq : it.getSchedulingVinasseReqList()) {
                LocalDate start = it.getBeginTime();
                //糟源代码
                String vinasseCode = schedulingVinasseReq.getVinasseCode();
                BigDecimal vinasseData = Optional.ofNullable(schedulingVinasseReq.getVinasseData())
                        .orElse(BigDecimal.ZERO);
                //平均甑口数
                BigDecimal averageZen = vinasseData.divide(it.getWorkDays(), 2, RoundingMode.HALF_UP);
                if (0 <= BigDecimal.ZERO.compareTo(averageZen)) {
                    continue;
                }
                final BigDecimal averageZenFinal = averageZen;
                for (int i = 0; i < workDays; i++) {
                    //根据基地 中心 糟源代码  日期
                    LocalDate finalDate = start;
                    String vc = vinasseCode;
//                    if (vinasseCode.startsWith("K")) {
//                        vc = vinasseCode.substring(1);
//                    }
                    List<FormulaDetailVO> formulaDetailsByPlantAndFormula = formulaService.getFormulaDetailsByPlantAndFormula(plantId, centerId, vc, start).stream()
                            .peek(fd -> {
                                fd.setVinasseCode(vinasseCode);
                                fd.setRowName(rowName);
                                //日物料需求量=平均日甑口*配方量
                                BigDecimal result = averageZenFinal.multiply(fd.getQuality());
                                fd.setQuality(result);
                                fd.setDayInfo(finalDate);
                            })
                            .collect(Collectors.toList());
                    formulaDetails.addAll(formulaDetailsByPlantAndFormula);

                    start = start.plusDays(1);
                }
            }
        });
        //用于计算扎帐期
        List<FormulaDetailByDay> formulaDetailByDays = formulaDetails.stream()
                .map(fd -> CopyUtil.simpleCopy(fd, FormulaDetailByDay.class))
                .peek(fd -> fd.setPlantId(plantId))
                .collect(Collectors.toList());
        formulaDetailByDayList.addAll(formulaDetailByDays);
        //汇总处理  根据投入 产出  耗粮进行分组处理
        List<SchedulingStatsDetail> schedulingStatsDetailList = getSchedulingStatsDetails(plantId, statsId, formulaDetails);
        this.saveBatch(schedulingStatsDetailList);

        //汇总排程的投入  产出  消耗
        schedulingStatsDetailList.stream()
                .collect(Collectors.groupingBy(SchedulingStatsDetail::getType))
                .entrySet()
                .forEach(it -> {
                    BigDecimal total = it.getValue().stream()
                            .map(SchedulingStatsDetail::getTotalQuality)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    //根据排程ID去更新数据
                    switch (it.getKey()) {
                        case "0":
                            scheduling.setFeeding(total);
                            break;
                        case "1":
                            scheduling.setConsumption(total);
                            break;
                        case "2":
                            scheduling.setWinOutput(total);
                            break;
                        default:
                    }
                });
        schedulingMapper.updateById(scheduling);
    }

    private RetortContainer getRetortContainer(Integer plantId, String souceCode) {
        //基地生效的模版下的生产甑口
        LambdaQueryWrapper<SchedulingTemplate> wrapper = new LambdaQueryWrapper<SchedulingTemplate>()
                .eq(SchedulingTemplate::getProductionbaseId, plantId)
                .eq(SchedulingTemplate::getState, StateEnum.TAKE_EFFECT.getCode())
                .eq(SchedulingTemplate::getDeleted, 0);
        SchedulingTemplate schedulingTemplate = schedulingTemplateMapper.selectOne(wrapper);

        List<String> ids = StringUtils.str2List(schedulingTemplate.getVinasseType(),",",true,true);
        List<String> needGains = retortContainerMapper.selectBatchIds(ids).stream().map(f -> f.getSourceCode()).collect(Collectors.toList());;
                //Arrays.asList("ZL", "CJ", "HZ", "CZ");
        LambdaQueryWrapper<RetortContainer> queryWrapper = new LambdaQueryWrapper<RetortContainer>()
                .eq(RetortContainer::getProductionBaseId, plantId)
                .eq(RetortContainer::getState, StateEnum.TAKE_EFFECT.getCode())
                .eq(RetortContainer::getSourceCode, souceCode);
        return Optional.ofNullable(souceCode)
                .map(v -> {
                    if (needGains.contains(souceCode)) {
                        List<RetortContainer> retortContainers = retortContainerMapper.selectList(queryWrapper);
                        return 1 == retortContainers.size() ? retortContainers.get(0) : null;
                    }
                    return null;
                })
                .orElse(null);
    }

    private List<SchedulingStatsDetail> getSchedulingStatsDetails(Integer plantId, Integer statsId, List<FormulaDetailVO> formulaDetails) {
        List<FormulaDetailVO> collect = formulaDetails.stream().filter(d -> "HJ1".equals(d.getMaterialName()) || "HJ2".equals(d.getMaterialName())).collect(Collectors.toList());
        Map<String, List<FormulaDetailVO>> groupByType = formulaDetails.stream()
                .collect(Collectors.groupingBy(FormulaDetailVO::getType));
        Assert.isTrue(groupByType.containsKey(StatsTypeEnum.INVESTMENT.getCode())
                && groupByType.containsKey(StatsTypeEnum.CONSUME.getCode()), "配方异常，缺少投入或者消耗类型的物料");
        List<SchedulingStatsDetail> schedulingStatsDetailList = new ArrayList<>();
        List<FormulaDetailVO> investmentDetail = groupByType.get(StatsTypeEnum.INVESTMENT.getCode());
        List<FormulaDetailVO> consumeDetail = groupByType.get(StatsTypeEnum.CONSUME.getCode());
        //投ZT   等于大转排   CJ生产甑口*回酒量+开ZL甑口*涨幅*回酒量
        //物料 ZT -> HJ1
        Predicate<FormulaDetailVO> predicateHJ1 = (it) -> "HJ1".equals(it.getMaterialName())
                && "大转排".equals(it.getRowName())
                && Arrays.asList("CJ", "ZL", "KDZ").contains(it.getVinasseCode());

        Predicate<FormulaDetailVO> predicateHJ1ZL = (it) -> "HJ1".equals(it.getMaterialName())
                && !"大转排".equals(it.getRowName())
                && Arrays.asList("ZL").contains(it.getVinasseCode());

        Consumer<FormulaDetailVO> peekHJ1 = (it) -> {
            //ZL甑口*涨幅*回酒量
            String vinasseCode = it.getVinasseCode();
            if ("ZL".equals(vinasseCode) || "KDZ".equals(vinasseCode)) {
                String vc = vinasseCode;
//                if (vc.startsWith("K")) {
//                    vc = vc.substring(1);
//                }
                RetortContainer retortContainer = this.getRetortContainer(plantId, vc);
                //校验甑口涨幅数据获取是否正确
                BigDecimal gainsCoefficient = Optional.ofNullable(retortContainer)
                        .map(RetortContainer::getGainsCoefficient)
                        .orElseThrow(() -> new BaseKnownException(822004, MessageFormat.format("{0}生产甑口信息未正确获取", vinasseCode)));
                it.setQuality(it.getQuality().multiply(gainsCoefficient));
            }
        };
        SchedulingStatsDetail investmentHJ1 = investmentByMaterial(statsId, deepCopy(investmentDetail), predicateHJ1, peekHJ1, StatsTypeEnum.INVESTMENT);
        SchedulingStatsDetail investmentHJ1ZL = investmentByMaterial(statsId, deepCopy(investmentDetail), predicateHJ1ZL, peekHJ1, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentHJ1);
        if (investmentHJ1 == null) {
            schedulingStatsDetailList.add(investmentHJ1ZL);
        }
        //投FJ2  等于生产CZ甑口*回酒量
        //物料 FJ2 ->HJ2
        Predicate<FormulaDetailVO> predicateHJ2Investment = it -> "HJ2".equals(it.getMaterialName())
                && "CZ".equals(it.getVinasseCode());
        SchedulingStatsDetail investmentHJ2 = investmentByMaterial(statsId, investmentDetail, predicateHJ2Investment, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentHJ2);
        //投粳高粱  等于生产CJ/CZ/HZ甑口*投粮量
        Predicate<FormulaDetailVO> predicateJGN = it -> "LGL3".equals(it.getMaterialName())
                && Arrays.asList("CJ", "CZ", "HZ").contains(it.getVinasseCode());
        SchedulingStatsDetail investmentJGN = investmentByMaterial(statsId, investmentDetail, predicateJGN, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentJGN);
        //投有机粳高粱    等于生产CJ/CZ/HZ甑口*投粮量
        Predicate<FormulaDetailVO> predicateYJJGN = it -> "LGL2".equals(it.getMaterialName())
                && Arrays.asList("CJ", "CZ", "HZ").contains(it.getVinasseCode());
        SchedulingStatsDetail investmentYJJGN = investmentByMaterial(statsId, investmentDetail, predicateYJJGN, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentYJJGN);
        //投中有机中温曲粉  等于生产CJ/CZ/HZ甑口*投曲量
        Predicate<FormulaDetailVO> predicateYJQF = it -> "QF1".equals(it.getMaterialName())
                && Arrays.asList("CJ", "CZ", "HZ").contains(it.getVinasseCode());
        SchedulingStatsDetail investmentYJQF = investmentByMaterial(statsId, investmentDetail, predicateYJQF, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentYJQF);
        //投中高温曲粉  等于生产CJ/CZ/HZ甑口*投曲量
        Predicate<FormulaDetailVO> predicateZGWQF = it -> ("中高温曲粉".equals(it.getMaterialName())||"QF2".equals(it.getMaterialName()))
                && Arrays.asList("CJ", "CZ", "HZ").contains(it.getVinasseCode());
        SchedulingStatsDetail investmentZGWQF = investmentByMaterial(statsId, investmentDetail, predicateZGWQF, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentZGWQF);
        //投翻沙曲粉  等于生产CZ甑口*投曲量
        Predicate<FormulaDetailVO> predicateFSQF = it -> ("翻沙曲粉".equals(it.getMaterialName())||"QF3".equals(it.getMaterialName()))
                && Arrays.asList("CJ", "CZ", "HZ").contains(it.getVinasseCode());
        SchedulingStatsDetail investmentFSQF = investmentByMaterial(statsId, investmentDetail, predicateFSQF, null, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentFSQF);
        //投稻壳   等于不同糟源生产甑口*各自的投糠量(稻壳)（ZL*涨幅系数）
        Predicate<FormulaDetailVO> predicateDK = it -> "DK".equals(it.getMaterialName());
        Consumer<FormulaDetailVO> peekDK = (it) -> {
            //ZL甑口*涨幅*投稻壳
            String vinasseCode = it.getVinasseCode();
            if ("ZL".equals(vinasseCode) || "KDZ".equals(vinasseCode) || "KHZ".equals(vinasseCode)) {
                String vc = vinasseCode;
//                if (vc.startsWith("K")) {
//                    vc = vc.substring(1);
//                }
                RetortContainer retortContainer = this.getRetortContainer(plantId, vc);
                //校验甑口涨幅数据获取是否正确
                BigDecimal gainsCoefficient = Optional.ofNullable(retortContainer)
                        .map(RetortContainer::getGainsCoefficient)
                        .orElseThrow(() -> new BaseKnownException(822004, MessageFormat.format("{0}生产甑口信息未正确获取", vinasseCode)));
                it.setQuality(it.getQuality().multiply(gainsCoefficient));
            }
        };
        SchedulingStatsDetail investmentDK = investmentByMaterial(statsId, investmentDetail, predicateDK, peekDK, StatsTypeEnum.INVESTMENT);
        schedulingStatsDetailList.add(investmentDK);

        //消耗 HJ2    等于大转排   开CZ甑口*回酒量+开ZL甑口*回酒量
        Predicate<FormulaDetailVO> predicateHJ2Consume = it -> "HJ2".equals(it.getMaterialName())
                && "大转排".equals(it.getRowName())
                && Arrays.asList("CZ", "ZL", "KDZ").contains(it.getVinasseCode());

        Predicate<FormulaDetailVO> predicateHJ2ConsumeZL = it -> "HJ2".equals(it.getMaterialName())
                && !"大转排".equals(it.getRowName())
                && Arrays.asList("ZL", "KDZ").contains(it.getVinasseCode());

        SchedulingStatsDetail consumeHJ2 = consumeCalculate(statsId, investmentDetail, predicateHJ2Consume, null, null);
        SchedulingStatsDetail consumeHJ2ZL = consumeCalculate(statsId, investmentDetail, predicateHJ2ConsumeZL, null, null);
        schedulingStatsDetailList.add(consumeHJ2);
        if (consumeHJ2 == null) {
            schedulingStatsDetailList.add(consumeHJ2ZL);
        }
        //耗粮 CJ 等于生产CJ/HZ/CZ甑口除以各自的涨幅，再乘以对应投粮量
        Predicate<FormulaDetailVO> predicateVinasseCJConsume = it -> Arrays.asList("LGL3", "LGL2").contains(it.getMaterialName())
                && Arrays.asList("CJ", "HZ", "CZ").contains(it.getVinasseCode());
        Consumer<FormulaDetailVO> peekVinasseCJ = (it) -> {
            //CJ/HZ/CZ甑口除以各自的涨幅，再乘以对应投粮量
            String vinasseCode = it.getVinasseCode();
            RetortContainer retortContainer = this.getRetortContainer(plantId, vinasseCode);
            //校验甑口涨幅数据获取是否正确
            BigDecimal gainsCoefficient = Optional.ofNullable(retortContainer)
                    .map(RetortContainer::getGainsCoefficient)
                    .orElseThrow(() -> new BaseKnownException(901002, MessageFormat.format("{0}生产甑口信息未正确获取", vinasseCode)));
            it.setQuality(it.getQuality().divide(gainsCoefficient, 4, BigDecimal.ROUND_HALF_UP));
        };
        SchedulingStatsDetail consumeVinasseCJ = consumeCalculate(statsId, deepCopy(investmentDetail), predicateVinasseCJConsume, peekVinasseCJ, "CJ");
        schedulingStatsDetailList.add(consumeVinasseCJ);

        //耗粮 CZ 等于大转排开CJ甑口/涨幅系数*投粮量
        Predicate<FormulaDetailVO> predicateVinasseCZConsume = it -> Arrays.asList("LGL3", "LGL2").contains(it.getMaterialName())
                && "大转排".equals(it.getRowName())
                && "CJ".equals(it.getVinasseCode());
        Consumer<FormulaDetailVO> peekVinasseCZ = (it) -> {
            //CJ甑口除以各自的涨幅，再乘以对应投粮量
            String vinasseCode = it.getVinasseCode();
            RetortContainer retortContainer = this.getRetortContainer(plantId, vinasseCode);
            //校验甑口涨幅数据获取是否正确
            BigDecimal gainsCoefficient = Optional.ofNullable(retortContainer)
                    .map(RetortContainer::getGainsCoefficient)
                    .orElseThrow(() -> new BaseKnownException(901002, MessageFormat.format("{0}生产甑口信息未正确获取", vinasseCode)));
            it.setQuality(it.getQuality().divide(gainsCoefficient, 4, BigDecimal.ROUND_HALF_UP));
        };
        SchedulingStatsDetail consumeVinasseCZ = consumeCalculate(statsId, investmentDetail, predicateVinasseCZConsume, peekVinasseCZ, "CZ");
        schedulingStatsDetailList.add(consumeVinasseCZ);

        //耗粮  ZL    等于开ZL甑口*耗粮量
        Predicate<FormulaDetailVO> predicateVinasseZLConsume = it -> Arrays.asList("LGL3", "LGL2").contains(it.getMaterialName())
                && Arrays.asList("ZL", "KDZ", "KHZ").contains(it.getVinasseCode());
        SchedulingStatsDetail consumeVinasseZL = consumeCalculate(statsId, consumeDetail, predicateVinasseZLConsume, null, "ZL");
        schedulingStatsDetailList.add(consumeVinasseZL);
        //总产    等于投HJ1总量*1.0645+耗HJ2*0.7521+(耗CJ+耗CZ+耗ZL)/2
        BigDecimal outTotal = Optional.ofNullable(investmentHJ1)
                .map(it -> it.getTotalQuality().multiply(new BigDecimal("1.0645")))
                .orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(investmentHJ1ZL)
                        .map(it -> it.getTotalQuality().multiply(new BigDecimal("1.0645")))
                        .orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(consumeHJ2)
                        .map(it -> it.getTotalQuality().multiply(new BigDecimal("0.7521")))
                        .orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(consumeHJ2ZL)
                        .map(it -> it.getTotalQuality().multiply(new BigDecimal("0.7521")))
                        .orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(consumeVinasseCJ)
                        .map(SchedulingStatsDetail::getTotalQuality)
                        .orElse(BigDecimal.ZERO)
                        .add(Optional.ofNullable(consumeVinasseCZ)
                                .map(SchedulingStatsDetail::getTotalQuality)
                                .orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(consumeVinasseZL)
                                .map(SchedulingStatsDetail::getTotalQuality)
                                .orElse(BigDecimal.ZERO))
                        .divide(new BigDecimal("2"), 4, BigDecimal.ROUND_HALF_UP));
        SchedulingStatsDetail out = new SchedulingStatsDetail()
                .setStatsId(statsId)
                .setMaterialCode("EstimatedTotal")
                .setMaterialName("预计总产")
                .setTotalQuality(outTotal)
                .setUnit("kg/千克")
                .setType(StatsTypeEnum.PRODUCE.getCode());
        schedulingStatsDetailList.add(out);
        return schedulingStatsDetailList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 对集合进行深拷贝 注意需要对泛型类进行序列化（实现serializable）
     *
     * @param src 源list
     * @param <T> list的对象泛型
     * @return List<T>
     * @throws ClassNotFoundException
     */
    public static <T> List<T> deepCopy(List<T> src) {
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
             ObjectOutputStream outputStream = new ObjectOutputStream(byteOut);) {
            outputStream.writeObject(src);
            try (ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
                 ObjectInputStream inputStream = new ObjectInputStream(byteIn);) {
                return (List<T>) inputStream.readObject();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }


    private SchedulingStatsDetail consumeCalculate(Integer statsId, List<FormulaDetailVO> formulaDetailVOS, Predicate<FormulaDetailVO> predicate, Consumer<FormulaDetailVO> peekConsume, String selfDefineName) {
        List<FormulaDetailVO> formulaDetailVOList = formulaDetailVOS.stream()
                .filter(predicate)
                .collect(Collectors.toList());
        if (Objects.nonNull(peekConsume)) {
            formulaDetailVOList = formulaDetailVOList.stream()
                    .peek(peekConsume)
                    .collect(Collectors.toList());
        }
        if (!formulaDetailVOList.isEmpty()) {
            log.info("汇总前数据{}", JSON.toJSONString(formulaDetailVOList));
            BigDecimal zt = formulaDetailVOList.stream()
                    .map(FormulaDetailVO::getQuality)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            SchedulingStatsDetail schedulingStatsDetail = new SchedulingStatsDetail()
                    .setStatsId(statsId)
                    .setTotalQuality(zt)
                    .setUnit(formulaDetailVOList.get(0).getUnit())
                    .setType(StatsTypeEnum.CONSUME.getCode());
            if (Objects.isNull(selfDefineName)) {
                schedulingStatsDetail
                        .setMaterialId(formulaDetailVOList.get(0).getMaterialId())
                        .setMaterialCode(formulaDetailVOList.get(0).getMaterialCode())
                        .setMaterialName(formulaDetailVOList.get(0).getMaterialName());
            } else {
                schedulingStatsDetail
                        .setMaterialCode("Consume" + selfDefineName)
                        .setMaterialName("(" + selfDefineName + ")");
            }
            log.info("封装体{}", JSON.toJSONString(schedulingStatsDetail));
            return schedulingStatsDetail;
        }
        return null;
    }

    private SchedulingStatsDetail investmentByMaterial(Integer statsId, List<FormulaDetailVO> formulaDetailVOS, Predicate<FormulaDetailVO> predicate, Consumer<FormulaDetailVO> peekConsume, StatsTypeEnum statsTypeEnum) {
        List<FormulaDetailVO> formulaDetailVOList = formulaDetailVOS.stream()
                .filter(predicate)
                .collect(Collectors.toList());
        if (Objects.nonNull(peekConsume)) {
            formulaDetailVOList = formulaDetailVOList.stream()
                    .peek(peekConsume)
                    .collect(Collectors.toList());
        }
        if (!formulaDetailVOList.isEmpty()) {
            FormulaDetailVO formulaDetailVO = formulaDetailVOList.get(0);
            BigDecimal zt = formulaDetailVOList.stream()
                    .map(FormulaDetailVO::getQuality)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return new SchedulingStatsDetail()
                    .setStatsId(statsId)
                    .setMaterialId(formulaDetailVO.getMaterialId())
                    .setMaterialCode(formulaDetailVO.getMaterialCode())
                    .setMaterialName(formulaDetailVO.getMaterialName())
                    .setTotalQuality(zt)
                    .setUnit(formulaDetailVO.getUnit())
                    .setType(statsTypeEnum.getCode());
        }
        return null;
    }

    @Override
    public List<SchedulingStatsDetailVO> listStatsDetailsByStats(List<Integer> statsIds) {
        LambdaQueryWrapper<SchedulingStatsDetail> queryWrapper = new LambdaQueryWrapper<SchedulingStatsDetail>()
                .in(SchedulingStatsDetail::getStatsId, statsIds);
        return schedulingStatsDetailMapper.selectList(queryWrapper).stream()
                .map(it -> CopyUtil.simpleCopy(it, SchedulingStatsDetailVO.class))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleNewDetails(Integer statsId, List<FormulaDetailByDay> formulaDetailByDayList) {
        if (CollectionUtils.isEmpty(formulaDetailByDayList)) {
            return;
        }
        List<FormulaDetailVO> formulaDetailVOList = formulaDetailByDayList.stream()
                .map(it -> (FormulaDetailVO) it)
                .collect(Collectors.toList());
        List<SchedulingStatsDetail> schedulingStatsDetailList = getSchedulingStatsDetails(formulaDetailByDayList.get(0).getPlantId(), statsId, formulaDetailVOList);
        this.saveBatch(schedulingStatsDetailList);
    }

    @Override
    public List<SchedulingStatsDetail> previewStatsDetail(List<SchedulingArrangeReq> schedulingArrangeReqList) {
        //查询基地  当前只处理单个基地
        Integer scheduleId = schedulingArrangeReqList.get(0).getScheduleId();
        Scheduling scheduling = Optional.ofNullable(schedulingMapper.selectById(scheduleId))
                .orElseThrow(() -> new BaseKnownException(330001, "未查到相关排程计划"));
        Integer plantId = Optional.of(scheduling)
                .map(it -> it.getProductionBaseIds())
                .map(Integer::parseInt)
                .orElseThrow(() -> new BaseKnownException(329001, "未查到相关基地"));
        //任取一个中心用于查找配方
        String centerId = scheduling.getCenterIds().split(",")[0];
        List<FormulaDetailVO> formulaDetails = new ArrayList<FormulaDetailVO>();
        schedulingArrangeReqList.forEach(it -> {
            //生产天数为0的不参与计算
            if (Objects.isNull(it.getProductionDays())
                    || Objects.isNull(it.getWorkDays())
                    || Objects.isNull(it.getSchedulingVinasseReqList())
                    || 0 == BigDecimal.ZERO.compareTo(it.getProductionDays())) {
                return;
            }
            int workDays = it.getWorkDays().intValue();
            LocalDate start = it.getBeginTime();
            Map<String, BigDecimal> vinasseMap = it.getSchedulingVinasseReqList().stream()
                    .collect(Collectors.toMap(SchedulingVinasseReq::getVinasseCode, SchedulingVinasseReq::getVinasseData, (a, b) -> b));
            for (SchedulingVinasseReq schedulingVinasseReq : it.getSchedulingVinasseReqList()) {
                if (Objects.isNull(schedulingVinasseReq.getVinasseData())
                        || 0 == BigDecimal.ZERO.compareTo(schedulingVinasseReq.getVinasseData())) {
                    continue;
                }
                //糟源代码
                String vinasseCode = schedulingVinasseReq.getVinasseCode();
                if ("KDZ".equals(vinasseCode) || "KHZ".equals(vinasseCode)) {
                    continue;
                }
                //平均甑口数
                BigDecimal averageZen = null;
                switch (vinasseCode) {
                    case "DZ":
                        //DZ = DZ+KDZ
                        averageZen = schedulingVinasseReq.getVinasseData()
                                .add(vinasseMap.getOrDefault("KDZ", BigDecimal.ZERO))
                                .divide(it.getWorkDays(), 2, RoundingMode.HALF_UP);
                        break;
                    case "HZ":
                        //HZ = HZ+KHZ
                        averageZen = schedulingVinasseReq.getVinasseData()
                                .add(vinasseMap.getOrDefault("KHZ", BigDecimal.ZERO))
                                .divide(it.getWorkDays(), 2, RoundingMode.HALF_UP);
                        break;
                    default:
                        averageZen = schedulingVinasseReq.getVinasseData().divide(it.getWorkDays(), 2, RoundingMode.HALF_UP);
                }
                final BigDecimal averageZenFinal = averageZen;
                for (int i = 0; i < workDays; i++) {
                    //根据基地  糟源代码  日期
                    List<FormulaDetailVO> formulaDetailsByPlantAndFormula = formulaService.getFormulaDetailsByPlantAndFormula(plantId, centerId, vinasseCode, start).stream()
                            .peek(fd -> {
                                //日物料需求量=平均日甑口*配方量
                                BigDecimal result = averageZenFinal.multiply(fd.getQuality());
                                fd.setQuality(result);
                            })
                            .collect(Collectors.toList());
                    formulaDetails.addAll(formulaDetailsByPlantAndFormula);
                    start = start.plusDays(1);
                }
            }
        });
        //汇总处理  根据投入 产出  耗粮进行分组处理
        return getSchedulingStatsDetails(plantId, null, formulaDetails);
    }
}
