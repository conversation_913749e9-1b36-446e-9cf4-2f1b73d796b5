package com.hvisions.plan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.plan.dto.plan.dto.PurchasePlanMaterialDTO;
import com.hvisions.plan.dto.plan.dto.PurchasePlanReq;
import com.hvisions.plan.dto.*;
import com.hvisions.plan.entity.MonthPlan;
import com.hvisions.plan.vo.*;
import com.hvisions.plan.dto.plan.vo.RowVO;
import com.hvisions.common.dto.PageInfo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 月度计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface IMonthPlanService extends IService<MonthPlan> {

    /**
     * 月计划首页
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.hvisions.brewage.plan.vo.MonthPlanVO>
     * <AUTHOR>
     * @date 2022/4/8 15:25
     */
    Page<MonthPlanVO> queryMainPagedMonthPlan(MonthPlanMainQueryReq req, PageInfo pageInfo);

    /**
     * 排程拆分月度计划
     *
     * @param scheduleId 排程 ID
     * <AUTHOR>
     * @date 2022/3/30 10:16
     */
    void splitMonthPlan(Integer scheduleId,Integer planType);

    /**
     * 导入实际投/耗/产
     *
     * @param file 导入文件
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/4/8 15:26
     */
    void importActualData(MultipartFile file);

    /**
     * 月计划下发
     *
     * @param planIdList 月计划ID
     * <AUTHOR>
     * @date 2022/4/7 11:04
     */
    void issue(List<Integer> planIdList);

    /**
     * 变更创建排程
     *
     * @param req 月计划变更
     * <AUTHOR>
     * @date 2022/4/11 14:54
     */
    void createScheduling(MonthPlanCreateSchedulingReq req);

    SchedulingArrangeByRowVO getMonthArrangeInfo(MonthPlanReq monthPlanReq);

    /**
     * 根据月计划获取采购预估
     *
     * @param req
     * <AUTHOR>
     * @date 2022/4/12 13:28
     */
    List<PurchasePlanMaterialDTO> purchasePlan(PurchasePlanReq req);

    /**
     * 根据车间  日期   查询排次
     *
     * @param locationId
     * @param day
     * @return
     */
    Integer getRowIdByDay(@RequestParam Integer locationId, @RequestParam String day);

    BigDecimal getVinasseQuantity(Integer centerId, Integer locationId, String vinasseCode, String startDate, String endDate);

    List<RowVO> listRowByYear(Integer centerId, Integer locationId, Integer year);

    List<RowVO> newListRowByYear(Integer centerId, Integer locationId, Integer year);


    /**
     * 产量及计划完成率
     *
     * @param req
     * @return
     */
    List<ProductionPlanCompletionRateVO> getProductionPlanCompletionRate(ProductionPlanCompletionRateReq req);

    /**
     * 产量完成情况
     *
     * @param req
     * @return
     */
    List<ProductionCompletionVO> getProductionCompletion(ProductionCompletionReq req);

    /**
     * 产量完成情况范围
     *
     * @param req
     * @return
     */
    List<ProductionCompletionRangeVO> getProductionCompletionRange(ProductionCompletionReq req);


    List<Integer> getHistoryScheduling(Integer monthPlanId);

    /**
     * 月计划变更获取排程第一步数据
     * @param id
     */
    SchedulingFirstStepVo firstStep(Integer id);

    MonthPlanMaterialTotalVO queryMaterialList(Integer schedulingId, Integer year, Integer month, Integer centerId, Integer locationId);

    List<MaterialRequirementVO> getMaterialRequirement(MaterialRequirementQueryDTO queryDTO);

    /**
     * 变更计划状态
     * @param req
     */
    void updateStatus(MonthPlanUpdateStatusReq req);

    /**
     * 基酒产出反馈
     * @param req
     */
    void baseLiquorProduction(BaseLiquorProductionReq req);

    /**
     * 生产物料投入反馈
     * @param req
     */
    void productionMaterialInput(ProductionMaterialInputReq req);
}
