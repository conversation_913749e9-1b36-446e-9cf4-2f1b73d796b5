package com.hvisions.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.brewage.feign.MkwineClient;
import com.hvisions.brewage.mkwine.vo.MaterialRequirePlanBaseVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.StaticsVO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.plan.enums.PlanStateEnum;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.dao.GradeWineFormulaMapper;
import com.hvisions.plan.dao.GwfDetailMapper;
import com.hvisions.plan.dao.MonthPlanMapper;
import com.hvisions.plan.dao.SchedulingMapper;
import com.hvisions.plan.entity.GradeWineFormula;
import com.hvisions.plan.entity.GwfDetail;
import com.hvisions.plan.entity.MonthPlan;
import com.hvisions.plan.service.MaterialRequirePlanServiceStrategy;
import com.hvisions.plan.utils.StringUtil;
import com.hvisions.plan.vo.GwpMaterialDetailVO;
import com.hvisions.plan.vo.GwpMaterialVO;
import com.hvisions.plan.dto.plan.vo.MaterialRequireReq;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.client.MaterialTypeClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 等级酒产出计划
 * <AUTHOR>
 * @date 2022/4/1 16:54
 */
@Service
public class GwpServiceStrategyImpl extends MaterialRequirePlanBaseService implements MaterialRequirePlanServiceStrategy {
    @Autowired
    private MonthPlanMapper monthPlanMapper;
    @Autowired
    private GradeWineFormulaMapper gradeWineFormulaMapper;
    @Autowired
    private GwfDetailMapper gwfDetailMapper;
    @Autowired
    SchedulingMapper schedulingMapper;
    @Autowired
    MaterialTypeClient materialTypeClient;
    @Autowired
    MaterialClient materialClient;
    @Autowired
    BaseWrapper baseWrapper;
    @Autowired
    MkwineClient mkwineClient;

    @Override
    public String getStrategy() {
        return "GradeWineProduct";
    }

    @Override
    public List<GwpMaterialVO> detailViewMaterialRequirePlan(MaterialRequireReq materialRequireReq) {

        Integer locationId = materialRequireReq.getLocationId();
        List<GwpMaterialVO> gwpMaterialVOList = new ArrayList<GwpMaterialVO>();
        // 获取等级酒换算配方
        LambdaQueryWrapper<GradeWineFormula> wrapper = new LambdaQueryWrapper<GradeWineFormula>()
                .eq(GradeWineFormula::getProductionBaseId, materialRequireReq.getPlantId())
                .eq(GradeWineFormula::getState, StateEnum.TAKE_EFFECT.getCode());
        GradeWineFormula entity = gradeWineFormulaMapper.selectOne(wrapper);
        if (Objects.isNull(entity)) {
            throw new BaseKnownException(10000, "当前基地未配置有效的等级酒配方");
        }
        List<GwfDetail> gwfDetailList = gwfDetailMapper.selectList(new LambdaQueryWrapper<GwfDetail>()
                .eq(GwfDetail::getGradeWineFormulaId, entity.getId()));

        Integer plantId = materialRequireReq.getPlantId();
        Integer centerId = materialRequireReq.getCenterId();
        Integer planTime = materialRequireReq.getPlanTime();
        if (Objects.isNull(planTime)) {
            planTime = LocalDate.now().getYear();
        }
        LambdaQueryWrapper<MonthPlan> queryWrapper = new LambdaQueryWrapper<MonthPlan>()
                .eq(Objects.nonNull(plantId), MonthPlan::getPlantId, plantId)
                .eq(Objects.nonNull(centerId), MonthPlan::getCenterId, centerId)
                .eq(Objects.nonNull(locationId), MonthPlan::getLocationId, locationId)
                .eq(Objects.nonNull(planTime), MonthPlan::getPlanTime, planTime)
                .ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode());

//        Integer monthId = materialRequireReq.getMonthId();
//        if (Objects.nonNull(monthId)) {
//            if (Objects.equals(monthId, 1)) {
//                Integer finalPlanTime = planTime;
//                queryWrapper.and(it -> it.eq(MonthPlan::getYear, finalPlanTime - 1).or().eq(MonthPlan::getMonth, monthId));
//            } else {
//                queryWrapper.eq(MonthPlan::getMonth, monthId);
//            }
//        }
        List<MonthPlan> monthPlans = monthPlanMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(monthPlans)) {
            return null;
        }

        Map<Integer, List<MonthPlan>> base = null;
        Map<Integer, List<MonthPlan>> centerAndWorkshop = null;
        if(StringUtil.isNotEmpty(plantId) && StringUtil.isEmpty(centerId) && StringUtil.isEmpty(locationId)){
            base = monthPlans.stream().sorted(Comparator.comparing(MonthPlan::getMonthBeginDate)).collect(Collectors.groupingBy(MonthPlan::getCenterId));
        }
        if(StringUtil.isNotEmpty(plantId) && StringUtil.isNotEmpty(centerId) ){
            centerAndWorkshop = monthPlans.stream().filter(f-> f.getPlantId().equals(plantId) && f.getCenterId().equals(centerId) ).collect(Collectors.groupingBy(MonthPlan::getMonth));
        }
        if(StringUtil.isNotEmpty(plantId) && StringUtil.isNotEmpty(centerId) && StringUtil.isNotEmpty(locationId) ){
            centerAndWorkshop = monthPlans.stream().filter(f-> f.getPlantId().equals(plantId) && f.getCenterId().equals(centerId) && f.getLocationId().equals(locationId)).collect(Collectors.groupingBy(MonthPlan::getMonth));
        }
        //基地详情
        if(StringUtil.isNotEmpty(base)){
            base.forEach((key, value) -> {
                // 计划基酒产出
                BigDecimal planProduce = value.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
                MonthPlan monthPlan = value.get(0);
                MonthPlan ml = value.get(value.size()-1);
                GwpMaterialVO gwpMaterialVO = new GwpMaterialVO();
                gwpMaterialVO.setPlantId(monthPlan.getPlantId());
                gwpMaterialVO.setPlantName(monthPlan.getPlantName());
                gwpMaterialVO.setCenterId(monthPlan.getCenterId());
                gwpMaterialVO.setCenterName(monthPlan.getCenterName());

                gwpMaterialVO.setStartTime(monthPlan.getMonthBeginDate());
                gwpMaterialVO.setEndTime(ml.getMonthEndDate());
                // 车间维度
//            if (Objects.nonNull(locationId)) {
                gwpMaterialVO.setLocationId(monthPlan.getLocationId());
                gwpMaterialVO.setLocationName(monthPlan.getLocationName());
//            }
                List<GwpMaterialDetailVO> collect = gwfDetailList.stream().map(it -> {
                    GwpMaterialDetailVO convert = baseWrapper.convert(it, GwpMaterialDetailVO.class);
                    // 计划产出量
                    BigDecimal multiply = it.getRatio().multiply(planProduce);
                    convert.setPlanProduce(multiply.setScale(2, RoundingMode.HALF_UP).doubleValue());
                    return convert;
                }).collect(Collectors.toList());
                gwpMaterialVO.setPlanProduce(planProduce.setScale(2, RoundingMode.HALF_UP));
                gwpMaterialVO.setMaterialList(collect);
                gwpMaterialVOList.add(gwpMaterialVO);
            });
        }
        //中心、车间详情
        if(StringUtil.isNotEmpty(centerAndWorkshop)){
            centerAndWorkshop.forEach((key, value) -> {
                // 计划基酒产出
                BigDecimal planProduce = value.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
                MonthPlan monthPlan = value.get(0);
                GwpMaterialVO gwpMaterialVO = new GwpMaterialVO();
                gwpMaterialVO.setPlantId(monthPlan.getPlantId());
                gwpMaterialVO.setPlantName(monthPlan.getPlantName());
                gwpMaterialVO.setCenterId(monthPlan.getCenterId());
                gwpMaterialVO.setCenterName(monthPlan.getCenterName());
                gwpMaterialVO.setMonth(key);
                gwpMaterialVO.setStartTime(monthPlan.getMonthBeginDate());
                gwpMaterialVO.setEndTime(monthPlan.getMonthEndDate());
                // 车间维度
//            if (Objects.nonNull(locationId)) {
                gwpMaterialVO.setLocationId(monthPlan.getLocationId());
                gwpMaterialVO.setLocationName(monthPlan.getLocationName());
//            }
                List<GwpMaterialDetailVO> collect = gwfDetailList.stream().map(it -> {
                    GwpMaterialDetailVO convert = baseWrapper.convert(it, GwpMaterialDetailVO.class);
                    // 计划产出量
                    BigDecimal multiply = it.getRatio().multiply(planProduce);
                    convert.setPlanProduce(multiply.setScale(2, RoundingMode.HALF_UP).doubleValue());
                    return convert;
                }).collect(Collectors.toList());
                gwpMaterialVO.setPlanProduce(planProduce.setScale(2, RoundingMode.HALF_UP));
                gwpMaterialVO.setMaterialList(collect);
                gwpMaterialVOList.add(gwpMaterialVO);
            });
        }

        //处理中心车间信息
        if (materialRequireReq.getPlantId() == null) {
//            gwpMaterialVO.setPlantId(null);
//            gwpMaterialVO.setCenterId(null);
//            gwpMaterialVO.setCenterName(null);
//            gwpMaterialVO.setLocationId(null);
//            gwpMaterialVO.setLocationName(null);
        }
        return gwpMaterialVOList;
    }

    @Override
    public List<GwpMaterialVO> listMaterialRequirePlan(MaterialRequireReq materialRequireReq) {
        this.setMonthPlanMapper(this.monthPlanMapper);
        Pair<String, Map<Integer, List<MonthPlan>>> planGroupByShowLevel = this.getPlanGroupByShowLevel(materialRequireReq);
        if (planGroupByShowLevel.getValue().isEmpty()) {
            return new ArrayList<>();
        }
        String showLevelKey = planGroupByShowLevel.getKey();
        return planGroupByShowLevel.getValue().values().stream().map(it -> {
            // 计划基酒产出
            BigDecimal planProduce = it.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算开始和结束时间
            List<Integer> scheduleIds = it.stream().map(MonthPlan::getSchedulingId).collect(Collectors.toList());
            MaterialRequirePlanBaseVO scheduleTime = schedulingMapper.getScheduleTime(scheduleIds);
            //包装数据
            MonthPlan randomGet = it.get(0);
            //获取实际基酒产出
            ResultVO<List<StaticsVO>> resultVO = mkwineClient.getActualOutputLiang(randomGet.getCenterId(),randomGet.getLocationId(),scheduleTime.getStartTime(),scheduleTime.getEndTime());
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(),resultVO.getMessage());
            }

            List<StaticsVO> actualOutputLiang=resultVO.getData();
            BigDecimal actProduce = new BigDecimal(0);
            for(StaticsVO staticsVO:actualOutputLiang){
                if(staticsVO!=null){
                    actProduce = actProduce.add(staticsVO.getQuantity());
                }

            }
            GwpMaterialVO productionMaterialVO = new GwpMaterialVO();
            productionMaterialVO.setShowLevel(showLevelKey);
            productionMaterialVO.setPlantId(randomGet.getPlantId());
            productionMaterialVO.setPlantName(randomGet.getPlantName());
            productionMaterialVO.setCenterId(randomGet.getCenterId());
            productionMaterialVO.setCenterName(randomGet.getCenterName());
            if ("location".equals(showLevelKey) || "center".equals(showLevelKey)) {
                productionMaterialVO.setLocationId(randomGet.getLocationId());
                productionMaterialVO.setLocationName(randomGet.getLocationName());
            }

            // 获取等级酒换算配方
            GradeWineFormula entity = new GradeWineFormula();
            if(randomGet.getPlantName().contains("黄舣")){
                LambdaQueryWrapper<GradeWineFormula> wrapper = new LambdaQueryWrapper<GradeWineFormula>()
                        .eq(GradeWineFormula::getProductionBaseId, randomGet.getPlantId())
                        .eq(GradeWineFormula::getState, StateEnum.TAKE_EFFECT.getCode());
                entity = gradeWineFormulaMapper.selectOne(wrapper);
            }else{
                //非遗、罗汉，基地+中心+生效校验
                LambdaQueryWrapper<GradeWineFormula> wrapper = new LambdaQueryWrapper<GradeWineFormula>()
                        .eq(GradeWineFormula::getProductionBaseId, randomGet.getPlantId())
                        .apply(Objects.nonNull(randomGet.getCenterCode()), "FIND_IN_SET ('" + randomGet.getCenterCode() + "',center_code)")
                        .eq(GradeWineFormula::getState, StateEnum.TAKE_EFFECT.getCode());
                entity = gradeWineFormulaMapper.selectOne(wrapper);
            }

            if (Objects.isNull(entity)) {
                throw new BaseKnownException(10000, "当前基地未配置有效的等级酒配方");
            }
            List<GwfDetail> gwfDetailList = gwfDetailMapper.selectList(new LambdaQueryWrapper<GwfDetail>()
                    .eq(GwfDetail::getGradeWineFormulaId, entity.getId()));

            List<GwpMaterialDetailVO> collect = gwfDetailList.stream().map(item -> {
                GwpMaterialDetailVO convert = baseWrapper.convert(item, GwpMaterialDetailVO.class);
                // 计划产出量
                BigDecimal multiply = item.getRatio().multiply(planProduce);
                convert.setPlanProduce(multiply.setScale(2, RoundingMode.HALF_UP).doubleValue());
                return convert;
            }).collect(Collectors.toList());
            productionMaterialVO.setPlanProduce(planProduce.setScale(2, RoundingMode.HALF_UP));
            productionMaterialVO.setActualProduce(actProduce);
            productionMaterialVO.setStartTime(scheduleTime.getStartTime());
            productionMaterialVO.setEndTime(scheduleTime.getEndTime());
            productionMaterialVO.setMaterialList(collect);
            //处理中心车间信息
            if (materialRequireReq.getPlantId() == null) {
//                productionMaterialVO.setPlantId(null);
//                productionMaterialVO.setCenterId(null);
//                productionMaterialVO.setCenterName(null);
//                productionMaterialVO.setLocationId(null);
//                productionMaterialVO.setLocationName(null);
            }
            return productionMaterialVO;
        }).collect(Collectors.toList());
    }
}
