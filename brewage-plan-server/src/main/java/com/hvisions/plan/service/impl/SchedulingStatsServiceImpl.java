package com.hvisions.plan.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.plan.dao.MonthPlanMapper;
import com.hvisions.plan.dao.SchedulingStatsMapper;
import com.hvisions.plan.dto.SchedulingArrangeReq;
import com.hvisions.plan.dto.SchedulingStatsDetailReq;
import com.hvisions.plan.dto.SchedulingStatsReq;
import com.hvisions.plan.dto.SchedulingVinasseReq;
import com.hvisions.plan.entity.MonthPlan;
import com.hvisions.plan.entity.SchedulingStats;
import com.hvisions.plan.service.ISchedulingStatsDetailService;
import com.hvisions.plan.service.ISchedulingStatsService;
import com.hvisions.plan.service.IStatsVinasseService;
import com.hvisions.plan.utils.StringUtil;
import com.hvisions.plan.vo.*;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.common.exception.BaseKnownException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 排程统计数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
@Slf4j
public class SchedulingStatsServiceImpl extends ServiceImpl<SchedulingStatsMapper, SchedulingStats> implements ISchedulingStatsService {
    @Autowired
    SchedulingStatsMapper schedulingStatsMapper;
    @Autowired
    IStatsVinasseService statsVinasseService;
    @Autowired
    ISchedulingStatsDetailService statsDetailService;
    @Resource
    MonthPlanMapper monthPlanMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleStats(SchedulingStatsReq schedulingStatsReq) {
        Integer statsId = this.handleStatsSubmit(schedulingStatsReq);
        statsVinasseService.handleVinasse(statsId, schedulingStatsReq.getStatsVinasseReqList());
        List<SchedulingStatsDetailReq> statsDetailReqList = schedulingStatsReq.getSchedulingStatsDetailReqList().stream()
                .peek(it -> it.setStatsId(statsId))
                .collect(Collectors.toList());
        statsDetailService.handleNewDetails(statsDetailReqList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOldState(Integer scheduleId) {
        List<Integer> oldStatsIds = schedulingStatsMapper.getBySchedulingId(scheduleId, null).stream()
                .map(SchedulingStats::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldStatsIds)) {
            return;
        }
        schedulingStatsMapper.deleteBatchIds(oldStatsIds);
        statsDetailService.deleteByStatsIds(oldStatsIds);
        statsVinasseService.deleteByStatsIds(oldStatsIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleNewStats(List<SchedulingArrangeReq> schedulingArrangeReqs) {
        Integer scheduleId = schedulingArrangeReqs.get(0).getScheduleId();
        List<FormulaDetailByDay> formulaDetailByDayList = new ArrayList<>(365);
        schedulingArrangeReqs.stream()
                .filter(it -> Objects.isNull(it.getCheckState()) || !it.getCheckState())
                .collect(Collectors.groupingBy(it -> it.getBeginTime().getYear() + "#" + it.getBeginTime().getMonthValue()))
                .values()
                .forEach(byMonth -> {
                    //stats
                    Integer statsId = this.handleStatsByArrange(byMonth);
                    //vinasse
                    Map<String, BigDecimal> vinasseMap = new HashMap<>();
                    byMonth.stream()
                            .filter(it -> !CollectionUtils.isEmpty(it.getSchedulingVinasseReqList()))
                            .flatMap(it -> it.getSchedulingVinasseReqList().stream())
                            .collect(Collectors.groupingBy(SchedulingVinasseReq::getVinasseCode))
                            .forEach((key, value) -> {
                                BigDecimal vinasseData = value.stream()
                                        .map(SchedulingVinasseReq::getVinasseData)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal::add)
                                        .orElse(BigDecimal.ZERO);
                                vinasseMap.put(key, vinasseData);
                            });
                    this.statsVinasseService.handleVinasse(statsId, "0", vinasseMap);
                    //statsDetail
                    this.statsDetailService.handleNewDetails(statsId, byMonth, formulaDetailByDayList);
                });
        handleZZQ(schedulingArrangeReqs, scheduleId, formulaDetailByDayList);
    }

    /**
     * 处理扎帐期
     *
     * @param schedulingArrangeReqs
     * @param scheduleId
     * @param formulaDetailByDayList
     */
    private void handleZZQ(List<SchedulingArrangeReq> schedulingArrangeReqs, Integer scheduleId, List<FormulaDetailByDay> formulaDetailByDayList) {
        if (CollectionUtils.isEmpty(formulaDetailByDayList)) {
            return;
        }
        //计算扎帐期  投入  产出   耗粮
        Map<LocalDate, List<FormulaDetailByDay>> zzqMaterialMap = new HashMap<>(16);
        List<FormulaDetailByDay> sortByDayAsc = formulaDetailByDayList.stream()
                .sorted(Comparator.comparing(FormulaDetailByDay::getDayInfo))
                .collect(Collectors.toList());
        LocalDate minDate = sortByDayAsc.get(0).getDayInfo();
        LocalDate nextMonth = minDate.plusMonths(1);
        LocalDate maxLD = sortByDayAsc.get(sortByDayAsc.size() - 1).getDayInfo();
        LocalDate calculateLD = 23 < minDate.getDayOfMonth() ?
                LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), 23) : LocalDate.of(minDate.getYear(), minDate.getMonth(), 23);

        LocalDate maxNext = maxLD.plusMonths(1);
        LocalDate maxCalculateLD = 23 < maxLD.getDayOfMonth() ?
                LocalDate.of(maxNext.getYear(), maxNext.getMonth(), 23) : LocalDate.of(maxLD.getYear(), maxLD.getMonth(), 23);
        this.handleZZQMaterial(zzqMaterialMap, sortByDayAsc, calculateLD, maxCalculateLD);
        //计算扎帐期 计划生产甑口数
        Map<LocalDate, List<SchedulingArrangeReq>> zzqZenMap = new HashMap<>(16);

        this.handleZZQZen(zzqZenMap, schedulingArrangeReqs, calculateLD, maxCalculateLD);
        Assert.isTrue(zzqZenMap.size() == zzqMaterialMap.size(), "扎帐期数量异常");
        zzqZenMap.forEach((k, v) -> {
            if (CollectionUtils.isEmpty(v)) {
                return;
            }
            //stats
            BigDecimal workDays = v.stream()
                    .map(SchedulingArrangeReq::getWorkDays)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            BigDecimal productionDays = v.stream()
                    .map(SchedulingArrangeReq::getProductionDays)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            //获取当前集合最小时间和最大时间
            List<LocalDate> timeSerize = v.stream()
                    .flatMap(it -> Stream.of(it.getBeginTime(), it.getEndTime()))
                    .sorted()
                    .collect(Collectors.toList());
            SchedulingStats schedulingStats = new SchedulingStats()
                    .setSchedulingId(scheduleId)
                    .setYear(k.getYear())
                    .setMonth(k.getMonthValue())
                    .setType(Boolean.TRUE)
                    .setMonthBeginDate(timeSerize.get(0))
                    .setMonthEndDate(timeSerize.get(timeSerize.size() - 1))
                    .setWorkDays(workDays)
                    .setProductionDays(productionDays);
            this.save(schedulingStats);
            Integer statsId = schedulingStats.getId();
            //vinasse
            Map<String, BigDecimal> vinasseMap = new HashMap<>();
            v.stream()
                    .filter(vinesse -> !CollectionUtils.isEmpty(vinesse.getSchedulingVinasseReqList()))
                    .flatMap(vinesse -> vinesse.getSchedulingVinasseReqList().stream())
                    .collect(Collectors.groupingBy(SchedulingVinasseReq::getVinasseCode))
                    .forEach((key, value) -> {
                        BigDecimal vinasseData = value.stream()
                                .map(SchedulingVinasseReq::getVinasseData)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO);
                        vinasseMap.put(key, vinasseData);
                    });
            this.statsVinasseService.handleVinasse(statsId, "1", vinasseMap);
            //details
            this.statsDetailService.handleNewDetails(statsId, zzqMaterialMap.get(k));
        });
    }

    private void handleZZQZen(Map<LocalDate, List<SchedulingArrangeReq>> zzqMap, List<SchedulingArrangeReq> schedulingArrangeReqList, LocalDate calculateLD, LocalDate maxCalculateLD) {
        if (calculateLD.isAfter(maxCalculateLD)) {
            return;
        }
        LocalDate startT = calculateLD.plusMonths(-1);
        List<SchedulingArrangeReq> calculateRange = schedulingArrangeReqList.stream()
                .filter(it -> {
                    LocalDate begin = it.getBeginTime();
                    LocalDate end = it.getEndTime();
                    return begin.isAfter(startT) && !end.isAfter(calculateLD);
                }).collect(Collectors.toList());
        zzqMap.put(calculateLD, calculateRange);

        //计算下一个周期
        this.handleZZQZen(zzqMap, schedulingArrangeReqList, calculateLD.plusMonths(1), maxCalculateLD);
    }

    private void handleZZQMaterial(Map<LocalDate, List<FormulaDetailByDay>> zzqMap, List<FormulaDetailByDay> sortByDayAsc, LocalDate calculateLD, LocalDate maxCalculateLD) {
        if (calculateLD.isAfter(maxCalculateLD)) {
            return;
        }
        LocalDate startT = calculateLD.plusMonths(-1);
        List<FormulaDetailByDay> calculateRange = sortByDayAsc.stream()
                .filter(it -> {
                    LocalDate dateInfo = it.getDayInfo();
                    return dateInfo.isAfter(startT) && !dateInfo.isAfter(calculateLD);
                }).collect(Collectors.toList());
        zzqMap.put(calculateLD, calculateRange);
        //计算下一个周期
        this.handleZZQMaterial(zzqMap, sortByDayAsc, calculateLD.plusMonths(1), maxCalculateLD);
    }

    @Override
    public List<SchedulingStatsVO> reviewSchedule(Integer scheduleId, Integer billType) {
        List<SchedulingStats> bySchedulingId = schedulingStatsMapper.getBySchedulingId(scheduleId, billType);
        List<Integer> statsIds = bySchedulingId.stream().map(SchedulingStats::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(statsIds)) {
            return Collections.EMPTY_LIST;
        }
        //查询明细
        Map<Integer, List<SchedulingStatsDetailVO>> detailMap = statsDetailService.listStatsDetailsByStats(statsIds).stream()
                .peek(it -> it.setTotalQuality(it.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
                .collect(Collectors.groupingBy(SchedulingStatsDetailVO::getStatsId));
        //查询糟源
        Map<Integer, List<StatsVinasseVO>> vinasseMap = statsVinasseService.listVinasseByStatsIds(statsIds).stream()
                .peek(it -> it.setVinasseData(it.getVinasseData().setScale(0, RoundingMode.HALF_UP)))
                .collect(Collectors.groupingBy(StatsVinasseVO::getStatsId));
        return bySchedulingId.stream()
                .map(it -> {
                    SchedulingStatsVO schedulingStatsVO = CopyUtil.simpleCopy(it, SchedulingStatsVO.class);
                    Integer statsId = it.getId();
                    schedulingStatsVO.setStatsVinasseVOS(vinasseMap.get(statsId));
                    schedulingStatsVO.setSchedulingStatsDetailVOList(detailMap.get(statsId));
                    return schedulingStatsVO;
                }).collect(Collectors.toList());
    }

    @Override
    public SchedulingStatsMapVO previewSchedule(List<SchedulingArrangeReq> schedulingArrangeReqs, Integer monthPlanId) {
        SchedulingStatsMapVO schedulingStatsMapVO = new SchedulingStatsMapVO();

        Object obj = redisTemplate.opsForValue().get("monthScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
        if(StringUtil.isNotEmpty(obj)){
            redisTemplate.delete("monthScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
        }
        redisTemplate.opsForValue().set("monthScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId(),schedulingArrangeReqs);

        Object obj2 = redisTemplate.opsForValue().get("monthScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
        List<SchedulingArrangeReq> reqList = (List<SchedulingArrangeReq>) (obj2);
        log.error("[SchedulingStatsServiceImpl][previewSchedule]Reids存算法==={}",reqList.toString());

        MonthPlan monthPlan = monthPlanMapper.selectById(monthPlanId);
        schedulingArrangeReqs.forEach(it -> {
            if(Objects.isNull(it.getProductionDays())){
                return;
            }
            //非假期不允许跨月
            if (0 > BigDecimal.ZERO.compareTo(it.getProductionDays())
                    && (it.getBeginTime().getYear() != it.getEndTime().getYear()
                    || it.getBeginTime().getMonth() != it.getEndTime().getMonth())) {
                throw new BaseKnownException(420001, "非节假日不允许跨年跨月");
            }
            LocalDate beginTime = it.getBeginTime();
            LocalDate endTime = it.getEndTime();
            //行记录   如果开始时间小于23号  结束时间不能大于23号
            LocalDate splitDate = LocalDate.of(beginTime.getYear(), beginTime.getMonthValue(), 23);
            if (0 > BigDecimal.ZERO.compareTo(it.getProductionDays())
                    && !beginTime.isAfter(splitDate) && endTime.isAfter(splitDate)) {
                //throw new BaseKnownException(818001,MessageFormat.format("{0}年{1}月存在跨扎帐期的情况，请修正之后再提交", String.valueOf(beginTime.getYear()), String.valueOf(beginTime.getMonth().getValue())));
            }
        });
        List<SchedulingStatsVO> schedulingStatsVOList = schedulingArrangeReqs.stream()
                .filter(it -> Objects.nonNull(it.getBeginTime()))
                .collect(Collectors.groupingBy(it -> it.getYear() + "#" + it.getMonth()))
                .values()
                .stream()
                .map(byMonth -> {
                    //stats
                    SchedulingArrangeReq randomGet = byMonth.get(0);
                    BigDecimal workdaysByMonth = byMonth.stream()
                            .map(SchedulingArrangeReq::getWorkDays)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    BigDecimal productionDaysByMonth = byMonth.stream()
                            .map(SchedulingArrangeReq::getProductionDays)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    //vinasse
                    List<StatsVinasseVO> statsVinasseVOList = byMonth.stream()
                            .filter(it -> CollectionUtils.isNotEmpty(it.getSchedulingVinasseReqList()))
                            .flatMap(it -> it.getSchedulingVinasseReqList().stream())
                            .collect(Collectors.groupingBy(SchedulingVinasseReq::getVinasseCode))
                            .entrySet()
                            .stream()
                            .map(e -> {
                                BigDecimal vinasseData = e.getValue().stream()
                                        .map(SchedulingVinasseReq::getVinasseData)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal::add)
                                        .orElse(BigDecimal.ZERO);
                                return new StatsVinasseVO()
                                        .setVinasseCode(e.getKey())
                                        .setVinasseData(vinasseData.setScale(0, RoundingMode.HALF_UP));
                            }).collect(Collectors.toList());
                    //statsDetail
                    List<SchedulingStatsDetailVO> schedulingStatsDetailVOList = this.statsDetailService.previewStatsDetail(byMonth).stream()
                            .peek(it -> it.setTotalQuality(it.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
                            .map(d -> CopyUtil.simpleCopy(d, SchedulingStatsDetailVO.class))
                            .collect(Collectors.toList());

                    return new SchedulingStatsVO()
                            .setYear(randomGet.getYear())
                            .setMonth(randomGet.getMonth())
                            .setType(false)
                            .setWorkDays(workdaysByMonth)
                            .setProductionDays(productionDaysByMonth)
                            .setStatsVinasseVOS(statsVinasseVOList)
                            .setSchedulingStatsDetailVOList(schedulingStatsDetailVOList);
                })
                .sorted(Comparator.comparing(SchedulingStatsVO::getYear).thenComparing(Comparator.comparing(SchedulingStatsVO::getMonth)))
                .collect(Collectors.toList());
        List<SchedulingStatsVO> monthList = schedulingStatsVOList.stream().filter(f-> f.getYear().equals(monthPlan.getYear()) && f.getMonth().equals(monthPlan.getMonth())).collect(Collectors.toList());
        schedulingStatsMapVO.setSchedulingStatsVOList(schedulingStatsVOList);
        //物料需求月差异量
        schedulingStatsMapVO.setSchedulingStatsDetailVOList(monthList.get(0).getSchedulingStatsDetailVOList());
        return schedulingStatsMapVO;
    }

    private Integer handleStatsByArrange(List<SchedulingArrangeReq> schedulingArrangeReqList) {
        SchedulingArrangeReq randomGet = schedulingArrangeReqList.get(0);
        LocalDate minDate = schedulingArrangeReqList.stream()
                .min(Comparator.comparing(SchedulingArrangeReq::getBeginTime))
                .map(SchedulingArrangeReq::getBeginTime)
                .orElse(null);
        LocalDate maxDate = schedulingArrangeReqList.stream()
                .max(Comparator.comparing(SchedulingArrangeReq::getEndTime))
                .map(SchedulingArrangeReq::getEndTime)
                .orElse(null);
        BigDecimal workdaysByMonth = schedulingArrangeReqList.stream()
                .map(SchedulingArrangeReq::getWorkDays)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        BigDecimal productionDaysByMonth = schedulingArrangeReqList.stream()
                .map(SchedulingArrangeReq::getProductionDays)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        SchedulingStats schedulingStats = new SchedulingStats()
                .setSchedulingId(randomGet.getScheduleId())
                .setYear(randomGet.getBeginTime().getYear())
                .setMonth(randomGet.getBeginTime().getMonth().getValue())
                .setMonthBeginDate(minDate)
                .setMonthEndDate(maxDate)
                .setType(false)
                .setWorkDays(workdaysByMonth)
                .setProductionDays(productionDaysByMonth);
        this.save(schedulingStats);
        return schedulingStats.getId();
    }

    private Integer handleStatsSubmit(SchedulingStatsReq schedulingStatsReq) {
        SchedulingStats schedulingStats = new SchedulingStats()
                .setSchedulingId(schedulingStatsReq.getSchedulingId())
                .setYear(schedulingStatsReq.getYear())
                .setMonth(schedulingStatsReq.getMonth())
                .setWorkDays(schedulingStatsReq.getWorkDays());
        this.save(schedulingStats);
        return schedulingStats.getId();
    }
}
