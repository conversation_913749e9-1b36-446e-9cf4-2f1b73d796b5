package com.hvisions.plan.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hvisions.brewage.excel.MonthActualDataExcel;
import com.hvisions.brewage.feign.MkwineClient;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsDTO;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsVO;
import com.hvisions.brewage.mkwine.dto.QueryWineQualityDTO;
import com.hvisions.brewage.mkwine.vo.GetWineQualityVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.StaticsVO;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.dao.*;
import com.hvisions.plan.dto.*;
import com.hvisions.plan.dto.plan.dto.PurchasePlanMaterialDTO;
import com.hvisions.plan.dto.plan.dto.PurchasePlanReq;
import com.hvisions.plan.dto.plan.vo.DqMaterialDetailVO;
import com.hvisions.plan.dto.plan.vo.MaterialRequireReq;
import com.hvisions.plan.dto.plan.vo.RowVO;
import com.hvisions.plan.dto.plan.vo.WorkshopPitOrderPotTaskQueryVO;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.PlanStateEnum;
import com.hvisions.plan.enums.StatsTypeEnum;
import com.hvisions.plan.sap.dto.SapBaseListDto;
import com.hvisions.plan.sap.dto.SapBaseResponseDto;
import com.hvisions.plan.sap.dto.ScheduleSyncDto;
import com.hvisions.plan.sap.dto.ScheduleSyncItemDto;
import com.hvisions.plan.sap.service.SapService;
import com.hvisions.plan.service.*;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.plan.utils.DateUtil;
import com.hvisions.plan.utils.StringUtil;
import com.hvisions.plan.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 月度计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MonthPlanServiceImpl extends ServiceImpl<MonthPlanMapper, MonthPlan> implements IMonthPlanService {

    @Resource
    SchedulingMapper schedulingMapper;
    @Resource
    SchedulingStatsMapper schedulingStatsMapper;
    @Resource
    SchedulingLocationMapper schedulingLocationMapper;
    @Resource
    BaseWrapper baseWrapper;
    @Resource
    MonthPlanDetailMapper monthPlanDetailMapper;
    @Resource
    IMonthPlanDetailService monthPlanDetailService;
    @Resource
    IMonthPlanVinasseService monthPlanVinasseService;
    @Resource
    StatsVinasseMapper statsVinasseMapper;
    @Resource
    MonthPlanVinasseMapper monthPlanVinasseMapper;
    @Resource
    StringRedisTemplate stringRedisTemplate;
    @Resource
    ISchedulingService schedulingService;
    @Resource
    ISchedulingArrangeService schedulingArrangeService;
    @Resource
    IAuditService auditService;
    @Resource
    private SapService sapService;
    @Autowired
    LocationExtendClient locationExtendClient;
    @Resource
    MonthPlanMapper monthPlanMapper;
    @Resource
    RowMapper rowMapper;
    @Resource
    MkwineClient mkwineClient;
    @Autowired
    SchedulingArrangeMapper schedulingArrangeMapper;
    @Resource
    ISchedulingLocationService schedulingLocationService;
    @Resource
    ISchedulingRowService schedulingRowService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private GwpServiceStrategyImpl gwpServiceStrategyImpl;
    @Resource
    private DqProductServiceStrategyImpl dqProductServiceStrategyImpl;

    /**
     * 月计划首页
     *
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.hvisions.brewage.plan.vo.MonthPlanVO>
     * <AUTHOR>
     * @date 2022/3/30 10:41
     */
    @Override
    public Page<MonthPlanVO> queryMainPagedMonthPlan(MonthPlanMainQueryReq req, PageInfo pageInfo) {
        Integer beginYear = req.getBeginYear();
        Integer endYear = req.getEndYear();
        Integer beginMonth = req.getBeginMonth();
        Integer endMonth = req.getEndMonth();

        if (Objects.isNull(beginYear) && Objects.isNull(endYear) && Objects.isNull(beginMonth) && Objects.isNull(endMonth)) {
            LocalDate now = LocalDate.now();
            req.setBeginYear(now.getYear());
            req.setEndYear(now.getYear());
            req.setBeginMonth(now.getMonthValue());
            req.setEndMonth(now.getMonthValue());
        }
        Page<MonthPlanVO> page = new Page<>(pageInfo.getPage(), pageInfo.getPageSize());
        List<MonthPlanVO> records = baseMapper.queryMainPagedMonthPlan(page, req);

        for (MonthPlanVO record : records) {
            LambdaQueryWrapper<MonthPlan> eq = new LambdaQueryWrapper<MonthPlan>()
                    .eq(MonthPlan::getCenterId, record.getCenterId())
                    .eq(MonthPlan::getYear, record.getYear())
                    .eq(MonthPlan::getMonth, record.getMonth())
                    .ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode())
                    .orderByDesc(MonthPlan::getId).last("LIMIT 1");
            MonthPlan monthPlan = baseMapper.selectOne(eq);
            if (Objects.nonNull(monthPlan)) {
                record.setPlantId(monthPlan.getPlantId());
                record.setPlantCode(monthPlan.getPlantCode());
                record.setPlantName(monthPlan.getPlantName());
                record.setCenterCode(monthPlan.getCenterCode());
                record.setCenterName(monthPlan.getCenterName());
                record.setSchedulingId(monthPlan.getSchedulingId());
                record.setMonthBeginDate(monthPlan.getMonthBeginDate());
                record.setMonthEndDate(monthPlan.getMonthEndDate());
                //处理实际产出
                QueryWineQualityDTO queryWineQualityDTO = new QueryWineQualityDTO();
                queryWineQualityDTO.setCenterIds(new ArrayList<Integer>() {{
                    add(record.getCenterId());
                }});
                queryWineQualityDTO.setStartDate(monthPlan.getMonthBeginDate());
                queryWineQualityDTO.setEndDate(monthPlan.getMonthEndDate());

                ResultVO<List<GetWineQualityVO>> resultVO = mkwineClient.getWineQualityData(queryWineQualityDTO);
                if(resultVO.getCode()!=200){
                    throw new BaseKnownException(resultVO.getCode(),resultVO.getMessage());
                }

                Optional.ofNullable(resultVO.getData())
                        .filter(it -> !it.isEmpty() && 1 == it.size())
                        .ifPresent(it -> record.setActualWinOutPut(BigDecimal.valueOf(it.get(0).getTo60Quantity())));

                final ProductionDetailsDTO dto = new ProductionDetailsDTO();
                dto.setStartTime(monthPlan.getMonthBeginDate());
                dto.setEndTime(monthPlan.getMonthEndDate());
                dto.setCenterId(record.getCenterId());
//                dto.setLocationId(monthPlan.getLocationId());
                ResultVO<ProductionDetailsVO> resultVO1 = mkwineClient.getProductionDetailsByDate(dto);
                if(resultVO1.getCode()!=200){
                    throw new BaseKnownException(resultVO1.getCode(),resultVO1.getMessage());
                }
                final ProductionDetailsVO details =resultVO1.getData();

                        record.setActualInputLiang(
                        details.getActualInputLiang().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                                && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
                );
                record.setActualUseLiang(
                        details.getActualUseLiang().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                                && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
                );
                record.setActualOutputWine(
                        details.getActualOutputWine().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                                && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
                );
                record.setActualSpeed(record.getActualOutputWine().multiply(BigDecimal.valueOf(100)).divide(record.getWinOutput(), 2, BigDecimal.ROUND_DOWN));
            }
        }

        page.setRecords(records);

        return page;
    }

    /**
     * 排程拆分月度计划
     *
     * @param scheduleId 排程 ID
     * <AUTHOR>
     * @date 2022/3/30 10:16
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void splitMonthPlan(Integer scheduleId,Integer planType) {
        Scheduling scheduling = schedulingMapper.selectById(scheduleId);
        // 查询排程涉及的车间
        LambdaQueryWrapper<SchedulingLocation> wrapper = new LambdaQueryWrapper<SchedulingLocation>().eq(SchedulingLocation::getSchedulingId, scheduleId);
        List<SchedulingLocation> schedulingLocations = schedulingLocationMapper.selectList(wrapper);

        // 排程统计数据
        List<SchedulingStatsDetailDto> statsListBySchedulingId = schedulingStatsMapper.getStatsListBySchedulingId(scheduleId);
        // 按年分组
        Map<Integer, List<SchedulingStatsDetailDto>> yearMap = statsListBySchedulingId.stream().collect(Collectors.groupingBy(SchedulingStatsDetailDto::getYear));

        LocalDate now = LocalDate.now();
        List<MonthPlan> monthPlans = new ArrayList<>();
        // 按车间循环
        for (SchedulingLocation location : schedulingLocations) {
            // 按年循环（可能存在跨年）
            for (List<SchedulingStatsDetailDto> value : yearMap.values()) {
                // 使用扎账期的生成当月 月计划
                Map<Integer, List<SchedulingStatsDetailDto>> monthMap = value.stream().collect(Collectors.groupingBy(SchedulingStatsDetailDto::getMonth));
                for (List<SchedulingStatsDetailDto> statsDetailDtoList : monthMap.values()) {
                    SchedulingStatsDetailDto detailDto = statsDetailDtoList.get(0);
                    // 仅处理当月及之后的，当月之前的数据不做处理
                    if (detailDto.getYear() < now.getYear() || (detailDto.getYear() == now.getYear() && detailDto.getMonth() < now.getMonthValue())) {
                        continue;
                    }
                    SapSyncConfigChildSaveDTO sapSyncConfig = schedulingMapper.findSapSyncConfig(detailDto.getYear(),detailDto.getMonth());
                    if(StringUtil.isNotEmpty(sapSyncConfig) && StringUtil.isNotEmpty(sapSyncConfig.getStartTime()) && StringUtil.isNotEmpty(sapSyncConfig.getEndTime())){
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                        String monthBeginDate = formatter.format(sapSyncConfig.getStartTime());
                        String monthEndDate = formatter.format(sapSyncConfig.getEndTime());

                        statsDetailDtoList.forEach(item->{
                            item.setMonthBeginDate(LocalDate.parse(monthBeginDate));
                            item.setMonthEndDate(LocalDate.parse(monthEndDate));
                        });

                    }
                    // 创建月计划
                    MonthPlan monthPlan = this.createMonthPlan(scheduling, location, statsDetailDtoList);
                    if(planType == 1 && monthPlan != null){
                        monthPlans.add(monthPlan);
                    }
                }
            }
        }

        if(monthPlans.size()>0){
            //月计划变更下发数据给SAP、8中心MES
            //10:基酒生产计划(30000132)
            SapBaseResponseDto dto = baseLiquorProductionPlanSap(monthPlans,2);
            if(dto.getEsMessage().getMsgty().equals("E")){
                //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
            }
            //20:等级酒产出计划
            dto = gradeWineProductionPlanSap(monthPlans,2);
            if(dto.getEsMessage().getMsgty().equals("E")){
                //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
            }
            //30:曲粉需求计划
            dto = qufenDemandPlanSap(monthPlans,2);
            if(dto.getEsMessage().getMsgty().equals("E")){
                //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
            }
        }
    }

    /**
     * 导入实际投/耗/产
     *
     * @param file 导入文件
     * <AUTHOR>
     * @date 2022/4/7 10:12
     */
    @Override
    public void importActualData(MultipartFile file) {
        List<MonthActualDataExcel> data = EasyExcelUtil.getImport(file, MonthActualDataExcel.class);

        String errorMessages = "";
        for (MonthActualDataExcel datum : data) {
            LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>().eq(MonthPlan::getYear, datum.getYear()).eq(MonthPlan::getMonth, datum.getMonth()).eq(MonthPlan::getLocationCode, datum.getLocationCode()).ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode());
            MonthPlan monthPlan = baseMapper.selectOne(wrapper);
            if (Objects.nonNull(monthPlan)) {
                monthPlan.setActualFeeding(datum.getActualFeeding());
                monthPlan.setActualConsumption(datum.getActualConsumption());
                monthPlan.setActualWinOutput(datum.getActualWinOutput());

                baseMapper.updateById(monthPlan);
            } else {
                String errorMsg = String.format("%s%s车间%s,未找到对应月计划\r\n", datum.getYear(), datum.getMonth(), datum.getLocationCode());
                errorMessages += errorMsg;
            }
        }
        if (StringUtils.isNotBlank(errorMessages)) {
            throw new BaseKnownException(10000, errorMessages);
        }
    }

    /**
     * 月计划下发
     *
     * @param planIdList 月计划ID
     * <AUTHOR>
     * @date 2022/4/7 11:04
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issue(List<Integer> planIdList) {
        // 更新月计划
        List<MonthPlan> monthPlan = baseMapper.selectBatchIds(planIdList);
        if (monthPlan.stream().anyMatch(it -> !it.getPlanState().equals(PlanStateEnum.NEW.getCode()))) {
            throw new BaseKnownException(10000, "非待下发状态不允许下发");
        }
        for (MonthPlan plan : monthPlan) {
            plan.setPlanState(PlanStateEnum.IMPLEMENT.getCode());
            baseMapper.updateById(plan);
        }
        //月计划下发数据给SAP、8中心MES
        //10:基酒生产计划(30000132)
        SapBaseResponseDto dto = baseLiquorProductionPlanSap(monthPlan,1);
        if(dto.getEsMessage().getMsgty().equals("E")){
            //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
        }
        //20:等级酒产出计划
        dto = gradeWineProductionPlanSap(monthPlan,1);
        if(dto.getEsMessage().getMsgty().equals("E")){
            //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
        }
        //30:曲粉需求计划
        dto = qufenDemandPlanSap(monthPlan,1);
        if(dto.getEsMessage().getMsgty().equals("E")){
            //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
        }
    }

    private SapBaseResponseDto baseLiquorProductionPlanSap(List<MonthPlan> monthPlans, Integer type) {
        //10:基酒生产计划(30000132)
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }

        scheduleSyncDto.setZjhcj("2");
        scheduleSyncDto.setZjhfl("10");
        LocationDTO base = schedulingMapper.getLocationById(monthPlans.get(0).getPlantId());
        scheduleSyncDto.setZjd(base.getName());

        Map<Integer,List<MonthPlan>> grouped = monthPlans.stream().collect(Collectors.groupingBy(MonthPlan::getLocationId));
        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        grouped.forEach((key, value)->{
            value.forEach(item->{

                ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
                //LocationDTO center = schedulingMapper.getLocationById(item.getCenterId());
                scheduleSyncItemDto.setZzx(item.getCenterName());
                scheduleSyncItemDto.setZcj(item.getLocationName());

                scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(item.getMonthBeginDate()));
                scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(item.getMonthEndDate()));
                scheduleSyncItemDto.setZnd(item.getPlanTime());
                scheduleSyncItemDto.setZyf(item.getPlanTime()+""+item.getMonth());
                scheduleSyncItemDto.setZyllx("30");
                List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();
                MaterialDTO materialDTO = schedulingMapper.findMaterial(2,"基酒");
                SapBaseListDto sapBaseListDto = new SapBaseListDto();
                sapBaseListDto.setMatnr(materialDTO.getMaterialCode());

                sapBaseListDto.setMenge(item.getWinOutput());
                sapBaseListDto.setMeins(materialDTO.getUomName());
                sapBaseList.add(sapBaseListDto);

                scheduleSyncItemDto.setList(sapBaseList);
                itemList.add(scheduleSyncItemDto);
            });
        });

        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    private SapBaseResponseDto gradeWineProductionPlanSap(List<MonthPlan> monthPlans,Integer type) {
        //20:等级酒产出计划
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }

        scheduleSyncDto.setZjhcj("2");
        scheduleSyncDto.setZjhfl("20");
        LocationDTO base = schedulingMapper.getLocationById(monthPlans.get(0).getPlantId());
        scheduleSyncDto.setZjd(base.getName());

        Map<Integer,List<MonthPlan>> grouped = monthPlans.stream().collect(Collectors.groupingBy(MonthPlan::getLocationId));
        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        grouped.forEach((key, value)->{
            value.forEach(item->{

                ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
                //LocationDTO center = schedulingMapper.getLocationById(item.getCenterId());
                scheduleSyncItemDto.setZzx(item.getCenterName());
                scheduleSyncItemDto.setZcj(item.getLocationName());

                scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(item.getMonthBeginDate()));
                scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(item.getMonthEndDate()));
                scheduleSyncItemDto.setZnd(item.getPlanTime());
                scheduleSyncItemDto.setZyf(item.getPlanTime()+""+item.getMonth());
                scheduleSyncItemDto.setZyllx("10");
                List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();

                MaterialRequireReq materialRequireReq = new MaterialRequireReq();
                materialRequireReq.setId(item.getId());
                materialRequireReq.setPlanTime(Integer.valueOf(item.getPlanTime()));
                materialRequireReq.setPlantId(item.getPlantId());
                materialRequireReq.setCenterId(item.getCenterId());
                materialRequireReq.setLocationId(item.getLocationId());
                List<GwpMaterialVO> lists = gwpServiceStrategyImpl.listMaterialRequirePlan(materialRequireReq);

                // 分组并求和
                Map<String, Double> groupedSum = lists.stream()
                        .collect(Collectors.groupingBy(
                                list -> list.getMaterialList().get(0).getMaterialName(), // 分组依据：每个列表的第一个元素
                                Collectors.summingDouble(list -> list.getMaterialList().stream().mapToDouble(GwpMaterialDetailVO::getPlanProduce).sum()) // 求和逻辑
                        ));

                groupedSum.forEach((materialName, sum)->{
                    SapBaseListDto sapBaseListDto = new SapBaseListDto();
                    MaterialDTO materialDTO = schedulingMapper.findMaterial(2,materialName);
                    sapBaseListDto.setMatnr(materialDTO.getMaterialCode());
                    sapBaseListDto.setMenge(BigDecimal.valueOf(sum));
                    sapBaseListDto.setMeins(materialDTO.getUomName());
                    sapBaseList.add(sapBaseListDto);
                });

                scheduleSyncItemDto.setList(sapBaseList);
                itemList.add(scheduleSyncItemDto);
            });
        });

        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    private SapBaseResponseDto qufenDemandPlanSap(List<MonthPlan> monthPlans,Integer type) {
        //30:曲粉需求计划
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }

        scheduleSyncDto.setZjhcj("2");
        scheduleSyncDto.setZjhfl("30");
        LocationDTO base = schedulingMapper.getLocationById(monthPlans.get(0).getPlantId());
        scheduleSyncDto.setZjd(base.getName());

        Map<Integer,List<MonthPlan>> grouped = monthPlans.stream().collect(Collectors.groupingBy(MonthPlan::getLocationId));
        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        grouped.forEach((key, value)->{
            value.forEach(item->{

                ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
                //LocationDTO center = schedulingMapper.getLocationById(item.getCenterId());
                scheduleSyncItemDto.setZzx(item.getCenterName());
                scheduleSyncItemDto.setZcj(item.getLocationName());

                scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(item.getMonthBeginDate()));
                scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(item.getMonthEndDate()));
                scheduleSyncItemDto.setZnd(item.getPlanTime());
                scheduleSyncItemDto.setZyf(item.getPlanTime()+"0"+item.getMonth());
                scheduleSyncItemDto.setZyllx("10");
                List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();

                MaterialRequireReq materialRequireReq = new MaterialRequireReq();
                materialRequireReq.setId(item.getId());
                materialRequireReq.setPlanTime(Integer.valueOf(item.getPlanTime()));
                materialRequireReq.setPlantId(item.getPlantId());
                materialRequireReq.setCenterId(item.getCenterId());
                materialRequireReq.setLocationId(item.getLocationId());
                List<DqMaterialVO> lists = dqProductServiceStrategyImpl.listMaterialRequirePlan(materialRequireReq);

                // 分组并求和
                Map<String, Double> groupedSum = lists.stream()
                        .collect(Collectors.groupingBy(
                                list -> list.getMaterialList().get(0).getMaterialName(), // 分组依据：每个列表的第一个元素
                                Collectors.summingDouble(list -> list.getMaterialList().stream().mapToDouble(DqMaterialDetailVO::getPlanProduce).sum()) // 求和逻辑
                        ));

                groupedSum.forEach((materialName, sum)->{
                    SapBaseListDto sapBaseListDto = new SapBaseListDto();
                    MaterialDTO materialDTO = schedulingMapper.findMaterial(4,materialName);
                    sapBaseListDto.setMatnr(materialDTO.getMaterialCode());
                    sapBaseListDto.setMenge(BigDecimal.valueOf(sum));
                    sapBaseListDto.setMeins(materialDTO.getUomName());
                    sapBaseList.add(sapBaseListDto);
                });

                scheduleSyncItemDto.setList(sapBaseList);
                itemList.add(scheduleSyncItemDto);
            });
        });

        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    /**
     * 变更创建排程
     *
     * @param req
     * @return void
     * <AUTHOR>
     * @date 2022/4/11 14:54
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createScheduling(MonthPlanCreateSchedulingReq req) {
        // 排程第一步
        Integer scheduleId = schedulingService.handleNewSchedule(req.getSchedulingReq());

        // 创建排程第二步
        List<SchedulingArrangeReq> schedulingArrangeReqs = req.getSchedulingArrangeReqs();
        schedulingArrangeReqs.forEach(it -> it.setScheduleId(scheduleId));
        schedulingArrangeService.handleArrange(schedulingArrangeReqs);

        // 提交审批
        AuditAddReq auditAddReq = req.getAuditAddReq();
        // 处理车间选择'全部'场景，根据中心循环查询所有车间ID
        if (Objects.equals(auditAddReq.getLocationIds(), CommonConsts.STRING_ZERO)) {
            List<Integer> centerIdList = Arrays.stream(auditAddReq.getCenterIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<Integer> locationIdList = new ArrayList<>();
            for (Integer centerId : centerIdList) {
                List<LocationDTO> locations = Optional.ofNullable(locationExtendClient.getLocationListByParentId(centerId)).filter(ResultVO::isSuccess).map(ResultVO::getData).orElseThrow(() -> new BaseKnownException(325003, centerId + "获取酿酒中心下属车间信息异常"));

                locationIdList.addAll(locations.stream().map(LocationDTO::getId).collect(Collectors.toList()));
            }
            auditAddReq.setLocationIds(StringUtils.join(locationIdList, ","));
        }
        auditAddReq.setPlanId(scheduleId);
        auditAddReq.setPlanType(1);
        auditAddReq.setPlanTime(req.getSchedulingReq().getPlanTime());
        auditAddReq.setBeginTime(req.getSchedulingReq().getBeginTime());
        auditAddReq.setEndTime(req.getSchedulingReq().getEndTime());
        auditAddReq.setMonth(req.getAuditAddReq().getMonth());
        auditService.addInfo(auditAddReq);
    }

    @Override
    public SchedulingArrangeByRowVO getMonthArrangeInfo(MonthPlanReq monthPlanReq) {
        //车间  排程年份  确定排程ID 加个月
        int monthValue = LocalDate.now().getMonthValue();
        monthPlanReq.setMonth(monthValue);
        Integer scheduleId = monthPlanMapper.getScheduleIdByPlantLocationTime(monthPlanReq);
        Assert.notNull(scheduleId, "未匹配到符合条件的排程信息");
        //根据排程ID查询排程相关信息  根据排次ID进行过滤
        return schedulingArrangeService.getByScheduleId(scheduleId).stream().filter(it -> it.getRowId().equals(monthPlanReq.getRowId())).findFirst().orElse(null);
    }

    /**
     * 创建月度计划
     *
     * <AUTHOR>
     * @date 2022/3/31 15:28
     */
    private MonthPlan createMonthPlan(Scheduling scheduling, SchedulingLocation location, List<SchedulingStatsDetailDto> statsDetailDtoList) {
        Integer locationId = location.getLocationId();
        List<SchedulingStatsDetailDto> collect = statsDetailDtoList.stream().filter(it -> Objects.equals(it.getBillType(), "1")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        SchedulingStatsDetailDto detailDto = collect.get(0);
        Integer year = detailDto.getYear();
        Integer month = detailDto.getMonth();
        // 判断车间当月（计划年份）有无生效的月计划
        LambdaQueryWrapper<MonthPlan> monthWrapper = new LambdaQueryWrapper<MonthPlan>().eq(MonthPlan::getYear, year).eq(MonthPlan::getMonth, month).eq(MonthPlan::getLocationId, locationId).eq(MonthPlan::getPlanTime, scheduling.getPlanTime()).orderByDesc(MonthPlan::getVersion);
        List<MonthPlan> monthPlans = baseMapper.selectList(monthWrapper).stream().filter(it -> PlanStateEnum.NEW.getCode().equals(it.getPlanState()) || PlanStateEnum.IMPLEMENT.getCode().equals(it.getPlanState())).collect(Collectors.toList());
        // 将待下发/执行中的批量归档
        if (CollectionUtils.isNotEmpty(monthPlans)) {
            monthPlans.forEach(it -> {
                it.setPlanState(PlanStateEnum.FILE.getCode());
                baseMapper.updateById(it);
            });
        }

        MonthPlan monthPlan = CopyUtil.simpleCopy(location, MonthPlan.class);
        monthPlan.setCode(generatePlanCode(year, month, location.getCenterCode(), location.getLocationCode()));
        monthPlan.setName(String.format("%s%s月度生产计划", location.getCenterName(), location.getLocationName()));
        monthPlan.setPlanTime(scheduling.getPlanTime());
        monthPlan.setYear(year);
        monthPlan.setMonth(month);
        monthPlan.setWorkDays(detailDto.getWorkDays());
        monthPlan.setVersion(CollectionUtils.isEmpty(monthPlans) ? 0 : monthPlans.get(0).getVersion() + 1);
        monthPlan.setPlanState(PlanStateEnum.NEW.getCode());
        monthPlan.setMonthBeginDate(detailDto.getMonthBeginDate());
        monthPlan.setMonthEndDate(detailDto.getMonthEndDate());
        // 扎账期天数
        Optional<SchedulingStatsDetailDto> optional = statsDetailDtoList.stream().filter(it -> Objects.equals(it.getBillType(), "1")).findFirst();
        monthPlan.setBillDays(optional.isPresent() ? optional.get().getWorkDays() : 0);

        // 投入
        BigDecimal feeding = statsDetailDtoList.stream().filter(it -> Objects.equals(it.getType(), StatsTypeEnum.INVESTMENT.getCode())).map(SchedulingStatsDetailDto::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add);
        monthPlan.setFeeding(feeding);

        // 消耗
        BigDecimal consumption = statsDetailDtoList.stream().filter(it -> Objects.equals(it.getType(), StatsTypeEnum.CONSUME.getCode())).map(SchedulingStatsDetailDto::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add);
        monthPlan.setConsumption(consumption);

        // 计划基酒产出
        BigDecimal winOutput = statsDetailDtoList.stream().filter(it -> Objects.equals(it.getType(), StatsTypeEnum.PRODUCE.getCode())).map(SchedulingStatsDetailDto::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add);
        monthPlan.setWinOutput(winOutput);

        baseMapper.insert(monthPlan);

        // 月计划详情
        List<MonthPlanDetail> monthPlanDetails = baseWrapper.convertToList(statsDetailDtoList, MonthPlanDetail.class);
        monthPlanDetails.forEach(it -> {
            it.setMonthPlanId(monthPlan.getId());
            monthPlanDetailMapper.insert(it);
        });
        // 月计划糟源
        List<Integer> statsIdList = statsDetailDtoList.stream().map(SchedulingStatsDetailDto::getStatsId).distinct().collect(Collectors.toList());
        List<StatsVinasse> statsVinasses = statsVinasseMapper.selectList(new LambdaQueryWrapper<StatsVinasse>().in(StatsVinasse::getStatsId, statsIdList));
        List<MonthPlanVinasse> monthPlanVinasses = baseWrapper.convertToList(statsVinasses, MonthPlanVinasse.class);
        monthPlanVinasses.forEach(it -> {
            it.setMonthPlanId(monthPlan.getId());
            monthPlanVinasseMapper.insert(it);
        });
        return monthPlan;
    }

    /**
     * 根据月计划获取采购预估
     *
     * @param req
     * <AUTHOR>
     * @date 2022/4/12 13:28
     */
    @Override
    public List<PurchasePlanMaterialDTO> purchasePlan(PurchasePlanReq req) {
        LocalDate beginDate = req.getBeginDate();
        LocalDate endDate = req.getEndDate();

        List<PurchasePlanMaterialDTO> planMaterialDTOS = new ArrayList<>();
        getMaterialList(beginDate, endDate, planMaterialDTOS);

        return planMaterialDTOS;
    }

    @Override
    public Integer getRowIdByDay(Integer locationId, String day) {
        LocalDate ld = DateUtil.parse2LocalDate(day, DateUtil.LD_PATTERN);
        //匹配月计划  排程
        Integer scheduleId = Optional.ofNullable(monthPlanMapper.getPlanByLocationAndDate(locationId, ld)).map(MonthPlan::getSchedulingId).orElseThrow(() -> new BaseKnownException(530001, "未查到符合条件的月计划"));
        //根据排程匹配排次
        return schedulingArrangeService.getRowIdByScheduleAndDate(scheduleId, ld);
    }

    @Override
    public BigDecimal getVinasseQuantity(Integer centerId, Integer locationId, String vinasseCode, String startDate, String endDate) {
        Integer year = null;
        Integer month = null;
        LocalDate rollDate = null;
        LocalDate start = DateUtil.parse2LocalDate(startDate, DateUtil.LD_PATTERN);
        LocalDate end = DateUtil.parse2LocalDate(endDate, DateUtil.LD_PATTERN);
        //查询所有的月计划以及糟源信息
        List<MonthPlanVinasseData> monthPlanVinasseData = new ArrayList<>();
        do {
            if (Objects.isNull(rollDate)) {
                rollDate = start;
            }
            year = rollDate.getYear();
            month = rollDate.getMonthValue();
            monthPlanVinasseData.addAll(monthPlanMapper.listMonthPlanVinasseData(centerId, locationId, year, month, vinasseCode));
            rollDate = rollDate.plusMonths(1);
        } while (!(rollDate.getYear() > end.getYear() || (rollDate.getYear() == end.getYear() && rollDate.getMonthValue() > end.getMonthValue())));
        //滚动查询日期范围  获取单个的
        BigDecimal quantity = BigDecimal.ZERO;
        rollDate = start;
        do {
            //查询当天的糟源产出量
            LocalDate finalDate = rollDate;
            BigDecimal dayQuantity = monthPlanVinasseData.stream().filter(it -> !finalDate.isBefore(it.getMonthBeginDate()) && !finalDate.isAfter(it.getMonthEndDate()) && Objects.nonNull(it.getVinasseData()) && Objects.nonNull(it.getWorkDays())).map(it -> {
                //求平均甑口数
                return it.getVinasseData().divide(new BigDecimal(it.getWorkDays()), 3, RoundingMode.HALF_UP);
            }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            quantity = quantity.add(dayQuantity);
            rollDate = rollDate.plusDays(1);
        } while (!rollDate.isAfter(end));
        return quantity;
    }

    @Override
    public List<RowVO> listRowByYear(Integer centerId, Integer locationId, Integer year) {
        //根据中心 车间 排程年份查询所有的月计划
        LambdaQueryWrapper<MonthPlan> planLambdaQueryWrapper = new LambdaQueryWrapper<MonthPlan>().eq(MonthPlan::getCenterId, centerId).eq(MonthPlan::getLocationId, locationId).eq(MonthPlan::getPlanTime, String.valueOf(year));
        List<Integer> rowIds = monthPlanMapper.selectList(planLambdaQueryWrapper).stream().flatMap(it -> schedulingArrangeService.listRowInfoByMonthPlan(it).stream()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rowIds)) {
            return new ArrayList<>();
        }
        return rowMapper.selectBatchIds(rowIds).stream().map(it -> CopyUtil.simpleCopy(it, RowVO.class)).collect(Collectors.toList());
    }

    @Override
    public List<RowVO> newListRowByYear(Integer centerId, Integer locationId, Integer year) {
        //根据中心 车间 排程年份查询所有的月计划
        LambdaQueryWrapper<MonthPlan> planLambdaQueryWrapper = new LambdaQueryWrapper<MonthPlan>().
                eq(MonthPlan::getCenterId, centerId).
                eq(MonthPlan::getLocationId, locationId).
                eq(MonthPlan::getPlanTime, String.valueOf(year))
                .le(MonthPlan::getMonthBeginDate,LocalDate.now())
                .ge(MonthPlan::getMonthEndDate,LocalDate.now());
        List<Integer> rowIds = monthPlanMapper.selectList(planLambdaQueryWrapper).stream().flatMap(it -> schedulingArrangeService.newListRowInfoByMonthPlan(it).stream()).distinct().collect(Collectors.toList());
        if(rowIds.size()==1){
            //取出rowIds的value
            Integer rowId = rowIds.get(0).intValue();
            RowIdVO rowSortByRowId = schedulingArrangeMapper.getRowSortByRowId(rowId);
            rowIds.add(rowSortByRowId.getTargetRowId());
            rowIds.add(rowSortByRowId.getNextRowId());
        }
        if (CollectionUtils.isEmpty(rowIds)) {
            return new ArrayList<>();
        }
        return rowMapper.selectBatchIds(rowIds).stream().map(it -> CopyUtil.simpleCopy(it, RowVO.class)).collect(Collectors.toList());
    }

    /**
     * 产量及计划完成率
     *
     * @param req
     * @return
     */
    @Override
    public List<ProductionPlanCompletionRateVO> getProductionPlanCompletionRate(ProductionPlanCompletionRateReq req) {
        // 查询全年月计划
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>().in(MonthPlan::getCenterId, req.getCenterIdList()).ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode()).eq(MonthPlan::getYear, req.getYear());
        List<MonthPlan> allMonthPlanList = baseMapper.selectList(wrapper);

        Map<Integer, List<MonthPlan>> plantPlanMap = allMonthPlanList.stream().collect(Collectors.groupingBy(MonthPlan::getPlantId));

        List<ProductionPlanCompletionRateVO> plantCompletionRateList = new ArrayList<ProductionPlanCompletionRateVO>();

        // 根据基地分组
        for (List<MonthPlan> plantMonthPlanList : plantPlanMap.values()) {
            MonthPlan monthPlan = plantMonthPlanList.get(0);
            ProductionPlanCompletionRateVO productionPlanCompletionRateVO = new ProductionPlanCompletionRateVO();
            productionPlanCompletionRateVO.setPlantId(monthPlan.getPlantId());
            productionPlanCompletionRateVO.setPlantCode(monthPlan.getPlantCode());
            productionPlanCompletionRateVO.setPlantName(monthPlan.getPlantName());

            List<ProductionPlanCompletionRateVO.CenterDetail> detailList = new ArrayList<>();
            // 根据中心分组
            Map<Integer, List<MonthPlan>> centerPlanMap = plantMonthPlanList.stream().collect(Collectors.groupingBy(MonthPlan::getCenterId));
            for (List<MonthPlan> centerMonthPlanList : centerPlanMap.values()) {
                MonthPlan plan = centerMonthPlanList.get(0);
                ProductionPlanCompletionRateVO.CenterDetail centerDetail = new ProductionPlanCompletionRateVO.CenterDetail();
                centerDetail.setCenterId(plan.getCenterId());
                centerDetail.setCenterCode(plan.getCenterCode());
                centerDetail.setCenterName(plan.getCenterName());

                List<Integer> monthPlanIdList = centerMonthPlanList.stream().map(MonthPlan::getId).collect(Collectors.toList());
                List<MonthPlanDetail> monthPlanDetailList = monthPlanDetailMapper.selectList(new LambdaQueryWrapper<MonthPlanDetail>().in(MonthPlanDetail::getMonthPlanId, monthPlanIdList));

                // 查询范围内的计划汇总
                List<MonthPlan> collect = centerMonthPlanList.stream().filter(it -> it.getMonth() >= req.getBeginMonth() && it.getMonth() <= req.getEndMonth()).collect(Collectors.toList());
                // 产出
                centerDetail.setActualWinOutput(collect.stream().map(MonthPlan::getActualWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add));
                centerDetail.setAllYearWinOutput(centerMonthPlanList.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add));
                // 投粮
                centerDetail.setActualFeeding(collect.stream().map(MonthPlan::getActualFeeding).reduce(BigDecimal.ZERO, BigDecimal::add));
                centerDetail.setAllYearFeeding(centerMonthPlanList.stream().map(MonthPlan::getFeeding).reduce(BigDecimal.ZERO, BigDecimal::add));

                List<ProductionPlanCompletionRateVO.MaterialDetail> materialDetailList = new ArrayList<>();
                // 投入物料明细
                Map<Integer, List<MonthPlanDetail>> materialMap = monthPlanDetailList.stream().filter(it -> it.getType().equals("0")).collect(Collectors.groupingBy(MonthPlanDetail::getMaterialId));
                for (List<MonthPlanDetail> value : materialMap.values()) {
                    MonthPlanDetail monthPlanDetail = value.get(0);
                    ProductionPlanCompletionRateVO.MaterialDetail materialDetail = new ProductionPlanCompletionRateVO.MaterialDetail();
                    materialDetail.setMaterialId(monthPlanDetail.getMaterialId());
                    materialDetail.setMaterialCode(monthPlanDetail.getMaterialCode());
                    materialDetail.setMaterialName(monthPlanDetail.getMaterialName());
                    materialDetail.setActualFeeding(value.stream().filter(it -> collect.stream().anyMatch(ii -> ii.getId().equals(it.getMonthPlanId()))).map(MonthPlanDetail::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add));
                    materialDetail.setAllYearFeeding(value.stream().map(MonthPlanDetail::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add));

                    materialDetailList.add(materialDetail);
                }

                centerDetail.setMaterialsDetail(materialDetailList);

                detailList.add(centerDetail);
            }
            productionPlanCompletionRateVO.setDetail(detailList);
            plantCompletionRateList.add(productionPlanCompletionRateVO);
        }

        return plantCompletionRateList;
    }

    /**
     * 产量完成情况
     *
     * @param req
     * @return
     */
    @Override
    public List<ProductionCompletionVO> getProductionCompletion(ProductionCompletionReq req) {
        // 查询年月计划
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .in(MonthPlan::getCenterId, req.getCenterIdList())
                .ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode())
                .eq(MonthPlan::getYear, req.getYear())
                .eq(MonthPlan::getMonth, req.getEndMonth());
        List<MonthPlan> allMonthPlanList = baseMapper.selectList(wrapper);

        Map<Integer, List<MonthPlan>> collect = allMonthPlanList.stream().collect(Collectors.groupingBy(MonthPlan::getCenterId));

        List<ProductionCompletionVO> completionList = new ArrayList<>();
        for (List<MonthPlan> value : collect.values()) {
            MonthPlan monthPlan = value.get(0);

            BigDecimal actualWinOutput = value.stream().map(MonthPlan::getActualWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal planWinOutput = value.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);

            ProductionCompletionVO productionCompletionVO = new ProductionCompletionVO();
            productionCompletionVO.setCenterId(monthPlan.getCenterId());
            productionCompletionVO.setCenterCode(monthPlan.getCenterCode());
            productionCompletionVO.setCenterName(monthPlan.getCenterName());
            productionCompletionVO.setWinOutput(planWinOutput);
            productionCompletionVO.setActualWinOutput(actualWinOutput);
            productionCompletionVO.setCompleteRate(actualWinOutput.divide(planWinOutput, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            completionList.add(productionCompletionVO);
        }

        return completionList;
    }

    /**
     * 产量完成情况范围
     *
     * @param req
     * @return
     */
    @Override
    public List<ProductionCompletionRangeVO> getProductionCompletionRange(ProductionCompletionReq req) {
        // 查询1-X年月计划
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .in(MonthPlan::getCenterId, req.getCenterIdList())
                .ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode())
                .eq(MonthPlan::getYear, req.getYear())
                .le(MonthPlan::getMonth, req.getEndMonth());
        Map<Integer, List<MonthPlan>> thisYearPlanMap = baseMapper.selectList(wrapper).stream().collect(Collectors.groupingBy(MonthPlan::getCenterId));

        // 查询去年1-X年月计划
        LambdaQueryWrapper<MonthPlan> lastWrapper = new LambdaQueryWrapper<MonthPlan>()
                .in(MonthPlan::getCenterId, req.getCenterIdList())
                .ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode())
                .eq(MonthPlan::getYear, req.getYear() - 1)
                .le(MonthPlan::getMonth, req.getEndMonth());
        Map<Integer, List<MonthPlan>> lastYearPlanMap = baseMapper.selectList(lastWrapper).stream().collect(Collectors.groupingBy(MonthPlan::getCenterId));

        List<ProductionCompletionRangeVO> result = new ArrayList<>();

        for (Map.Entry<Integer, List<MonthPlan>> entry : thisYearPlanMap.entrySet()) {
            Integer centerId = entry.getKey();
            List<MonthPlan> thisYearList = entry.getValue();
            MonthPlan monthPlan = thisYearList.get(0);

            BigDecimal thisActualWinOutput = thisYearList.stream().map(MonthPlan::getActualWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal thisPlanWinOutput = thisYearList.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<MonthPlan> lastYearList = lastYearPlanMap.get(centerId);
            BigDecimal lastActualWinOutput = lastYearList.stream().map(MonthPlan::getActualWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal lastPlanWinOutput = lastYearList.stream().map(MonthPlan::getWinOutput).reduce(BigDecimal.ZERO, BigDecimal::add);

            ProductionCompletionRangeVO productionCompletionRangeVO = new ProductionCompletionRangeVO();
            productionCompletionRangeVO.setCenterId(monthPlan.getCenterId());
            productionCompletionRangeVO.setCenterCode(monthPlan.getCenterCode());
            productionCompletionRangeVO.setCenterName(monthPlan.getCenterName());
            productionCompletionRangeVO.setLastActualWinOutput(lastActualWinOutput);
            productionCompletionRangeVO.setActualWinOutput(thisActualWinOutput);

            // 产量同比 （本期 - 同期）/ 同期 * 100
            BigDecimal actualWinOutputYearOnYear = thisActualWinOutput.subtract(lastActualWinOutput)
                    .divide(lastActualWinOutput, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            productionCompletionRangeVO.setActualWinOutputYearOnYear(actualWinOutputYearOnYear);

            productionCompletionRangeVO.setLastWinOutput(lastPlanWinOutput);
            productionCompletionRangeVO.setWinOutput(thisPlanWinOutput);

            // 今年完成率
            BigDecimal thisCompleteRate = thisActualWinOutput.divide(thisPlanWinOutput, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            productionCompletionRangeVO.setThisCompleteRate(thisCompleteRate);
            // 去年完成率
            BigDecimal lastCompleteRate = lastActualWinOutput.divide(lastPlanWinOutput, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            productionCompletionRangeVO.setLastCompleteRate(lastCompleteRate);

            // 产量同比 （本期 - 同期）/ 同期 * 100
            BigDecimal completeRateYearOnYear = thisCompleteRate.subtract(lastCompleteRate)
                    .divide(lastCompleteRate, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            productionCompletionRangeVO.setCompleteRateYearOnYear(completeRateYearOnYear);

            result.add(productionCompletionRangeVO);
        }

        return result;
    }

    @Override
    public List<Integer> getHistoryScheduling(Integer monthPlanId) {
        return monthPlanMapper.getHistoryScheduling(monthPlanId).stream()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public SchedulingFirstStepVo firstStep(Integer id) {

        MonthPlan monthPlan = monthPlanMapper.selectById(id);
        Integer scheduleId = monthPlan.getSchedulingId();
        Integer centerId = monthPlan.getCenterId();
        Integer locationId = monthPlan.getLocationId();

        //糟源类型月差异量
        SapSyncConfigChildSaveDTO sapSyncConfig = schedulingMapper.findSapSyncConfig(monthPlan.getYear(),monthPlan.getMonth());
        List<SchedulingVinasseVO> schedulingVinasseVOList = new ArrayList<SchedulingVinasseVO>();
        Map<String, List<String>> vinasseMap = new LinkedHashMap<>();
        if(StringUtil.isNotEmpty(sapSyncConfig) && StringUtil.isNotEmpty(sapSyncConfig.getStartTime()) && StringUtil.isNotEmpty(sapSyncConfig.getEndTime())){
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String monthBeginDate = formatter.format(sapSyncConfig.getStartTime());
            String monthEndDate = formatter.format(sapSyncConfig.getEndTime());
            //排次
            List<SchedulingArrangeByRowVO>  list = schedulingArrangeService.getByScheduleId(scheduleId);
            list.forEach(item->{
                List<SchedulingArrangeVO> schedulingArrangeVOList = item.getSchedulingArrangeVOList().stream().filter(f -> f.getBeginTime().compareTo(LocalDate.parse(monthBeginDate)) >= 0
                        && f.getEndTime().compareTo(LocalDate.parse(monthEndDate)) <= 0).collect(Collectors.toList());
                //排程
                schedulingArrangeVOList.forEach(it->{
                    //排程糟源
                    schedulingVinasseVOList.addAll(it.getSchedulingVinasseVOList());
                });
            });

            if(schedulingVinasseVOList.size()>0){
                Map<String, BigDecimal> sumByGroup = schedulingVinasseVOList.stream()
                        .collect(Collectors.toMap(
                                SchedulingVinasseVO::getVinasseCode,
                                SchedulingVinasseVO::getVinasseData,
                                BigDecimal::add, // 使用add方法来合并相同的key的BigDecimal值
                                LinkedHashMap::new // 使用TreeMap来保持key的自然顺序
                        ));
                log.error("=========sumByGroup========{}",sumByGroup.toString());

                sumByGroup.forEach((key, value) ->{

                    Map<String, Object> locationMap = monthPlanMapper.getLocationById(locationId).get(0);
                    String locationCode = locationMap.get("code").toString();
                    String location = locationCode.substring(0, locationCode.length() - 2) + locationCode.substring(locationCode.length() - 1);

                    Map<String, Object> centerMap = monthPlanMapper.getLocationById(centerId).get(0);
                    String center = centerMap.get("code").toString();

                    List<WorkshopPitOrderPotTaskQueryVO> taskList = monthPlanMapper.workshopPitOrderPotTaskQuery(monthBeginDate,monthEndDate,key,center,location);

                    String completeNumber = String.valueOf(taskList.size());
                    String planNumber = String.valueOf(value.intValue());
                    int differenceNumber = value.intValue() - taskList.size();
                        List<String> str = new ArrayList<>();
                        str.add(planNumber);
                        str.add(completeNumber);
                        str.add(String.valueOf(differenceNumber));
                        vinasseMap.put(key,str);
                    });
            }


        }

        //获取排程第一步数据
        Scheduling scheduling = schedulingService.getById(scheduleId);

        LambdaQueryWrapper<SchedulingLocation> locationWrapper = new LambdaQueryWrapper<SchedulingLocation>()
                .eq(SchedulingLocation::getSchedulingId, scheduleId);
        List<SchedulingLocation> schedulingLocationList = schedulingLocationService.list(locationWrapper);

        LambdaQueryWrapper<SchedulingRow> rowWrapper = new LambdaQueryWrapper<SchedulingRow>()
                .eq(SchedulingRow::getSchedulingId, scheduleId);
        List<SchedulingRow> schedulingRowList = schedulingRowService.list(rowWrapper);

        SchedulingFirstStepVo schedulingFirstStepVo = new SchedulingFirstStepVo();
        schedulingFirstStepVo.setSchedulingVO(baseWrapper.convert(scheduling, SchedulingVO.class));
        schedulingFirstStepVo.setSchedulingLocationList(baseWrapper.convertToList(schedulingLocationList, SchedulingLocationVO.class));
        schedulingFirstStepVo.setSchedulingRowList(baseWrapper.convertToList(schedulingRowList, SchedulingRowVO.class));
        schedulingFirstStepVo.setVinasseMap(vinasseMap);
        //获取算法
        Object obj = redisTemplate.opsForValue().get("monthScheduleId-"+scheduleId);
        if(StringUtil.isNotEmpty(obj)){
            List<SchedulingArrangeReq> list = (List<SchedulingArrangeReq>) (obj);
            schedulingFirstStepVo.setSchedulingArrangeReqs(list);
        }
        return schedulingFirstStepVo;
    }

    @Override
    public MonthPlanMaterialTotalVO queryMaterialList(Integer schedulingId, Integer year, Integer month, Integer centerId, Integer locationId) {
        Scheduling scheduling = schedulingService.getById(schedulingId);

        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .eq(MonthPlan::getPlanTime, scheduling.getPlanTime())
                .eq(MonthPlan::getCenterId, centerId)
                .eq(Objects.nonNull(locationId), MonthPlan::getLocationId, locationId)
                .eq(MonthPlan::getYear, year)
                .eq(MonthPlan::getMonth, month)
                .in(MonthPlan::getPlanState, Lists.newArrayList(PlanStateEnum.NEW.getCode(), PlanStateEnum.IMPLEMENT.getCode(), PlanStateEnum.FINISH.getCode()));

        List<MonthPlan> list = monthPlanMapper.selectList(wrapper);
        List<Integer> monthPlanIdList = list.stream().map(MonthPlan::getId).collect(Collectors.toList());

        List<MonthPlanDetail> monthPlanDetailList = monthPlanDetailService.list(new LambdaQueryWrapper<MonthPlanDetail>()
                .in(MonthPlanDetail::getMonthPlanId, monthPlanIdList));

        List<MonthPlanVinasse> monthPlanVinasseList = monthPlanVinasseService.list(new LambdaQueryWrapper<MonthPlanVinasse>()
                .in(MonthPlanVinasse::getMonthPlanId, monthPlanIdList));

        MonthPlanMaterialTotalVO monthPlanMaterialTotalVO = new MonthPlanMaterialTotalVO();

        // 自然月
        List<MonthPlanMaterialVO> monthPlanMaterialList = baseWrapper.convertToList(list, MonthPlanMaterialVO.class);
        monthPlanMaterialList.forEach(it -> {
            List<MonthPlanDetail> collect = monthPlanDetailList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "0"))
                    .peek(ii -> ii.setTotalQuality(ii.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
                    .sorted(Comparator.comparing(MonthPlanDetail::getType))
                    .collect(Collectors.toList());
            it.setMonthPlanDetailVOS(baseWrapper.convertToList(collect, MonthPlanDetailVO.class));

            List<MonthPlanVinasse> collect1 = monthPlanVinasseList.stream()
                    .filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "0"))
                    .peek(ii -> ii.setVinasseData(ii.getVinasseData().setScale(0, RoundingMode.HALF_UP)))
                    .collect(Collectors.toList());
            it.setMonthPlanVinasseVOS(baseWrapper.convertToList(collect1, MonthPlanVinasseVO.class));
        });

        // 扎账期
        List<MonthPlanMaterialVO> billMaterialList = baseWrapper.convertToList(list, MonthPlanMaterialVO.class);
        billMaterialList.forEach(it -> {
            List<MonthPlanDetail> collect = monthPlanDetailList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "1"))
                    .peek(ii -> ii.setTotalQuality(ii.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
                    .sorted(Comparator.comparing(MonthPlanDetail::getType))
                    .collect(Collectors.toList());
            it.setMonthPlanDetailVOS(baseWrapper.convertToList(collect, MonthPlanDetailVO.class));

            List<MonthPlanVinasse> collect1 = monthPlanVinasseList.stream()
                    .filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "1"))
                    .peek(ii -> ii.setVinasseData(ii.getVinasseData().setScale(0, RoundingMode.HALF_UP)))
                    .collect(Collectors.toList());
            it.setMonthPlanVinasseVOS(baseWrapper.convertToList(collect1, MonthPlanVinasseVO.class));
        });
        monthPlanMaterialTotalVO.setMonthPlanMaterialList(monthPlanMaterialList);
        monthPlanMaterialTotalVO.setBillMaterialList(billMaterialList);

        return monthPlanMaterialTotalVO;
    }

    @Override
    public List<MaterialRequirementVO> getMaterialRequirement(MaterialRequirementQueryDTO queryDTO) {
        return monthPlanMapper.selectMaterialRequirement(queryDTO);
    }

    @Override
    public void updateStatus(MonthPlanUpdateStatusReq req) {
        log.info("调用变更计划状态，传入参数-------》{}", JSONObject.toJSONString(req));
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .eq(MonthPlan::getDeleted,0)
                .eq(MonthPlan::getCode,req.getPlanNo());
        MonthPlan mp = monthPlanMapper.selectOne(wrapper);
        if(mp != null){
            mp.setPlanState(req.getStatus());
            monthPlanMapper.updateById(mp);
        }else{
            throw new BaseKnownException(500, "未查询到此计划");
        }
    }

    @Override
    public void baseLiquorProduction(BaseLiquorProductionReq req) {
        log.info("调用基酒产出反馈，传入参数-------》{}", JSONObject.toJSONString(req));
    }

    @Override
    public void productionMaterialInput(ProductionMaterialInputReq req) {
        log.info("调用生产物料投入反馈，传入参数-------》{}", JSONObject.toJSONString(req));
    }

    public void getMaterialList(LocalDate beginDate, LocalDate endDate, List<PurchasePlanMaterialDTO> planMaterialDTOS) {

        // 获取当前汇算月的最后一天
        LocalDate monthLastDay = beginDate.with(TemporalAdjusters.lastDayOfMonth());

        // 当前月的天数
        int monthDayNum = beginDate.lengthOfMonth();
        if (monthLastDay.isBefore(endDate)) {
            // 说明还有下一个月
            int days = Period.between(beginDate, monthLastDay).getDays() + 1;
            // 组装数据
            addPlanMaterialDTOS(planMaterialDTOS, monthDayNum, days, monthLastDay.getYear(), monthLastDay.getMonthValue());

            getMaterialList(monthLastDay.plusDays(1), endDate, planMaterialDTOS);
        } else {
            int days = Period.between(beginDate, endDate).getDays() + 1;
            // 组装数据
            addPlanMaterialDTOS(planMaterialDTOS, monthDayNum, days, monthLastDay.getYear(), monthLastDay.getMonthValue());
        }
    }

    private void addPlanMaterialDTOS(List<PurchasePlanMaterialDTO> planMaterialDTOS, int monthDayNum, int days, int year, int month) {
        Map<Integer, PurchasePlanMaterialDTO> materialDTOMap = planMaterialDTOS.stream().collect(Collectors.toMap(PurchasePlanMaterialDTO::getMaterialId, it -> it, (a, b) -> b));

        // 获取月计划
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>().eq(MonthPlan::getYear, year).eq(MonthPlan::getMonth, month).ne(MonthPlan::getPlanState, PlanStateEnum.FILE.getCode());
        List<MonthPlan> monthPlans = baseMapper.selectList(wrapper);
        Set<Integer> monthPlanIdSet = monthPlans.stream().map(MonthPlan::getId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(monthPlanIdSet)) {
            return;
        }
        // 获取月计划详情
        LambdaQueryWrapper<MonthPlanDetail> queryWrapper = new LambdaQueryWrapper<MonthPlanDetail>().in(MonthPlanDetail::getMonthPlanId, monthPlanIdSet).eq(MonthPlanDetail::getType, "0").eq(MonthPlanDetail::getBillType, "0");
        List<MonthPlanDetail> monthPlanDetails = monthPlanDetailMapper.selectList(queryWrapper);

        // 数据汇总
        Map<Integer, List<MonthPlanDetail>> collect = monthPlanDetails.stream().collect(Collectors.groupingBy(MonthPlanDetail::getMaterialId));
        for (Map.Entry<Integer, List<MonthPlanDetail>> integerListEntry : collect.entrySet()) {
            Integer materialId = integerListEntry.getKey();
            List<MonthPlanDetail> value = integerListEntry.getValue();
            BigDecimal reduce = value.stream().map(MonthPlanDetail::getTotalQuality).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 按自然日比例计算
            BigDecimal dayTotalNum = reduce.multiply(BigDecimal.valueOf(days)).divide(BigDecimal.valueOf(monthDayNum), 6, BigDecimal.ROUND_HALF_UP);

            PurchasePlanMaterialDTO purchasePlanMaterialDTO = materialDTOMap.get(materialId);
            if (Objects.nonNull(purchasePlanMaterialDTO)) {
                purchasePlanMaterialDTO.setTotalQuality(purchasePlanMaterialDTO.getTotalQuality().add(dayTotalNum));
            } else {
                PurchasePlanMaterialDTO convert = baseWrapper.convert(value.get(0), PurchasePlanMaterialDTO.class);
                convert.setTotalQuality(dayTotalNum);

                planMaterialDTOS.add(convert);
            }
        }
    }

    /**
     * 生成月度计划编码
     *
     * @param year         年
     * @param month        月
     * @param centerCode   中心code
     * @param locationCode 车间code
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/31 15:29
     */
    public String generatePlanCode(int year, int month, String centerCode, String locationCode) {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyyMM");
        String dateString = LocalDate.of(year, month, 1).format(formatters);

        String cacheKey = "PP" + dateString + centerCode + locationCode;
        Boolean absent = stringRedisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (Objects.nonNull(absent) && absent) {
            return cacheKey + "01";
        }
        //创建失败  自增
        Long afterIncrease = stringRedisTemplate.opsForValue().increment(cacheKey, 1);
        if (Objects.nonNull(afterIncrease) && CommonConsts.TENANT_ID_99L < afterIncrease) {
            throw new BaseKnownException(325005, "当前数值已超出99限制");
        }
        return cacheKey + String.format("%02d", afterIncrease);
    }
}
