package com.hvisions.plan.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.plan.dao.*;
import com.hvisions.plan.dto.PmDetailDto;
import com.hvisions.plan.dto.SchedulingArrangeReq;
import com.hvisions.plan.dto.SchedulingVinasseReq;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.service.*;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.plan.utils.DateUtil;
import com.hvisions.plan.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 排程安排表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
@Slf4j
public class SchedulingArrangeServiceImpl
        extends ServiceImpl<SchedulingArrangeMapper, SchedulingArrange> implements ISchedulingArrangeService {
    @Autowired
    ISchedulingVinasseService schedulingVinasseService;
    @Autowired
    ISchedulingStatsService schedulingStatsService;
    @Autowired
    SchedulingArrangeMapper schedulingArrangeMapper;
    @Autowired
    SchedulingRowMapper schedulingRowMapper;
    @Autowired
    RowMapper rowMapper;
    @Autowired
    SchedulingMapper schedulingMapper;
    @Autowired
    IFormulaService formulaService;
    @Autowired
    IFormulaDetailService formulaDetailService;
    @Autowired
    IPmDetailService pmDetailService;
    @Autowired
    VinasseSourceMapper vinasseSourceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleArrange(List<SchedulingArrangeReq> schedulingArrangeReqs) {
        Integer scheduleId = schedulingArrangeReqs.get(0).getScheduleId();
        Scheduling scheduling = Optional.ofNullable(schedulingMapper.selectById(scheduleId))
                .orElseThrow(() -> new BaseKnownException(82201, "未查到相关排程信息"));
        //查询生产模式对应的糟源信息
        List<Integer> modeIds = schedulingArrangeReqs.stream()
                .map(SchedulingArrangeReq::getModeId)
                .distinct()
                .collect(Collectors.toList());
        List<PmDetailDto> pmDetailList = pmDetailService.getListByModeIdList(modeIds);
        Map<Integer, List<String>> modeIdDetailMap = pmDetailList.stream().collect(Collectors.groupingBy(PmDetailDto::getProductionModeId,
                Collectors.mapping(PmDetailDto::getSourceCode, Collectors.toList())));
        //第一步提交的糟源列表
//        List<String> vinasseCodesDB = Optional.ofNullable(scheduling.getZenIds())
//                .map(it -> Arrays.asList(it.split(",")))
//                .map(it -> vinasseSourceMapper.selectBatchIds(it).stream().map(VinasseSource::getCode).collect(Collectors.toList()))
//                .orElseThrow(() -> new BaseKnownException(825001, "排程第一步未配置糟源列表"));
        schedulingArrangeReqs.forEach(it -> {
            //非假期不允许跨月
            LocalDate beginTime = it.getBeginTime();
            LocalDate endTime = it.getEndTime();
            BigDecimal productionDays = Optional.ofNullable(it.getProductionDays())
                    .orElse(BigDecimal.ZERO);
            if (0 > BigDecimal.ZERO.compareTo(productionDays)
                    && (beginTime.getYear() != endTime.getYear()
                    || beginTime.getMonth() != endTime.getMonth())) {
                throw new BaseKnownException(420001, "非节假日不允许跨年跨月");
            }


            //判断生产模式对应的糟源代码是否在第一步提交的糟源列表中
//            if (1 < scheduling.getCenterIds().split(",").length) {
//                List<String> vinasseCodes = Optional.ofNullable(modeIdDetailMap.get(it.getModeId()))
//                        .orElseThrow(() -> new BaseKnownException(822003, "未查到相关模式"));
//                boolean allMatch = vinasseCodes.stream()
//                        .allMatch(z -> vinasseCodesDB.contains(z));
//                Assert.isTrue(allMatch, "当前生产模式的糟源不含在第一步提交的糟源列表中，请从第一步重新开始");
//            }

            //行记录   如果开始时间小于23号  结束时间不能大于23号
            LocalDate splitDate = LocalDate.of(beginTime.getYear(), beginTime.getMonthValue(), 23);
            if (0 > BigDecimal.ZERO.compareTo(productionDays)
                    && !beginTime.isAfter(splitDate) && endTime.isAfter(splitDate)) {
                //throw new BaseKnownException(818001,MessageFormat.format("{0}年{1}月存在跨扎帐期的情况，请修正之后再提交", String.valueOf(beginTime.getYear()), String.valueOf(beginTime.getMonth().getValue())));
            }

            if (CollectionUtils.isEmpty(it.getSchedulingVinasseReqList())) {
                return;
            }
            //校验   生产模式中的糟源是生效日期是否包含排次时间
            List<String> sourceCodes = it.getSchedulingVinasseReqList().stream()
                    .filter(v -> Objects.nonNull(v.getVinasseData()) && 0 < v.getVinasseData().compareTo(BigDecimal.ZERO))
                    .map(SchedulingVinasseReq::getVinasseCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sourceCodes)) {
                return;
            }
            this.checkSourceCodeValid(beginTime, endTime, sourceCodes,scheduling);
        });
        schedulingArrangeReqs.forEach(schedulingArrangeReq -> {
            Integer arrangeId = handleArrangeSubmit(schedulingArrangeReq);
            if (CollectionUtils.isEmpty(schedulingArrangeReq.getSchedulingVinasseReqList())) {
                return;
            }
            List<SchedulingVinasseReq> schedulingVinasseReqs = schedulingArrangeReq.getSchedulingVinasseReqList().stream()
                    .peek(it -> it.setArrangeId(arrangeId))
                    .collect(Collectors.toList());
            schedulingVinasseService.handleVinasse(schedulingVinasseReqs);
        });
        //处理排程的结束时间  取生产天数大于0且最大的endTime
        LocalDate endTime = schedulingArrangeReqs.stream()
                .filter(it -> Objects.nonNull(it.getProductionDays()))
                .filter(it -> 0 > BigDecimal.ZERO.compareTo(it.getProductionDays()))
                .max(Comparator.comparing(SchedulingArrangeReq::getEndTime))
                .map(SchedulingArrangeReq::getEndTime)
                .orElseThrow(() -> new BaseKnownException(401001, "未匹配到最大结束时间"));
        schedulingMapper.updateEndTimeById(scheduleId, endTime);
        this.schedulingStatsService.handleNewStats(schedulingArrangeReqs);
    }

    private void checkSourceCodeValid(LocalDate beginTime, LocalDate endTime, List<String> sourceCodes,Scheduling scheduling) {
        LambdaQueryWrapper<Formula> formulaLambdaQueryWrapper = new LambdaQueryWrapper<Formula>()
                .eq(Formula::getDeleted,0)
                .eq(Formula::getState, StateEnum.TAKE_EFFECT.getCode())
                .eq(Formula::getProductionBaseId,Integer.valueOf(scheduling.getProductionBaseIds()))
                .in(Formula::getSourceCode, sourceCodes);
        List<Formula> formulaList = formulaService.list(formulaLambdaQueryWrapper);
        Map<String, List<Formula>> formulaMap = formulaList.stream()
                .collect(Collectors.groupingBy(Formula::getSourceCode));
        for (String ss : sourceCodes) {
            if ("KDZ".equals(ss) || "KHZ".equals(ss)) {
                continue;
            }
            List<Formula> formulas = formulaMap.get(ss);
            if (CollectionUtils.isEmpty(formulas)) {
                throw new BaseKnownException(705001, MessageFormat.format("{0}糟源没有生效的", ss));
            }
            List<Integer> formulaIds = formulas.stream().map(Formula::getId).collect(Collectors.toList());
            LambdaQueryWrapper<FormulaDetail> formulaDetailQueryWrapper = new LambdaQueryWrapper<FormulaDetail>()
                    .eq(FormulaDetail::getDeleted,0)
                    .in(FormulaDetail::getFormulaId, formulaIds);
            List<FormulaDetail> formulaDetailList = formulaDetailService.list(formulaDetailQueryWrapper);
            if (CollectionUtils.isEmpty(formulaDetailList)) {
                throw new BaseKnownException(705001, MessageFormat.format("{0}糟源没有生效的物料明细", ss));
            }
            LocalDate rollTime = beginTime;
            do {
                final LocalDate compareTime = rollTime;
                log.info("滚动日期{},配方{}", DateUtil.formatLocaldate(rollTime, DateUtil.LD_PATTERN), JSONUtil.toJsonStr(formulas));
                boolean fitDate = formulaDetailList.stream()
                        .anyMatch(f -> {
                            LocalDate startLD = DateUtil.parse2LocalDate(beginTime.getYear() + "-" + f.getBeginTime(), DateUtil.LD_PATTERN);
                            LocalDate endLD = DateUtil.parse2LocalDate(endTime.getYear() + "-" + f.getEndTime(), DateUtil.LD_PATTERN);
                            return !compareTime.isBefore(startLD) && !compareTime.isAfter(endLD);

                        });
                Assert.isTrue(fitDate, MessageFormat.format("{0}至{1}时间内，{2}糟源没有生效配方物料明细，请配置",
                        DateUtil.formatLocaldate(beginTime, DateUtil.LD_PATTERN),
                        DateUtil.formatLocaldate(endTime, DateUtil.LD_PATTERN),
                        ss));
                rollTime = rollTime.plusDays(1);
            } while (!rollTime.isAfter(endTime));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOldArrange(List<Integer> rowIds) {
        //查询糟源关联主键
        List<Integer> rangeIdByRowIds = schedulingArrangeMapper.getRangeIdByRowIds(rowIds);
        if (CollectionUtils.isEmpty(rangeIdByRowIds)) {
            return;
        }
        //删除当前排程的行数据  第二步
        schedulingArrangeMapper.deleteBatchIds(rangeIdByRowIds);
        //关联数据  糟源  第二步
        schedulingVinasseService.deleteOldVinasse(rangeIdByRowIds);
    }

    @Override
    public List<SchedulingArrangeByRowVO> getByScheduleId(Integer scheduleId) {
        //查询排次安排信息
        List<ScheduleArrangeWithRow> scheduleArrangeWithRows = schedulingArrangeMapper.listArrangeBySchedule(scheduleId);
        if (CollectionUtils.isEmpty(scheduleArrangeWithRows)) {
            return Collections.emptyList();
        }
        Map<Integer, List<ScheduleArrangeWithRow>> groupByRow = scheduleArrangeWithRows
                .stream()
                .collect(Collectors.groupingBy(ScheduleArrangeWithRow::getSchedulingRowId));
        //查询排次名称
        List<Integer> rowIds = scheduleArrangeWithRows.stream().map(ScheduleArrangeWithRow::getRowId).collect(Collectors.toList());
        Map<Integer, Row> rowMap = rowMapper.selectBatchIds(rowIds).stream()
                .collect(Collectors.toMap(Row::getId, v -> v, (a, b) -> b));
        return groupByRow.entrySet().stream()
                .map(entry -> {
                    Integer rowId = entry.getValue().get(0).getRowId();
                    List<SchedulingArrangeVO> arrangeVOList = entry.getValue().stream()
                            .map(arrange -> {
                                SchedulingArrangeVO schedulingArrangeVO = CopyUtil.simpleCopy(arrange, SchedulingArrangeVO.class);
                                LambdaQueryWrapper<SchedulingVinasse> vinasseLambdaQueryWrapper = new LambdaQueryWrapper<SchedulingVinasse>()
                                        .eq(SchedulingVinasse::getArrangeId, arrange.getId());
                                List<SchedulingVinasseVO> schedulingVinasseVOList = schedulingVinasseService.list(vinasseLambdaQueryWrapper).stream()
                                        .map(vinasse -> CopyUtil.simpleCopy(vinasse, SchedulingVinasseVO.class))
                                        .collect(Collectors.toList());
                                schedulingArrangeVO.setSchedulingVinasseVOList(schedulingVinasseVOList);
                                return schedulingArrangeVO;
                            }).collect(Collectors.toList());
                    Row row = Optional.ofNullable(rowMap.get(rowId))
                            .orElseThrow(() -> new BaseKnownException(407008, "未匹配到相关排次信息"));
                    return new SchedulingArrangeByRowVO()
                            .setSchedulingRowId(entry.getKey())
                            .setRowName(row.getName())
                            .setRowId(rowId)
                            .setCheckState(row.getCheckState())
                            .setSchedulingArrangeVOList(arrangeVOList);
                })
                .sorted(Comparator.comparing(SchedulingArrangeByRowVO::getSchedulingRowId))
                .collect(Collectors.toList());
    }

    @Override
    public Integer getRowIdByScheduleAndDate(Integer scheduleId, LocalDate ld) {
        return schedulingArrangeMapper.getRowIdByScheduleAndDate(scheduleId, ld);
    }

    @Override
    public List<Integer> listRowInfoByMonthPlan(MonthPlan monthPlan) {
        //根据排程拿取arrange信息  根据时间段匹配   获取排次行ID
        return schedulingArrangeMapper.listArrangeRowByScheduleId(monthPlan.getSchedulingId()).stream()
//                .filter(it -> !it.getBeginTime().isAfter(monthPlan.getMonthBeginDate()) && !it.getEndTime().isBefore(monthPlan.getMonthEndDate()))
                .map(ScheduleArrangeTimeVO::getRowId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> newListRowInfoByMonthPlan(MonthPlan monthPlan) {
        //根据排程拿取arrange信息  根据时间段匹配   获取排次行ID
        return schedulingArrangeMapper.listArrangeRowByScheduleId(monthPlan.getSchedulingId()).stream()
                .filter(it -> !(it.getEndTime().isBefore(monthPlan.getMonthBeginDate()) || it.getBeginTime().isAfter(monthPlan.getMonthEndDate())))
                .map(ScheduleArrangeTimeVO::getRowId)
                .collect(Collectors.toList());
    }

    private Integer handleArrangeSubmit(SchedulingArrangeReq schedulingArrangeReq) {
        //查询排次行ID
        Integer scheduleRowId = schedulingRowMapper.getRowIdBySchedulingIdAndRowId(schedulingArrangeReq.getScheduleId(), schedulingArrangeReq.getRowId());
        Assert.notNull(scheduleRowId, "未查到相关排次行ID");
        SchedulingArrange entity = CopyUtil.simpleCopy(schedulingArrangeReq, SchedulingArrange.class);
        entity.setSchedulingRowId(scheduleRowId);
        this.save(entity);
        return entity.getId();
    }
}
