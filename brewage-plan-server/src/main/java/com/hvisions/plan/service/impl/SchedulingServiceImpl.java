package com.hvisions.plan.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.brewage.feign.MkwineClient;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsDTO;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsVO;
import com.hvisions.brewage.mkwine.dto.QueryWineQualityDTO;
import com.hvisions.brewage.mkwine.vo.GetWineQualityVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.StaticsVO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.client.LocationExtendClient;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.plan.constant.SchedulingArrangeConstant;
import com.hvisions.plan.dao.*;
import com.hvisions.plan.dto.*;
import com.hvisions.plan.dto.plan.vo.DqMaterialDetailVO;
import com.hvisions.plan.dto.plan.vo.MaterialRequireReq;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.AuditStateEnum;
import com.hvisions.plan.enums.PlanAuditStateEnum;
import com.hvisions.plan.enums.PlanStateEnum;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.excel.SchedulingImport;
import com.hvisions.plan.operlog.utils.StringUtils;
import com.hvisions.plan.sap.dto.SapBaseListDto;
import com.hvisions.plan.sap.dto.SapBaseResponseDto;
import com.hvisions.plan.sap.dto.ScheduleSyncDto;
import com.hvisions.plan.sap.dto.ScheduleSyncItemDto;
import com.hvisions.plan.sap.service.SapService;
import com.hvisions.plan.service.*;
import com.hvisions.plan.utils.*;
import com.hvisions.plan.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <p>
 * 排程计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
@Slf4j
public class SchedulingServiceImpl extends ServiceImpl<SchedulingMapper, Scheduling> implements ISchedulingService {
    @Autowired
    SchedulingMapper schedulingMapper;
    @Autowired
    SchedulingLocationMapper schedulingLocationMapper;
    @Autowired
    ZengkouCalculationMapper zengkouCalculationMapper;
    @Autowired
    LocationExtendClient locationExtendClient;
    @Autowired
    AuditMapper auditMapper;
    @Resource
    MkwineClient mkwineClient;
    @Autowired
    ISchedulingRowService schedulingRowService;
    @Autowired
    ISchedulingLocationService schedulingLocationService;
    @Autowired
    ISchedulingStatsService schedulingStatsService;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    IAuditService auditService;
    @Autowired
    ISchedulingArrangeService schedulingArrangeService;
    @Autowired
    SchedulingRowMapper schedulingRowMapper;
    @Autowired
    IFormulaService formulaService;
    @Autowired
    VinasseSourceMapper vinasseSourceMapper;
    @Autowired
    FormulaMapper formulaMapper;

    @Resource
    private SapService sapService;
    @Resource
    private GwpServiceStrategyImpl gwpServiceStrategyImpl;
    @Resource
    private DqProductServiceStrategyImpl dqProductServiceStrategyImpl;
    @Autowired
    private RedisTemplate redisTemplate;

    @Resource(name = "scheduling_arrange")
    private BaseExtendService extendService;

    @Override
    public Page pageSchedule(SchedulingQueryReq req, PageInfo pageInfo) {
        Page<SchedulingVO> infoReturn = new Page<>(pageInfo.getPage(), pageInfo.getPageSize());
        List<SchedulingVO> schedulings = schedulingMapper.listScheduling(infoReturn, req);
        infoReturn.setRecords(schedulings);
        if (0 == schedulings.size()) {
            return infoReturn;
        }
        //处理基地  中心  车间
        List<Integer> scheduleIds = schedulings.stream().map(SchedulingVO::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SchedulingLocation> queryWrapper = new LambdaQueryWrapper<SchedulingLocation>()
                .in(SchedulingLocation::getSchedulingId, scheduleIds);
        List<SchedulingLocation> schedulingLocations = schedulingLocationMapper.selectList(queryWrapper);
        if (0 == schedulingLocations.size()) {
            return infoReturn;
        }
        Map<Integer, List<SchedulingLocation>> groupByScheduleId = schedulingLocations.stream()
                .collect(Collectors.groupingBy(SchedulingLocation::getSchedulingId));
        infoReturn.getRecords().forEach(item -> {

            Object obj = redisTemplate.opsForValue().get("ScheduleId-"+item.getId());
            if(StringUtil.isNotEmpty(obj)){
                List<SchedulingArrangeReq> list = (List<SchedulingArrangeReq>) (obj);
                item.setSchedulingArrangeReqs(list);
            }

            //处理基地
            List<SchedulingLocation> byScheduleId = groupByScheduleId.get(item.getId());
            if (Objects.isNull(byScheduleId)) {
                return;
            }
            item.setProductionBaseName(byScheduleId.get(0).getPlantName());
            //处理中心
            String centerNames = byScheduleId.stream()
                    .map(SchedulingLocation::getCenterName)
                    .distinct()
                    .collect(Collectors.joining("/"));
            List<Integer> centerids = byScheduleId.stream()
                    .map(SchedulingLocation::getCenterId)
                    .distinct().collect(Collectors.toList());
            item.setCenterNames(centerNames);
            //处理车间
            Map<Integer, List<SchedulingLocation>> byCenter = byScheduleId.stream().collect(Collectors.groupingBy(SchedulingLocation::getCenterId));
            //如果多个车间  默认展示全部
            if (1 < byCenter.size()) {
                item.setLocationNames("全部车间");
            } else {
                //查询中心的车间信息
                Optional.ofNullable(locationExtendClient.getLocationListByParentId(byScheduleId.get(0).getCenterId()))
                        .filter(ResultVO::isSuccess)
                        .ifPresent(l -> {
                            if (l.getData().size() == byCenter.get(byScheduleId.get(0).getCenterId()).size()) {
                                item.setLocationNames("全部车间");
                            } else {
                                String locationNames = l.getData().stream()
                                        .filter(l1 -> Arrays.stream(item.getLocationIds().split(","))
                                                .anyMatch(ll -> l1.getId().equals(Integer.parseInt(ll))))
                                        .map(LocationDTO::getName)
                                        .collect(Collectors.joining("/"));
                                item.setLocationNames(locationNames);
                            }
                        });
            }
            
            //查询审核状态  审批人 审批时间
            Optional.ofNullable(auditMapper.getAuditByPlanId(item.getId()))
                    .ifPresent(a -> {
                        //二级审核人有审核结果取二级审核人  否则以及审核人
                        if (Objects.nonNull(a.getAuditTwoResult())) {
                            item.setAuditorName(a.getAuditTwoName());
                            item.setAuditTime(a.getAuditTwoTime());
                        } else {
                            item.setAuditorName(a.getAuditOneName());
                            item.setAuditTime(a.getAuditOneTime());
                        }
                    });
            //处理实际产出
            QueryWineQualityDTO queryWineQualityDTO = new QueryWineQualityDTO();
            List<String> stringList = StringUtils.str2List(item.getCenterIds(),",",true,true);
            queryWineQualityDTO.setCenterIds(stringList.stream()
                    .map(Integer::valueOf)
                    .collect(Collectors.toList()));

            queryWineQualityDTO.setStartDate(item.getBeginTime());
            queryWineQualityDTO.setEndDate(item.getEndTime());

            ResultVO<List<GetWineQualityVO>> resultVO = mkwineClient.getWineQualityData(queryWineQualityDTO);
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(),resultVO.getMessage());
            }

            Optional.ofNullable(resultVO.getData())
                    .filter(it -> !it.isEmpty() && 1 == it.size())
                    .ifPresent(it -> item.setActualWinOutPut(BigDecimal.valueOf(it.get(0).getTo60Quantity())));

            final ProductionDetailsDTO dto = new ProductionDetailsDTO();
            List<String> centerIds = StringUtils.str2List(item.getCenterIds(),",",true,true);
            if(centerIds.size()>0){

                BigDecimal actualInputLiang = new BigDecimal(0);
                BigDecimal actualUseLiang = new BigDecimal(0);
                BigDecimal actualOutputWine = new BigDecimal(0);
                BigDecimal actualSpeed = new BigDecimal(0);
                
                centerIds.forEach(item2->{
                    dto.setStartTime(item.getBeginTime());
                    dto.setEndTime(item.getEndTime());
                    dto.setCenterId(Integer.valueOf(item2));
//                dto.setLocationId(monthPlan.getLocationId());
                    ResultVO<ProductionDetailsVO> resultVO1 = mkwineClient.getProductionDetailsByDate(dto);
                    if(resultVO1.getCode()!=200){
                        throw new BaseKnownException(resultVO1.getCode(),resultVO1.getMessage());
                    }
                    final ProductionDetailsVO details =resultVO1.getData();

//                    item.setActualInputLiang(
//                            details.getActualInputLiang().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
//                                    && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
//                    );
                    actualInputLiang.add(details.getActualInputLiang().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
                            && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));

//                    item.setActualUseLiang(
//                            details.getActualUseLiang().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
//                                    && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
//                    );
                    actualUseLiang.add(details.getActualUseLiang().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
                            && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));

//                    item.setActualOutputWine(
//                            details.getActualOutputWine().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
//                                    && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
//                    );
                    actualOutputWine.add(details.getActualOutputWine().stream().filter(f -> f.getActDate().compareTo(item.getBeginTime()) >= 0
                            && f.getActDate().compareTo(item.getEndTime()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));

//                    item.setActualSpeed(item.getActualOutputWine().multiply(BigDecimal.valueOf(100)).divide(item.getWinOutput(), 2, BigDecimal.ROUND_DOWN));
                    //actualSpeed.add(actualOutputWine.multiply(BigDecimal.valueOf(100)).divide(item.getWinOutput(), 2, BigDecimal.ROUND_DOWN));

                });
                item.setActualInputLiang(actualInputLiang);
                item.setActualUseLiang(actualUseLiang);
                item.setActualOutputWine(actualOutputWine);
                if(actualOutputWine.compareTo(BigDecimal.ZERO) > 0){
                    item.setActualSpeed(actualOutputWine.multiply(BigDecimal.valueOf(100)).divide(item.getWinOutput(), 2, BigDecimal.ROUND_DOWN));
                }

            }
            
        });
        return infoReturn;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer handleNewSchedule(SchedulingReq schedulingReq) {

        if("0".equals(schedulingReq.getCenterIds().trim())){
            //全部中心
            List<LocationDTO> list = schedulingMapper.getLocationListByParentId(Integer.valueOf(schedulingReq.getProductionBaseIds()));
            if(list.size()>0){
                list = list.stream().filter(f-> !f.getName().equals("原辅料管理部") && !f.getName().equals("制曲中心") && !f.getName().equals("酒管中心")).collect(Collectors.toList());
                List<String> stringList = list.stream()
                        .map(dto -> String.valueOf(dto.getId()))
                        .collect(Collectors.toList());
                schedulingReq.setCenterIds(String.join(",",stringList));
            }
        }
        Integer scheduleId = this.handleSchedule(schedulingReq);
        schedulingReq.setId(scheduleId);
        //处理生产基地相关信息
        schedulingLocationService.handleNewLocation(scheduleId, schedulingReq.getProductionBaseIds(), schedulingReq.getCenterIds(), schedulingReq.getLocationIds());
        //处理排次信息
        schedulingRowService.handleNewRow(scheduleId, schedulingReq.getSchedulingRowReqList());
        return scheduleId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void validData(Integer id) {
        Scheduling scheduling = baseMapper.selectById(id);
        //“计划时间”+“生产基地”+“中心”+“车间”
        LambdaQueryWrapper<Scheduling> queryWrapper = new LambdaQueryWrapper<Scheduling>()
                .eq(Scheduling::getProductionBaseIds, scheduling.getProductionBaseIds())
                .eq(Scheduling::getCenterIds, scheduling.getCenterIds())
//                .eq(Scheduling::getLocationIds, scheduling.getLocationIds())
                .eq(Scheduling::getPlanTime, scheduling.getPlanTime());
        List<Scheduling> schedulingList = baseMapper.selectList(queryWrapper);
        //处理版本
        Integer version = Optional.of(schedulingList)
                .flatMap(it -> it.stream().filter(c -> Objects.nonNull(c.getVersion())).max(Comparator.comparing(Scheduling::getVersion)))
                .map(Scheduling::getVersion)
                .orElse(0);
        scheduling.setVersion(version + 1);
        scheduling.setPlanState(PlanStateEnum.IMPLEMENT.getCode());
        scheduling.setAuditState(PlanAuditStateEnum.APPROVAL_AGREE.getCode());
        this.updateById(scheduling);
        //处理旧版本问题
        schedulingList.stream().filter(it -> PlanStateEnum.IMPLEMENT.getCode().equals(it.getPlanState())).findFirst()
                .ifPresent(it -> this.invalidData(it.getId()));
        if(scheduling.getType() != 1){
            if(schedulingList.stream().filter(it -> PlanStateEnum.IMPLEMENT.getCode().equals(it.getPlanState())).collect(Collectors.toList()).size()>0){
                //修改
                //下发数据给SAP、8中心MES
                //排程计划
                //10:基酒生产计划(30000132)
                SapBaseResponseDto dto = baseLiquorProductionPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
                //20:等级酒产出计划
                dto = gradeWineProductionPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
                //30:曲粉需求计划
                dto = qufenDemandPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
            }else{
                //新增
                //下发数据给SAP、8中心MES
                //排程计划
                //10:基酒生产计划(30000132)
                SapBaseResponseDto dto = baseLiquorProductionPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
                //20:等级酒产出计划
                dto = gradeWineProductionPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
                //30:曲粉需求计划
                dto = qufenDemandPlanSap(scheduling,2);
                if(dto.getEsMessage().getMsgty().equals("E")){
                    //throw new BaseKnownException(10000, dto.getEsMessage().getMsgtx());
                }
            }
        }
    }

    private SapBaseResponseDto baseLiquorProductionPlanSap(Scheduling scheduling, Integer type){
        //10:基酒生产计划(30000132)
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }
        scheduleSyncDto.setZjhcj("1");
        scheduleSyncDto.setZjhfl("10");
        LocationDTO base = schedulingMapper.getLocationById(Integer.valueOf(scheduling.getProductionBaseIds()));
        scheduleSyncDto.setZjd(base.getName());
        List<String> centerIds = Stream.of(scheduling.getCenterIds().split(",")).collect(Collectors.toList());

        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        centerIds.forEach(centerId->{
            ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
            LocationDTO center = schedulingMapper.getLocationById(Integer.valueOf(centerId));
            scheduleSyncItemDto.setZzx(center.getName());

            scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(scheduling.getBeginTime()));
            scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(scheduling.getEndTime()));
            scheduleSyncItemDto.setZnd(scheduling.getPlanTime());
            scheduleSyncItemDto.setZyllx("30");
            List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();
            MaterialDTO materialDTO = schedulingMapper.findMaterial(2,"基酒");
            SapBaseListDto sapBaseListDto = new SapBaseListDto();
            sapBaseListDto.setMatnr(materialDTO.getMaterialCode());
            BigDecimal locationCount = BigDecimal.valueOf(0);
            if(scheduling.getLocationIds().equals("0")){
                locationCount = BigDecimal.valueOf(schedulingMapper.getLocationListByParentId(Integer.valueOf(centerId)).size());
            }else{
                locationCount = BigDecimal.valueOf(Stream.of(scheduling.getLocationIds().split(",")).collect(Collectors.toList()).size());
            }
            sapBaseListDto.setMenge(scheduling.getWinOutput().multiply(locationCount));
            sapBaseListDto.setMeins(materialDTO.getUomName());
            sapBaseList.add(sapBaseListDto);

            scheduleSyncItemDto.setList(sapBaseList);
            itemList.add(scheduleSyncItemDto);

        });
        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    private SapBaseResponseDto gradeWineProductionPlanSap(Scheduling scheduling, Integer type){
        //20:等级酒产出计划
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }
        scheduleSyncDto.setZjhcj("1");
        scheduleSyncDto.setZjhfl("20");
        LocationDTO base = schedulingMapper.getLocationById(Integer.valueOf(scheduling.getProductionBaseIds()));
        scheduleSyncDto.setZjd(base.getName());
        List<String> centerIds = Stream.of(scheduling.getCenterIds().split(",")).collect(Collectors.toList());

        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        centerIds.forEach(centerId->{
            ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
            LocationDTO center = schedulingMapper.getLocationById(Integer.valueOf(centerId));
            scheduleSyncItemDto.setZzx(center.getName());

            scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(scheduling.getBeginTime()));
            scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(scheduling.getEndTime()));
            scheduleSyncItemDto.setZnd(scheduling.getPlanTime());
            scheduleSyncItemDto.setZyllx("10");
            List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();

            MaterialRequireReq materialRequireReq = new MaterialRequireReq();
            materialRequireReq.setSchedulingId(scheduling.getId());
            materialRequireReq.setCenterId(Integer.valueOf(centerId));
            List<GwpMaterialVO> lists = gwpServiceStrategyImpl.listMaterialRequirePlan(materialRequireReq);

            // 分组并求和
            Map<String, Double> groupedSum = lists.stream()
                    .collect(Collectors.groupingBy(
                            list -> list.getMaterialList().get(0).getMaterialName(), // 分组依据：每个列表的第一个元素
                            Collectors.summingDouble(list -> list.getMaterialList().stream().mapToDouble(GwpMaterialDetailVO::getPlanProduce).sum()) // 求和逻辑
                    ));

            groupedSum.forEach((key, value)->{
                SapBaseListDto sapBaseListDto = new SapBaseListDto();
                MaterialDTO materialDTO = schedulingMapper.findMaterial(2,key);
                sapBaseListDto.setMatnr(materialDTO.getMaterialCode());
                sapBaseListDto.setMenge(BigDecimal.valueOf(value));
                sapBaseListDto.setMeins(materialDTO.getUomName());
                sapBaseList.add(sapBaseListDto);
            });

            scheduleSyncItemDto.setList(sapBaseList);
            itemList.add(scheduleSyncItemDto);

        });
        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    private SapBaseResponseDto qufenDemandPlanSap(Scheduling scheduling, Integer type){
        //30:曲粉需求计划
        ScheduleSyncDto scheduleSyncDto = new ScheduleSyncDto();
        //1-创建 2-更改
        if(type == 1){
            scheduleSyncDto.setChangid("1");
        }else{
            scheduleSyncDto.setChangid("2");
        }
        scheduleSyncDto.setZjhcj("1");
        scheduleSyncDto.setZjhfl("30");
        LocationDTO base = schedulingMapper.getLocationById(Integer.valueOf(scheduling.getProductionBaseIds()));
        scheduleSyncDto.setZjd(base.getName());
        List<String> centerIds = Stream.of(scheduling.getCenterIds().split(",")).collect(Collectors.toList());

        List<ScheduleSyncItemDto> itemList = new ArrayList<ScheduleSyncItemDto>();
        centerIds.forEach(centerId->{
            ScheduleSyncItemDto scheduleSyncItemDto = new ScheduleSyncItemDto();
            LocationDTO center = schedulingMapper.getLocationById(Integer.valueOf(centerId));
            scheduleSyncItemDto.setZzx(center.getName());

            scheduleSyncItemDto.setZjhzq1(DateUtil.LocalDateToString(scheduling.getBeginTime()));
            scheduleSyncItemDto.setZjhzq2(DateUtil.LocalDateToString(scheduling.getEndTime()));
            scheduleSyncItemDto.setZnd(scheduling.getPlanTime());
            scheduleSyncItemDto.setZyllx("10");
            List<SapBaseListDto> sapBaseList = new ArrayList<SapBaseListDto>();

            MaterialRequireReq materialRequireReq = new MaterialRequireReq();
            materialRequireReq.setSchedulingId(scheduling.getId());
            materialRequireReq.setCenterId(Integer.valueOf(centerId));
            List<DqMaterialVO> lists = dqProductServiceStrategyImpl.listMaterialRequirePlan(materialRequireReq);

            // 分组并求和
            Map<String, Double> groupedSum = lists.stream()
                    .collect(Collectors.groupingBy(
                            list -> list.getMaterialList().get(0).getMaterialName(), // 分组依据：每个列表的第一个元素
                            Collectors.summingDouble(list -> list.getMaterialList().stream().mapToDouble(DqMaterialDetailVO::getPlanProduce).sum()) // 求和逻辑
                    ));

            groupedSum.forEach((key, value)->{
                SapBaseListDto sapBaseListDto = new SapBaseListDto();
                MaterialDTO materialDTO = schedulingMapper.findMaterial(4,key);
                sapBaseListDto.setMatnr(materialDTO.getMaterialCode());
                sapBaseListDto.setMenge(BigDecimal.valueOf(value));
                sapBaseListDto.setMeins(materialDTO.getUomName());
                sapBaseList.add(sapBaseListDto);
            });

            scheduleSyncItemDto.setList(sapBaseList);
            itemList.add(scheduleSyncItemDto);

        });
        scheduleSyncDto.setItemList(itemList);
        return sapService.scheduleSynchronization(scheduleSyncDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidData(Integer id) {
        Scheduling scheduling = schedulingMapper.selectById(id);
        Assert.isTrue(PlanStateEnum.IMPLEMENT.getCode().equals(scheduling.getPlanState()), "当前记录非生效状态，不允许存档操作");
        scheduling.setPlanState(StateEnum.FILE.getCode());
        schedulingMapper.updateById(scheduling);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateSchedule(SchedulingReq schedulingReq) {
        //审核中的排程不允许编辑
        Integer oldScheduleId = schedulingReq.getId();
        Scheduling oldSchedule = schedulingMapper.selectById(oldScheduleId);
        if (AuditStateEnum.IN_APPROVAL.getCode().equals(oldSchedule.getAuditState())) {
            throw new BaseKnownException(407001, "审批中的排程不允许编辑");
        }
        //干掉旧的排程计划相关数据
        schedulingMapper.physicsDeleteByScheduleId(oldScheduleId);
        schedulingRowService.deleteOldRow(oldScheduleId);
        schedulingStatsService.deleteOldState(oldScheduleId);
        schedulingLocationService.deleteOldLocation(oldScheduleId);
        //新增排程
        return this.handleNewSchedule(schedulingReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleArrange(List<SchedulingArrangeReq> schedulingArrangeReqs,Integer type) {

        if(type == 2 || type ==1){
            Object obj = redisTemplate.opsForValue().get("ScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
            if(StringUtil.isNotEmpty(obj)){
                redisTemplate.delete("ScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
            }
            redisTemplate.opsForValue().set("ScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId(),schedulingArrangeReqs);

            Object obj2 = redisTemplate.opsForValue().get("ScheduleId-"+schedulingArrangeReqs.get(0).getScheduleId().toString());
            List<SchedulingArrangeReq> reqList = (List<SchedulingArrangeReq>) (obj2);
            log.error("[SchedulingServiceImpl][handleArrange]Reids存算法==={}",reqList.toString());
        }

        Integer oldScheduleId = schedulingArrangeReqs.get(0).getScheduleId();
        //审核中的排程不允许编辑
        Scheduling oldSchedule = schedulingMapper.selectById(oldScheduleId);
        if (AuditStateEnum.IN_APPROVAL.getCode().equals(oldSchedule.getAuditState())) {
            throw new BaseKnownException(407001, "审批中的排程不允许编辑");
        }
        //删除之前排程的第二步数据
        List<Integer> rowIdByScheduleId = schedulingRowMapper.getRowIdByScheduleId(oldScheduleId);
        schedulingArrangeService.deleteOldArrange(rowIdByScheduleId);
        //删除之前排程的第三步数据
        schedulingStatsService.deleteOldState(oldScheduleId);
        //处理第二步数据
        schedulingArrangeService.handleArrange(schedulingArrangeReqs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void raiseAudit(AuditAddReq req) {
        //如果第二步未完成提交则不允许发起审批
        Integer planId = req.getPlanId();
        Boolean secondStepSubmit = schedulingMapper.secondStepSubmit(planId);
        Assert.isTrue(Objects.nonNull(secondStepSubmit) && secondStepSubmit, "排程创建第二步提交未完成，不允许发起审批");
        req.setPlanType(0);
//        if(StringUtil.isEmpty(req.getPlanTime())){
            Scheduling schedule = schedulingMapper.selectById(planId);
            req.setPlanTime(schedule.getPlanTime());
            req.setBeginTime(schedule.getBeginTime());
            req.setEndTime(schedule.getEndTime());
//        }
        auditService.addInfo(req);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer importData(MultipartFile file,String planName,String productionBaseIds,String centerIds,String locationIds,String planTime) throws IOException, IllegalAccessException {

       // List<SchedulingImport> data =  ExcelUtil.getEntityList(file, 0, SchedulingImport.class, extendService.getExtendColumnInfo());
        //List<SchedulingImport> data = EasyExcelUtil.getImport(file, SchedulingImport.class);
        List<SchedulingImport> data = ExcelRemoveUtils.getImport(file, SchedulingImport.class);

        List<String> rowNames = new ArrayList<String>();
        SchedulingReq schedulingReq = new SchedulingReq();
        List<SchedulingRowReq> schedulingRowReqList = new ArrayList<SchedulingRowReq>();
        List<SchedulingImport> zhuanerpaiBegin = new ArrayList<SchedulingImport>();
        List<SchedulingImport> zhuanerpaiEnd = new ArrayList<SchedulingImport>();
        List<SchedulingImport> zhuansanpai = new ArrayList<SchedulingImport>();
        List<SchedulingImport> zhuansipai = new ArrayList<SchedulingImport>();
        List<SchedulingImport> xiaozhuanpai = new ArrayList<SchedulingImport>();
        List<SchedulingImport> rejiweixiu = new ArrayList<SchedulingImport>();
        List<SchedulingImport> dazhuanpai = new ArrayList<SchedulingImport>();
        List<SchedulingArrangeReq> arrangeReqList  = new ArrayList<SchedulingArrangeReq>();

        //获取排次导入信息
        if(data.size()>0){
            List<Row> infos = schedulingMapper.findRankingInformationByProductionbaseId(Integer.valueOf(productionBaseIds));
            data.forEach(item->{
                if(StringUtil.isNotEmpty(item.getRowName())){
                    rowNames.add(item.getRowName());
                }
//                BigDecimal cj = item.getCJ()!=null && item.getCJ().compareTo(BigDecimal.ZERO)>0?item.getCJ().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal cz = item.getCZ()!=null && item.getCZ().compareTo(BigDecimal.ZERO)>0?item.getCZ().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal zl = item.getZL()!=null && item.getZL().compareTo(BigDecimal.ZERO)>0?item.getZL().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal hz = item.getHZ()!=null && item.getHZ().compareTo(BigDecimal.ZERO)>0?item.getHZ().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal kdz = item.getKDZ()!=null && item.getKDZ().compareTo(BigDecimal.ZERO)>0?item.getKDZ().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal khz = item.getKHZ()!=null && item.getKHZ().compareTo(BigDecimal.ZERO)>0?item.getKHZ().divide(BigDecimal.valueOf(72),1,BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO;
//                BigDecimal needDays = cj.add(cz).add(zl).add(hz).add(kdz).add(khz).setScale(1, RoundingMode.HALF_UP);
//                item.setNeedDays(needDays);
                //处理时间
                Integer year = Integer.valueOf(item.getYear());
                String[] monthAnaDay = item.getTime().split("~");
                String[] beginMonths = monthAnaDay[0].split("-");
                String[] endMonths = monthAnaDay[1].split("-");
                Integer beginDay = Integer.valueOf(beginMonths[1]);
                Integer endDay = Integer.valueOf(endMonths[1]);
                Integer beginMonth = Integer.valueOf(beginMonths[0]);
                Integer endMonth = Integer.valueOf(endMonths[0]);

                String beginDayStr = "";
                String endDayStr = "";
                String beginMonthStr = "";
                String endMonthStr = "";
                if(beginMonth>0 && beginMonth<10){
                    beginMonthStr = "0"+beginMonth;
                }else{
                    beginMonthStr = beginMonth.toString();
                }
                if(endMonth>0 && endMonth<10){
                    endMonthStr = "0"+endMonth;
                }else{
                    endMonthStr = endMonth.toString();
                }

                if(beginDay>0 && beginDay<10){
                    beginDayStr = "0"+beginDay;
                }else{
                    beginDayStr = beginDay.toString();
                }
                if(endDay>0 && endDay<10){
                    endDayStr = "0"+endDay;
                }else{
                    endDayStr = endDay.toString();
                }

                String yearStr = "";
                String beginTime = year+"-"+beginMonthStr+"-"+beginDayStr;
                if(beginMonths[0].equals("12")){
                    if(!beginMonths[0].equals(endMonths[0])){
                        year = year+1;
                    }
                }

                String endTime = year+"-"+endMonthStr+"-"+endDayStr;
                item.setBeginTime(beginTime);
                item.setEndTime(endTime);
            });
            if(infos.size() != rowNames.size()){
                throw new BaseKnownException(407001, "导入模版排次数据与排程模版配置的排次不一致");
            }

            //赋值排次名称、排次顺序
            AtomicInteger index = new AtomicInteger(1);
            IntStream.range(0, data.size() - 1).forEach(i -> {
                SchedulingImport current = data.get(i);
                current.setSort(index.get());
                SchedulingImport next = data.get(i + 1);

                if(StringUtil.isNotEmpty(next.getRowName())){
                    index.incrementAndGet();
                    next.setSort(index.get());
                }else{
                    next.setRowName(current.getRowName());
                    next.setSort(index.get());
                }
                System.out.println("Current: " + current + ", Next: " + next);
            });
//            Map<Integer, List<SchedulingImport>> groupedByNames = data.stream()
//                    .collect(Collectors.groupingBy(SchedulingImport::getSort));
//
//            groupedByNames.forEach((name, schedulingImport) -> {
//                System.out.println("Name: " + name);
//                schedulingImport.forEach(person -> System.out.println(" - Age: " + person.getRowName()));
//            });
//            log.error("==========================={}",groupedByNames.toString());

            //封装排程数据
            LocalDate beginTime = LocalDate.parse(data.get(0).getBeginTime());
            LocalDate endTime = LocalDate.parse(data.get(data.size()-1).getEndTime());
            schedulingReq.setBeginTime(beginTime);
            schedulingReq.setEndTime(endTime);
            schedulingReq.setPlanTime(planTime);
            schedulingReq.setType(2);
            schedulingReq.setName(planName);
            schedulingReq.setProductionBaseIds(productionBaseIds);
            schedulingReq.setCenterIds(centerIds);
            schedulingReq.setLocationIds(locationIds);

            //封装排次数据
            if(rowNames.size()>0){
                AtomicInteger sort = new AtomicInteger(1);
                rowNames.forEach(item->{

                    SchedulingRowReq schedulingRowReq = new SchedulingRowReq();
                    Row row = schedulingMapper.findRankingInformationByProductionbaseIdAndRowNameAndSort(Integer.valueOf(productionBaseIds),item, sort.get());
                    if(row != null){
                        schedulingRowReq.setRowId(row.getId());
                        schedulingRowReq.setUseState(true);
                        schedulingRowReq.setTotalDays(Integer.valueOf(row.getTotalDays()));
                        schedulingRowReq.setRankingName(row.getName());
                        schedulingRowReq.setRankingOrder(row.getRowSequence());
                        schedulingRowReqList.add(schedulingRowReq);
                        sort.incrementAndGet();
                    }else{
                        throw new BaseKnownException(407001, "未查询到"+item+"排次信息");
                    }

                });

            }

            //分割各排次数据
            data.forEach(item2->{
                schedulingRowReqList.forEach(item->{
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(1)){
                        zhuanerpaiBegin.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(2)){
                        zhuansanpai.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(3)){
                        zhuansipai.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(4)){
                        xiaozhuanpai.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(5)){
                        rejiweixiu.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(6)){
                        dazhuanpai.add(item2);
                    }
                    if(item2.getRowName().equals(item.getRankingName()) && item2.getSort().equals(item.getRankingOrder()) && item2.getSort().equals(7)){
                        zhuanerpaiEnd.add(item2);
                    }
                });
            });

            //取值各排次开始时间、结束时间
            AtomicInteger ii = new AtomicInteger(1);
            schedulingRowReqList.forEach(item->{
                if(ii.get() ==1){
                    LocalDate beginTimes = LocalDate.parse(zhuanerpaiBegin.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(zhuanerpaiBegin.get(zhuanerpaiBegin.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==2){
                    LocalDate beginTimes = LocalDate.parse(zhuansanpai.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(zhuansanpai.get(zhuansanpai.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==3){
                    LocalDate beginTimes = LocalDate.parse(zhuansipai.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(zhuansipai.get(zhuansipai.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==4){
                    LocalDate beginTimes = LocalDate.parse(xiaozhuanpai.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(xiaozhuanpai.get(xiaozhuanpai.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==5){
                    LocalDate beginTimes = LocalDate.parse(rejiweixiu.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(rejiweixiu.get(rejiweixiu.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==6){
                    LocalDate beginTimes = LocalDate.parse(dazhuanpai.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(dazhuanpai.get(dazhuanpai.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }else if(ii.get() ==7){
                    LocalDate beginTimes = LocalDate.parse(zhuanerpaiEnd.get(0).getBeginTime());
                    LocalDate endTimes = LocalDate.parse(zhuanerpaiEnd.get(zhuanerpaiEnd.size()-1).getEndTime());
                    item.setBeginTime(beginTimes);
                    item.setEndTime(endTimes);
                }
                ii.incrementAndGet();
            });
            schedulingReq.setSchedulingRowReqList(schedulingRowReqList);
        }
        log.error("=============zhuanerpai=============={}",zhuanerpaiBegin.toString());
        log.error("=============zhuansanpai=============={}",zhuansanpai.toString());
        log.error("=============zhuansipai=============={}",zhuansipai.toString());
        log.error("=============xiaozhuanpai=============={}",xiaozhuanpai.toString());
        log.error("=============rejiweixiu=============={}",rejiweixiu.toString());
        log.error("=============dazhuanpai=============={}",dazhuanpai.toString());
        log.error("=============zhuanerpaiEnd=============={}",zhuanerpaiEnd.toString());
        //封装计划排程参数
        log.error("=============rowNames=============={}",rowNames.toString());

        log.error("==============schedulingReq============={}",schedulingReq.toString());
        //验证糟源生效
        Set<String> codeList = new HashSet<String>();
        data.forEach(item->{
            if(item.getCJ() != null){
                codeList.add("CJ");
            }
            if(item.getCZ() != null){
                codeList.add("CZ");
            }
            if(item.getZL() != null){
                codeList.add("ZL");
            }
            if(item.getHZ() != null){
                codeList.add("HZ");
            }
            if(item.getKDZ() != null){
                codeList.add("KDZ");
            }
            if(item.getKHZ() != null){
                codeList.add("KHZ");
            }
            if(item.getKZL() != null){
                codeList.add("KZL");
            }
            if(item.getCB() != null){
                codeList.add("CB");
            }
            if(item.getMZ() != null){
                codeList.add("MZ");
            }
            if(item.getDZ() != null){
                codeList.add("DZ");
            }
            if(item.getCT() != null){
                codeList.add("CT");
            }
        });
        List<Integer> ids = vinasseSourceMapper.selectBatchCodes(codeList,Integer.valueOf(productionBaseIds));
        if(ids.size()>0){
            ids.forEach(item->{
                List<Integer> sourceIds= vinasseSourceMapper.selectFormulaSourceId(item);
                if(sourceIds.size()<=0){
                    throw new BaseKnownException(407001, "导入糟源与糟源配方不一致");
                }
            });
        }
        schedulingReq.setZenIds(ids);
        Integer scheduleId = handleNewSchedule(schedulingReq);
        data.forEach(item->{
            item.setScheduleId(scheduleId);
            Row row = schedulingMapper.findRankingInformationByProductionbaseIdAndRowNameAndSort(Integer.valueOf(productionBaseIds),item.getRowName(), item.getSort());
            if(row != null){
                item.setRowId(row.getId());
            }else{
                throw new BaseKnownException(407001, "未查询到"+item.getRowName()+"排次信息");
            }
//            ProductionMode productionMode = schedulingMapper.findProductionModeByCode(item.getProductionMode());
//            if(productionMode != null){
//                item.setModeId(productionMode.getId());
//            }else{
//                throw new BaseKnownException(407001, "未查询到"+item.getProductionMode()+"生产模式");
//            }
            SchedulingArrangeReq schedulingArrangeReq = CopyUtil.simpleCopy(item,SchedulingArrangeReq.class);
            schedulingArrangeReq.setBeginTime(LocalDate.parse(item.getBeginTime()));
            schedulingArrangeReq.setEndTime(LocalDate.parse(item.getEndTime()));

            List<SchedulingVinasseReq> vinasseReqList = new ArrayList<SchedulingVinasseReq>();

            if(item.getCJ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("CJ");
                schedulingVinasseReq.setVinasseData(item.getCJ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getCZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("CZ");
                schedulingVinasseReq.setVinasseData(item.getCZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getZL() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("ZL");
                schedulingVinasseReq.setVinasseData(item.getZL());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getHZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("HZ");
                schedulingVinasseReq.setVinasseData(item.getHZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getKDZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("KDZ");
                schedulingVinasseReq.setVinasseData(item.getKDZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getKHZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("KHZ");
                schedulingVinasseReq.setVinasseData(item.getKHZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getKZL() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("KZL");
                schedulingVinasseReq.setVinasseData(item.getKZL());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getCB() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("CB");
                schedulingVinasseReq.setVinasseData(item.getCB());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getMZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("MZ");
                schedulingVinasseReq.setVinasseData(item.getMZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getDZ() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("DZ");
                schedulingVinasseReq.setVinasseData(item.getDZ());
                vinasseReqList.add(schedulingVinasseReq);
            }
            if(item.getCT() != null){
                SchedulingVinasseReq schedulingVinasseReq = new SchedulingVinasseReq();
                schedulingVinasseReq.setVinasseCode("CT");
                schedulingVinasseReq.setVinasseData(item.getCT());
                vinasseReqList.add(schedulingVinasseReq);
            }
            schedulingArrangeReq.setSchedulingVinasseReqList(vinasseReqList);
            arrangeReqList.add(schedulingArrangeReq);
        });
        log.error("==============reqs============={}",arrangeReqList.toString());
        //1、导入，2编辑
        handleArrange(arrangeReqList,1);
        return scheduleId;
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     */
    @Override
    public ExcelExportDto getImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateImportFile(SchedulingImport.class,
                SchedulingArrangeConstant.TEMPLATE_FILE_NAME, extendService.getExtendColumnInfo());
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName(FileNameUtil.createExcelFullFileName(SchedulingArrangeConstant.TEMPLATE_FILE_NAME));
        return excelExportDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void zengKouCalculation() {
        LambdaQueryWrapper<ZengkouCalculation> queryWrapper = new LambdaQueryWrapper<ZengkouCalculation>()
                .eq(ZengkouCalculation::getDeleted, 0);
        zengkouCalculationMapper.delete(queryWrapper);
        //获取基地
        List<LocationDTO> baseList = schedulingMapper.getLocationListByType(10);
        baseList.forEach(base->{
            //获取中心
            List<LocationDTO> centerList = schedulingMapper.getLocationListByParentId(base.getId()).stream().filter(f-> !f.getName().equals("原辅料管理部") && !f.getName().equals("制曲中心") && !f.getName().equals("酒管中心")).collect(Collectors.toList());
            centerList.forEach(center->{
                //获取车间
                List<LocationDTO> locationList = schedulingMapper.getLocationListByParentId(center.getId());
                locationList.forEach(location->{

                    Integer cj = 0;
                    Integer hz = 0;
                    Integer cz = 0;
                    Integer kzl = 0;
                    Integer khz = 0;
                    Integer time = 0;

                    //涨幅系数
                    RetortContainerVO cjzf = schedulingMapper.getRetortContainer(base.getId(),"CJ");
                    RetortContainerVO czzf = schedulingMapper.getRetortContainer(base.getId(),"CZ");
                    RetortContainerVO hzzf = schedulingMapper.getRetortContainer(base.getId(),"HZ");
                    RetortContainerVO kzlzf = schedulingMapper.getRetortContainer(base.getId(),"KZL");
                    RetortContainerVO khzzf = schedulingMapper.getRetortContainer(base.getId(),"KHZ");
                    //窖池订单已同步的入窖甑口
                    Integer CJ = schedulingMapper.zengKouCalculation(center.getId(),location.getId(),"CJ");
                    Boolean flag = false;
                    if(CJ == 0){
                        flag = true;
                    }
                    if(base.getName().contains("非遗") && center.getName().contains("国窖")){
                        if(flag){
                            hz = 0;
                            cz = 0;
                            kzl = 0;
                        }else{
                            //HZ,国窖基地算法为：(CJ/19)*2
                            hz = (CJ/19)*2;
                            //CZ，国窖基地算法：（CJ-CJ/1.4-HZ/1.3）*1.4
                            cz = (CJ-(CJ/cjzf.getGainsCoefficient().intValue())-(hz/hzzf.getGainsCoefficient().intValue())) * czzf.getGainsCoefficient().intValue();
                            //开ZL，国窖基地算法：CJ-CJ/1.4+HZ-HZ/1.3+CZ-CZ/1.4-开HZ
                            kzl = CJ-(CJ/cjzf.getGainsCoefficient().intValue())+hz-(hz/hzzf.getGainsCoefficient().intValue())+cz-(cz/czzf.getGainsCoefficient().intValue())-hzzf.getRetortCount();
                        }
                        //开HZ,国窖基地算法：取值HZ甑口数
                        khz = hzzf.getRetortCount();
                    }
                    if(base.getName().contains("非遗") && center.getName().contains("小市")){
                        if(flag){
                            //HZ，小市基地算法：（CJ-CJ/1.4-CZ/1.4）*1.3
                            hz = 0;
                            //CZ，小市基地算法为：(CJ/19)*2
                            cz = 0;
                        }else{
                            //HZ，小市基地算法：（CJ-CJ/1.4-CZ/1.4）*1.3
                            hz = (CJ-(CJ/cjzf.getGainsCoefficient().intValue())-(cz/czzf.getGainsCoefficient().intValue())) * hzzf.getGainsCoefficient().intValue();
                            //CZ，小市基地算法为：(CJ/19)*2
                            cz = (CJ/19)*2;
                        }
                        //KZL，小市基地算法：取值CZ甑口
                        kzl = czzf.getRetortCount();
                        //KHZ， 小市基地算法：取值HZ甑口数
                        khz = hzzf.getRetortCount();
                    }
                    if(base.getName().contains("罗汉")){
                        if(flag){
                            //HZ,罗汉固定位：702-705中心无HZ，706中心与非遗算法一致
                                hz =0;
                                cz = 0;
                                kzl = 0;
                        }else{
                            //HZ,罗汉固定位：702-705中心无HZ，706中心与非遗算法一致
                            if(center.getName().contains("706")){
                                hz =(CJ/19)*2;
                            }else{
                                hz =0;
                            }
                            if(center.getName().contains("702") || center.getName().contains("703") || center.getName().contains("704") || center.getName().contains("705")){
                                //CZ,罗汉基地算法：702-705中心CZ=（CJ-CJ/1.4）*1.4
                                cz = (CJ-(CJ/cjzf.getGainsCoefficient().intValue())) * czzf.getGainsCoefficient().intValue();
                                //KZL，罗汉基地算法：702-705中心开ZL=CJ-CJ/1.4+CZ-CZ/1.4
                                kzl = CJ-(CJ/cjzf.getGainsCoefficient().intValue()) + cz - (cz/czzf.getGainsCoefficient().intValue());
                            }else{
                                cz = 0;
                                kzl = 0;
                            }
                        }

                        //KHZ，罗汉基地算法：取值HZ甑口数
                        khz = hzzf.getRetortCount();
                    }
                    if(base.getName().contains("黄舣")){
                        if(flag){
                            hz = 0;
                            kzl = 0;
                            khz = 0;
                        }else{
                            //HZ,黄舣基地算法为：（CJ-CJ/1.4-CZ/1.4-开ZL甑口数）*1.4
                            hz = (CJ-(CJ/cjzf.getGainsCoefficient().intValue())-(cz/czzf.getGainsCoefficient().intValue())-kzlzf.getRetortCount()) * hzzf.getGainsCoefficient().intValue();
                            //KZL，黄舣基地算法：(CJ/18)*2;
                            kzl = (CJ/18)*2;
                            //KHZ，黄舣基地算法：CJ-CJ/1.4+HZ-HZ/1.4+CZ-CZ/1.4-开ZL
                            khz = CJ-(CJ/cjzf.getGainsCoefficient().intValue()) + hz - (hz/hzzf.getGainsCoefficient().intValue()) + cz - (cz/czzf.getGainsCoefficient().intValue()) - kzl;

                        }
                        //CZ，黄舣基地算法：取值开ZL甑口数
                        cz = kzlzf.getRetortCount();
                    }

                    if(flag){
                        time = 0;
                    }else{
                        //需要天数算法所有基地通用：CJ/CJ每天甑口数+CZ/CZ每天甑口数+HZ/HZ每天甑口数+开HZ*开HZ涨幅/开HZ每天甑口数+开ZL*开ZL涨幅系数/开ZL每天甑口数
                        time = (CJ/cjzf.getRetortCount()) + (cz/czzf.getRetortCount()) + (hz/hzzf.getRetortCount()) + (khz * khzzf.getGainsCoefficient().intValue()/khzzf.getRetortCount()) + (kzl*kzlzf.getGainsCoefficient().intValue()/kzlzf.getRetortCount());
                    }

                    ZengkouCalculation zengkouCalculation = new ZengkouCalculation();
                    zengkouCalculation.setBaseId(base.getId());
                    zengkouCalculation.setCenterId(center.getId());
                    zengkouCalculation.setLocationId(location.getId());
                    zengkouCalculation.setCj(CJ.toString());
                    zengkouCalculation.setCz(cz.toString());
                    zengkouCalculation.setHz(hz.toString());
                    zengkouCalculation.setKzl(kzl.toString());
                    zengkouCalculation.setKhz(khz.toString());
                    zengkouCalculation.setTime(time.toString());
                    zengkouCalculationMapper.insert(zengkouCalculation);
                });
            });
        });

    }


    /**
     * 处理排程主表
     *
     * @param schedulingReq
     * @return
     */
    private Integer handleSchedule(SchedulingReq schedulingReq) {
        String zenIds = Optional.ofNullable(schedulingReq.getZenIds())
                .map(it -> it.stream().map(String::valueOf).collect(Collectors.joining(",")))
                .orElseThrow(() -> new BaseKnownException(822002, "糟源代码列表必选"));
        Map<Integer, String> zenMap = vinasseSourceMapper.selectBatchIds(schedulingReq.getZenIds()).stream()
                .collect(Collectors.toMap(VinasseSource::getId, VinasseSource::getCode));
        //校验 糟源+中心确定的配方只有一个
        schedulingReq.getZenIds().forEach(it -> {
            FormulaQueryReq formulaQueryReq = new FormulaQueryReq();
            formulaQueryReq.setCenters(Arrays.stream(schedulingReq.getCenterIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
            String sourceCode = zenMap.get(it);
            Assert.notBlank(sourceCode, "未查到相关糟源代码");
            formulaQueryReq.setSourceCode(sourceCode);
            formulaQueryReq.setCenter(schedulingReq.getCenterIds());
            formulaQueryReq.setFormulaState(Integer.parseInt(StateEnum.TAKE_EFFECT.getCode()));
            formulaQueryReq.setProductionBaseId(Integer.valueOf(schedulingReq.getProductionBaseIds()));
            //List<Integer> formulaIds = formulaService.listFormulaByCenter(formulaQueryReq);

            formulaQueryReq.getCenters().forEach(item->{
                LambdaQueryWrapper<Formula> lambdaQueryWrapper = new LambdaQueryWrapper<Formula>()
                        .eq(Objects.nonNull(formulaQueryReq.getProductionBaseId()), Formula::getProductionBaseId, formulaQueryReq.getProductionBaseId())
                        //.like(Strings.isNotBlank(req.getFormulaName()), Formula::getName, req.getFormulaName())
                        .like(Strings.isNotBlank(sourceCode), Formula::getSourceCode, sourceCode)
                        //.like(Strings.isNotBlank(item.toString()), Formula::getCenters, item.toString())
                        .apply( "FIND_IN_SET ('" + item + "',centers)")
                        .eq(Objects.nonNull(formulaQueryReq.getFormulaState()), Formula::getState, formulaQueryReq.getFormulaState());
                List<Formula> formulaList = formulaMapper.selectList(lambdaQueryWrapper);
                if(formulaList.size()<=0){
                    LocationDTO center = schedulingMapper.getLocationById(item);
                    Assert.isTrue(formulaList.size()<=0, MessageFormat.format("{0}糟源在{1}中心没有生效", sourceCode,center.getName()));
                }
                //Assert.isTrue(0 < formulaIds.size(), MessageFormat.format("{0}糟源没有同时生效所选择的中心", sourceCode));
            });

        });
        Scheduling entity = new Scheduling()
                .setCode(generateFlowNum(schedulingReq.getPlanTime()))
                .setName(schedulingReq.getName())
                .setPlanTime(schedulingReq.getPlanTime())
                .setBeginTime(schedulingReq.getBeginTime())
                .setEndTime(schedulingReq.getEndTime())
                .setPlanState(PlanStateEnum.NEW.getCode())
                .setProductionBaseIds(schedulingReq.getProductionBaseIds())
                .setCenterIds(schedulingReq.getCenterIds())
                .setType(schedulingReq.getType())
                .setZenIds(zenIds)
                .setAuditState(AuditStateEnum.NEW.getCode())
                .setLocationIds(schedulingReq.getLocationIds());
        this.save(entity);
        return entity.getId();
    }

    /**
     * 创建编码
     *
     * @param planTime
     * @return
     */
    public String generateFlowNum(String planTime) {
        if (Strings.isEmpty(planTime) || !planTime.matches("^\\d{4}$")) {
            throw new BaseKnownException(325004, "计划时间格式异常");
        }
        String cacheKey = "ScheduleCode" + planTime;
        //判断key是否存在 不存在查看数据库是否存在当前年份的排程   取最大值set到redis   否则set0
        String num = stringRedisTemplate.opsForValue().get(cacheKey);
        if (Objects.isNull(num)) {
            List<String> codes = schedulingMapper.listCodeByPlanTime(planTime);
            if (CollectionUtils.isEmpty(codes)) {
                Boolean operateResult = stringRedisTemplate.opsForValue().setIfAbsent(cacheKey, "0");
                Assert.isTrue(Objects.nonNull(operateResult) && operateResult, "缓存操作失败,请稍后再试");
            } else {
                codes.stream()
                        .map(it -> Integer.parseInt(it.substring(4)))
                        .max(Integer::compare)
                        .ifPresent(it -> {
                            Boolean operateResult = stringRedisTemplate.opsForValue().setIfAbsent(cacheKey, "0");
                            Assert.isTrue(Objects.nonNull(operateResult) && operateResult, "缓存操作失败,请稍后再试");
                        });
            }
        }
        Long afterIncrease = stringRedisTemplate.opsForValue().increment(cacheKey, 1);
        if (Objects.nonNull(afterIncrease) && 999L < afterIncrease) {
            throw new BaseKnownException(325005, "当前数值已超出999限制");
        }
        return "PP" + planTime + String.format("%03d", afterIncrease);
    }
}
