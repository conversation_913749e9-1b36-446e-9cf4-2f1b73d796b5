package com.hvisions.plan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.plan.dto.SchedulingQueryReq;
import com.hvisions.plan.dto.SchedulingReq;
import com.hvisions.plan.dto.SchedulingTemplateQueryReq;
import com.hvisions.plan.dto.SchedulingTemplateReq;
import com.hvisions.plan.entity.SchedulingTemplate;
import com.hvisions.plan.vo.SchedulingTemplateDetailVO;
import com.hvisions.plan.vo.SchedulingTemplateVO;
import com.hvisions.plan.vo.SchedulingVO;

/**
 * <p>
 * 排程模版 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ISchedulingTemplateService extends IService<SchedulingTemplate> {
    /**
     * 分页查询排程模版
     *
     * @param req
     * @param pageInfo
     * @return
     */
    Page<SchedulingTemplateVO> pageSchedule(SchedulingTemplateQueryReq req, PageInfo pageInfo);

    /**
     * 处理新增排程模版
     *
     * @param schedulingReq
     */
    Integer handleNewSchedule(SchedulingTemplateReq schedulingReq);

    /**
     * 生效
     *
     * @param id
     */
    void validData(Integer id);

    /**
     * 归档
     *
     * @param id
     */
    void invalidData(Integer id);

    /**
     * 删除
     *
     * @param id
     */
    String delete(Integer id);

    /**
     * 详情
     *
     * @param id
     */
    SchedulingTemplateDetailVO detail(Integer id);

    /**
     * 更新排程模版
     *
     * @param schedulingReq
     */
    Integer updateSchedule(SchedulingTemplateReq schedulingReq);

    /**
     *
     * 复制
     * @param id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/24 16:48
     *
     */
    void copyInfo(Integer id);
}
