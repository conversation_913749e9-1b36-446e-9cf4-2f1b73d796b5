package com.hvisions.plan.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hvisions.brewage.excel.MonthActualDataExcel;
import com.hvisions.brewage.feign.MkwineClient;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsDTO;
import com.hvisions.brewage.mkwine.dto.ProductionDetailsVO;
import com.hvisions.brewage.mkwine.dto.QueryWineQualityDTO;
import com.hvisions.brewage.mkwine.vo.GetWineQualityVO;
import com.hvisions.brewage.mkwine.vo.WorkshopPit.StaticsVO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.plan.consts.ApiMessage;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.dao.AuditMapper;
import com.hvisions.plan.dao.SchedulingMapper;
import com.hvisions.plan.dto.*;
import com.hvisions.plan.dto.plan.dto.PurchasePlanMaterialDTO;
import com.hvisions.plan.dto.plan.dto.PurchasePlanReq;
import com.hvisions.plan.dto.plan.vo.RowVO;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.PlanStateEnum;
import com.hvisions.plan.service.IMonthPlanDetailService;
import com.hvisions.plan.service.IMonthPlanService;
import com.hvisions.plan.service.IMonthPlanVinasseService;
import com.hvisions.plan.service.ISchedulingService;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.plan.utils.StringUtil;
import com.hvisions.plan.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 月度计划管理相关接口
 *
 * <AUTHOR>
 * 2022-03-23
 */
@RestController
@RequestMapping("/system/MonthPlan")
@Api(value = "月度计划管理相关接口", tags = "月度计划管理相关接口")
public class MonthPlanController {

    @Resource
    IMonthPlanService baseService;
    @Resource
    BaseWrapper baseWrapper;
    @Resource
    IMonthPlanDetailService monthPlanDetailService;
    @Resource
    IMonthPlanVinasseService monthPlanVinasseService;
    @Resource
    ISchedulingService schedulingService;
    @Resource
    SchedulingMapper schedulingMapper;
    @Resource
    MkwineClient mkwineClient;
    @Resource
    AuditMapper auditMapper;



    /**
     * 月计划首页
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.hvisions.brewage.plan.vo.MonthPlanVO>
     * <AUTHOR>
     * @date 2022/4/8 15:25
     */
    @GetMapping("/mainPage")
    @ApiOperation(value = "查询分页月度计划(中心维度)")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<MonthPlanVO> queryMainPagedMonthPlan(MonthPlanMainQueryReq req, PageInfo pageInfo) {
        return baseService.queryMainPagedMonthPlan(req, pageInfo);
    }

    /**
     * 查询分页月度计划
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页月度计划")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<MonthPlanVO> queryPagedMonthPlan(MonthPlanQueryReq req, PageInfo pageInfo) {
        //如果查询传入了排程计划的编码  则优先查询指定的排程
        List<Integer> anditQuery = Optional.ofNullable(req.getAuditState())
                .filter(Strings::isNotBlank)
                .map(it -> {
                    LambdaQueryWrapper<Audit> auditQueryWrapper = new LambdaQueryWrapper<Audit>()
                            .like(Audit::getAuditState, it)
                            .eq(Audit::getPlanType,1);
                    return auditMapper.selectList(auditQueryWrapper).stream().map(Audit::getPlanId).collect(Collectors.toList());
                }).orElse(null);
        List<Integer> scheduleQuery = Optional.ofNullable(req.getSchedulingCode())
                .filter(Strings::isNotBlank)
                .map(it -> {
                    LambdaQueryWrapper<Scheduling> scheduleQueryWrapper = new LambdaQueryWrapper<Scheduling>()
                            .like(Scheduling::getCode, it);
                    return schedulingMapper.selectList(scheduleQueryWrapper).stream().map(Scheduling::getId).collect(Collectors.toList());
                }).orElse(null);
        if(StringUtil.isNotEmpty(anditQuery)){
            if(anditQuery.size()>0){
                scheduleQuery.addAll(anditQuery);
            }
        }

        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .like(StringUtils.isNotBlank(req.getCode()), MonthPlan::getCode, req.getCode())
                .eq(Objects.nonNull(req.getPlantId()), MonthPlan::getPlantId, req.getPlantId())
                .eq(Objects.nonNull(req.getCenterId()), MonthPlan::getCenterId, req.getCenterId())
                .eq(Objects.nonNull(req.getLocationId()), MonthPlan::getLocationId, req.getLocationId())
                .eq(Objects.nonNull(req.getPlanState()), MonthPlan::getPlanState, req.getPlanState())
                .in(CollectionUtils.isNotEmpty(scheduleQuery), MonthPlan::getSchedulingId, scheduleQuery)
                .eq(Objects.nonNull(req.getMonth()), MonthPlan::getMonth, req.getMonth());

        if (Objects.nonNull(req.getBeginYear()) && Objects.nonNull(req.getBeginMonth())) {
            wrapper.and(it -> it.gt(MonthPlan::getYear, req.getBeginYear())
                    .or(ii -> ii.eq(MonthPlan::getYear, req.getBeginYear()).ge(MonthPlan::getMonth, req.getBeginMonth())));
        }
        if (Objects.nonNull(req.getEndYear()) && Objects.nonNull(req.getEndMonth())) {
            wrapper.and(it -> it.lt(MonthPlan::getYear, req.getEndYear())
                    .or(ii -> ii.eq(MonthPlan::getYear, req.getEndYear()).le(MonthPlan::getMonth, req.getEndMonth())));
        }
        wrapper.orderByAsc(MonthPlan::getPlanState).orderByAsc(MonthPlan::getId);
        IPage<MonthPlan> page = baseService.page(new Page<>(pageInfo.getPage(), pageInfo.getPageSize()), wrapper);

        Page<MonthPlanVO> monthPlanVOPage = baseWrapper.convertToPage(page, MonthPlanVO.class);
        //处理排程编码
        if (CollectionUtils.isEmpty(monthPlanVOPage.getRecords())) {
            return monthPlanVOPage;
        }

        //获取实际投入和实际消耗
        final ProductionDetailsDTO dto = new ProductionDetailsDTO();
        dto.setCenterId(req.getCenterId());
        dto.setLocationId(req.getLocationId());
        final LocalDate startDate =
                monthPlanVOPage.getRecords().stream().map(MonthPlanVO::getMonthBeginDate).min(LocalDate::compareTo).get();
        dto.setStartTime(startDate);
        final LocalDate endDate =
                monthPlanVOPage.getRecords().stream().map(MonthPlanVO::getMonthEndDate).max(LocalDate::compareTo).get();
        dto.setEndTime(endDate);


        ResultVO<ProductionDetailsVO> resultVO = mkwineClient.getProductionDetailsByDate(dto);
        if(resultVO.getCode()!=200){
            throw new BaseKnownException(resultVO.getCode(),resultVO.getMessage());
        }

        final ProductionDetailsVO details =resultVO.getData();

        List<Integer> scheduleIds = monthPlanVOPage.getRecords().stream().map(MonthPlanVO::getSchedulingId).collect(Collectors.toList());
        Map<Integer, Scheduling> codeMap = schedulingMapper.selectBatchIds(scheduleIds).stream()
                .collect(Collectors.toMap(Scheduling::getId, v -> v));

        LambdaQueryWrapper<Audit> auditQueryWrapper = new LambdaQueryWrapper<Audit>()
                .eq(Audit::getPlanType,1)
                .in(Audit::getPlanId,scheduleIds);
        Map<Integer, Audit> auditMap = auditMapper.selectList(auditQueryWrapper).stream()
                .collect(Collectors.toMap(Audit::getPlanId, v -> v));

        monthPlanVOPage.getRecords().forEach(record -> {

            Scheduling scheduling = Optional.ofNullable(codeMap.get(record.getSchedulingId()))
                    .orElseThrow(() -> new BaseKnownException(823001, "未查到相关排程信息"));
            record.setSchedulingCode(scheduling.getCode());

            if(auditMap.size()>0){
                Audit audit = auditMap.get(record.getSchedulingId());
                if(StringUtil.isNotEmpty(audit)){
                    record.setAuditState(audit.getAuditState());
                }
            }

            //record.setZenIds(scheduling.getZenIds());
            //处理基酒产出
            QueryWineQualityDTO queryWineQualityDTO = new QueryWineQualityDTO();
            queryWineQualityDTO.setCenterIds(new ArrayList<Integer>() {{
                add(record.getCenterId());
            }});
            queryWineQualityDTO.setLocationIds(new ArrayList<Integer>() {{
                add(record.getLocationId());
            }});
            queryWineQualityDTO.setStartDate(record.getMonthBeginDate());
            queryWineQualityDTO.setEndDate(record.getMonthEndDate());

            ResultVO<List<GetWineQualityVO>> resultVO1 = mkwineClient.getWineQualityData(queryWineQualityDTO);
            if(resultVO1.getCode()!=200){
                throw new BaseKnownException(resultVO1.getCode(),resultVO1.getMessage());
            }

            Optional.ofNullable(resultVO1.getData())
                    .filter(it -> !it.isEmpty() && 1 == it.size())
                    .ifPresent(it -> record.setActualWinOutPut(BigDecimal.valueOf(it.get(0).getTo60Quantity())));

            record.setActualInputLiang(
                    details.getActualInputLiang().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                    && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
            );
            record.setActualUseLiang(
                    details.getActualUseLiang().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                    && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
            );
            record.setActualOutputWine(
                    details.getActualOutputWine().stream().filter(f -> f.getActDate().compareTo(record.getMonthBeginDate()) >= 0
                    && f.getActDate().compareTo(record.getMonthEndDate()) <= 0).map(StaticsVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
            );
            //计算进度
            record.setActualSpeed(record.getActualOutputWine().multiply(BigDecimal.valueOf(100)).divide(record.getWinOutput(), 2, BigDecimal.ROUND_DOWN));

        });
        return monthPlanVOPage;
    }


    /**
     * 查询分页月度计划
     *
     * @param req      查询条件
     */
    @PostMapping("/pageCopy")
    @ApiOperation(value = "查询分页月度计划-下发专用")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<MonthPlanVO> pageCopy(@RequestBody MonthPlanQueryCopyReq req) {
        //如果查询传入了排程计划的编码  则优先查询指定的排程
        List<Integer> scheduleQuery = Optional.ofNullable(req.getSchedulingCode())
                .filter(Strings::isNotBlank)
                .map(it -> {
                    LambdaQueryWrapper<Scheduling> scheduleQueryWrapper = new LambdaQueryWrapper<Scheduling>()
                            .like(Scheduling::getCode, it);
                    return schedulingMapper.selectList(scheduleQueryWrapper).stream().map(Scheduling::getId).collect(Collectors.toList());
                }).orElse(null);
        if(req.getCenterId().size()>0){
            Integer id = req.getCenterId().get(0);
            if(id == 0){
                req.getCenterId().clear();
                //全部中心
                List<LocationDTO> list = schedulingMapper.getLocationListByParentId(Integer.valueOf(req.getPlantId().get(0)));
                if(list.size()>0){
                    list = list.stream().filter(f-> !f.getName().equals("原辅料管理部") && !f.getName().equals("制曲中心") && !f.getName().equals("酒管中心")).collect(Collectors.toList());
                    List<Integer> stringList = list.stream()
                            .map(dto -> dto.getId())
                            .collect(Collectors.toList());
                    req.getCenterId().addAll(stringList);
                }
            }
        }
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .like(StringUtils.isNotBlank(req.getCode()), MonthPlan::getCode, req.getCode())
                .in(CollectionUtils.isNotEmpty(req.getPlantId()), MonthPlan::getPlantId, req.getPlantId())
                .in(CollectionUtils.isNotEmpty(req.getCenterId()), MonthPlan::getCenterId, req.getCenterId())
                .in(CollectionUtils.isNotEmpty(req.getLocationId()), MonthPlan::getLocationId, req.getLocationId())
                .eq(Objects.nonNull(req.getPlanState()), MonthPlan::getPlanState, req.getPlanState())
                .in(CollectionUtils.isNotEmpty(scheduleQuery), MonthPlan::getSchedulingId, scheduleQuery)
                .in(CollectionUtils.isNotEmpty(req.getMonth()), MonthPlan::getMonth, req.getMonth());

        if (Objects.nonNull(req.getBeginYear()) && Objects.nonNull(req.getBeginMonth())) {
            wrapper.and(it -> it.gt(MonthPlan::getYear, req.getBeginYear())
                    .or(ii -> ii.eq(MonthPlan::getYear, req.getBeginYear()).ge(MonthPlan::getMonth, req.getBeginMonth())));
        }
        if (Objects.nonNull(req.getEndYear()) && Objects.nonNull(req.getEndMonth())) {
            wrapper.and(it -> it.lt(MonthPlan::getYear, req.getEndYear())
                    .or(ii -> ii.eq(MonthPlan::getYear, req.getEndYear()).le(MonthPlan::getMonth, req.getEndMonth())));
        }
        wrapper.orderByAsc(MonthPlan::getPlanState).orderByAsc(MonthPlan::getId);
        IPage<MonthPlan> page = baseService.page(new Page<>(req.getPage(), req.getPageSize()), wrapper);

        Page<MonthPlanVO> monthPlanVOPage = baseWrapper.convertToPage(page, MonthPlanVO.class);
        //处理排程编码
        if (CollectionUtils.isEmpty(monthPlanVOPage.getRecords())) {
            return monthPlanVOPage;
        }

        List<Integer> scheduleIds = monthPlanVOPage.getRecords().stream().map(MonthPlanVO::getSchedulingId).collect(Collectors.toList());
        Map<Integer, Scheduling> codeMap = schedulingMapper.selectBatchIds(scheduleIds).stream()
                .collect(Collectors.toMap(Scheduling::getId, v -> v));
        monthPlanVOPage.getRecords().forEach(record -> {
            Scheduling scheduling = Optional.ofNullable(codeMap.get(record.getSchedulingId()))
                    .orElseThrow(() -> new BaseKnownException(823001, "未查到相关排程信息"));
            record.setSchedulingCode(scheduling.getCode());
            //record.setZenIds(scheduling.getZenIds());
            //处理基酒产出
            QueryWineQualityDTO queryWineQualityDTO = new QueryWineQualityDTO();
            queryWineQualityDTO.setCenterIds(new ArrayList<Integer>() {{
                add(record.getCenterId());
            }});
            queryWineQualityDTO.setLocationIds(new ArrayList<Integer>() {{
                add(record.getLocationId());
            }});
            queryWineQualityDTO.setStartDate(record.getMonthBeginDate());
            queryWineQualityDTO.setEndDate(record.getMonthEndDate());

            ResultVO<List<GetWineQualityVO>> resultVO1 = mkwineClient.getWineQualityData(queryWineQualityDTO);
            if(resultVO1.getCode()!=200){
                throw new BaseKnownException(resultVO1.getCode(),resultVO1.getMessage());
            }
            Optional.ofNullable(resultVO1.getData())
                    .filter(it -> !it.isEmpty() && 1 == it.size())
                    .ifPresent(it -> record.setActualWinOutPut(BigDecimal.valueOf(it.get(0).getTo60Quantity())));
        });
        return monthPlanVOPage;
    }

    // 2022/3/31 实际投耗产

    @GetMapping("/material")
    @ApiOperation(value = "查询月度计划-物料查看")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public MonthPlanMaterialTotalVO queryMaterialList(@RequestParam("schedulingId") Integer schedulingId,
                                                      @RequestParam("year") Integer year,
                                                      @RequestParam("month") Integer month,
                                                      @RequestParam("centerId") Integer centerId,
                                                      @RequestParam(value = "locationId", required = false) Integer locationId) {

        return baseService.queryMaterialList(schedulingId,year,month,centerId,locationId);
//        Scheduling scheduling = schedulingService.getById(schedulingId);
//
//        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
//                .eq(MonthPlan::getPlanTime, scheduling.getPlanTime())
//                .eq(MonthPlan::getCenterId, centerId)
//                .eq(Objects.nonNull(locationId), MonthPlan::getLocationId, locationId)
//                .eq(MonthPlan::getYear, year)
//                .eq(MonthPlan::getMonth, month)
//                .in(MonthPlan::getPlanState, Lists.newArrayList(PlanStateEnum.NEW.getCode(), PlanStateEnum.IMPLEMENT.getCode(), PlanStateEnum.FINISH.getCode()));
//
//        List<MonthPlan> list = baseService.list(wrapper);
//        List<Integer> monthPlanIdList = list.stream().map(MonthPlan::getId).collect(Collectors.toList());
//
//        List<MonthPlanDetail> monthPlanDetailList = monthPlanDetailService.list(new LambdaQueryWrapper<MonthPlanDetail>()
//                .in(MonthPlanDetail::getMonthPlanId, monthPlanIdList));
//
//        List<MonthPlanVinasse> monthPlanVinasseList = monthPlanVinasseService.list(new LambdaQueryWrapper<MonthPlanVinasse>()
//                .in(MonthPlanVinasse::getMonthPlanId, monthPlanIdList));
//
//        MonthPlanMaterialTotalVO monthPlanMaterialTotalVO = new MonthPlanMaterialTotalVO();
//
//        // 自然月
//        List<MonthPlanMaterialVO> monthPlanMaterialList = baseWrapper.convertToList(list, MonthPlanMaterialVO.class);
//        monthPlanMaterialList.forEach(it -> {
//            List<MonthPlanDetail> collect = monthPlanDetailList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
//                    .filter(ii -> Objects.equals(ii.getBillType(), "0"))
//                    .peek(ii -> ii.setTotalQuality(ii.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
//                    .sorted(Comparator.comparing(MonthPlanDetail::getType))
//                    .collect(Collectors.toList());
//            it.setMonthPlanDetailVOS(baseWrapper.convertToList(collect, MonthPlanDetailVO.class));
//
//            List<MonthPlanVinasse> collect1 = monthPlanVinasseList.stream()
//                    .filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
//                    .filter(ii -> Objects.equals(ii.getBillType(), "0"))
//                    .peek(ii -> ii.setVinasseData(ii.getVinasseData().setScale(0, RoundingMode.HALF_UP)))
//                    .collect(Collectors.toList());
//            it.setMonthPlanVinasseVOS(baseWrapper.convertToList(collect1, MonthPlanVinasseVO.class));
//        });
//
//        // 扎账期
//        List<MonthPlanMaterialVO> billMaterialList = baseWrapper.convertToList(list, MonthPlanMaterialVO.class);
//        billMaterialList.forEach(it -> {
//            List<MonthPlanDetail> collect = monthPlanDetailList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
//                    .filter(ii -> Objects.equals(ii.getBillType(), "1"))
//                    .peek(ii -> ii.setTotalQuality(ii.getTotalQuality().setScale(0, RoundingMode.HALF_UP)))
//                    .sorted(Comparator.comparing(MonthPlanDetail::getType))
//                    .collect(Collectors.toList());
//            it.setMonthPlanDetailVOS(baseWrapper.convertToList(collect, MonthPlanDetailVO.class));
//
//            List<MonthPlanVinasse> collect1 = monthPlanVinasseList.stream()
//                    .filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
//                    .filter(ii -> Objects.equals(ii.getBillType(), "1"))
//                    .peek(ii -> ii.setVinasseData(ii.getVinasseData().setScale(0, RoundingMode.HALF_UP)))
//                    .collect(Collectors.toList());
//            it.setMonthPlanVinasseVOS(baseWrapper.convertToList(collect1, MonthPlanVinasseVO.class));
//        });
//        monthPlanMaterialTotalVO.setMonthPlanMaterialList(monthPlanMaterialList);
//        monthPlanMaterialTotalVO.setBillMaterialList(billMaterialList);
//
//        return monthPlanMaterialTotalVO;
    }

    /**
     * 月计划下发
     *
     * @param planIdList 月计划id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/31 14:49
     */
    @PostMapping("/issue")
    @ApiOperation(value = "月计划下发")
    public String issue(@RequestBody List<Integer> planIdList) {
        baseService.issue(planIdList);

        return ApiMessage.UPDATE_SUCCESS;
    }

    /**
     * 月计划变更获取排程第一步数据
     *
     * @return com.hvisions.brewage.plan.vo.SchedulingFirstStepVo
     * <AUTHOR>
     * @date 2022/4/7 10:29
     */
    @PostMapping("/firstStep/{id}")
    @ApiOperation(value = "月计划变更获取排程第一步数据")
    public SchedulingFirstStepVo firstStep(@PathVariable Integer id) {

        return baseService.firstStep(id);
    }

    /**
     * 变更创建排程
     *
     * @param req 月计划变更
     * <AUTHOR>
     * @date 2022/4/11 14:54
     */
    @PostMapping("/createScheduling")
    @ApiOperation(value = "月计划变更创建排程")
    public String createScheduling(@RequestBody MonthPlanCreateSchedulingReq req) {
        baseService.createScheduling(req);
        return ApiMessage.UPDATE_SUCCESS;
    }

    /**
     * 变更计划状态-8中心MES
     *
     * @param req 变更计划状态-8中心MES
     * <AUTHOR>
     * @date 2025/9/11 14:54
     */
    @PostMapping("/updateStatus")
    @ApiOperation(value = "变更计划状态")
    public String updateStatus(@RequestBody MonthPlanUpdateStatusReq req) {
        baseService.updateStatus(req);
        return ApiMessage.UPDATE_SUCCESS;
    }

    /**
     * 基酒产出反馈-8中心MES
     *
     * @param req 基酒产出反馈-8中心MES
     * <AUTHOR>
     * @date 2025/9/11 14:54
     */
    @PostMapping("/baseLiquorProduction")
    @ApiOperation(value = "基酒产出反馈")
    public String baseLiquorProduction(@RequestBody BaseLiquorProductionReq req) {
        baseService.baseLiquorProduction(req);
        return ApiMessage.OPERATE_SUCCESS;
    }

    /**
     * 生产物料投入反馈-8中心MES
     *
     * @param req 生产物料投入反馈-8中心MES
     * <AUTHOR>
     * @date 2025/9/11 14:54
     */
    @PostMapping("/productionMaterialInput")
    @ApiOperation(value = "生产物料投入反馈")
    public String productionMaterialInput(@RequestBody ProductionMaterialInputReq req) {
        baseService.productionMaterialInput(req);
        return ApiMessage.OPERATE_SUCCESS;
    }

    /**
     * 排程拆分月度计划
     *
     * @param scheduleId 排程 ID
     * <AUTHOR>
     * @date 2022/3/30 10:16
     */
    @PostMapping("/splitMonthPlan/{scheduleId}")
    @ApiOperation(value = "排程拆分月度计划")
    public void splitMonthPlan(@PathVariable Integer scheduleId) {
        baseService.splitMonthPlan(scheduleId,0);
    }

    /**
     * 实际投/耗/产模版下载
     *
     * @return com.hvisions.common.dto.ExcelExportDto
     * <AUTHOR>
     * @date 2022/4/7 10:30
     */
    @PostMapping("/getImportTemplate")
    @ApiOperation(value = "实际投/耗/产模版下载")
    public ExcelExportDto getImportTemplate() {
        return EasyExcelUtil.getExcel(new ArrayList<>(), MonthActualDataExcel.class, "模版下载.xlsx");
    }

    /**
     * 导入实际投/耗/产
     *
     * @param file 文件
     */
    @PostMapping("/importActualData")
    @ApiOperation(value = "导入实际投/耗/产")
    public String importActualData(MultipartFile file) {
        baseService.importActualData(file);

        return ApiMessage.IMPORT_SUCCESS;
    }

    @GetMapping("/getMonthArrangeInfo")
    @ApiOperation("查询月计划")
    public SchedulingArrangeByRowVO getMonthArrangeInfo(MonthPlanReq monthPlanReq) {
        return this.baseService.getMonthArrangeInfo(monthPlanReq);
    }

    /**
     * 采购计划对接月计划
     *
     * @return java.util.List<com.hvisions.brewage.dto.plan.dto.PurchasePlanMaterialDTO>
     * <AUTHOR>
     * @date 2022/4/12 15:38
     */
    @PostMapping("/purchasePlan")
    @ApiOperation(value = "采购计划对接月计划")
    public List<PurchasePlanMaterialDTO> purchasePlan(@RequestBody PurchasePlanReq req) {
        return baseService.purchasePlan(req);
    }


    /**
     *
     * 日计划查看生产情况接口
     * @param req
     * @return java.util.List<com.hvisions.brewage.plan.vo.MonthPlanMaterialVO>
     * <AUTHOR>
     * @date 2022/4/19 15:27
     *
     */
    @PostMapping("/monthPlanMaterial")
    @ApiOperation(value = "日计划查看生产情况接口")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public List<MonthPlanMaterialVO> queryMaterialList(@RequestBody MonthPlanMaterialReq req) {
        LambdaQueryWrapper<MonthPlan> wrapper = new LambdaQueryWrapper<MonthPlan>()
                .eq(MonthPlan::getCenterId, req.getCenterId())
                .eq(MonthPlan::getLocationId, req.getLocationId())
                .le(MonthPlan::getMonthBeginDate, req.getPlanTime())
                .ge(MonthPlan::getMonthEndDate, req.getPlanTime())
                .in(MonthPlan::getPlanState, Lists.newArrayList(PlanStateEnum.NEW.getCode(), PlanStateEnum.IMPLEMENT.getCode(), PlanStateEnum.FINISH.getCode()));

        List<MonthPlan> list = baseService.list(wrapper);
        List<Integer> monthPlanIdList = list.stream().map(MonthPlan::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(monthPlanIdList)) {
            return new ArrayList<>();
        }
        List<MonthPlanDetail> monthPlanDetailList = monthPlanDetailService.list(new LambdaQueryWrapper<MonthPlanDetail>()
                .in(MonthPlanDetail::getMonthPlanId, monthPlanIdList));

        List<MonthPlanVinasse> monthPlanVinasseList = monthPlanVinasseService.list(new LambdaQueryWrapper<MonthPlanVinasse>()
                .in(MonthPlanVinasse::getMonthPlanId, monthPlanIdList));

        // 自然月
        List<MonthPlanMaterialVO> monthPlanMaterialList = baseWrapper.convertToList(list, MonthPlanMaterialVO.class);
        monthPlanMaterialList.forEach(it -> {
            List<MonthPlanDetail> collect = monthPlanDetailList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "0"))
                    .sorted(Comparator.comparing(MonthPlanDetail::getType))
                    .collect(Collectors.toList());
            it.setMonthPlanDetailVOS(baseWrapper.convertToList(collect, MonthPlanDetailVO.class));

            List<MonthPlanVinasse> collect1 = monthPlanVinasseList.stream().filter(ii -> Objects.equals(ii.getMonthPlanId(), it.getId()))
                    .filter(ii -> Objects.equals(ii.getBillType(), "0")).collect(Collectors.toList());
            it.setMonthPlanVinasseVOS(baseWrapper.convertToList(collect1, MonthPlanVinasseVO.class));
        });

        return monthPlanMaterialList;
    }

    @GetMapping("/rowByDay")
    @ApiOperation(value = "根据日期和车间查找排次ID")
    public Integer getRowIdByDay(@RequestParam Integer locationId, @RequestParam String day) {
        return baseService.getRowIdByDay(locationId, day);
    }

    @GetMapping("/vinasseQuantity/{vinasseCode}")
    @ApiOperation(value = "根据日期，中心，车间，糟源代码查询产出量")
    public BigDecimal getVinasseQuantity(@RequestParam Integer centerId,
                                         @RequestParam Integer locationId,
                                         @PathVariable String vinasseCode,
                                         @RequestParam String startDate,
                                         @RequestParam String endDate) {
        return baseService.getVinasseQuantity(centerId, locationId, vinasseCode, startDate, endDate);
    }

    @GetMapping("/rows")
    @ApiOperation(value = "根据中心，车间，排程年份查询排次")
    public List<RowVO> listRowByYear(@RequestParam Integer centerId,
                                     @RequestParam Integer locationId,
                                     @RequestParam Integer year) {
        return baseService.listRowByYear(centerId, locationId, year);
    }

    @GetMapping("/new/rows")
    @ApiOperation(value = "根据中心，车间，排程年份查询排次")
    public List<RowVO> newListRowByYear(@RequestParam Integer centerId,
                                        @RequestParam Integer locationId,
                                        @RequestParam Integer year) {
        return baseService.newListRowByYear(centerId, locationId, year);
    }

    /**
     * 产量及计划完成率
     *
     * @param req
     * @return
     */
    @GetMapping("/production_plan_completion_rate")
    @ApiOperation(value = "产量及计划完成率")
    public List<ProductionPlanCompletionRateVO> getProductionPlanCompletionRate(@RequestBody ProductionPlanCompletionRateReq req){
        return baseService.getProductionPlanCompletionRate(req);
    }

    /**
     * 产量完成情况
     *
     * @param req
     * @return
     */
    @GetMapping("/production_completion")
    @ApiOperation(value = "产量及计划完成率")
    public List<ProductionCompletionVO> getProductionCompletion(@RequestBody ProductionCompletionReq req){
       return baseService.getProductionCompletion(req);
    }

    /**
     * 产量完成情况范围
     *
     * @param req
     * @return
     */
    @GetMapping("/production_completion_range")
    @ApiOperation(value = "产量及计划完成率范围")
    public void getProductionCompletionRange(@RequestBody ProductionCompletionReq req) {
        baseService.getProductionCompletionRange(req);
    }

    @GetMapping("/history/{id}")
    @ApiOperation("月计划查询变更记录")
    public List<Integer> getHistoryScheduling(@PathVariable(value = "id") Integer monthPlanId) {
        return baseService.getHistoryScheduling(monthPlanId);
    }

    @ApiOperation(value = "查询物料需求")
    @RequestMapping(value = "/getMaterialRequirement" , method = RequestMethod.POST)
    public List<MaterialRequirementVO> getMaterialRequirement(@RequestBody MaterialRequirementQueryDTO queryDTO){
        return baseService.getMaterialRequirement(queryDTO);
    }
}
