/**
 * Copyright (c) 2018-2028, Ce<PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.controller;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.plan.advice.UserAuditorAware;
import com.hvisions.plan.consts.ApiMessage;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.consts.ValidateType;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.dto.FormulaQueryReq;
import com.hvisions.plan.dto.FormulaReq;
import com.hvisions.plan.dto.PreCheckResp;
import com.hvisions.plan.entity.Formula;
import com.hvisions.plan.service.IFormulaService;
import com.hvisions.plan.vo.FormulaAllInfo;
import com.hvisions.plan.vo.FormulaDetailVO;
import com.hvisions.plan.vo.FormulaVO;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 糟源配方管理相关接口
 *
 * <AUTHOR>
 * 2022-03-23
 */
@RestController
@RequestMapping("/system/Formula")
@Api(value = "糟源配方管理相关接口", tags = "糟源配方管理相关接口")
public class FormulaController {

    @Autowired
    IFormulaService baseService;
    @Autowired
    BaseWrapper baseWrapper;
    @Autowired
    UserAuditorAware userAuditorAware;

    /**
     * 查询分页糟源配方
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页糟源配方")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<FormulaVO> queryPagedFormula(FormulaQueryReq req, PageInfo pageInfo) {
        //预查询  带上中心信息
        List<Integer> formulaByCenter = baseService.listFormulaByCenter(req);
        LambdaQueryWrapper<Formula> wrapper = new LambdaQueryWrapper<Formula>()
                .eq(Objects.nonNull(req.getProductionBaseId()), Formula::getProductionBaseId, req.getProductionBaseId())
                .like(Strings.isNotBlank(req.getFormulaName()), Formula::getName, req.getFormulaName())
                .like(Strings.isNotBlank(req.getSourceCode()), Formula::getSourceCode, req.getSourceCode())
                .eq(Objects.nonNull(req.getFormulaState()), Formula::getState, req.getFormulaState())
                .ne(Objects.isNull(req.getFormulaState()), Formula::getState, StateEnum.FILE.getCode())
                .in(!CollectionUtils.isEmpty(formulaByCenter), Formula::getId, formulaByCenter)
                .orderByAsc(Formula::getState,
                        Formula::getProductionBaseId, Formula::getSourceCode);
        IPage<Formula> page = baseService.page(new Page<>(pageInfo.getPage(), pageInfo.getPageSize()), wrapper);
        Page<FormulaVO> formulaVOPage = baseWrapper.convertToPage(page, FormulaVO.class);
        List<Integer> userIds = formulaVOPage.getRecords().stream().map(FormulaVO::getCreatorId).collect(Collectors.toList());
        Map<Integer, String> userNameMap = userAuditorAware.getUserNameMap(userIds);
        formulaVOPage.getRecords().forEach(it -> {
            it.setCreatorName(userNameMap.getOrDefault(it.getCreatorId(), ""));
        });
        return formulaVOPage;
    }

    /**
     * 新增糟源配方
     *
     * @param req
     * @return
     */
    @PostMapping("")
    @ApiOperation(value = "新增糟源配方")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String add(@RequestBody @Validated({Default.class, ValidateType.Add.class}) FormulaReq req) {
        Formula entity = CopyUtil.simpleCopy(req, Formula.class);
        entity.setState(StateEnum.NEW.getCode());
        String centers = Arrays.stream(req.getCenters())
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        entity.setCenters(centers);
        baseService.save(entity);
        return ApiMessage.ADD_SUCCESS;
    }

    /**
     * 获取单个糟源配方信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个糟源配方信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public FormulaVO get(@PathVariable Integer id) {
        Formula entity = baseService.getById(id);
        FormulaVO formulaVO = CopyUtil.simpleCopy(entity, FormulaVO.class);
        return formulaVO;
    }

    /**
     * 修改糟源配方
     *
     * @param req
     * @return
     */
    @PutMapping("")
    @ApiOperation(value = "修改糟源配方")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String update(@RequestBody @Validated({Default.class, ValidateType.Update.class}) FormulaReq req) {
        Formula entity = CopyUtil.simpleCopy(req, Formula.class);
        String centers = Arrays.stream(req.getCenters())
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        entity.setCenters(centers);
        baseService.updateById(entity);
        return ApiMessage.UPDATE_SUCCESS;
    }

    /**
     * 删除糟源配方
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除糟源配方")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String delete(@PathVariable Integer id) {
        //生效配方不允许删除
        Formula formula = baseService.getById(id);
        Assert.isTrue(StateEnum.NEW.getCode().equals(formula.getState()), "当前记录非生效状态，不允许存档操作");
        baseService.removeById(id);
        return ApiMessage.DELETE_SUCCESS;
    }

    /**
     * 生效/归档
     *
     * @return
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "生效/归档")
    public String changeRowState(@PathVariable Integer id, @RequestParam Integer state) {
        switch (state) {
            case 1:
                //生效
                baseService.validData(id);
                return ApiMessage.OPERATE_SUCCESS;
            case 2:
                //归档
                baseService.invalidData(id);
                return ApiMessage.OPERATE_SUCCESS;
            default:
                throw new BaseKnownException(323001, "非法状态");
        }
    }

    @GetMapping("/formulaDetailList")
    @ApiOperation("查询配方详情")
    public List<FormulaDetailVO> getFormulaDetails(@RequestParam Integer formulaId) {
        return baseService.getFormulaDetailsByFormulaId(formulaId);
    }

    @PostMapping("/copy")
    @ApiOperation("复制")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String copy(@RequestBody @Validated({Default.class, ValidateType.Add.class}) FormulaReq req) {
        baseService.copy(req);
        return ApiMessage.OPERATE_SUCCESS;
    }

    @GetMapping("/allFormula")
    @ApiOperation(("获取所有的配方"))
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public List<FormulaAllInfo> allFormula() {
        return baseService.listAllFormulaInfo();
    }

    /**
     * @Des 获取生效的配方
     * <AUTHOR>
     * @Date 2022/6/15 17:51:36
     * @Param
     * @Return
     */
    @GetMapping("/getFormulaByWorking")
    @ApiOperation(("获取所有生效的配方"))
    public List<Formula> getFormulaByWorking() {
        return baseService.getFormulaByWorking();
    }

    @GetMapping("/getFormulaByWorkingByCenterId/{centerId}")
    //@ApiOperation(value = "通过中心id获取所有的配方")
    @ApiOperation(value = "通过中心id获取所有有效的配方")
    public List<Formula> getFormulaByWorkingByCenterId(@ApiParam(value = "中心id") @PathVariable Integer centerId) {
        //return baseService.getFormulaByWorkingByCenterId(centerId);
        return baseService.getValidFormulaByWorkingByCenterId(centerId);
    }


    /**
     * 校验相同基地  相同糟源  相同日期段    中心是否偏少
     *
     * @param formulaId
     * @return
     */
    @GetMapping("/check/{id}")
    @ApiOperation("校验相同基地  相同糟源  相同日期段    中心是否偏少")
    public PreCheckResp checkSameVinasseAndPeriod(@PathVariable("id") Integer formulaId) {
        return baseService.checkSameVinasseAndPeriod(formulaId);
    }
}