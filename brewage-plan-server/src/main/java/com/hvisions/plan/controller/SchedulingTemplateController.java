/**
 * Copyright (c) 2018-2028, <PERSON><PERSON> 孙岑 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hvisions.plan.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.plan.consts.ApiMessage;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.consts.ValidateType;
import com.hvisions.plan.dto.SchedulingQueryReq;
import com.hvisions.plan.dto.SchedulingReq;
import com.hvisions.plan.dto.SchedulingTemplateQueryReq;
import com.hvisions.plan.dto.SchedulingTemplateReq;
import com.hvisions.plan.entity.SchedulingTemplate;
import com.hvisions.plan.enums.PlanStateEnum;
import com.hvisions.plan.service.ISchedulingTemplateService;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.plan.vo.SchedulingTemplateDetailVO;
import com.hvisions.plan.vo.SchedulingTemplateVO;
import com.hvisions.plan.vo.SchedulingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;

/**
 * 排程模版管理相关接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/system/SchedulingTemplate")
@Api(value = "排程模版管理相关接口", tags = "排程模版管理相关接口")
public class SchedulingTemplateController {

    @Autowired
    ISchedulingTemplateService baseService;
    @Autowired
    BaseWrapper baseWrapper;

    /**
     * 查询分页排程模版
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页排程模版")
//    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<SchedulingTemplateVO> queryPagedScheduling(SchedulingTemplateQueryReq req, PageInfo pageInfo) {
        return baseService.pageSchedule(req, pageInfo);
    }

    /**
     * 新增排程模版
     *
     * @param req
     * @return
     */
    @PostMapping("")
    @ApiOperation(value = "新增排程模版")
//    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Integer add(@RequestBody @Validated({Default.class, ValidateType.Add.class}) SchedulingTemplateReq req) {
        return baseService.handleNewSchedule(req);
    }

    /**
     * 获取单个排程模版信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个排程模版信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public SchedulingTemplateDetailVO get(@PathVariable Integer id) {
        //SchedulingTemplate entity = baseService.getById(id);
        return baseService.detail(id);
    }

    /**
     * 修改排程模版
     *
     * @param req
     * @return
     */
    @PutMapping("")
    @ApiOperation(value = "修改排程模版")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Integer update(@RequestBody @Validated({Default.class, ValidateType.Update.class}) SchedulingTemplateReq req) {
        return baseService.updateSchedule(req);
    }


    /**
     * 删除排程模版
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除排程模版")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String delete(@PathVariable Integer id) {
//        SchedulingTemplate byId = baseService.getById(id);
//        Assert.isTrue(PlanStateEnum.NEW.getCode().equals(byId.getState()), "当前计划非新建状态，不允许进行删除操作");
//        baseService.removeById(id);
        return baseService.delete(id);
    }

    /**
     * 生效/归档
     *
     * @return
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "生效/归档")
    public String changeRowState(@PathVariable Integer id, @RequestParam Integer state) {
        switch (state) {
            case 1:
                //生效
                baseService.validData(id);
                return ApiMessage.OPERATE_SUCCESS;
            case 2:
                //归档
                baseService.invalidData(id);
                return ApiMessage.OPERATE_SUCCESS;
            default:
                throw new BaseKnownException(323001, "非法状态");
        }
    }

    /**
     * 复制排程模版
     *
     * @param id 配方ID
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/24 16:48
     */
    @PutMapping("/copy/{id}")
    @ApiOperation(value = "复制")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String copy(@PathVariable Integer id) {
        baseService.copyInfo(id);
        return ApiMessage.COPY_SUCCESS;
    }

}