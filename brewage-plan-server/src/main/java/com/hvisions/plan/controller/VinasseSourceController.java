package com.hvisions.plan.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.plan.consts.ApiMessage;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.consts.ValidateType;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.dto.VinasseSourceQueryReq;
import com.hvisions.plan.dto.VinasseSourceReq;
import com.hvisions.plan.service.IFormulaService;
import com.hvisions.plan.service.IRetortContainerService;
import com.hvisions.plan.service.ISchedulingTemplateService;
import com.hvisions.plan.service.IVinasseSourceService;
import com.hvisions.plan.vo.VinasseSourceVO;
import com.hvisions.plan.utils.BaseWrapper;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;
import java.util.List;
import java.util.Objects;

/**
 * 糟源信息管理相关接口
 *
 * <AUTHOR>
 * 2022-03-23
 */
@RestController
@RequestMapping("/system/VinasseSource")
@Api(value = "糟源信息管理相关接口", tags = "糟源信息管理相关接口")
public class VinasseSourceController {

    @Autowired
    IVinasseSourceService baseService;
    @Autowired
    IFormulaService formulaService;
    @Autowired
    IRetortContainerService retortContainerService;
    @Autowired
    ISchedulingTemplateService schedulingTemplateService;
    @Autowired
    BaseWrapper baseWrapper;

    /**
     * 查询分页糟源信息
     *
     * @param req      查询条件
     * @param pageInfo 分页对象
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页糟源信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public Page<VinasseSourceVO> queryPagedVinasseSource(VinasseSourceQueryReq req, PageInfo pageInfo) {
        LambdaQueryWrapper<VinasseSource> wrapper = new LambdaQueryWrapper<VinasseSource>()
                .eq(Objects.nonNull(req.getProductionBaseId()), VinasseSource::getProductionBaseId, req.getProductionBaseId())
                .like(StringUtils.isNotBlank(req.getCode()), VinasseSource::getCode, req.getCode())
                .like(StringUtils.isNotBlank(req.getName()), VinasseSource::getName, req.getName())
                .ne(Objects.isNull(req.getState()), VinasseSource::getState, StateEnum.FILE.getCode())
                .eq(StringUtils.isNotBlank(req.getState()), VinasseSource::getState, req.getState())
                .like(StringUtils.isNotBlank(req.getTypeCode()), VinasseSource::getTypeCode, req.getTypeCode())
                .between(Objects.nonNull(req.getBeginDate()) && Objects.nonNull(req.getEndDate()), VinasseSource::getCreateTime, req.getBeginDate(), req.getEndDate())
                .orderByDesc(VinasseSource::getProductionBaseId)
                .orderByDesc(VinasseSource::getCreateTime);
        IPage<VinasseSource> page = baseService.page(new Page<>(pageInfo.getPage(), pageInfo.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, VinasseSourceVO.class);
    }

    /**
     * 查询分页糟源信息
     *
     * @param req 查询条件
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询糟源信息列表")
    public List<VinasseSourceVO> queryListVinasseSource(VinasseSourceQueryReq req) {
        req.setProductionBaseId(1);
        LambdaQueryWrapper<VinasseSource> wrapper = new LambdaQueryWrapper<VinasseSource>()
                .eq(Objects.nonNull(req.getProductionBaseId()), VinasseSource::getProductionBaseId, req.getProductionBaseId())
                .like(StringUtils.isNotBlank(req.getCode()), VinasseSource::getCode, req.getCode())
                .like(StringUtils.isNotBlank(req.getName()), VinasseSource::getName, req.getName())
                .eq(VinasseSource::getState, StateEnum.TAKE_EFFECT.getCode())
                .like(StringUtils.isNotBlank(req.getTypeCode()), VinasseSource::getTypeCode, req.getTypeCode())
                .orderByDesc(VinasseSource::getProductionBaseId)
                .orderByDesc(VinasseSource::getCreateTime);
        List<VinasseSource> page = baseService.list(wrapper);
        return baseWrapper.convertToList(page, VinasseSourceVO.class);
    }

    /**
     * 新增糟源信息
     *
     * @param req
     * @return
     */
    @PostMapping("")
    @ApiOperation(value = "新增糟源信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String add(@RequestBody @Validated({Default.class, ValidateType.Add.class}) VinasseSourceReq req) {
        VinasseSource entity = CopyUtil.simpleCopy(req, VinasseSource.class);

        // 生产基地+糟源代码唯一校验，有则更新，无则新增
        LambdaQueryWrapper<VinasseSource> wrapper = new LambdaQueryWrapper<VinasseSource>()
                .eq(VinasseSource::getProductionBaseId, req.getProductionBaseId())
                .eq(VinasseSource::getCode, req.getCode());
        VinasseSource vinasseSource = baseService.getOne(wrapper);
        if (Objects.nonNull(vinasseSource)) {
            entity.setId(vinasseSource.getId());
        }
        entity.setState(StateEnum.TAKE_EFFECT.getCode());
        baseService.save(entity);
        return ApiMessage.ADD_SUCCESS;
    }

    /**
     * 获取单个糟源信息信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个糟源信息信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public VinasseSourceVO get(@PathVariable Integer id) {
        VinasseSource entity = baseService.getById(id);
        return CopyUtil.simpleCopy(entity, VinasseSourceVO.class);
    }

    /**
     * 修改糟源信息
     *
     * @param req
     * @return
     */
    @PutMapping("")
    @ApiOperation(value = "修改糟源信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String update(@RequestBody @Validated({Default.class, ValidateType.Update.class}) VinasseSourceReq req) {
        VinasseSource vinasseSource = baseService.getById(req.getId());
        if (formulaService.count(new LambdaQueryWrapper<Formula>()
                .eq(Formula::getSourceCode, vinasseSource.getCode())
                .eq(Formula::getState, StateEnum.TAKE_EFFECT.getCode())) > 0) {
            throw new BaseKnownException(10000, "该糟源已经被配方应用，不允许修改");
        }

        VinasseSource entity = CopyUtil.simpleCopy(req, VinasseSource.class);
        baseService.updateById(entity);
        return ApiMessage.UPDATE_SUCCESS;
    }

    /**
     * 归档糟源信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "归档糟源信息")
    @ApiImplicitParam(name = "token", value = "Token", paramType = CommonConsts.ParamType.HEADER, required = true)
    public String delete(@PathVariable Integer id) {

        VinasseSource vinasseSource = baseService.getById(id);
        //校验糟源配方新增、生效状态
        if (formulaService.count(new LambdaQueryWrapper<Formula>()
                .eq(Formula::getSourceId, vinasseSource.getId())
                        .eq(Formula::getDeleted,0)
                .and(wq -> wq
                        .eq(Formula::getState, StateEnum.NEW.getCode())
                        .or()
                        .eq(Formula::getState, StateEnum.TAKE_EFFECT.getCode())
                )) > 0) {
            throw new BaseKnownException(10000, "存在新增或已生效的糟源配方中含有该糟源代码,不允许归档该糟源代码");
        }
        //校验生产甑口新增、生效状态
        if(retortContainerService.count(new LambdaQueryWrapper<RetortContainer>()
                .eq(RetortContainer::getSourceCode, vinasseSource.getCode())
                .eq(RetortContainer::getDeleted,0)
                .eq(RetortContainer::getProductionBaseId,vinasseSource.getProductionBaseId())
                .and(wq -> wq
                        .eq(RetortContainer::getState, StateEnum.NEW.getCode())
                        .or()
                        .eq(RetortContainer::getState, StateEnum.TAKE_EFFECT.getCode())
                )) > 0){
            throw new BaseKnownException(10000, "存在新增或已生效的生产甑口中含有该糟源代码,不允许归档该糟源代码");
        }
        //校验排程模板新增、生效状态
//        if(schedulingTemplateService.count(new LambdaQueryWrapper<SchedulingTemplate>()
//                .eq(SchedulingTemplate::getDeleted,0)
//                .apply( "FIND_IN_SET ('" + vinasseSource.getId() + "',vinasse_type)")
//                .and(wq -> wq
//                        .eq(SchedulingTemplate::getState, StateEnum.NEW.getCode())
//                        .or()
//                        .eq(SchedulingTemplate::getState, StateEnum.TAKE_EFFECT.getCode())
//                )) > 0){
//            throw new BaseKnownException(10000, "存在新增或已生效的排程模板中含有该糟源代码,不允许归档该糟源代码");
//        }
        //校验生产模式新增、生效状态

        vinasseSource.setState(StateEnum.FILE.getCode());
        baseService.updateById(vinasseSource);
        return ApiMessage.OPERATE_SUCCESS;
    }

    /**
     * 生效/归档
     *
     * @return
     */
//    @PutMapping("/{id}")
//    @ApiOperation(value = "生效/归档")
//    public String changeRowState(@PathVariable Integer id, @RequestParam Integer state) {
//        switch (state) {
//            case 1:
//                //生效
//                baseService.validData(id);
//                return ApiMessage.OPERATE_SUCCESS;
//            case 2:
//                //归档
//                baseService.invalidData(id);
//                return ApiMessage.OPERATE_SUCCESS;
//            default:
//                throw new BaseKnownException(323001, "非法状态");
//        }
//    }

}