<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.sorghum.SorghumInboundTaskDetailMapper">

    <select id="findByTaskId" resultType="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDetailDTO">
        SELECT
           *
        FROM
        t_mpd_sorghum_inbound_task_detail t
        WHERE
            t.task_id = #{parentId}
            AND t.deleted = 0
    </select>

    <select id="selectByMaterialCodeAndTaskNoAndTime" resultType="com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTaskDetail">
        SELECT
            *
        FROM t_mpd_sorghum_inbound_task_detail
        WHERE deleted = 0
            AND material_code = #{materialCode}
            AND control_task_no = #{controlTaskNo}
            AND DATE_FORMAT(actual_start_time, '%Y%m%d') = #{actualStartTime}
        LIMIT 1
    </select>

    <select id="selectByControlTaskNo" resultType="com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTaskDetail">
        SELECT
            *
        FROM t_mpd_sorghum_inbound_task_detail
        WHERE deleted = 0
            AND control_task_no = #{controlTaskNo}
        LIMIT 1
    </select>

    <select id="selectSummaryByOrderId" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(quantity), 0) as quantity
        FROM t_mpd_sorghum_inbound_task_detail
        WHERE deleted = 0
            AND task_id = #{orderId}
    </select>
</mapper>