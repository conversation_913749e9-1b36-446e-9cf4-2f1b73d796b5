<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumShipmentOrderMapper">

    <select id="getSorghumShipmentOrderPageList_old" resultMap="orderMap_old">
        SELECT sso.*,pt.`name` production_type_name,p.plan_no,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.send_warehouse_id)  send_warehouse,
        (SELECT w.`name` FROM t_mpd_warehouse w WHERE w.id = sso.accept_warehouse_id)  accept_warehouse

        FROM `t_mpd_sorghum_shipment_order` sso
        LEFT JOIN t_mpd_sorghum_shipment_plan p ON p.`id` = sso.plan_id AND p.deleted = 0
        LEFT JOIN t_mpd_production_type pt ON pt.`code` = sso.production_type_code AND pt.deleted = 0

        WHERE sso.deleted = 0
        <if test="materialCode != null and materialCode != ''">
            AND sso.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND sso.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="centerId != null">
            AND sso.`center_id` = #{centerId}
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(sso.order_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY sso.order_date DESC,sso.id DESC
    </select>
    <resultMap id="orderMap_old" type="com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO">
        <id column="id" property="id"/>
        <result property="planId" column="plan_id"/>
        <result property="planNo" column="plan_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="centerCode" column="center"/>
        <result property="orderDate" column="order_date"/>
        <result property="productionTypeCode" column="production_type_code"/>
        <result property="productionTypeName" column="production_type_name"/>
        <result property="sendWarehouseId" column="send_warehouse_id"/>
        <result property="acceptWarehouseId" column="accept_warehouse_id"/>
        <result property="sendWarehouse" column="send_warehouse"/>
        <result property="acceptWarehouse" column="accept_warehouse"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="requirementQuantity" column="requirement_quantity"/>
        <result property="inventoryQuantity" column="inventory_quantity"/>
        <result property="planQuantity" column="plan_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="createTime" column="create_time"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail_old"
        >
        </collection>
    </resultMap>
    <select id="selectDetail_old" resultType="com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
	    (SELECT rl.`name` FROM t_mpd_rl_management rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_ss_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0 AND ssod.order_id = #{id}
    </select>




    <resultMap id="orderMap" type="com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO">
        <id column="id" property="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="centerCode" column="center"/>
        <result property="orderDate" column="order_date"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="unit" column="unit"/>
        <result property="requirementQuantity" column="requirement_quantity"/>
        <result property="inventoryQuantity" column="inventory_quantity"/>
        <result property="planQuantity" column="plan_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="createTime" column="create_time"/>
        <collection property="detailList"
                    column="id"
                    javaType="java.util.List" select="selectDetail"
        >
        </collection>
    </resultMap>
    <select id="getSorghumShipmentOrderPageList" resultMap="orderMap">
        SELECT sso.id,sso.order_no,sso.center,sso.order_date,sso.material_id,sso.material_code,sso.material_name,
        sso.unit,sso.requirement_quantity,sso.inventory_quantity,sso.plan_quantity,sso.actual_quantity,sso.create_time
        FROM `t_mpd_sorghum_shipment_order` sso
        WHERE sso.deleted = 0
        <if test="materialCode != null and materialCode != ''">
            AND sso.`material_code` LIKE concat('%',#{materialCode},'%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND sso.`material_name` LIKE concat('%',#{materialName},'%')
        </if>
        <if test="centerId != null">
            AND sso.`center_id` = #{centerId}
        </if>
        <if test="startDate != null and endDate != null">
            AND DATE_FORMAT(sso.order_date, '%Y-%m-%d') BETWEEN
            DATE_FORMAT(#{startDate}, '%Y-%m-%d') AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        </if>
        ORDER BY sso.order_date DESC,sso.id DESC
    </select>
    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_ss_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0 AND ssod.order_id = #{id}
    </select>

    <select id="getSorghumShipmentOrderDetailPageList"
            resultType="com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO">
        SELECT ssod.*,u.user_name create_name,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.send_silo_id AND rl.deleted = 0) sendSilo,
        (SELECT rl.`name` FROM t_mpd_material_silo rl WHERE rl.id = ssod.accept_silo_id AND rl.deleted = 0) acceptSilo
        FROM t_mpd_ss_order_detail ssod
        LEFT JOIN authority.sys_user u ON ssod.creator_id = u.id
        WHERE ssod.deleted = 0
          and ssod.flow_batch = #{batch}
        order by ssod.id desc
    </select>

    <!-- 根据工单日期查询当天的任务 -->
    <select id="selectByDateAndMaterialCode" resultType="com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder">
        SELECT *
        FROM t_mpd_sorghum_shipment_order
        WHERE deleted = 0
          AND DATE_FORMAT(order_date, '%Y-%m-%d') = DATE_FORMAT(#{orderDate}, '%Y-%m-%d')
          AND material_code = #{materialCode}
        LIMIT 1
    </select>

    <!-- 根据工单日期查询当天最大流水号 -->
    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT SUBSTRING(order_no, -3)
        FROM t_mpd_sorghum_shipment_order
        WHERE deleted = 0
          AND DATE_FORMAT(order_date, '%Y-%m-%d') = DATE_FORMAT(#{orderDate}, '%Y-%m-%d')
        ORDER BY order_no DESC
        LIMIT 1
    </select>
</mapper>
