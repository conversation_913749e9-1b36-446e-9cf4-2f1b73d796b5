<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.SsOrderDetailMapper">

    <select id="selectByMaterialCodeAndTaskNoAndTime" resultType="com.hvisions.rawmaterial.entity.TMpdSsOrderDetail">
        SELECT * FROM t_mpd_ss_order_detail
        WHERE deleted = 0
        AND material_code = #{materialCode}
        AND control_task_no = #{taskNo}
        AND DATE_FORMAT(actual_begin_time, '%Y-%m-%d') = DATE_FORMAT(#{startTime}, '%Y-%m-%d')
    </select>

    <select id="selectSummaryByOrderId" resultType="java.math.BigDecimal">
        SELECT SUM(actual_quantity) FROM t_mpd_ss_order_detail
        WHERE deleted = 0
        AND order_id = #{orderId}
    </select>
</mapper>