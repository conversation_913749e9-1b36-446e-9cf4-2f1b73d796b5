<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.sorghum.SorghumInboundTaskMapper">

    <!-- 优化后的结果映射，使用一次查询获取所有数据 -->
    <resultMap id="pageResultMap" type="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <collection property="detailList" ofType="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDetailDTO">
            <id column="detail_id" jdbcType="VARCHAR" property="id" />
            <result column="detail_task_id" jdbcType="VARCHAR" property="taskId" />
            <result column="detail_silo_name" jdbcType="VARCHAR" property="siloName" />
            <result column="detail_silo_code" jdbcType="VARCHAR" property="siloCode" />
            <result column="detail_material_name" jdbcType="VARCHAR" property="materialName" />
            <result column="detail_material_code" jdbcType="VARCHAR" property="materialCode" />
            <result column="detail_unit" jdbcType="VARCHAR" property="unit" />
            <result column="detail_quantity" jdbcType="DECIMAL" property="quantity" />
            <result column="detail_actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
            <result column="detail_actual_completion_time" jdbcType="TIMESTAMP" property="actualCompletionTime" />
            <result column="detail_status" jdbcType="INTEGER" property="status" />
            <result column="detail_creator_name" jdbcType="VARCHAR" property="creatorName" />
            <result column="detail_unique_id" jdbcType="VARCHAR" property="uniqueId" />
            <result column="detail_create_time" jdbcType="TIMESTAMP" property="createTime" />
            <result column="detail_update_time" jdbcType="TIMESTAMP" property="updateTime" />
        </collection>
    </resultMap>

    <select id="selectByCondition" resultMap="pageResultMap">
        SELECT
            sit.id,
            sit.order_no,
            sit.order_date,
            sit.material_name,
            sit.material_code,
            sit.quantity,
            sit.unit,
            sit.remark,
            sit.create_time,
            sit.update_time,
            -- 子表字段，使用别名避免冲突
            sitd.id as detail_id,
            sitd.task_id as detail_task_id,
            sitd.silo_name as detail_silo_name,
            sitd.silo_code as detail_silo_code,
            sitd.material_name as detail_material_name,
            sitd.material_code as detail_material_code,
            sitd.unit as detail_unit,
            sitd.quantity as detail_quantity,
            sitd.actual_start_time as detail_actual_start_time,
            sitd.actual_completion_time as detail_actual_completion_time,
            sitd.status as detail_status,
            sitd.creator_name as detail_creator_name,
            sitd.unique_id as detail_unique_id,
            sitd.create_time as detail_create_time,
            sitd.update_time as detail_update_time
        FROM t_mpd_sorghum_inbound_task sit
        <choose>
            <!-- 当有子表相关的筛选条件时，使用INNER JOIN确保子表有数据 -->
            <when test="(siloCode != null and siloCode != '') or (status != null) or (orderDateStart != null) or (orderDateEnd != null)">
                INNER JOIN t_mpd_sorghum_inbound_task_detail sitd ON sit.id = sitd.task_id AND sitd.deleted = 0
                <if test="siloCode != null and siloCode != ''">
                    AND sitd.accept_silo_code = #{siloCode}
                </if>
                <if test="status != null">
                    AND sitd.status = #{status}
                </if>
                <if test="orderDateStart != null">
                    AND DATE(sitd.actual_start_time) &gt;= DATE(#{orderDateStart})
                </if>
                <if test="orderDateEnd != null">
                    AND DATE(sitd.actual_start_time) &lt;= DATE(#{orderDateEnd})
                </if>
            </when>
            <!-- 没有子表筛选条件时，使用LEFT JOIN显示所有主表记录 -->
            <otherwise>
                LEFT JOIN t_mpd_sorghum_inbound_task_detail sitd ON sit.id = sitd.task_id AND sitd.deleted = 0
            </otherwise>
        </choose>
        WHERE sit.deleted = 0
        <!-- 物料编码：同时查询主表和子表 -->
        <if test="materialCode != null and materialCode != ''">
            AND (sit.material_code = #{materialCode} OR sitd.material_code = #{materialCode})
        </if>
        <!-- 工单号：查询主表 -->
        <if test="orderNo != null and orderNo != ''">
            AND sit.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        ORDER BY sit.order_date DESC, sit.create_time DESC, sitd.actual_start_time DESC
    </select>

    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            MAX(SUBSTRING(order_no, 9, 3)) AS max_serial_no
        FROM
            t_mpd_sorghum_inbound_task
        WHERE
            deleted = 0
            AND order_no LIKE CONCAT('RC', DATE_FORMAT(#{orderDate}, '%y%m%d'), '%')
    </select>

    <select id="selectByDateAndMaterialCode" resultType="com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTask">
        SELECT
        *
        FROM t_mpd_sorghum_inbound_task
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y-%m-%d') = DATE_FORMAT(#{dateStr}, '%Y-%m-%d')
            AND material_code = #{materialCode}
        LIMIT 1
    </select>

    <select id="selectDetail" resultType="com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDetailDTO">
        SELECT
            id, task_id, accept_silo_code AS siloCode, accept_silo_name as  siloName, material_name, material_code, unit, quantity,
            actual_start_time, actual_completion_time, status, creator_name, unique_id,
            create_time, update_time
        FROM t_mpd_sorghum_inbound_task_detail
        WHERE deleted = 0
            AND task_id = #{id}
        ORDER BY actual_start_time DESC
    </select>
</mapper>