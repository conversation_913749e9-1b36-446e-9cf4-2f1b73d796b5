<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.mapper.MaterialSwitchRecordMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.materialswitch.TMpdMaterialSwitchRecord">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="business_system" jdbcType="VARCHAR" property="businessSystem" />
        <result column="material_type" jdbcType="VARCHAR" property="materialType" />
        <result column="task_type" jdbcType="VARCHAR" property="taskType" />
        <result column="data_type" jdbcType="VARCHAR" property="dataType" />
        <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
        <result column="silo_no" jdbcType="VARCHAR" property="siloNo" />
        <result column="silo_name" jdbcType="VARCHAR" property="siloName" />
        <result column="before_material_type" jdbcType="VARCHAR" property="beforeMaterialType" />
        <result column="before_material_code" jdbcType="VARCHAR" property="beforeMaterialCode" />
        <result column="before_material_name" jdbcType="VARCHAR" property="beforeMaterialName" />
        <result column="after_material_type" jdbcType="VARCHAR" property="afterMaterialType" />
        <result column="after_material_code" jdbcType="VARCHAR" property="afterMaterialCode" />
        <result column="after_material_name" jdbcType="VARCHAR" property="afterMaterialName" />
        <result column="switch_time" jdbcType="TIMESTAMP" property="switchTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="executor_id" jdbcType="INTEGER" property="executorId" />
        <result column="executor_name" jdbcType="VARCHAR" property="executorName" />
        <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime" />
        <result column="central_control_task_id" jdbcType="VARCHAR" property="centralControlTaskId" />
        <result column="workshop_code" jdbcType="VARCHAR" property="workshopCode" />
        <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
        <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
        <result column="center_name" jdbcType="VARCHAR" property="centerName" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
        <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
        <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id, business_system, material_type, task_type, data_type, task_no, silo_no, silo_name,
        before_material_type, before_material_code, before_material_name,
        after_material_type, after_material_code, after_material_name,
        switch_time, status, executor_id, executor_name, completion_time,
        central_control_task_id, workshop_code, workshop_name, center_code, center_name,
        remark, data_source, unique_id, create_time, update_time, creator_id, updater_id, deleted
    </sql>

    <!-- 分页查询物料切换记录 -->
    <select id="queryMaterialSwitchRecords" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0
        <if test="queryDTO.businessSystems != null and queryDTO.businessSystems.size() > 0">
            AND business_system IN
            <foreach collection="queryDTO.businessSystems" item="businessSystem" open="(" separator="," close=")">
                #{businessSystem}
            </foreach>
        </if>
        <if test="queryDTO.materialTypes != null and queryDTO.materialTypes.size() > 0">
            AND material_type IN
            <foreach collection="queryDTO.materialTypes" item="materialType" open="(" separator="," close=")">
                #{materialType}
            </foreach>
        </if>
        <if test="queryDTO.taskType != null and queryDTO.taskType != ''">
            AND task_type = #{queryDTO.taskType}
        </if>
        <if test="queryDTO.dataType != null and queryDTO.dataType != ''">
            AND data_type = #{queryDTO.dataType}
        </if>
        <if test="queryDTO.taskNo != null and queryDTO.taskNo != ''">
            AND task_no LIKE CONCAT('%', #{queryDTO.taskNo}, '%')
        </if>
        <if test="queryDTO.siloNo != null and queryDTO.siloNo != ''">
            AND silo_no LIKE CONCAT('%', #{queryDTO.siloNo}, '%')
        </if>
        <if test="queryDTO.siloName != null and queryDTO.siloName != ''">
            AND silo_name LIKE CONCAT('%', #{queryDTO.siloName}, '%')
        </if>
        <if test="queryDTO.beforeMaterialType != null and queryDTO.beforeMaterialType != ''">
            AND before_material_type = #{queryDTO.beforeMaterialType}
        </if>
        <if test="queryDTO.beforeMaterialCode != null and queryDTO.beforeMaterialCode != ''">
            AND before_material_code = #{queryDTO.beforeMaterialCode}
        </if>
        <if test="queryDTO.afterMaterialType != null and queryDTO.afterMaterialType != ''">
            AND after_material_type = #{queryDTO.afterMaterialType}
        </if>
        <if test="queryDTO.afterMaterialCode != null and queryDTO.afterMaterialCode != ''">
            AND after_material_code = #{queryDTO.afterMaterialCode}
        </if>
        <if test="queryDTO.statuses != null and queryDTO.statuses.size() > 0">
            AND status IN
            <foreach collection="queryDTO.statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="queryDTO.executorName != null and queryDTO.executorName != ''">
            AND executor_name LIKE CONCAT('%', #{queryDTO.executorName}, '%')
        </if>
        <if test="queryDTO.centralControlTaskId != null and queryDTO.centralControlTaskId != ''">
            AND central_control_task_id = #{queryDTO.centralControlTaskId}
        </if>
        <if test="queryDTO.workshopCode != null and queryDTO.workshopCode != ''">
            AND workshop_code = #{queryDTO.workshopCode}
        </if>
        <if test="queryDTO.centerCode != null and queryDTO.centerCode != ''">
            AND center_code = #{queryDTO.centerCode}
        </if>
        <if test="queryDTO.switchTimeStart != null">
            AND switch_time &gt;= #{queryDTO.switchTimeStart}
        </if>
        <if test="queryDTO.switchTimeEnd != null">
            AND switch_time &lt;= #{queryDTO.switchTimeEnd}
        </if>
        <if test="queryDTO.completionTimeStart != null">
            AND completion_time &gt;= #{queryDTO.completionTimeStart}
        </if>
        <if test="queryDTO.completionTimeEnd != null">
            AND completion_time &lt;= #{queryDTO.completionTimeEnd}
        </if>
        <if test="queryDTO.createTimeStart != null">
            AND create_time &gt;= #{queryDTO.createTimeStart}
        </if>
        <if test="queryDTO.createTimeEnd != null">
            AND create_time &lt;= #{queryDTO.createTimeEnd}
        </if>
        <if test="queryDTO.dataSource != null and queryDTO.dataSource != ''">
            AND data_source = #{queryDTO.dataSource}
        </if>
        <if test="queryDTO.uniqueId != null and queryDTO.uniqueId != ''">
            AND unique_id = #{queryDTO.uniqueId}
        </if>
        ORDER BY switch_time DESC, create_time DESC
    </select>

    <!-- 根据任务号查询物料切换记录 -->
    <select id="getMaterialSwitchRecordByTaskNo" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND task_no = #{taskNo}
        LIMIT 1
    </select>

    <!-- 根据中控任务ID查询物料切换记录 -->
    <select id="getMaterialSwitchRecordByTaskId" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND central_control_task_id = #{centralControlTaskId}
        LIMIT 1
    </select>

    <!-- 根据唯一标识查询物料切换记录 -->
    <select id="getMaterialSwitchRecordByUniqueId" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND unique_id = #{uniqueId}
        LIMIT 1
    </select>

    <!-- 查询进行中的物料切换记录 -->
    <select id="getInProgressRecords" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND status = 1
        ORDER BY switch_time DESC
    </select>

    <!-- 根据筒仓号查询最新的物料切换记录 -->
    <select id="getLatestMaterialSwitchRecordBySilo" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND silo_no = #{siloNo}
        ORDER BY switch_time DESC
        LIMIT 1
    </select>

    <!-- 根据筒仓号和状态查询物料切换记录 -->
    <select id="getMaterialSwitchRecordsBySiloAndStatus" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND silo_no = #{siloNo} AND status = #{status}
        ORDER BY switch_time DESC
    </select>

    <!-- 根据业务系统查询物料切换记录 -->
    <select id="getMaterialSwitchRecordsByBusinessSystem" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND business_system = #{businessSystem}
        ORDER BY switch_time DESC
    </select>

    <!-- 根据物料类型查询物料切换记录 -->
    <select id="getMaterialSwitchRecordsByMaterialType" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND material_type = #{materialType}
        ORDER BY switch_time DESC
    </select>

    <!-- 根据执行人查询物料切换记录 -->
    <select id="getMaterialSwitchRecordsByExecutor" resultType="com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND executor_name LIKE CONCAT('%', #{executorName}, '%')
        ORDER BY switch_time DESC
    </select>

    <!-- 统计指定状态的记录数量 -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND status = #{status}
    </select>

    <!-- 统计指定筒仓的记录数量 -->
    <select id="countBySiloNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND silo_no = #{siloNo}
    </select>

    <!-- 统计指定业务系统的记录数量 -->
    <select id="countByBusinessSystem" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND business_system = #{businessSystem}
    </select>

    <!-- 统计指定物料类型的记录数量 -->
    <select id="countByMaterialType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND material_type = #{materialType}
    </select>

    <!-- 根据日期统计记录数量 -->
    <select id="countByDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND DATE(switch_time) = #{dateStr}
    </select>

    <!-- 检查任务号是否存在 -->
    <select id="existsByTaskNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND task_no = #{taskNo}
    </select>

    <!-- 检查唯一标识是否存在 -->
    <select id="existsByUniqueId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_material_switch_record
        WHERE deleted = 0 AND unique_id = #{uniqueId}
    </select>

</mapper>
