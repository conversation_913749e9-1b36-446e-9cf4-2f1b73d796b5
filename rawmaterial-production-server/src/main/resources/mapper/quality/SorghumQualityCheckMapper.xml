<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.SorghumQualityCheckMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.TMpdSorghumQualityCheck">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="business_system" jdbcType="VARCHAR" property="businessSystem"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="task_type" jdbcType="VARCHAR" property="taskType"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
        <result column="silo_no" jdbcType="VARCHAR" property="siloNo"/>
        <result column="silo_name" jdbcType="VARCHAR" property="siloName"/>
        <result column="quality_check_location" jdbcType="VARCHAR" property="qualityCheckLocation"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="starch_content" jdbcType="DECIMAL" property="starchContent"/>
        <result column="moisture_content" jdbcType="DECIMAL" property="moistureContent"/>
        <result column="protein_content" jdbcType="DECIMAL" property="proteinContent"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="inspector" jdbcType="VARCHAR" property="inspector"/>
        <result column="equipment" jdbcType="VARCHAR" property="equipment"/>
        <result column="method" jdbcType="VARCHAR" property="method"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="central_control_task_id" jdbcType="VARCHAR" property="centralControlTaskId"/>
        <result column="unique_id" jdbcType="VARCHAR" property="uniqueId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="log_id" jdbcType="VARCHAR" property="logId"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <!-- 通用列名 -->
    <sql id="Base_Column_List">
        id, business_system, material_type, task_type, data_type, task_no, silo_no, silo_name,
        quality_check_location, start_time, end_time, starch_content, moisture_content, protein_content,
        material_code, material_name, unit, inspector, equipment, method, batch_no, status, remark,
        data_source, central_control_task_id, unique_id, create_time, update_time, creator_name, log_id, is_deleted
    </sql>

    <!-- 根据任务号查询快检记录 -->
    <select id="selectByTaskNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE task_no = #{taskNo}
        AND is_deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据任务号和筒仓号查询快检记录 -->
    <select id="selectByTaskNoAndSiloNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE task_no = #{taskNo}
        AND silo_no = #{siloNo}
        AND is_deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据筒仓号和时间范围查询快检记录 -->
    <select id="selectBySiloNoAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE silo_no = #{siloNo}
        AND start_time >= #{startTime}
        AND end_time <![CDATA[<=]]> #{endTime}
        AND is_deleted = 0
        ORDER BY start_time DESC
    </select>

    <!-- 根据唯一标识查询快检记录 -->
    <select id="selectByUniqueId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE unique_id = #{uniqueId}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据中控任务ID查询快检记录 -->
    <select id="selectByCentralControlTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE central_control_task_id = #{centralControlTaskId}
        AND is_deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据物料编码和时间范围查询快检记录 -->
    <select id="selectByMaterialCodeAndTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE material_code = #{materialCode}
        AND start_time >= #{startTime}
        AND end_time <![CDATA[<=]]> #{endTime}
        AND is_deleted = 0
        ORDER BY start_time DESC
    </select>

    <!-- 根据检测状态查询快检记录 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_sorghum_quality_check
        WHERE status = #{status}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入快检记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_mpd_sorghum_quality_check (
        business_system, material_type, task_type, data_type, task_no, silo_no, silo_name,
        quality_check_location, start_time, end_time, starch_content, moisture_content, protein_content,
        material_code, material_name, unit, inspector, equipment, method, batch_no, status, remark,
        data_source, central_control_task_id, unique_id, create_time, update_time, creator_name, log_id, is_deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.businessSystem}, #{record.materialType}, #{record.taskType}, #{record.dataType},
            #{record.taskNo}, #{record.siloNo}, #{record.siloName}, #{record.qualityCheckLocation},
            #{record.startTime}, #{record.endTime}, #{record.starchContent}, #{record.moistureContent},
            #{record.proteinContent}, #{record.materialCode}, #{record.materialName}, #{record.unit},
            #{record.inspector}, #{record.equipment}, #{record.method}, #{record.batchNo}, #{record.status},
            #{record.remark}, #{record.dataSource}, #{record.centralControlTaskId}, #{record.uniqueId},
            #{record.createTime}, #{record.updateTime}, #{record.creatorName}, #{record.logId}, #{record.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 更新检测状态 -->
    <update id="updateStatus">
        UPDATE t_mpd_sorghum_quality_check
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

</mapper>
