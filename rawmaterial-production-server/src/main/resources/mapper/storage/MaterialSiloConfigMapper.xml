<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper">

    <!-- 物料筒仓树形结构映射 -->
    <resultMap id="materialSiloTreeMap" type="com.hvisions.rawmaterial.dto.silo.MaterialSiloTreeDTO">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="erpCode" column="erp_code"/>
        <result property="maxCapacity" column="max_capacity"/>
        <result property="safetyStock" column="safety_stock"/>
        <result property="materialType" column="material_type"/>
        <result property="parentId" column="parent_id"/>
        <result property="flag" column="flag"/>
        <result property="unit" column="unit"/>
        <result property="state" column="state"/>
        <collection
                property="childrenList"
                column="{id=id, materialType=material_type}"
                select="selectMaterialSiloChildren"
                ofType="com.hvisions.rawmaterial.dto.silo.MaterialSiloTreeDTO"
                javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <!-- 部门树形结构映射 -->
    <resultMap id="departmentTreeMap" type="com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO">
        <result property="id" column="id"/>
        <result property="departmentCode" column="department_code"/>
        <result property="departmentName" column="department_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="enableMark" column="enable_mark"/>
        <collection
                property="childrenList"
                column="id"
                select="selectDepartmentChildren"
                ofType="com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO"
                javaType="java.util.ArrayList">
        </collection>
    </resultMap>

    <!-- 获取物料筒仓树形结构 -->
    <select id="getMaterialSiloTree" resultMap="materialSiloTreeMap">
        SELECT 
            m.id, 
            m.code, 
            m.name, 
            m.erp_code,
            m.max_capacity,
            m.safety_stock, 
            m.material_type,
            m.parent_id,
            m.unit,
            m.state,
            CASE 
                WHEN m.parent_id = 0 THEN 'root'
                WHEN EXISTS(SELECT 1 FROM t_mpd_material_silo c WHERE c.parent_id = m.id AND c.deleted = 0) THEN 'parent'
                ELSE 'leaf'
            END as flag
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0 
            AND m.parent_id = 0 
            AND m.material_type = #{materialType}
        ORDER BY m.code
    </select>

    <!-- 获取子筒仓 -->
    <select id="selectMaterialSiloChildren" resultMap="materialSiloTreeMap">
        SELECT 
            m.id, 
            m.code, 
            m.name, 
            m.erp_code,
            m.max_capacity,
            m.safety_stock, 
            m.material_type,
            m.parent_id,
            m.unit,
            m.state,
            CASE 
                WHEN EXISTS(SELECT 1 FROM t_mpd_material_silo c WHERE c.parent_id = m.id AND c.deleted = 0) THEN 'parent'
                ELSE 'leaf'
            END as flag
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0 
            AND m.parent_id = #{id}
            AND m.material_type = #{materialType}
        ORDER BY m.code
    </select>

    <!-- 分页查询物料筒仓列表 -->
    <select id="getMaterialSiloPageList" resultMap="materialSiloTreeMap">
        SELECT 
            m.id, 
            m.code, 
            m.name, 
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state,
            p.name as parentName,
            CASE 
                WHEN m.parent_id = 0 THEN 'root'
                WHEN EXISTS(SELECT 1 FROM t_mpd_material_silo c WHERE c.parent_id = m.id AND c.deleted = 0) THEN 'parent'
                ELSE 'leaf'
            END as flag
        FROM t_mpd_material_silo m
        LEFT JOIN t_mpd_material_silo p ON m.parent_id = p.id AND p.deleted = 0
        WHERE m.deleted = 0
        <if test="materialType != null">
            AND m.material_type = #{materialType}
        </if>
        <if test="code != null and code != ''">
            AND m.code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="name != null and name != ''">
            AND m.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="parentId != null">
            AND m.parent_id = #{parentId}
        </if>
        <if test="state != null and state != ''">
            AND m.state = #{state}
        </if>
        ORDER BY m.parent_id, m.code
    </select>

    <!-- 获取部门树形结构 -->
    <select id="getDepartmentTree" resultMap="departmentTreeMap">
        SELECT 
            d.id, 
            d.department_code, 
            d.department_name, 
            d.parent_id,
            d.enable_mark
        FROM authority.sys_department d
        WHERE d.parent_id = 0 
            AND d.enable_mark = 1
        ORDER BY d.department_code
    </select>

    <!-- 获取子部门 -->
    <select id="selectDepartmentChildren" resultMap="departmentTreeMap">
        SELECT 
            d.id, 
            d.department_code, 
            d.department_name, 
            d.parent_id,
            d.enable_mark
        FROM authority.sys_department d
        WHERE d.parent_id = #{id}
            AND d.enable_mark = 1
        ORDER BY d.department_code
    </select>

    <!-- 获取部门子节点 -->
    <select id="getDepartmentChildren" resultType="com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO">
        SELECT
            d.id,
            d.department_code as departmentCode,
            d.department_name as departmentName,
            d.parent_id as parentId,
            d.enable_mark as enableMark
        FROM authority.sys_department d
        WHERE d.parent_id = #{parentId}
            AND d.enable_mark = 1
        ORDER BY d.department_code
    </select>

    <!-- 查询所有物料类型最后一层的数据（第三级） -->
    <select id="getAllMaterialTypeLeafNodes" resultMap="materialSiloTreeMap">
        SELECT
            m.id,
            m.code,
            m.name,
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state,
            'leaf' as flag
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND NOT EXISTS(SELECT 1 FROM t_mpd_material_silo c WHERE c.parent_id = m.id AND c.deleted = 0)
            AND m.parent_id != 0
            AND EXISTS(SELECT 1 FROM t_mpd_material_silo p WHERE p.id = m.parent_id AND p.parent_id != 0 AND p.deleted = 0)
        ORDER BY m.material_type, m.code
    </select>

    <select id="findSiloBySiloCode" resultType="com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO">
        SELECT
            m.id,
            m.code,
            m.name,
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND m.code = #{siloCode}
            AND m.material_type = #{materialType}
    </select>

    <select id="selectSiloCodesByMaterialType" resultType="java.lang.String">
        SELECT
            m.code
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND m.material_type = #{materialType}
    </select>

    <select id="getMaterialSiloByCode" resultType="com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO">
        SELECT
            m.id,
            m.code,
            m.name,
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND m.code = #{code}
        limit 1
    </select>

    <select id="getAllMaterialByCode" resultMap="materialSiloTreeMap">
        WITH RECURSIVE material_tree AS (
            SELECT
                m.id,
                m.code,
                m.name,
                m.erp_code,
                m.max_capacity,
                m.safety_stock,
                m.material_type,
                m.parent_id,
                m.unit,
                m.state,
                1 as level
            FROM t_mpd_material_silo m
            WHERE m.deleted = 0
              AND m.code = #{code}

        UNION ALL

            SELECT
                c.id,
                c.code,
                c.name,
                c.erp_code,
                c.max_capacity,
                c.safety_stock,
                c.material_type,
                c.parent_id,
                c.unit,
                c.state,
                t.level + 1
            FROM t_mpd_material_silo c
                     INNER JOIN material_tree t ON c.parent_id = t.id
            WHERE c.deleted = 0
        )
        SELECT
            mt.*,
            'leaf' AS flag
        FROM material_tree mt
        WHERE NOT EXISTS (
            SELECT 1
            FROM t_mpd_material_silo c
            WHERE c.parent_id = mt.id
              AND c.deleted = 0
        )
          AND mt.level >= 3
        ORDER BY mt.material_type, mt.code;
    </select>

    <select id="getSiloConfigFistList" resultType="com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO">
        SELECT
            m.id,
            m.code,
            m.name,
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND m.parent_id = 0
        GROUP BY m.code, m.name
        ORDER BY m.material_type, m.code
    </select>

    <select id="getLocationList" resultType="com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO">
        SELECT
            m.id,
            m.code,
            m.name,
            m.erp_code,
            m.max_capacity,
            m.safety_stock,
            m.material_type,
            m.parent_id,
            m.unit,
            m.state,
            m2.code as parentCode
        FROM t_mpd_material_silo m
        left join t_mpd_material_silo m2 on m.parent_id = m2.id and m2.deleted = 0
        WHERE m.deleted = 0
            AND m.parent_id != 0
            <!--<if test="code != null">
                AND m.code = #{code}
            </if>-->
            <if test="name != null">
                AND m.`name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="materialType != null">
                AND m.material_type = #{materialType}
            </if>
            <if test="parentCode != null">
                AND m2.code like concat('%',#{parentCode},'%')
            </if>
        GROUP BY m.id
        ORDER BY m.id DESC
    </select>

    <select id="getMaterialSiloByCodeAndType" resultType="com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO">
        SELECT
        m.id,
        m.code,
        m.name,
        m.erp_code,
        m.max_capacity,
        m.safety_stock,
        m.material_type,
        m.parent_id,
        m.unit,
        m.state
        FROM t_mpd_material_silo m
        WHERE m.deleted = 0
            AND m.code = #{code}
            AND m.material_type = #{materialType}
        limit 1
    </select>
</mapper>
