<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.mapper.SiloTransferRecordMapper">

    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.silotransfer.TMpdSiloTransferRecord">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="business_system" jdbcType="VARCHAR" property="businessSystem" />
        <result column="material_type" jdbcType="VARCHAR" property="materialType" />
        <result column="task_type" jdbcType="VARCHAR" property="taskType" />
        <result column="data_type" jdbcType="VARCHAR" property="dataType" />
        <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
        <result column="source_silo_no" jdbcType="VARCHAR" property="sourceSiloNo" />
        <result column="source_silo_name" jdbcType="VARCHAR" property="sourceSiloName" />
        <result column="target_silo_no" jdbcType="VARCHAR" property="targetSiloNo" />
        <result column="target_silo_name" jdbcType="VARCHAR" property="targetSiloName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="actual_weight" jdbcType="DECIMAL" property="actualWeight" />
        <result column="planned_weight" jdbcType="DECIMAL" property="plannedWeight" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="executor_id" jdbcType="INTEGER" property="executorId" />
        <result column="executor_name" jdbcType="VARCHAR" property="executorName" />
        <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime" />
        <result column="central_control_task_id" jdbcType="VARCHAR" property="centralControlTaskId" />
        <result column="workshop_code" jdbcType="VARCHAR" property="workshopCode" />
        <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
        <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
        <result column="center_name" jdbcType="VARCHAR" property="centerName" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
        <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
        <result column="progress_percentage" jdbcType="DECIMAL" property="progressPercentage" />
        <result column="failure_reason" jdbcType="VARCHAR" property="failureReason" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
        <result column="deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id, business_system, material_type, task_type, data_type, task_no, 
        source_silo_no, source_silo_name, target_silo_no, target_silo_name,
        material_code, material_name, unit, actual_weight, planned_weight,
        start_time, end_time, status, executor_id, executor_name, completion_time,
        central_control_task_id, workshop_code, workshop_name, center_code, center_name,
        remark, data_source, unique_id, progress_percentage, failure_reason,
        create_time, update_time, creator_id, updater_id, deleted
    </sql>

    <!-- 分页查询倒仓任务记录 -->
    <select id="querySiloTransferRecords" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0
        <if test="queryDTO.businessSystems != null and queryDTO.businessSystems.size() > 0">
            AND business_system IN
            <foreach collection="queryDTO.businessSystems" item="businessSystem" open="(" separator="," close=")">
                #{businessSystem}
            </foreach>
        </if>
        <if test="queryDTO.materialTypes != null and queryDTO.materialTypes.size() > 0">
            AND material_type IN
            <foreach collection="queryDTO.materialTypes" item="materialType" open="(" separator="," close=")">
                #{materialType}
            </foreach>
        </if>
        <if test="queryDTO.taskType != null and queryDTO.taskType != ''">
            AND task_type = #{queryDTO.taskType}
        </if>
        <if test="queryDTO.dataType != null and queryDTO.dataType != ''">
            AND data_type = #{queryDTO.dataType}
        </if>
        <if test="queryDTO.taskNo != null and queryDTO.taskNo != ''">
            AND task_no LIKE CONCAT('%', #{queryDTO.taskNo}, '%')
        </if>
        <if test="queryDTO.sourceSiloNo != null and queryDTO.sourceSiloNo != ''">
            AND source_silo_no LIKE CONCAT('%', #{queryDTO.sourceSiloNo}, '%')
        </if>
        <if test="queryDTO.sourceSiloName != null and queryDTO.sourceSiloName != ''">
            AND source_silo_name LIKE CONCAT('%', #{queryDTO.sourceSiloName}, '%')
        </if>
        <if test="queryDTO.targetSiloNo != null and queryDTO.targetSiloNo != ''">
            AND target_silo_no LIKE CONCAT('%', #{queryDTO.targetSiloNo}, '%')
        </if>
        <if test="queryDTO.targetSiloName != null and queryDTO.targetSiloName != ''">
            AND target_silo_name LIKE CONCAT('%', #{queryDTO.targetSiloName}, '%')
        </if>
        <if test="queryDTO.materialCode != null and queryDTO.materialCode != ''">
            AND material_code = #{queryDTO.materialCode}
        </if>
        <if test="queryDTO.materialName != null and queryDTO.materialName != ''">
            AND material_name LIKE CONCAT('%', #{queryDTO.materialName}, '%')
        </if>
        <if test="queryDTO.statuses != null and queryDTO.statuses.size() > 0">
            AND status IN
            <foreach collection="queryDTO.statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="queryDTO.executorName != null and queryDTO.executorName != ''">
            AND executor_name LIKE CONCAT('%', #{queryDTO.executorName}, '%')
        </if>
        <if test="queryDTO.centralControlTaskId != null and queryDTO.centralControlTaskId != ''">
            AND central_control_task_id = #{queryDTO.centralControlTaskId}
        </if>
        <if test="queryDTO.workshopCode != null and queryDTO.workshopCode != ''">
            AND workshop_code = #{queryDTO.workshopCode}
        </if>
        <if test="queryDTO.centerCode != null and queryDTO.centerCode != ''">
            AND center_code = #{queryDTO.centerCode}
        </if>
        <if test="queryDTO.startTimeBegin != null">
            AND start_time &gt;= #{queryDTO.startTimeBegin}
        </if>
        <if test="queryDTO.startTimeEnd != null">
            AND start_time &lt;= #{queryDTO.startTimeEnd}
        </if>
        <if test="queryDTO.endTimeBegin != null">
            AND end_time &gt;= #{queryDTO.endTimeBegin}
        </if>
        <if test="queryDTO.endTimeEnd != null">
            AND end_time &lt;= #{queryDTO.endTimeEnd}
        </if>
        <if test="queryDTO.completionTimeBegin != null">
            AND completion_time &gt;= #{queryDTO.completionTimeBegin}
        </if>
        <if test="queryDTO.completionTimeEnd != null">
            AND completion_time &lt;= #{queryDTO.completionTimeEnd}
        </if>
        <if test="queryDTO.createTimeBegin != null">
            AND create_time &gt;= #{queryDTO.createTimeBegin}
        </if>
        <if test="queryDTO.createTimeEnd != null">
            AND create_time &lt;= #{queryDTO.createTimeEnd}
        </if>
        <if test="queryDTO.actualWeightMin != null">
            AND actual_weight &gt;= #{queryDTO.actualWeightMin}
        </if>
        <if test="queryDTO.actualWeightMax != null">
            AND actual_weight &lt;= #{queryDTO.actualWeightMax}
        </if>
        <if test="queryDTO.progressPercentageMin != null">
            AND progress_percentage &gt;= #{queryDTO.progressPercentageMin}
        </if>
        <if test="queryDTO.progressPercentageMax != null">
            AND progress_percentage &lt;= #{queryDTO.progressPercentageMax}
        </if>
        <if test="queryDTO.dataSource != null and queryDTO.dataSource != ''">
            AND data_source = #{queryDTO.dataSource}
        </if>
        <if test="queryDTO.uniqueId != null and queryDTO.uniqueId != ''">
            AND unique_id = #{queryDTO.uniqueId}
        </if>
        <if test="queryDTO.includeFailureReason != null and queryDTO.includeFailureReason == true">
            AND failure_reason IS NOT NULL AND failure_reason != ''
        </if>
        ORDER BY start_time DESC, create_time DESC
    </select>

    <!-- 根据任务号查询倒仓任务记录 -->
    <select id="getSiloTransferRecordByTaskNo" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND task_no = #{taskNo}
        LIMIT 1
    </select>

    <!-- 根据中控任务ID查询倒仓任务记录 -->
    <select id="getSiloTransferRecordByTaskId" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND central_control_task_id = #{centralControlTaskId}
        LIMIT 1
    </select>

    <!-- 根据唯一标识查询倒仓任务记录 -->
    <select id="getSiloTransferRecordByUniqueId" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND unique_id = #{uniqueId}
        LIMIT 1
    </select>

    <!-- 查询执行中的倒仓任务记录 -->
    <select id="getInProgressRecords" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND status = 2
        ORDER BY start_time DESC
    </select>

    <!-- 查询待执行的倒仓任务记录 -->
    <select id="getPendingRecords" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND status = 1
        ORDER BY create_time ASC
    </select>

    <!-- 根据源筒仓号查询最新的倒仓任务记录 -->
    <select id="getLatestSiloTransferRecordBySourceSilo" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND source_silo_no = #{sourceSiloNo}
        ORDER BY start_time DESC
        LIMIT 1
    </select>

    <!-- 根据目标筒仓号查询最新的倒仓任务记录 -->
    <select id="getLatestSiloTransferRecordByTargetSilo" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND target_silo_no = #{targetSiloNo}
        ORDER BY start_time DESC
        LIMIT 1
    </select>

    <!-- 根据筒仓号查询最新的倒仓任务记录（源或目标） -->
    <select id="getLatestSiloTransferRecordBySilo" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND (source_silo_no = #{siloNo} OR target_silo_no = #{siloNo})
        ORDER BY start_time DESC
        LIMIT 1
    </select>

    <!-- 根据源筒仓和状态查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsBySourceSiloAndStatus" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND source_silo_no = #{sourceSiloNo} AND status = #{status}
        ORDER BY start_time DESC
    </select>

    <!-- 根据目标筒仓和状态查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsByTargetSiloAndStatus" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND target_silo_no = #{targetSiloNo} AND status = #{status}
        ORDER BY start_time DESC
    </select>

    <!-- 根据筒仓和状态查询倒仓任务记录（源或目标） -->
    <select id="getSiloTransferRecordsBySiloAndStatus" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND (source_silo_no = #{siloNo} OR target_silo_no = #{siloNo}) AND status = #{status}
        ORDER BY start_time DESC
    </select>

    <!-- 根据业务系统查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsByBusinessSystem" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND business_system = #{businessSystem}
        ORDER BY start_time DESC
    </select>

    <!-- 根据物料类型查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsByMaterialType" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND material_type = #{materialType}
        ORDER BY start_time DESC
    </select>

    <!-- 根据执行人查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsByExecutor" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND executor_name LIKE CONCAT('%', #{executorName}, '%')
        ORDER BY start_time DESC
    </select>

    <!-- 根据时间范围查询倒仓任务记录 -->
    <select id="getSiloTransferRecordsByTimeRange" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND start_time &gt;= #{startTime} AND start_time &lt;= #{endTime}
        ORDER BY start_time DESC
    </select>

    <!-- 统计指定状态的记录数量 -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND status = #{status}
    </select>

    <!-- 统计指定源筒仓的记录数量 -->
    <select id="countBySourceSiloNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND source_silo_no = #{sourceSiloNo}
    </select>

    <!-- 统计指定目标筒仓的记录数量 -->
    <select id="countByTargetSiloNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND target_silo_no = #{targetSiloNo}
    </select>

    <!-- 统计指定筒仓的记录数量（源或目标） -->
    <select id="countBySiloNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND (source_silo_no = #{siloNo} OR target_silo_no = #{siloNo})
    </select>

    <!-- 统计指定业务系统的记录数量 -->
    <select id="countByBusinessSystem" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND business_system = #{businessSystem}
    </select>

    <!-- 统计指定物料类型的记录数量 -->
    <select id="countByMaterialType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND material_type = #{materialType}
    </select>

    <!-- 根据日期统计记录数量 -->
    <select id="countByDate" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND DATE(start_time) = #{dateStr}
    </select>

    <!-- 检查任务号是否存在 -->
    <select id="existsByTaskNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND task_no = #{taskNo}
    </select>

    <!-- 检查唯一标识是否存在 -->
    <select id="existsByUniqueId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND unique_id = #{uniqueId}
    </select>

    <!-- 检查筒仓是否有进行中的倒仓任务 -->
    <select id="existsInProgressTaskBySiloNo" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0 AND (source_silo_no = #{siloNo} OR target_silo_no = #{siloNo}) AND status = 2
    </select>

    <!-- 获取倒仓任务统计信息 -->
    <select id="getSiloTransferStatistics" resultType="com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO">
        SELECT
            business_system,
            material_type,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as in_progress_count,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as cancelled_count,
            SUM(actual_weight) as total_weight,
            AVG(actual_weight) as avg_weight
        FROM t_mpd_silo_transfer_record
        WHERE deleted = 0
        <if test="businessSystem != null and businessSystem != ''">
            AND business_system = #{businessSystem}
        </if>
        <if test="materialType != null and materialType != ''">
            AND material_type = #{materialType}
        </if>
        <if test="startTime != null">
            AND start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND start_time &lt;= #{endTime}
        </if>
        GROUP BY business_system, material_type
        ORDER BY business_system, material_type
    </select>

</mapper>
