<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskDetailMapper">

    <select id="findByTaskId" resultType="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO">
        SELECT
            *
        FROM t_mpd_rice_husk_inbound_task_detail
        WHERE deleted = 0
            AND task_id = #{taskId}
        ORDER BY actual_start_time DESC
    </select>

    <select id="selectByMaterialCodeAndTaskNoAndTime" resultType="com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTaskDetail">
        SELECT
        *
        FROM t_mpd_rice_husk_inbound_task_detail
        WHERE deleted = 0
            AND material_code = #{materialCode}
            AND control_task_no = #{controlTaskNo}
            AND DATE_FORMAT(actual_start_time, '%Y-%m-%d') = DATE_FORMAT(#{actualStartTime}, '%Y-%m-%d')
        LIMIT 1
    </select>

    <select id="selectByControlTaskNo" resultType="com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTaskDetail">
        SELECT
        *
        FROM t_mpd_rice_husk_inbound_task_detail
        WHERE deleted = 0
            AND control_task_no = #{controlTaskNo}
        LIMIT 1
    </select>

    <select id="selectSummaryByOrderId" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(quantity), 0) as quantity
        FROM t_mpd_rice_husk_inbound_task_detail
        WHERE deleted = 0
            AND task_id = #{orderId}
    </select>
</mapper>
