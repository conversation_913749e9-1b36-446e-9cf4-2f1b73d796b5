<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskMapper">

    <select id="selectMaxSerialNoByDate" resultType="java.lang.String">
        SELECT
            MAX(SUBSTRING(order_no, 9, 3)) AS max_serial_no
        FROM
            t_mpd_rice_husk_inbound_task
        WHERE
            deleted = 0
            AND order_no LIKE CONCAT('RC', DATE_FORMAT(#{orderDate}, '%y%m%d'), '%')
    </select>

    <select id="selectByDateAndMaterialCode" resultType="com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTask">
        SELECT
        *
        FROM t_mpd_rice_husk_inbound_task
        WHERE deleted = 0
            AND DATE_FORMAT(order_date, '%Y-%m-%d') = DATE_FORMAT(#{dateStr}, '%Y-%m-%d')
            AND material_code = #{materialCode}
        LIMIT 1
    </select>


    <!-- 优化后的结果映射，使用一次查询获取所有数据 -->
    <resultMap id="pageResultMap" type="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
        <result column="material_name" jdbcType="VARCHAR" property="materialName" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="quantity" jdbcType="DECIMAL" property="quantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <collection property="detailList" ofType="com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO">
            <id column="detail_id" jdbcType="INTEGER" property="id" />
            <result column="detail_task_id" jdbcType="INTEGER" property="taskId" />
            <result column="detail_silo_name" jdbcType="VARCHAR" property="siloName" />
            <result column="detail_silo_code" jdbcType="VARCHAR" property="siloCode" />
            <result column="detail_material_name" jdbcType="VARCHAR" property="materialName" />
            <result column="detail_material_code" jdbcType="VARCHAR" property="materialCode" />
            <result column="detail_unit" jdbcType="VARCHAR" property="unit" />
            <result column="detail_quantity" jdbcType="DECIMAL" property="quantity" />
            <result column="detail_actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
            <result column="detail_actual_completion_time" jdbcType="TIMESTAMP" property="actualCompletionTime" />
            <result column="detail_status" jdbcType="INTEGER" property="status" />
            <result column="detail_creator_name" jdbcType="VARCHAR" property="creatorName" />
            <result column="detail_unique_id" jdbcType="VARCHAR" property="uniqueId" />
            <result column="detail_create_time" jdbcType="TIMESTAMP" property="createTime" />
            <result column="detail_update_time" jdbcType="TIMESTAMP" property="updateTime" />
        </collection>
    </resultMap>

    <select id="getRiceHuskInboundTaskPageList" resultMap="pageResultMap">
        SELECT
            hik.id,
            hik.order_no,
            hik.order_date,
            hik.material_name,
            hik.material_code,
            hik.quantity,
            hik.unit,
            hik.remark,
            hik.create_time,
            hik.update_time,
            -- 子表字段，使用别名避免冲突
            hikd.id as detail_id,
            hikd.task_id as detail_task_id,
            hikd.silo_name as detail_silo_name,
            hikd.silo_code as detail_silo_code,
            hikd.material_name as detail_material_name,
            hikd.material_code as detail_material_code,
            hikd.unit as detail_unit,
            hikd.quantity as detail_quantity,
            hikd.actual_start_time as detail_actual_start_time,
            hikd.actual_completion_time as detail_actual_completion_time,
            hikd.status as detail_status,
            hikd.creator_name as detail_creator_name,
            hikd.unique_id as detail_unique_id,
            hikd.create_time as detail_create_time,
            hikd.update_time as detail_update_time
        FROM t_mpd_rice_husk_inbound_task hik
        <choose>
            <!-- 当有子表相关的筛选条件时，使用INNER JOIN确保子表有数据 -->
            <when test="(siloCode != null and siloCode != '') or (status != null) or (orderDateStart != null) or (orderDateEnd != null)">
                INNER JOIN t_mpd_rice_husk_inbound_task_detail hikd ON hik.id = hikd.task_id AND hikd.deleted = 0
                <if test="siloCode != null and siloCode != ''">
                    AND hikd.silo_code = #{siloCode}
                </if>
                <if test="status != null">
                    AND hikd.status = #{status}
                </if>
                <if test="orderDateStart != null">
                    AND DATE(hikd.actual_start_time) &gt;= DATE(#{orderDateStart})
                </if>
                <if test="orderDateEnd != null">
                    AND DATE(hikd.actual_start_time) &lt;= DATE(#{orderDateEnd})
                </if>
            </when>
            <!-- 没有子表筛选条件时，使用LEFT JOIN显示所有主表记录 -->
            <otherwise>
                LEFT JOIN t_mpd_rice_husk_inbound_task_detail hikd ON hik.id = hikd.task_id AND hikd.deleted = 0
            </otherwise>
        </choose>
        WHERE hik.deleted = 0
        <!-- 物料编码：同时查询主表和子表 -->
        <if test="materialCode != null and materialCode != ''">
            AND (hik.material_code = #{materialCode} OR hikd.material_code = #{materialCode})
        </if>
        <!-- 工单号：查询主表 -->
        <if test="orderNo != null and orderNo != ''">
            AND hik.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        ORDER BY hik.order_date DESC, hik.create_time DESC, hikd.actual_start_time DESC
    </select>

</mapper>
