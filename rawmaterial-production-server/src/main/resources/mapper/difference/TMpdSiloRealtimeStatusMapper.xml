<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.realtime.TMpdSiloRealtimeStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.rawmaterial.entity.silo.TMpdSiloRealtimeDO">
        <id column="id" property="id"/>
        <result column="business_system" property="businessSystem"/>
        <result column="material_type" property="materialType"/>
        <result column="task_type" property="taskType"/>
        <result column="data_type" property="dataType"/>
        <result column="task_no" property="taskNo"/>
        <result column="silo_no" property="siloNo"/>
        <result column="inventory_weight" property="inventoryWeight"/>
        <result column="material_code" property="materialCode"/>
        <result column="status" property="status"/>
        <result column="material_id" property="materialId"/>
        <result column="material_name" property="materialName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- DTO查询映射结果 -->
    <resultMap id="DTOResultMap" type="com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusDTO">
        <id column="id" property="id"/>
        <result column="business_system" property="businessSystem"/>
        <result column="material_type" property="materialType"/>
        <result column="task_type" property="taskType"/>
        <result column="data_type" property="dataType"/>
        <result column="task_no" property="taskNo"/>
        <result column="silo_no" property="siloNo"/>
        <result column="inventory_weight" property="inventoryWeight"/>
        <result column="material_code" property="materialCode"/>
        <result column="status" property="status"/>
        <result column="material_id" property="materialId"/>
        <result column="material_name" property="materialName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="updater_id" property="updaterId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_system, material_type, task_type, data_type, task_no, silo_no,
        inventory_weight, material_code, status, material_id, material_name,
        create_time, update_time, creator_id, updater_id, site_num, deleted
    </sql>

    <!-- 分页查询筒仓实时状态信息列表 -->
    <select id="selectSiloRealtimeStatusList" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        <if test="query.businessSystem != null and query.businessSystem != ''">
            AND business_system = #{query.businessSystem}
        </if>
        <if test="query.businessSystemList != null and query.businessSystemList.size() > 0">
            AND business_system IN
            <foreach collection="query.businessSystemList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.materialType != null and query.materialType != ''">
            AND material_type = #{query.materialType}
        </if>
        <if test="query.materialTypeList != null and query.materialTypeList.size() > 0">
            AND material_type IN
            <foreach collection="query.materialTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.taskType != null and query.taskType != ''">
            AND task_type = #{query.taskType}
        </if>
        <if test="query.taskTypeList != null and query.taskTypeList.size() > 0">
            AND task_type IN
            <foreach collection="query.taskTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.dataType != null and query.dataType != ''">
            AND data_type = #{query.dataType}
        </if>
        <if test="query.dataTypeList != null and query.dataTypeList.size() > 0">
            AND data_type IN
            <foreach collection="query.dataTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.taskNo != null and query.taskNo != ''">
            AND task_no LIKE CONCAT('%', #{query.taskNo}, '%')
        </if>
        <if test="query.taskNoList != null and query.taskNoList.size() > 0">
            AND task_no IN
            <foreach collection="query.taskNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.siloNo != null and query.siloNo != ''">
            AND silo_no LIKE CONCAT('%', #{query.siloNo}, '%')
        </if>
        <if test="query.siloNoList != null and query.siloNoList.size() > 0">
            AND silo_no IN
            <foreach collection="query.siloNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.materialCode != null and query.materialCode != ''">
            AND material_code LIKE CONCAT('%', #{query.materialCode}, '%')
        </if>
        <if test="query.materialCodeList != null and query.materialCodeList.size() > 0">
            AND material_code IN
            <foreach collection="query.materialCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.status != null and query.status != ''">
            AND status = #{query.status}
        </if>
        <if test="query.statusList != null and query.statusList.size() > 0">
            AND status IN
            <foreach collection="query.statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.materialId != null">
            AND material_id = #{query.materialId}
        </if>
        <if test="query.materialIdList != null and query.materialIdList.size() > 0">
            AND material_id IN
            <foreach collection="query.materialIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.materialName != null and query.materialName != ''">
            AND material_name LIKE CONCAT('%', #{query.materialName}, '%')
        </if>
        <if test="query.minInventoryWeight != null">
            AND inventory_weight >= #{query.minInventoryWeight}
        </if>
        <if test="query.maxInventoryWeight != null">
            AND inventory_weight &lt;= #{query.maxInventoryWeight}
        </if>
        <if test="query.createTimeStart != null">
            AND create_time >= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            AND create_time &lt;= #{query.createTimeEnd}
        </if>
        <if test="query.updateTimeStart != null">
            AND update_time >= #{query.updateTimeStart}
        </if>
        <if test="query.updateTimeEnd != null">
            AND update_time &lt;= #{query.updateTimeEnd}
        </if>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY update_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据业务系统和任务号查询筒仓实时状态信息 -->
    <select id="selectByBusinessSystemAndTaskNo" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND business_system = #{businessSystem}
        AND task_no = #{taskNo}
        ORDER BY update_time DESC
    </select>

    <!-- 根据筒仓号查询最新的实时状态信息 -->
    <select id="selectLatestBySiloNo" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND silo_no = #{siloNo}
        ORDER BY update_time DESC
        LIMIT 1
    </select>

    <!-- 根据物料类型查询筒仓实时状态信息 -->
    <select id="selectByMaterialType" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND material_type = #{materialType}
        ORDER BY update_time DESC
    </select>

    <!-- 根据业务系统和物料类型查询筒仓实时状态信息 -->
    <select id="selectByBusinessSystemAndMaterialType" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND business_system = #{businessSystem}
        AND material_type = #{materialType}
        ORDER BY update_time DESC
    </select>

    <!-- 根据数据类型查询筒仓实时状态信息 -->
    <select id="selectByDataType" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND data_type = #{dataType}
        ORDER BY update_time DESC
    </select>

    <!-- 批量插入筒仓实时状态信息 -->
    <insert id="batchInsert">
        INSERT INTO t_mpd_silo_realtime (
        business_system, material_type, task_type, data_type, task_no, silo_no,
        inventory_weight, material_code, status, material_id, material_name,
        create_time, update_time, creator_id, updater_id, site_num, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.businessSystem}, #{item.materialType}, #{item.taskType}, #{item.dataType},
            #{item.taskNo}, #{item.siloNo}, #{item.inventoryWeight}, #{item.materialCode},
            #{item.status}, #{item.materialId}, #{item.materialName}, #{item.createTime},
            #{item.updateTime}, #{item.creatorId}, #{item.updaterId}, #{item.siteNum}, #{item.deleted}
            )
        </foreach>
    </insert>

    <!-- 根据任务号删除筒仓实时状态信息 -->
    <update id="deleteByTaskNo">
        UPDATE t_mpd_silo_realtime
        SET deleted = 1, update_time = NOW()
        WHERE deleted = 0 AND task_no = #{taskNo}
    </update>

    <!-- 根据筒仓号和时间范围删除历史数据 -->
    <update id="deleteHistoryData">
        UPDATE t_mpd_silo_realtime
        SET deleted = 1, update_time = NOW()
        WHERE deleted = 0
        <if test="siloNo != null and siloNo != ''">
            AND silo_no = #{siloNo}
        </if>
        AND create_time &lt; #{beforeTime}
    </update>

    <select id="selectBySiloNos" resultType="java.math.BigDecimal">
        SELECT
        inventory_weight
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        <if test="siloIdCodes != null and siloIdCodes.length > 0">
            AND silo_no IN
            <foreach collection="siloIdCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY update_time DESC
        LIMIT 1
    </select>

    <select id="selectByMaterialCodeAndTaskNoAndSiloNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND material_code = #{materialCode}
        AND task_no = #{taskNo}
        AND silo_no = #{siloNo}
        LIMIT 1
    </select>

    <select id="selectByMaterialCodeAndQuantityAndSiloNoAndTaskNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND material_code = #{materialCode}
        AND inventory_weight = #{inventoryWeight}
        AND silo_no = #{siloNo}
        AND task_no = #{taskNo}
        LIMIT 1
    </select>

    <select id="getListLatestSiloRealtimeStatusBySiloNo" resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_mpd_silo_realtime
        WHERE deleted = 0
        AND silo_no IN
        <foreach collection="siloNoList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY update_time DESC
    </select>
</mapper>
