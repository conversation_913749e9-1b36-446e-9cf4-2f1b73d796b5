<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.rawmaterial.dao.RtOrderDetailMapper">

    <!-- 根据物料编码、任务号、开始时间查询 -->
    <select id="selectByMaterialCodeAndTaskNoAndTime" resultType="com.hvisions.rawmaterial.entity.TMpdRtOrderDetail">
        SELECT *
        FROM t_mpd_rt_order_detail
        WHERE material_code = #{materialCode}
        AND control_task_no = #{taskNo}
        AND actual_begin_time = #{startTime}
        AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据工单id查询工单数量汇总 -->
    <select id="selectSummaryByOrderId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(actual_quantity), 0)
        FROM t_mpd_rt_order_detail
        WHERE order_id = #{orderId}
        AND deleted = 0
    </select>

</mapper>
