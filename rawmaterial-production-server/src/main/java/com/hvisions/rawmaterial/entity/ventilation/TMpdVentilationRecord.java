package com.hvisions.rawmaterial.entity.ventilation;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 通风记录表
 * <AUTHOR>
 * @version 1.0
 * @Description: 通风记录实体类
 * @Date: 2024/07/14
 */
@Table(name = "t_mpd_ventilation_record")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_ventilation_record", comment = "通风记录表")
@Data
@TableName("t_mpd_ventilation_record")
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
public class TMpdVentilationRecord extends SysBase {

    /**
     * 序号（流水号）
     */
    @Column(columnDefinition = "varchar(50) COMMENT '序号（流水号）'")
    private String serialNumber;

    /**
     * 工单号：TF+YYMMDD+三位流水
     */
    @Column(length = 20, columnDefinition = "varchar(20) NOT NULL COMMENT '工单号'")
    private String workOrderNumber;

    /**
     * 工单日期
     */
    @Column(columnDefinition = "date NOT NULL COMMENT '工单日期'")
    private Date workOrderDate;

    /**
     * 筒仓ID
     */
    @Column(columnDefinition = "int COMMENT '筒仓ID'")
    private Integer siloId;

    /**
     * 通风筒仓名称
     */
    @Column(length = 100, columnDefinition = "varchar(100) COMMENT '通风筒仓名称'")
    private String siloName;

    /**
     * 通风筒仓编码
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '通风筒仓编码'")
    private String siloCode;

    /**
     * 物料名称
     */
    @Column(length = 100, columnDefinition = "varchar(100) COMMENT '物料名称'")
    private String materialName;

    /**
     * 物料编码
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 物料ID
     */
    @Column(columnDefinition = "int COMMENT '物料ID'")
    private Integer materialId;

    /**
     * 通风时长（分钟）
     */
    @Column(columnDefinition = "int COMMENT '通风时长（分钟）'")
    private Integer ventilationDuration;

    /**
     * 通风前温度
     */
    @Column(columnDefinition = "decimal(5,2) COMMENT '通风前温度'")
    private Double temperatureBefore;

    /**
     * 通风后温度
     */
    @Column(columnDefinition = "decimal(5,2) COMMENT '通风后温度'")
    private Double temperatureAfter;

    /**
     * 开始时间
     */
    @Column(columnDefinition = "datetime COMMENT '开始时间'")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(columnDefinition = "datetime COMMENT '结束时间'")
    private Date endTime;

    /**
     * 状态：1-执行中，2-已完成
     */
    @Column(columnDefinition = "int NOT NULL DEFAULT 1 COMMENT '状态：1-执行中，2-已完成'")
    private Integer status;

    /**
     * 温度维护人ID
     */
    @Column(columnDefinition = "int COMMENT '温度维护人ID'")
    private Integer temperatureMaintainerId;

    /**
     * 温度维护人姓名
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '温度维护人姓名'")
    private String temperatureMaintainerName;

    /**
     * 温度维护时间
     */
    @Column(columnDefinition = "datetime COMMENT '温度维护时间'")
    private Date temperatureMaintainTime;

    /**
     * 备注
     */
    @Column(length = 500, columnDefinition = "varchar(500) COMMENT '备注'")
    private String remark;

    /**
     * 中控系统任务ID
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '中控系统任务ID'")
    private String centralControlTaskId;

    /**
     * 请求日志记录ID
     */
    @Column(columnDefinition = "varchar(255) COMMENT '请求日志记录ID'")
    private String logId;
}
