package com.hvisions.rawmaterial.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hvisions.brewage.mkwine.DataBaseAuditListener;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>Title: SysBase</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@MappedSuperclass
@EntityListeners({AuditingEntityListener.class, DataBaseAuditListener.class})
public class SysBase {

    /**
     * 主键
     */
    @Id
    @Column(columnDefinition="int COMMENT '主键'")
    @TableId(type = IdType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(updatable = false,columnDefinition="datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'")
    @TableField(fill = FieldFill.INSERT)
    @CreatedDate
    protected Date createTime;

    /**
     * 修改时间
     */
    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '更新时间'")
    @LastModifiedDate
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date updateTime;

    /**
     * 创建人
     */
    @Column(updatable = false)
    @CreatedBy
    @TableField(fill = FieldFill.INSERT)
    protected Integer creatorId;

    /**
     * 修改人
     */
    @LastModifiedBy
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Integer updaterId;

    /**
     * 用于后续saas服务租户字段
     */
    @Column(columnDefinition = "varchar(255) COMMENT '租户'")
    protected String siteNum;


    /**
     * 删除标识
     */
    @TableLogic
    @Column(columnDefinition = "bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'")
    protected Boolean deleted;
}
