package com.hvisions.rawmaterial.entity.sorghum;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 高粱转粮工单-二期改为高粱发料任务
 * @date 2022/4/25 10:09
 */
@Table(name = "t_mpd_sorghum_shipment_order")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_sorghum_shipment_order", comment = "高粱转粮工单")
@Entity
@TableName("t_mpd_sorghum_shipment_order")
@KeySequence("t_mpd_sorghum_shipment_order_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdSorghumShipmentOrder extends SysBase {

    /**
     * 计划id
     */
    //private Integer planId;
    /**
     * 工单号
     */
    @Column(name = "order_no",columnDefinition = "varchar(255) COMMENT '工单号'")
    private String orderNo;
    /**
     * 工单日期
     */
    @Column(name = "order_date",columnDefinition = "datetime COMMENT '工单日期'")
    private Date orderDate;
    /**
     * 生产类型编码
     */
    //@Column(name = "production_type_code",columnDefinition = "varchar(255) COMMENT '生产类型编码'")
    //private String productionTypeCode;

    /**
     * 中心id
     */
    //@Column(name = "center_id",columnDefinition = "int COMMENT '中心id'")
    //private Integer centerId;
    /**
     * 中心编码
     */
    //@Column(name = "center",columnDefinition = "varchar(255) COMMENT '中心编码'")
    //private String center;

    /**
     * 物料id
     */
    @Column(name = "material_id",columnDefinition = "int COMMENT '物料id'")
    private Integer materialId;
    /**
     * 物料编码
     */
    @Column(name = "material_code",columnDefinition = "varchar(255) COMMENT '物料编码'")
    private String materialCode;
    /**
     * 物料名称
     */
    @Column(name = "material_name",columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;
    /**
     * 物料单位
     */
    @Column(name = "unit",columnDefinition = "varchar(255) COMMENT '物料单位'")
    private String unit;
    /**
     * 物料需求数量
     */
    //@Column(name = "requirement_quantity",columnDefinition = "decimal(10,2) COMMENT '物料需求数量'")
    //private BigDecimal requirementQuantity;
    /**
     * 当前库存数量
     */
    @Column(name = "inventory_quantity",columnDefinition = "decimal(10,2) COMMENT '当前库存数量'")
    private BigDecimal inventoryQuantity;
    /**
     * 计划数量
     */
    //@Column(name = "plan_quantity",columnDefinition = "decimal(10,2) COMMENT '计划数量'")
    //private BigDecimal planQuantity;
    /**
     * 实际数量
     */
    @Column(name = "actual_quantity",columnDefinition = "decimal(10,2) COMMENT '实际数量'")
    private BigDecimal actualQuantity;


}
