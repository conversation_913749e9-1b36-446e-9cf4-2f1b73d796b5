package com.hvisions.rawmaterial.entity.ricehusk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 稻壳入仓任务详情
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Table(name = "t_mpd_rice_husk_inbound_task_detail")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_rice_husk_inbound_task_detail", comment = "稻壳入仓任务详情")
@Entity
@TableName("t_mpd_rice_husk_inbound_task_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdRiceHuskInboundTaskDetail extends SysBase {

    /**
     * 任务ID
     */
    @Column(name = "task_id", columnDefinition = "int COMMENT '任务ID'")
    private Integer taskId;

    /**
     * 筒仓id
     */
    @Column(name = "silo_id", columnDefinition = "int COMMENT '入仓id'")
    private Integer siloId;

    /**
     * 入仓名称
     */
    @Column(name = "silo_name", columnDefinition = "varchar(255) COMMENT '入仓名称'")
    private String siloName;

    /**
     * 入仓编码
     */
    @Column(name = "silo_code", columnDefinition = "varchar(255) COMMENT '入仓编码'")
    private String siloCode;

    /**
     * 物料名称
     */
    @Column(name = "material_name", columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;

    /**
     * 物料编码
     */
    @Column(name = "material_code", columnDefinition = "varchar(255) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 计量单位
     */
    @Column(name = "unit", columnDefinition = "varchar(255) COMMENT '计量单位'")
    private String unit;

    /**
     * 数量
     */
    @Column(name = "quantity", columnDefinition = "decimal(20,6) COMMENT '数量'")
    private BigDecimal quantity;

    /**
     * 实际开始时间
     */
    @Column(name = "actual_start_time", columnDefinition = "datetime COMMENT '实际开始时间'")
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    @Column(name = "actual_completion_time", columnDefinition = "datetime COMMENT '实际完成时间'")
    private Date actualCompletionTime;

    /**
     * 状态
     */
    @Column(name = "status", columnDefinition = "int COMMENT '状态'")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "creator_name", columnDefinition = "varchar(255) COMMENT '创建人'")
    private String creatorName;

    /**
     * 唯一标识
     */
    @Column(name = "unique_id", columnDefinition = "varchar(255) COMMENT '唯一标识'")
    private String uniqueId;

    /**
     * 中控任务单号
     */
    @Column(name = "control_task_no", columnDefinition = "varchar(255) COMMENT '中控任务单号'")
    private String controlTaskNo;

    /**
     * 日志id
     */
    @Column(name = "log_id", columnDefinition = "varchar(255) COMMENT '日志id'")
    private String logId;
}
