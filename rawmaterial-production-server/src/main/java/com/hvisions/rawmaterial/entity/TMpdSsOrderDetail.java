package com.hvisions.rawmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 高粱转粮工单详情
 * @date 2022/4/25 10:09
 */
@Table(name = "t_mpd_ss_order_detail")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_ss_order_detail", comment = "高粱转粮工单详情")
@Entity
@TableName("t_mpd_ss_order_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TMpdSsOrderDetail extends SysBase {

    /**
     * 工单id
     */
    @Column(columnDefinition = "int COMMENT '工单id'")
    private Integer orderId;
    /**
     * 分发筒仓id(库区)
     */
    @Column(columnDefinition = "int COMMENT '分发筒仓id(库区)'")
    private Integer sendSiloId;
    /**
     * 接收筒仓id(库区)
     */
    @Column(columnDefinition = "int COMMENT '接收筒仓id(库区)'")
    private Integer acceptSiloId;
    /**
     * 物料id
     */
    @Column(columnDefinition = "int COMMENT '物料id'")
    private Integer materialId;
    /**
     * 物料编码
     */
    @Column(columnDefinition = "varchar(255) COMMENT '物料编码'")
    private String materialCode;
    /**
     * 物料名称
     */
    @Column(columnDefinition = "varchar(255) COMMENT '物料名称'")
    private String materialName;
    /**
     * 物料单位
     */
    @Column(columnDefinition = "varchar(255) COMMENT '物料单位'")
    private String unit;
    /**
     * 物料需求数量
     */
    @Column(columnDefinition = "decimal(20,6) COMMENT '物料需求数量'")
    private BigDecimal requirementQuantity;

    /**
     * 计划数量
     */
    @Column(columnDefinition = "decimal(20,6) COMMENT '计划数量'")
    private BigDecimal planQuantity;
    /**
     * 实际数量
     */
    @Column(columnDefinition = "decimal(20,6) COMMENT '实际数量'")
    private BigDecimal actualQuantity;

    /**
     * 秤零时重量
     */
    @Column(columnDefinition = "decimal(20,6) COMMENT '秤零时重量'")
    private BigDecimal tempQuantity;
    /**
     * 批次
     */
    @Column(columnDefinition = "varchar(255) COMMENT '批次'")
    private String batch;
    /**
     * 实际开始时间
     */
    @Column(columnDefinition = "datetime COMMENT '实际开始时间'")
    private Date actualBeginTime;
    /**
     * 实际结束时间
     */
    @Column(columnDefinition = "datetime COMMENT '实际结束时间'")
    private Date actualEndTime;

    /**
     * 执行状态： 0执行中，1已完成
     */
    @Column(columnDefinition = "varchar(255) COMMENT '执行状态： 0执行中，1已完成'")
    private String state;

    /**
     * 是否是手动：0-自动，1-手动
     */
    @Column(columnDefinition = "varchar(255) COMMENT '是否是手动：0-自动，1-手动'")
    private String isManual;

    /**
     * 创建人
     */
    @Column(columnDefinition = "varchar(255) COMMENT '创建人'")
    private String creatorName;

    /**
     * 唯一标识
     */
    @Column(columnDefinition = "varchar(255) COMMENT '唯一标识'")
    private String uniqueId;

    /**
     * 中控任务单号
     */
    @Column(columnDefinition = "varchar(255) COMMENT '中控任务单号'")
    private String controlTaskNo;

    /**
     * 日志id
     */
    @Column(columnDefinition = "varchar(255) COMMENT '日志id'")
    private String logId;
}
