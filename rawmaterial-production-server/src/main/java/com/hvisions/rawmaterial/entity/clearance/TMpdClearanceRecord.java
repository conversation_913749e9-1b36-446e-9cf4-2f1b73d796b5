package com.hvisions.rawmaterial.entity.clearance;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.rawmaterial.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 清仓记录表
 * <AUTHOR>
 * @version 1.0
 * @Description: 清仓记录实体类
 */
@Table(name = "t_mpd_clearance_record")
@org.hibernate.annotations.Table(appliesTo = "t_mpd_clearance_record", comment = "清仓记录表")
@Data
@TableName("t_mpd_clearance_record")
@Entity
@DynamicInsert
@DynamicUpdate
@EqualsAndHashCode(callSuper = true)
public class TMpdClearanceRecord extends SysBase {

    /**
     * 工单号：QC+YYMMDD+三位流水
     */
    @Column(length = 20, columnDefinition = "varchar(20) NOT NULL COMMENT '工单号'")
    private String workOrderNumber;

    /**
     * 工单日期
     */
    @Column(columnDefinition = "date NOT NULL COMMENT '工单日期'")
    private Date workOrderDate;

    /**
     * 清仓筒仓名称
     */
    @Column(length = 100, columnDefinition = "varchar(100) COMMENT '清仓筒仓名称'")
    private String siloName;

    /**
     * 清仓筒仓编号
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '清仓筒仓编号'")
    private String siloCode;

    /**
     * 筒仓ID
     */
    @Column(columnDefinition = "int COMMENT '筒仓ID'")
    private Integer siloId;

    /**
     * 物料编码
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '物料编码'")
    private String materialCode;

    /**
     * 物料名称
     */
    @Column(length = 100, columnDefinition = "varchar(100) COMMENT '物料名称'")
    private String materialName;

    /**
     * 计量单位
     */
    @Column(length = 20, columnDefinition = "varchar(20) COMMENT '计量单位'")
    private String unit;

    /**
     * 清仓前数量
     */
    @Column(columnDefinition = "decimal(20,6) COMMENT '清仓前数量'")
    private BigDecimal quantityBefore;

    /**
     * 清仓时间
     */
    @Column(columnDefinition = "datetime COMMENT '清仓时间'")
    private Date clearanceTime;

    /**
     * 状态：1-执行中，2-已完成
     */
    @Column(columnDefinition = "int NOT NULL DEFAULT 1 COMMENT '状态：1-执行中，2-已完成'")
    private Integer status;

    /**
     * 批次号
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '批次号'")
    private String batchNumber;

    /**
     * 备注
     */
    @Column(length = 500, columnDefinition = "varchar(500) COMMENT '备注'")
    private String remark;

    /**
     * 中控系统任务ID
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '中控系统任务ID'")
    private String centralControlTaskId;

    /**
     * 执行人ID
     */
    @Column(columnDefinition = "int COMMENT '执行人ID'")
    private Integer executorId;

    /**
     * 执行人姓名
     */
    @Column(length = 50, columnDefinition = "varchar(50) COMMENT '执行人姓名'")
    private String executorName;

    /**
     * 完成时间
     */
    @Column(columnDefinition = "datetime COMMENT '完成时间'")
    private Date completionTime;

    /**
     * 请求参数
     */
    @Column(columnDefinition = "text COMMENT '请求参数'")
    private String requestParams;
}
