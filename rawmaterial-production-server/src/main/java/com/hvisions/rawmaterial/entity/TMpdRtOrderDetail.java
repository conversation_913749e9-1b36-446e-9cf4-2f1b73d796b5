package com.hvisions.rawmaterial.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 稻壳转运工单详情
 * @date 2022/4/25 10:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mpd_rt_order_detail")
public class TMpdRtOrderDetail extends SysBase {
    
    /**
     * 工单id
     */
    private Integer orderId;
    /**
     * 分发筒仓id(库区)
     */
    private Integer sendSiloId;
    /**
     * 接收筒仓id(库区)
     */
    private Integer acceptSiloId;


    /**
     * 物料id
     */
    private Integer materialId;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料单位
     */
    private String unit;
    /**
     * 物料需求数量
     */
    private BigDecimal requirementQuantity;

    /**
     * 计划数量
     */
    private BigDecimal planQuantity;
    /**
     * 实际数量
     */
    private BigDecimal actualQuantity;
    /**
     * 批次
     */
    private String batch;
    /**
     * 实际开始实际
     */
    private Date actualBeginTime;
    /**
     * 实际结束时间
     */
    private Date actualEndTime;
    /**
     * 0执行种，1待执行
     */
    private String state;

    /**
     * 是否是手动：0-自动，1-手动
     */
    private String isManual;

    /**
     * 中控任务号
     */
    private String controlTaskNo;

    /**
     * 日志ID
     */
    private String logId;

}
