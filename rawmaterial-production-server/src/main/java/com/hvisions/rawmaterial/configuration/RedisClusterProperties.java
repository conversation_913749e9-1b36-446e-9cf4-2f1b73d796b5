package com.hvisions.rawmaterial.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "spring.redis.cluster")
public class RedisClusterProperties {
    private List<String> nodes;
    private String password;

    // Getters and Setters
    public List<String> getNodes() { return nodes; }
    public void setNodes(List<String> nodes) { this.nodes = nodes; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}