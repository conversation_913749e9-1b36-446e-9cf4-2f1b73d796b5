package com.hvisions.rawmaterial.enums;

/**
 * 原料生产日志捕获枚举
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public enum LogCaptureEnum {
    
    SORGHUM_INBOUND_SYNC_CENTRAL_CONTROL(
            "rawmaterial-production-server", 
            "中控系统", 
            "/api/unified/v1/task",
            "中控系统调用MES", 
            "高粱入仓任务-中控同步到MES"
    ),
    
    RICE_HUSK_INBOUND_SYNC_CENTRAL_CONTROL(
            "rawmaterial-production-server", 
            "中控系统", 
            "/api/unified/v1/task",
            "中控系统调用MES", 
            "稻壳入仓任务-中控同步到MES"
    ),

    SORGHUM_TRANSFER(
            "rawmaterial-production-server",
            "中控系统",
            "/api/unified/v1/task",
            "中控系统调用MES",
            "高粱二期转一期的转运任务信息"
    ),
    
    SORGHUM_TRANSFER_SYNC_CENTRAL_CONTROL(
            "rawmaterial-production-server", 
            "中控系统", 
            "/api/unified/v1/task",
            "中控系统调用MES", 
            "高粱转运工单-中控同步到MES"
    ),

    /**
     * 通风记录
     */
    VENTILATION_RECORD_SYNC_CENTRAL_CONTROL(
            "rawmaterial-production-server",
            "中控系统",
            "/api/unified/v1/task",
            "中控系统调用MES",
            "通风记录-中控同步到MES"
    )
    ;

    private String logModular;
    private String logInvocation;
    private String location;
    private String controllerName;
    private String methodName;

    public String getLogModular() {
        return logModular;
    }

    public String getLogInvocation() {
        return logInvocation;
    }

    public String getLocation() {
        return location;
    }

    public String getControllerName() {
        return controllerName;
    }

    public String getMethodName() {
        return methodName;
    }

    LogCaptureEnum(String logModular, String logInvocation, String location, String controllerName, String methodName) {
        this.logModular = logModular;
        this.logInvocation = logInvocation;
        this.location = location;
        this.controllerName = controllerName;
        this.methodName = methodName;
    }
}
