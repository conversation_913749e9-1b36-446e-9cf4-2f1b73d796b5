package com.hvisions.rawmaterial.dto.monitoring;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 告警通知DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class AlertNotification {
    
    /**
     * 告警ID
     */
    private Integer id;
    
    /**
     * 规则ID
     */
    private Integer ruleId;
    
    /**
     * 告警类型
     */
    private String alertType;
    
    /**
     * 告警级别
     */
    private String alertLevel;
    
    /**
     * 告警消息
     */
    private String alertMessage;
    
    /**
     * 指标名称
     */
    private String metricName;
    
    /**
     * 当前值
     */
    private String currentValue;
    
    /**
     * 阈值
     */
    private String thresholdValue;
    
    /**
     * 告警时间
     */
    private LocalDateTime alertTime;
    
    /**
     * 告警状态: PENDING, ACKNOWLEDGED, RESOLVED
     */
    private String alertStatus;
    
    /**
     * 确认用户ID
     */
    private Integer acknowledgeUserId;
    
    /**
     * 确认用户名
     */
    private String acknowledgeUserName;
    
    /**
     * 确认时间
     */
    private LocalDateTime acknowledgeTime;
    
    /**
     * 确认备注
     */
    private String acknowledgeRemark;
}