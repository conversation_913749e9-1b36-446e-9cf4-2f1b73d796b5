package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 清仓记录DTO
 */
@Data
@ApiModel(value = "清仓记录信息")
public class ClearanceRecordDTO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "序号（流水号）")
    private String serialNumber;

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "工单日期")
    private Date workOrderDate;

    @ApiModelProperty(value = "清仓筒仓名称")
    private String siloName;

    @ApiModelProperty(value = "清仓筒仓编号")
    private String siloCode;

    @ApiModelProperty(value = "筒仓ID")
    private Integer siloId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "清仓前数量")
    private BigDecimal quantityBefore;

    @ApiModelProperty(value = "清仓时间")
    private Date clearanceTime;

    @ApiModelProperty(value = "状态：1-执行中，2-已完成")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "批次号")
    private String batchNumber;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "中控系统任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "执行人ID")
    private Integer executorId;

    @ApiModelProperty(value = "执行人姓名")
    private String executorName;

    @ApiModelProperty(value = "完成时间")
    private Date completionTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;
}
