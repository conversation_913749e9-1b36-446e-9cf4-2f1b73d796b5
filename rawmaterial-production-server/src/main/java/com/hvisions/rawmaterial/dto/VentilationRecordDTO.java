package com.hvisions.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 通风记录DTO
 * @Date: 2024/07/14
 */
@Data
@ApiModel(value = "通风记录信息")
public class VentilationRecordDTO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "序号（流水号）")
    private String serialNumber;

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "工单日期")
    private Date workOrderDate;

    @ApiModelProperty(value = "筒仓ID")
    private Integer siloId;

    @ApiModelProperty(value = "通风筒仓名称", required = true)
    @NotBlank(message = "通风筒仓名称不能为空")
    private String siloName;

    @ApiModelProperty(value = "通风筒仓编码")
    private String siloCode;

    @ApiModelProperty(value = "物料名称", required = true)
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "通风时长（分钟）", required = true)
    @NotNull(message = "通风时长不能为空")
    private Integer ventilationDuration;

    @ApiModelProperty(value = "通风前温度")
    private Double temperatureBefore;

    @ApiModelProperty(value = "通风后温度")
    private Double temperatureAfter;

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "状态：1-执行中，2-已完成")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "温度维护人ID")
    private Integer temperatureMaintainerId;

    @ApiModelProperty(value = "温度维护人姓名")
    private String temperatureMaintainerName;

    @ApiModelProperty(value = "温度维护时间")
    private Date temperatureMaintainTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "中控系统任务ID")
    private String centralControlTaskId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /*@ApiModelProperty(value = "请求参数")
    private String requestParams;*/

    @ApiModelProperty(value = "请求日志记录ID")
    private String logId;
}
