package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.TMpdSsOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:高粱转粮工单详情
 * @date 2022/4/22 10:18
 */
@Mapper
public interface SsOrderDetailMapper extends BaseMapper<TMpdSsOrderDetail> {

    /**
     * 根据物料编码、任务号、开始时间查询
     * @param materialCode
     * @param taskNo
     * @param startTime
     * @return
     */
    TMpdSsOrderDetail selectByMaterialCodeAndTaskNoAndTime(
            @Param("materialCode") String materialCode,
            @Param("taskNo") String taskNo,
            @Param("startTime") Date startTime);

    /**
     * 根据工单id查询工单详情的汇总
     * @param id
     * @return
     */
    BigDecimal selectSummaryByOrderId(Integer id);
}
