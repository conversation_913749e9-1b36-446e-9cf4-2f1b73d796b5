package com.hvisions.rawmaterial.dao.sorghum;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskQueryDTO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 高粱入仓任务Mapper接口
 *
 * <AUTHOR>
 * @date 2026-06-27
 */
@Mapper
public interface SorghumInboundTaskMapper extends BaseMapper<TMpdSorghumInboundTask> {

    /**
     * 根据条件查询高粱入仓任务列表
     * @return 高粱入仓任务列表
     */
    List<SorghumInboundTaskDTO> selectByCondition(SorghumInboundTaskQueryDTO  query);

    /**
     * 根据工单日期查询当天最大流水号
     *
     * @param orderDate 工单日期
     * @return 最大流水号
     */
    String selectMaxSerialNoByDate(@Param("orderDate") Date orderDate);

    /**
     * 根据日期和物料编码查询任务
     *
     * @param dateStr 日期字符串（YYYYMMDD格式）
     * @param materialCode 物料编码
     * @return 任务记录
     */
    TMpdSorghumInboundTask selectByDateAndMaterialCode(@Param("dateStr") Date dateStr, @Param("materialCode") String materialCode);
}