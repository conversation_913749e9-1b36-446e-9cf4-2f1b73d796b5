package com.hvisions.rawmaterial.dao.materialsilo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloQueryDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloQueryPageDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloTreeDTO;
import com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 物料筒仓配置Mapper接口
 * @author: z19235
 * @time: 2025/01/01
 */
@Mapper
public interface MaterialSiloConfigMapper extends BaseMapper<TMpdMateriaSiloDO> {

    /**
     * 获取物料筒仓树形结构
     * @param materialType 物料类型：0-高粱，1-稻壳，2-小麦
     * @return 树形结构列表
     */
    List<MaterialSiloTreeDTO> getMaterialSiloTree(@Param("materialType") Integer materialType);

    /**
     * 分页查询物料筒仓列表
     * @param queryDTO 查询条件
     * @return 筒仓列表
     */
    List<MaterialSiloTreeDTO> getMaterialSiloPageList(MaterialSiloQueryPageDTO queryDTO);

    /**
     * 获取部门树形结构
     * @return 部门树形结构
     */
    List<DepartmentTreeDTO> getDepartmentTree();

    /**
     * 获取子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<DepartmentTreeDTO> getDepartmentChildren(@Param("parentId") Integer parentId);

    /**
     * 查询所有物料类型第三级最后一层的数据
     * @return 所有物料类型第三级最后一层的筒仓列表
     */
    List<MaterialSiloTreeDTO> getAllMaterialTypeLeafNodes();

    TMpdMateriaSiloDO findSiloBySiloCode(String siloCode);

    String[] selectSiloCodesByMaterialType(@NotNull(message = "物料类型不能为空") Integer materialType);

    /**
     * 根据编码查询单个物料筒仓
     * @param code
     * @return
     */
    MaterialSiloConfigDTO getMaterialSiloByCode(@Param("code") String code);

    /**
     * 根据ID查询单个物料筒仓
     * @param code
     * @param materialType
     * @return
     */
    MaterialSiloConfigDTO getMaterialSiloByCodeAndType(@Param("code") String code, @Param("materialType") Integer materialType);

    /**
     * 根据编码查询所有匹配的物料
     * @param code
     * @return
     */
    List<MaterialSiloTreeDTO> getAllMaterialByCode(@Param("code") String code);

    /**
     * 获取物料筒仓配置列表-一级
     * @return
     */
    List<MaterialSiloConfigDTO> getSiloConfigFistList();

    List<MaterialSiloConfigDTO> getLocationList(MaterialSiloQueryDTO queryDTO);
}
