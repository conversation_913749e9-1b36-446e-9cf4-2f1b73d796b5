package com.hvisions.rawmaterial.dao.sorghum;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDetailDTO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2025/6/27 14:43
 * @Author: zhangq
 * @Version: 1.0
 */
@Mapper
public interface SorghumInboundTaskDetailMapper extends BaseMapper<TMpdSorghumInboundTaskDetail> {

    /**
     * 根据任务ID查询详情列表
     *
     * @param parentId 任务ID
     * @return 详情列表
     */
    List<SorghumInboundTaskDetailDTO> findByTaskId(String parentId);

    /**
     * 根据物料编码、任务号、开始时间查询详情
     *
     * @param materialCode 物料编码
     * @param controlTaskNo 任务号
     * @param actualStartTime 开始时间
     * @return 详情记录
     */
    TMpdSorghumInboundTaskDetail selectByMaterialCodeAndTaskNoAndTime(
        @Param("materialCode") String materialCode,
        @Param("controlTaskNo") String controlTaskNo,
        @Param("actualStartTime") Date actualStartTime);

    /**
     * 根据唯一标识查询详情
     *
     * @param controlTaskNo 唯一标识
     * @return 详情记录
     */
    TMpdSorghumInboundTaskDetail selectByControlTaskNo(@Param("controlTaskNo") String controlTaskNo);

    /**
     * 根据任务ID查询数量
     *
     * @param id 任务ID
     * @return 数量
     */
    BigDecimal selectSummaryByOrderId(Integer id);
}
