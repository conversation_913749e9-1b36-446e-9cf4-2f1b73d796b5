package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转粮工单
 * @date 2022/4/22 10:18
 */
@Mapper
public interface SorghumShipmentOrderMapper extends BaseMapper<TMpdSorghumShipmentOrder> {
    List<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(SorghumShipmentOrderPageQueryDTO queryDTO);

    List<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(SorghumShipmentOrderPageQueryDTO sorghumShipmentOrderPageQueryDTO);

    /**
     * 根据工单日期查询当天的任务
     *
     * @param orderDate 工单日期
     * @return 当天的任务
     */
    TMpdSorghumShipmentOrder selectByDateAndMaterialCode(
            @Param("orderDate") Date orderDate,
            @Param("materialCode") String materialCode
    );

    /**
     * 根据工单日期查询当天最大流水号
     *
     * @param orderDate 工单日期
     * @return 最大流水号
     */
    String selectMaxSerialNoByDate(@Param("orderDate") Date orderDate);

}
