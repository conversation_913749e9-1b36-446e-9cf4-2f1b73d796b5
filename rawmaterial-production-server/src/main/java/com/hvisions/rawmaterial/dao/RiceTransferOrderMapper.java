package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.production.rice.transfer.detail.RiceTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.entity.TMpdRiceTransferOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳转运工单
 * @date 2022/4/22 10:18
 */
@Mapper
public interface RiceTransferOrderMapper extends BaseMapper<TMpdRiceTransferOrder> {
    List<RiceTransferOrderPageDTO> getRiceTransferOrderPageList(RiceTransferOrderPageQueryDTO queryDTO);

    List<RiceTransferDetailListDTO> getRiceTransferOrderDetailPageList(RiceTransferOrderPageQueryDTO riceTransferOrderPageQueryDTO);

    /**
     * 根据日期和物料编码查询工单信息
     * @param recordDate 记录日期
     * @param materialCode 物料编码
     * @return 稻壳转运工单
     */
    TMpdRiceTransferOrder selectByDateAndMaterialCode(
            @Param("recordDate") Date recordDate,
            @Param("materialCode") String materialCode);
}
