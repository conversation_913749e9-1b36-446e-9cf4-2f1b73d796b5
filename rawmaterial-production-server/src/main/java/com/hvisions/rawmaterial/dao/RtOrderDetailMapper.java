package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.TMpdRtOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:稻壳转运工单详情
 * @date 2022/4/22 10:18
 */
@Mapper
public interface RtOrderDetailMapper extends BaseMapper<TMpdRtOrderDetail> {

    /**
     * 根据物料编码、任务号、开始时间查询
     * @param materialCode 物料编码
     * @param taskNo 任务号
     * @param startTime 开始时间
     * @return 稻壳转运工单详情
     */
    TMpdRtOrderDetail selectByMaterialCodeAndTaskNoAndTime(
            @Param("materialCode") String materialCode,
            @Param("taskNo") @NotNull(message = "任务号不能为空") String taskNo,
            @Param("startTime") Date startTime);

    /**
     * 根据工单id查询工单数量汇总
     * @param orderId 工单ID
     * @return 汇总数量
     */
    BigDecimal selectSummaryByOrderId(@Param("orderId") Integer orderId);
}
