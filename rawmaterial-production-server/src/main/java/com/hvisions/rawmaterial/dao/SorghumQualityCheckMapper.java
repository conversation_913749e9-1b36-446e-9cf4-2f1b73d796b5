package com.hvisions.rawmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.entity.TMpdSorghumQualityCheck;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 高粱筒仓快检数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-23
 */
@Mapper
public interface SorghumQualityCheckMapper extends BaseMapper<TMpdSorghumQualityCheck> {

    /**
     * 根据任务号查询快检记录
     *
     * @param taskNo 任务号
     * @return 快检记录
     */
    TMpdSorghumQualityCheck selectByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 根据任务号和筒仓号查询快检记录
     *
     * @param taskNo 任务号
     * @param siloNo 筒仓号
     * @return 快检记录
     */
    TMpdSorghumQualityCheck selectByTaskNoAndSiloNo(@Param("taskNo") String taskNo, @Param("siloNo") String siloNo);

    /**
     * 根据筒仓号和时间范围查询快检记录
     *
     * @param siloNo 筒仓号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 快检记录列表
     */
    List<TMpdSorghumQualityCheck> selectBySiloNoAndTimeRange(@Param("siloNo") String siloNo, 
                                                             @Param("startTime") String startTime, 
                                                             @Param("endTime") String endTime);

    /**
     * 根据唯一标识查询快检记录
     *
     * @param uniqueId 唯一标识
     * @return 快检记录
     */
    TMpdSorghumQualityCheck selectByUniqueId(@Param("uniqueId") String uniqueId);

    /**
     * 根据中控任务ID查询快检记录
     *
     * @param centralControlTaskId 中控任务ID
     * @return 快检记录
     */
    TMpdSorghumQualityCheck selectByCentralControlTaskId(@Param("centralControlTaskId") String centralControlTaskId);

    /**
     * 批量插入快检记录
     *
     * @param records 快检记录列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("records") List<TMpdSorghumQualityCheck> records);

    /**
     * 根据物料编码和时间范围查询快检记录
     *
     * @param materialCode 物料编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 快检记录列表
     */
    List<TMpdSorghumQualityCheck> selectByMaterialCodeAndTimeRange(@Param("materialCode") String materialCode,
                                                                   @Param("startTime") String startTime,
                                                                   @Param("endTime") String endTime);

    /**
     * 根据检测状态查询快检记录
     *
     * @param status 检测状态
     * @return 快检记录列表
     */
    List<TMpdSorghumQualityCheck> selectByStatus(@Param("status") Integer status);

    /**
     * 更新检测状态
     *
     * @param id 记录ID
     * @param status 新状态
     * @return 更新成功的记录数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
}
