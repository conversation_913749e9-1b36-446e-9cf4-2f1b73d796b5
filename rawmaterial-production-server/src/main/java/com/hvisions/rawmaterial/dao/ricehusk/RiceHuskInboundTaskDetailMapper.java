package com.hvisions.rawmaterial.dao.ricehusk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO;
import com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 稻壳入仓任务详情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface RiceHuskInboundTaskDetailMapper extends BaseMapper<TMpdRiceHuskInboundTaskDetail> {

    /**
     * 根据任务ID查询详情列表
     * 
     * @param taskId 任务ID
     * @return 详情列表
     */
    List<RiceHuskInboundTaskDetailDTO> findByTaskId(@Param("taskId") String taskId);

    /**
     * 根据物料编码、实际数量、开始时间查询详情
     *
     * @param materialCode 物料编码
     * @param controlTaskNo 实际数量
     * @param actualStartTime 开始时间
     * @return 详情记录
     */
    TMpdRiceHuskInboundTaskDetail selectByMaterialCodeAndTaskNoAndTime(
        @Param("materialCode") String materialCode,
        @Param("controlTaskNo") String controlTaskNo,
        @Param("actualStartTime") Date actualStartTime);

    /**
     * 根据任务单号查询详情
     *
     * @param taskNo 任务单号
     * @return 详情记录
     */
    TMpdRiceHuskInboundTaskDetail selectByControlTaskNo(@NotNull(message = "任务号不能为空") @Param("controlTaskNo") String taskNo);

    /**
     * 根据订单ID查询数量
     *
     * @param id 订单ID
     * @return 数量
     */
    BigDecimal selectSummaryByOrderId(Integer id);
}
