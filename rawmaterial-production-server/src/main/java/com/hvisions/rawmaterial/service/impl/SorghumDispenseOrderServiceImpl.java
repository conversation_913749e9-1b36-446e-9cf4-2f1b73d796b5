package com.hvisions.rawmaterial.service.impl;


import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.brewage.feign.purchase.PurchaseClient;
import com.hvisions.brewage.purchase.dto.InventoryAllocationDto;
import com.hvisions.brewage.purchase.dto.OrderWriteOffHeaderDto;
import com.hvisions.brewage.purchase.dto.SapBaseResponseDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.powder.client.SapClient;
import com.hvisions.powder.dto.qudou.SapPostVO;
import com.hvisions.rawmaterial.advice.UserAuditorAware;
import com.hvisions.rawmaterial.consts.SapConst;
import com.hvisions.rawmaterial.dao.SdOrderDetailMapper;
import com.hvisions.rawmaterial.dao.SorghumDispenseOrderMapper;
import com.hvisions.rawmaterial.dao.WarehouseDataMapper;
import com.hvisions.rawmaterial.dto.production.loss.sorghum.SorghumLossDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.SorghumAndBranBatchPostDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.SorghumAndBranPostDetailDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.SorghumDispenseDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.dto.storage.warehouse.WarehousePageDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSdOrderDetail;
import com.hvisions.rawmaterial.entity.TMpdSorghumDispenseOrder;
import com.hvisions.rawmaterial.entity.TMpdWarehouseData;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.service.SorghumDispenseOrderService;
import com.hvisions.rawmaterial.service.WarehouseService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.GenerateCodeUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @description:高粱分发工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumDispenseOrderServiceImpl implements SorghumDispenseOrderService {

    @Resource
    private SorghumDispenseOrderMapper sorghumDispenseOrderMapper;

    @Resource
    private WarehouseDataMapper warehouseDataMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private SdOrderDetailMapper orderDetailMapper;

    @Resource
    private SdOrderDetailMapper sdOrderDetailMapper;

    @Resource
    private GenerateCodeUtil generateCodeUtil;

    @Resource
    private SapClient sapClient;

    @Resource
    private PurchaseClient purchaseClient;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Override
    public Page<SorghumDispenseOrderPageDTO> getSorghumDispenseOrderPageList(SorghumDispenseOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumDispenseOrderMapper::getSorghumDispenseOrderPageList, queryDTO, SorghumDispenseOrderPageDTO.class);
    }

    /***
     * @Description 判断是否属于3大中心
     *
     * <AUTHOR>
     * @Date 2023-9-18 16:38
     * @param center 中心编码
     * @return java.lang.Boolean true 属于，false 不属于
     **/
    public Boolean judgeThreeCenter(String center) {
        if ("709".equals(center) || "713".equals(center) || "718".equals(center)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /***
     * @Description 手动新增高粱粉发放详情
     *
     * <AUTHOR>
     * @Date 2022-9-5 16:31
     * @param detailInsertDTOS
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumDispenseOrder order = sorghumDispenseOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());

            BigDecimal count = new BigDecimal(0);
            List<TMpdSdOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdSdOrderDetail.class);

            List<SorghumLossDTO> sorghumLossDTOList = new ArrayList<>();
            for (TMpdSdOrderDetail orderDetail : orderDetails) {

                orderDetail.setCreateTime(new Date());
                orderDetail.setActualBeginTime(StringUtil.isEmpty(orderDetail.getActualBeginTime()) ? new Date() : orderDetail.getActualBeginTime());
                orderDetail.setActualEndTime(StringUtil.isEmpty(orderDetail.getActualEndTime()) ? new Date() : orderDetail.getActualEndTime());
                orderDetail.setIsManual("1");

                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);

                count = count.add(orderDetail.getActualQuantity());

                // 新增损耗数据
                SorghumLossDTO sorghumLossDTO = DtoMapper.convert(orderDetail, SorghumLossDTO.class);
                sorghumLossDTO.setCenterId(order.getCenterId());
                sorghumLossDTO.setCenter(order.getCenter());
                sorghumLossDTO.setIssueQuantity(orderDetail.getActualQuantity());
                sorghumLossDTO.setActualBeginTime(order.getOrderDate());
                sorghumLossDTOList.add(sorghumLossDTO);
                res += orderDetailMapper.insert(orderDetail);
            }

            // 批量新增损耗数据
//            res += sorghumLossService.batchInsertOrUpdateSorghumLoss(sorghumLossDTOList);
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumDispenseOrderMapper.updateById(order);
        }
        return res;
    }


    /***
     * @Description 高粱粉发放冲销
     *
     * <AUTHOR>
     * @Date 2022-9-5 18:55
     * @param matDocs
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer writeOff(List<String> matDocs) {
        int res = 0;
        for (String matDoc : matDocs) {
            // 获取已经过账的且过账凭证号为选择的
            List<TMpdSdOrderDetail> sdOrderDetails = sdOrderDetailMapper.selectList(Wrappers.<TMpdSdOrderDetail>lambdaQuery()
                    .eq(TMpdSdOrderDetail::getMatDoc, matDoc)
                    .eq(TMpdSdOrderDetail::getDeleted, 0)
                    .eq(TMpdSdOrderDetail::getSapState, "1")
            );
            // sap冲销
            OrderWriteOffHeaderDto headerDto = new OrderWriteOffHeaderDto();
            headerDto.setHeaderKey(generateCodeUtil.generatePlanCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO));
            headerDto.setPstingDate(cn.hutool.core.date.DateUtil.format(sdOrderDetails.get(0).getPostingTime(), DatePattern.PURE_DATETIME_PATTERN));
            headerDto.setMjahr(sdOrderDetails.get(0).getDocYear());
            headerDto.setMblnr(matDoc);
            ResultVO<SapBaseResponseDto> resultVO = purchaseClient.writeOff(headerDto);
            if(resultVO.getCode()!=200){
                throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
            }
            SapBaseResponseDto response =resultVO.getData();
            BigDecimal writeOffWeight = BigDecimal.ZERO;
            if (!"S".equals(response.getEsMessage().getMsgty())) {
                // 冲销失败
                throw new BaseKnownException(10000, "sap冲销失败" + response.getEsMessage().getMsgtx());
            } else if ("S".equals(response.getEsMessage().getMsgty())) {
                // 冲销成功，将工单详情制空
                for (TMpdSdOrderDetail orderDetail : sdOrderDetails) {
                    orderDetail.setSapState("0");
                    orderDetail.setMatDoc("");
                    orderDetail.setIsWriteOff("1");
                    writeOffWeight = writeOffWeight.add(orderDetail.getActualQuantity());
                    res += sdOrderDetailMapper.updateById(orderDetail);
                }
                // 新增sap冲销记录
//                SapPostVO sapPostVO = new SapPostVO();
//                sapPostVO.setMaterialCode(sdOrderDetails.get(0).getMaterialCode());
//                sapPostVO.setMaterialName(sdOrderDetails.get(0).getMaterialName());
//                sapPostVO.setUnit(sdOrderDetails.get(0).getUnit());
//                sapPostVO.setWeight(writeOffWeight.negate());
//                sapPostVO.setType(0);
//                sapPostVO.setMovementTypeId(311);
//                sapPostVO.setOperatorType("高粱粉发放冲销");
//                sapPostVO.setCertificateDate(LocalDate.now());
//                sapPostVO.setValueJson(JSONObject.toJSONString(headerDto));
//                sapPostVO.setSapCode(SapConst.PURCHASE_RECEIVING_REVOKE_NO);
//                sapPostVO.setState("1");
//                sapClient.insertPurchaseSapPostRecord(sapPostVO);
                //更改过账记录为已冲销
                sapClient.reversalSapPostStatus(matDoc);
            }
        }
        return res;
    }

    /***
     * @Description 高粱发放详情，库存回退
     *
     * <AUTHOR>
     * @Date 2023-1-10 9:56
     * @param sdOrderDetail
     * @return Integer
     **/
    private Integer backStock(TMpdSdOrderDetail sdOrderDetail) {

        /**
         * 库存回退
         *      1、根据发出筒仓和批次获取库存记录（删除的 || 没有删除的）
         *      2、将库存加回来
         */

        int res = 0;
        TMpdWarehouseData warehouseData = warehouseDataMapper.getWarehouseDataDetailByStorageIdAndBatch(sdOrderDetail.getSendSiloId(), sdOrderDetail.getBatch().split(",")[0]);
        if (warehouseData.getDeleted()) { // 批次全部删除了
            res += warehouseDataMapper.updateDeletedStock(warehouseData.getId(), sdOrderDetail.getActualQuantity());
        } else { // 批次存在
            warehouseData.setStockQuantity(warehouseData.getStockQuantity().add(sdOrderDetail.getActualQuantity()));
            res += warehouseDataMapper.updateById(warehouseData);
        }
        return res;
    }

    /***
     * @Description 高粱粉发放过账
     *
     * <AUTHOR>
     * @Date 2022-9-6 10:23
     * @param postDTO
     * @return java.lang.Integer
     **/
    @Override
    @Transactional
    public Integer posting(SorghumAndBranBatchPostDTO postDTO) {
        int res = 0;
        if (StringUtil.isEmpty(postDTO.getSorghumAndBranPostDetailDTOList()) && postDTO.getSorghumAndBranPostDetailDTOList().size() == 0) {
            throw new BaseKnownException(10000, "无过账记录！");
        }
        Date postDate = getPostDate(postDTO.getPostingDate());
        ResultVO<Boolean> resultVO = purchaseClient.judgeSapPostTime(postDate);
        if(resultVO.getCode()!=200){
            throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
        }
        boolean isPost =resultVO.getData();
        //如果日期在当月24号之后则返回为false, 不进行同步并将状态改为同步中,同步日期更改为次月1号, 由定时任务同步
        if (!isPost) {
            log.info("高粱粉发放过账：日期判断后不过账数据");
            // 遍历工单详情，修改工单详情
            for (SorghumAndBranPostDetailDTO sorghumAndBranPostDetailDTO : postDTO.getSorghumAndBranPostDetailDTOList()) {
                TMpdSdOrderDetail orderDetail = orderDetailMapper.selectById(sorghumAndBranPostDetailDTO.getId());
                orderDetail.setPostingTime(postDate);
                orderDetail.setSapState("2");
                res += sdOrderDetailMapper.updateById(orderDetail);
            }
            return res;
        }

        // 获取首个工单详情
        TMpdSdOrderDetail firstOrderDetail = sdOrderDetailMapper.selectById(postDTO.getSorghumAndBranPostDetailDTOList().get(0).getId());
        // 获取工单
        TMpdSorghumDispenseOrder order = sorghumDispenseOrderMapper.selectById(firstOrderDetail.getOrderId());
        // 同步到sap
        WarehousePageDTO warehouse = warehouseService.getWarehouseByDepartmentCode(order.getCenter());
        InventoryAllocationDto inventoryAllocationDto = new InventoryAllocationDto();
        inventoryAllocationDto.setOrderNo(generateCodeUtil.generatePlanCode(SapConst.INVENTORY_ALLOCATION_NO));
        inventoryAllocationDto.setMaterial(postDTO.getMaterialCode());
        inventoryAllocationDto.setSlogrt("1106");
        inventoryAllocationDto.setDlogrt(warehouse.getErpCode());
        inventoryAllocationDto.setEntryQnt(postDTO.getPostingQuantity());
        inventoryAllocationDto.setCertificateDate(LocalDate.now());
        inventoryAllocationDto.setCertificateDate(postDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        ResultVO<SapBaseResponseDto> resultVO2 = purchaseClient.inventoryAllocation(inventoryAllocationDto);
        if(resultVO2.getCode()!=200){
            throw new BaseKnownException(resultVO2.getCode(), resultVO2.getMessage());
        }
        SapBaseResponseDto responseDto = resultVO2.getData();

        if (!"S".equals(responseDto.getEsMessage().getMsgty())) {
            // 过账失败
            throw new BaseKnownException(10000, "熟糠发放SAP过账失败，原因：" + responseDto.getEsMessage().getMsgtx());
        } else if ("S".equals(responseDto.getEsMessage().getMsgty())) {
            // 过账成功
            // 遍历工单详情，修改工单详情
            List<TMpdSdOrderDetail> updateList = new ArrayList<>();
            for (SorghumAndBranPostDetailDTO sorghumAndBranPostDetailDTO : postDTO.getSorghumAndBranPostDetailDTOList()) {
                TMpdSdOrderDetail orderDetail = orderDetailMapper.selectById(sorghumAndBranPostDetailDTO.getId());
                orderDetail.setMatDoc(responseDto.getEvOutput().getOutput().get(0).getMblnr());
                orderDetail.setDocYear(DateUtil.yearFormat(postDate));
                orderDetail.setActualQuantity(sorghumAndBranPostDetailDTO.getPostingQuantity());
                orderDetail.setMaterialId(postDTO.getMaterialId());
                orderDetail.setMaterialCode(postDTO.getMaterialCode());
                orderDetail.setMaterialName(postDTO.getMaterialName());
                orderDetail.setSapState("1");
                orderDetail.setPostingTime(postDate);
                //res += sdOrderDetailMapper.updateById(orderDetail);
                updateList.add(orderDetail);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                res += sdOrderDetailMapper.updatePostByBatch(updateList);
            }
            // 生成sap过账记录
            SapPostVO sapPostVO = new SapPostVO();
            sapPostVO.setMaterialCode(postDTO.getMaterialCode());
            sapPostVO.setMaterialName(postDTO.getMaterialName());
            sapPostVO.setWeight(postDTO.getPostingQuantity());
            sapPostVO.setUnit(firstOrderDetail.getUnit());
            sapPostVO.setMovementTypeId(311);
            sapPostVO.setType(0);
            try {
                userAuditorAware.getCurrentUserAudit().ifPresent(u -> {
                    sapPostVO.setOperatorId(u.getId());
                    sapPostVO.setOperator(u.getUserName());
                });
            } catch (Exception e) {
                log.error("过账操作设置操作用户失败:" + e.getMessage());
            }
            sapPostVO.setInitialWarehouseCode("1106");
            sapPostVO.setInitialWarehouseName("黄舣粮库");
            sapPostVO.setTargetWarehouseCode(warehouse.getErpCode());
            sapPostVO.setTargetWarehouseName(warehouse.getName());
            sapPostVO.setOperatorType("高粱粉发放手动过账");
            sapPostVO.setOperatingTime(LocalDateTime.now());
            sapPostVO.setCertificateDate(postDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            sapPostVO.setSapCode(SapConst.INVENTORY_ALLOCATION_NO);
            sapPostVO.setValueJson(JSONObject.toJSONString(inventoryAllocationDto));
            sapPostVO.setOrderDate(order.getOrderDate());
            sapPostVO.setState("1");
            sapPostVO.setCertificateNumber(responseDto.getEvOutput().getOutput().get(0).getMblnr());
            sapPostVO.setCertificateYear(responseDto.getEvOutput().getOutput().get(0).getMjahr());
            sapClient.insertPurchaseSapPostRecord(sapPostVO);

        }
        return res;

    }


    /**
     * 获取过账日期
     * @param from
     * @return
     */
    private Date getPostDate(Date from) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(from);
        int postDay = calendar.get(Calendar.DAY_OF_MONTH); // 当前过账时间日期
        // 如果过账日期 >= 24, 则将过账日期改成次月1号
        if (postDay >= 24){
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date postTime = calendar.getTime();
            log.info("过账日期 >= 24,更改过账日期为次月1号:" + DateUtil.dateFormat(postTime));
            return postTime;
        }
        return from;
    }

    /***
     * @Description 高粱稻壳发放详情（批次追溯）
     *
     * <AUTHOR>
     * @Date 2022-11-7 15:55
     * @param queryDTO
     * @return com.hvisions.purchase.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO
     **/
    @Override
    public List<MaterialDispenseDetailDTO> getMaterialDispenseDetail(MaterialDispenseDetailQueryDTO queryDTO) {
        List<MaterialDispenseDetailDTO> list = new ArrayList<>();
        if (queryDTO.getEndTime() != null) {
            MaterialDispenseDetailQueryDTO dto = new MaterialDispenseDetailQueryDTO();
            dto.setCenterId(queryDTO.getCenterId());
            dto.setType(queryDTO.getType());
            Date begin = queryDTO.getStartTime();
            LocalDateTime localDateTime = begin.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime().minusDays(10l);
            dto.setStartTime(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            dto.setEndTime(queryDTO.getStartTime());
            List<MaterialDispenseDetailDTO> now = sdOrderDetailMapper.getMaterialDispenseDetail(dto);
            if (now.size() > 0) {
                list.add(now.get(0));
            }
        }
        if (queryDTO.getStartTime() != null && queryDTO.getEndTime() != null) {
            List<MaterialDispenseDetailDTO> dto = sdOrderDetailMapper.getMaterialDispenseDetail(queryDTO);
            list.addAll(dto);
        }
        return list;
    }

    /**
     * 更新高粱粉发放工单详情
     * @param updateFlourTransferDTO
     * @return
     */
    @Override
    public Integer updateSorghumDispense(UpdateFlourTransferDTO updateFlourTransferDTO) {
        TMpdSdOrderDetail tMpdSdOrderDetail = orderDetailMapper.selectOne(new LambdaQueryWrapper<TMpdSdOrderDetail>().eq(TMpdSdOrderDetail::getJobIdent, updateFlourTransferDTO.getJobIdent()).eq(TMpdSdOrderDetail::getDeleted, false));
        if (tMpdSdOrderDetail == null) {
            throw new BaseKnownException(10000, "未获取到详细数据");
        }
        TMpdSorghumDispenseOrder oldOrder = sorghumDispenseOrderMapper.selectById(tMpdSdOrderDetail.getOrderId());
        TMpdSorghumDispenseOrder newOrder = sorghumDispenseOrderMapper.selectOne(new LambdaQueryWrapper<TMpdSorghumDispenseOrder>()
                .eq(TMpdSorghumDispenseOrder::getDeleted, false)
                .eq(TMpdSorghumDispenseOrder::getCenter, oldOrder.getCenter())
                .like(TMpdSorghumDispenseOrder::getOrderDate, DateUtil.format(updateFlourTransferDTO.getEndDateActual(),"yyyy-MM-dd"))
                .orderByDesc(TMpdSorghumDispenseOrder::getOrderDate)
                .last("limit 1"));
        if (newOrder != null && !Objects.equals(newOrder.getId(), oldOrder.getId())) {
            tMpdSdOrderDetail.setOrderId(newOrder.getId());
            tMpdSdOrderDetail.setActualEndTime(updateFlourTransferDTO.getEndDateActual());
            //更改工单数据，对汇总数据进行加减
            oldOrder.setActualQuantity(oldOrder.getActualQuantity().subtract(tMpdSdOrderDetail.getActualQuantity()));
            sorghumDispenseOrderMapper.updateById(oldOrder);
            newOrder.setActualQuantity(newOrder.getActualQuantity().add(tMpdSdOrderDetail.getActualQuantity()));
            sorghumDispenseOrderMapper.updateById(newOrder);
            log.info("更新高粱粉发放工单详情, 原工单号{}, 现工单号{}", oldOrder.getOrderNo(), newOrder.getOrderNo());
            return orderDetailMapper.updateById(tMpdSdOrderDetail);
        }
        log.info("更新高粱粉发放工单详情,工单号数据一致不用更新:" + JSONObject.toJSONString(updateFlourTransferDTO));
        return 0;
    }

    /**
     * 撤销自动同步
     * @param ids
     * @return
     */
    @Override
    public Integer revoke(List<Integer> ids) {
        int count = 0;
        for (Integer id : ids) {
            TMpdSdOrderDetail orderDetail = orderDetailMapper.selectById(id);
            if (!"2".equals(orderDetail.getSapState())) {
                throw new BaseKnownException(10001, "只有待同步的数据可以撤回");
            }
            UpdateWrapper<TMpdSdOrderDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("posting_time", null).set("sap_state", "0").eq("id", id);
            count += sdOrderDetailMapper.update(null, updateWrapper);
        }
        return count;
    }

    @Override
    public Page<SorghumDispenseDetailListDTO> getSorghumDispenseOrderDetailPageList(SorghumDispenseOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumDispenseOrderMapper::getSorghumDispenseOrderDetailPageList, queryDTO, SorghumDispenseDetailListDTO.class);
    }

    @Override
    public String createSorghumPowderDistribution(UnifiedTaskDTO taskDTO) {
        return "";
    }

    /***
     * @Description 每月1号1点调用，将上月未过账的发放数据同步给sap
     *
     * <AUTHOR>
     * @Date 2022-12-15 14:32
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 0 12 1 * ?")
    @SchedulerLock(name = "scheduledSorghumSyncSapLock")
    public void scheduledSorghumSyncSap() {
        log.info("高粱粉发放1号定时同步sap开始");

        ResultVO<Date> resultVO = purchaseClient.isYearPost();
        if(resultVO.getCode()!=200){
            throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
        }
        Date yearTime =resultVO.getData();

        List<TMpdSdOrderDetail> sdOrderDetails;
        if (yearTime != null) {
            sdOrderDetails = sdOrderDetailMapper.selectList(Wrappers.<TMpdSdOrderDetail>lambdaQuery()
                    .eq(TMpdSdOrderDetail::getSapState, "2")
                    .between(TMpdSdOrderDetail::getPostingTime, yearTime, DateUtil.getAppointDay(new Date(), 1))
            );
        } else {
            sdOrderDetails = sdOrderDetailMapper.selectList(Wrappers.<TMpdSdOrderDetail>lambdaQuery()
                    .eq(TMpdSdOrderDetail::getSapState, "2")
                    .between(TMpdSdOrderDetail::getPostingTime, DateUtil.getUpMonthAppointDay(24), DateUtil.getAppointDay(new Date(), 1))

            );
        }
        log.info("高粱粉发放1号定时同步sap数据为==>{}", sdOrderDetails.toString());
        int res = 0;
        if (sdOrderDetails.size() > 0) {
            for (TMpdSdOrderDetail orderDetail : sdOrderDetails) {
                TMpdSorghumDispenseOrder order = sorghumDispenseOrderMapper.selectById(orderDetail.getOrderId());
                WarehousePageDTO warehouse = warehouseService.getWarehouseByDepartmentCode(order.getCenter());
                if (StringUtil.isEmpty(warehouse)) {
                    log.info("该中心不存在sap仓库，无法过账" + order.getCenter());
                    continue;
                }
                InventoryAllocationDto inventoryAllocationDto = new InventoryAllocationDto();
                inventoryAllocationDto.setOrderNo(generateCodeUtil.generatePlanCode(SapConst.INVENTORY_ALLOCATION_NO));
                inventoryAllocationDto.setMaterial(orderDetail.getMaterialCode());
                inventoryAllocationDto.setSlogrt("1106");
                inventoryAllocationDto.setDlogrt((StringUtil.isEmpty(warehouse) || StringUtil.isEmpty(warehouse.getErpCode())) ? "" : warehouse.getErpCode());
                inventoryAllocationDto.setEntryQnt(orderDetail.getActualQuantity());
//                inventoryAllocationDto.setCertificateDate(LocalDate.now());
                inventoryAllocationDto.setCertificateDate(orderDetail.getPostingTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                ResultVO<SapBaseResponseDto> resultVO2 = purchaseClient.inventoryAllocation(inventoryAllocationDto);
                if(resultVO2.getCode()!=200){
                    throw new BaseKnownException(resultVO2.getCode(), resultVO2.getMessage());
                }
                SapBaseResponseDto responseDto = resultVO2.getData();;
                SapPostVO sapPostVO = new SapPostVO();
                sapPostVO.setMaterialCode(orderDetail.getMaterialCode());
                sapPostVO.setMaterialName(orderDetail.getMaterialName());
                sapPostVO.setUnit(orderDetail.getUnit());
                sapPostVO.setWeight(orderDetail.getActualQuantity());
                sapPostVO.setMovementTypeId(311);
                sapPostVO.setType(0);
                sapPostVO.setInitialWarehouseCode("1106");
                sapPostVO.setInitialWarehouseName("黄舣粮库");
                sapPostVO.setTargetWarehouseCode(warehouse.getErpCode());
                sapPostVO.setTargetWarehouseName(warehouse.getName());
                sapPostVO.setOperatorType("高粱粉发放同步");
                sapPostVO.setSapCode(SapConst.INVENTORY_ALLOCATION_NO);
                sapPostVO.setOrderDate(order.getOrderDate());
                sapPostVO.setCertificateDate(orderDetail.getPostingTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                sapPostVO.setValueJson(JSONObject.toJSONString(inventoryAllocationDto));

                if ("S".equals(responseDto.getEsMessage().getMsgty())) {
                    sapPostVO.setState("1");
                    sapPostVO.setCertificateNumber(responseDto.getEvOutput().getOutput().get(0).getMblnr());
                    sapPostVO.setCertificateYear(responseDto.getEvOutput().getOutput().get(0).getMjahr());
                    orderDetail.setMatDoc(responseDto.getEvOutput().getOutput().get(0).getMblnr());
                    orderDetail.setDocYear(DateUtil.yearFormat(orderDetail.getPostingTime()));
                    orderDetail.setSapState("1");
                } else {
                    purchaseClient.sendPostFailNotice("高粱粉发放", order.getOrderNo(), responseDto.getEsMessage().getMsgtx());
                    sapPostVO.setState("2");
                    sapPostVO.setFailReason(responseDto.getEsMessage().getMsgtx());
                }
                sapClient.insertPurchaseSapPostRecord(sapPostVO);

                res += sdOrderDetailMapper.updateById(orderDetail);
            }
        }
        log.info("高粱粉1号定时同步sap数据完成==》" + res);
    }
}
