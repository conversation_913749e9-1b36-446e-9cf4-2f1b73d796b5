package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.ClearanceRecordDTO;
import com.hvisions.rawmaterial.dto.ClearanceRecordQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 清仓记录服务接口
 * @Date: 2025/07/14
 */
public interface ClearanceRecordService {

    /**
     * 创建清仓记录（中控系统同步）
     * @param dto 清仓记录信息
     * @return 清仓记录ID
     */
    String createClearanceRecord(ClearanceRecordDTO dto);

    /**
     * 更新清仓记录状态为已完成（中控系统同步）
     * @param centralControlTaskId 中控系统任务ID
     * @param completionTime 完成时间
     * @return 是否成功
     */
    Boolean completeClearanceRecord(String centralControlTaskId, java.util.Date completionTime);

    /**
     * 分页查询清仓记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<ClearanceRecordDTO> queryClearanceRecords(ClearanceRecordQueryDTO queryDTO);

    /**
     * 根据ID查询清仓记录详情
     * @param id 清仓记录ID
     * @return 清仓记录详情
     */
    ClearanceRecordDTO getClearanceRecordById(Integer id);

    /**
     * 根据工单号查询清仓记录
     * @param workOrderNumber 工单号
     * @return 清仓记录详情
     */
    ClearanceRecordDTO getClearanceRecordByWorkOrderNumber(String workOrderNumber);

    /**
     * 根据中控系统任务ID查询清仓记录
     * @param centralControlTaskId 中控系统任务ID
     * @return 清仓记录详情
     */
    ClearanceRecordDTO getClearanceRecordByTaskId(String centralControlTaskId);

    /**
     * 查询执行中的清仓记录
     * @return 执行中的清仓记录列表
     */
    List<ClearanceRecordDTO> getExecutingRecords();

    /**
     * 根据筒仓查询最新的清仓记录
     * @param siloCode 筒仓编号
     * @return 最新的清仓记录
     */
    ClearanceRecordDTO getLatestClearanceRecordBySilo(String siloCode);

    /**
     * 删除清仓记录
     * @param id 清仓记录ID
     * @return 是否成功
     */
    Boolean deleteClearanceRecord(Integer id);

    /**
     * 生成工单号
     * @return 工单号
     */
    String generateWorkOrderNumber();

    /**
     * 批次切换处理
     * @param siloCode 筒仓编号
     * @param oldBatchNumber 旧批次号
     * @param newBatchNumber 新批次号
     * @return 是否成功
     */
    Boolean processBatchSwitch(String siloCode, String oldBatchNumber, String newBatchNumber);

    /**
     * 库存校准处理
     * @param siloCode 筒仓编号
     * @return 是否成功
     */
    Boolean processInventoryCalibration(String siloCode);
}
