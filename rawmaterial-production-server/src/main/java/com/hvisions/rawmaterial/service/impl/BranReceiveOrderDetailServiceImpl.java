package com.hvisions.rawmaterial.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.rawmaterial.dao.*;
import com.hvisions.rawmaterial.dto.PiBaseDataDto;
import com.hvisions.rawmaterial.dto.production.bran.production.detail.BranIssueDetailDTO;
import com.hvisions.rawmaterial.dto.production.bran.production.detail.BranReceiveDetailDTO;
import com.hvisions.rawmaterial.dto.production.bran.production.detail.HuskReceiveDetailDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.storage.Rl.RlManagementDetailDTO;
import com.hvisions.rawmaterial.entity.TMpdBranIssueOrder;
import com.hvisions.rawmaterial.entity.TMpdBranProductionOrder;
import com.hvisions.rawmaterial.entity.TMpdBranTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdRiceDispenseOrder;
import com.hvisions.rawmaterial.service.*;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.common.exception.BaseKnownException;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BranReceiveOrderDetailServiceImpl implements BranReceiveOrderDetailService {

    @Resource
    private RiceDispenseOrderService riceDispenseOrderService;

    @Resource
    private BranTransferOrderService branTransferOrderService;

    @Resource
    private BranProductionOrderService branProductionOrderService;

    @Resource
    private BranIssueOrderService branIssueOrderService;

    @Resource
    private RiceDispenseOrderMapper riceDispenseOrderMapper;

    @Resource
    private BranTransferOrderMapper branTransferOrderMapper;

    @Resource
    private BranProductionOrderMapper branProductionOrderMapper;

    @Resource
    private BranIssueOrderMapper branIssueOrderMapper;

    @Resource
    private RlManagementMapper rlManagementMapper;

    @Override
    public Integer addHuskIssue(PiBaseDataDto piBaseDataDto) {
        if (!"MESDKHCC".equals(piBaseDataDto.getGroupName())) {
            return 0;
        }
        List<HuskReceiveDetailDTO> huskReceiveDetailDTOS = convertHuskPiData(piBaseDataDto);
        if (StringUtil.isEmpty(huskReceiveDetailDTOS)) {
            return 0;
        }
        log.info("开始接收稻壳发放任务,接收数据为：" + JSON.toJSONString(huskReceiveDetailDTOS));
        List<DetailInsertDTO> detailInsertDTOS = new ArrayList<>(huskReceiveDetailDTOS.size());

        //找到稻壳发放最新的一条数据
        LambdaQueryWrapper<TMpdRiceDispenseOrder> wrapper = new LambdaQueryWrapper<TMpdRiceDispenseOrder>()
                .eq(TMpdRiceDispenseOrder::getDeleted, false)
                .like(TMpdRiceDispenseOrder::getOrderDate, DateUtil.format(huskReceiveDetailDTOS.get(0).getEndTime(), "yyyy-MM-dd"))
                .orderByDesc(TMpdRiceDispenseOrder::getOrderDate)
                .last("LIMIT 1");
        TMpdRiceDispenseOrder latest = riceDispenseOrderMapper.selectOne(wrapper);

        if (latest != null) {
            //组装数据
            huskReceiveDetailDTOS.forEach(item -> {
                DetailInsertDTO insertDTO = new DetailInsertDTO();
                insertDTO.setOrderId(latest.getId());
                String siloCode = getStorageCode(item.getSendSilo());
                //根据筒仓编码获取筒仓信息
                RlManagementDetailDTO rlData = rlManagementMapper.getRlManagementDetailByCentralData(siloCode);
                if (rlData != null) {
                    insertDTO.setSendSiloId(rlData.getId());
                } else {
                    throw new BaseKnownException(1000, "传入筒仓编号有误！");
                }
                insertDTO.setMaterialId(99);
                insertDTO.setMaterialCode("11000560");
                insertDTO.setMaterialName("DK");
                insertDTO.setUnit("kg");
                insertDTO.setActualQuantity(new BigDecimal(item.getFeedWeight()));
                insertDTO.setActualBeginTime(item.getStartTime());
                insertDTO.setActualEndTime(item.getEndTime());
                detailInsertDTOS.add(insertDTO);
            });
        }
        log.info("开始插入稻壳发放详情，数据为：" + JSON.toJSONString(detailInsertDTOS));

        try {
            //批量插入
            riceDispenseOrderService.insertOrderDetail(detailInsertDTOS);
        } catch (Exception e) {
            log.error("RiceDispenseOrderService.insertOrderDetail  插入数据失败：" + e);
        }
        return 1;
    }

    @Transactional
    @Override
    public Integer addBranTransfer(PiBaseDataDto piBaseDataDto) {
        List<BranReceiveDetailDTO> branReceiveDetailDTOS = convertPiData(piBaseDataDto);
        if (StringUtil.isEmpty(branReceiveDetailDTOS)) {
            return 0;
        }
        log.info("开始接收稻壳传输任务,接收数据为：" + JSON.toJSONString(branReceiveDetailDTOS));

        //熟糠传输
        List<DetailInsertDTO> detailInsertDTOS = new ArrayList<>(branReceiveDetailDTOS.size());

        //蒸糠生产
        List<DetailInsertDTO> ZKInsertDTOS = new ArrayList<>(branReceiveDetailDTOS.size());

        //拿到中心code
        String locationCode = branReceiveDetailDTOS.get(0).getPlant();

        //找到熟糠传输最新的一条数据
        LambdaQueryWrapper<TMpdBranTransferOrder> wrapper = new LambdaQueryWrapper<TMpdBranTransferOrder>()
                .eq(TMpdBranTransferOrder::getDeleted, false)
                .eq(TMpdBranTransferOrder::getCenter, locationCode)
                .like(TMpdBranTransferOrder::getOrderDate, DateUtil.format(branReceiveDetailDTOS.get(0).getEndTime(), "yyyy-MM-dd"))
                .orderByDesc(TMpdBranTransferOrder::getOrderDate)
                .last("LIMIT 1");
        TMpdBranTransferOrder latest = branTransferOrderMapper.selectOne(wrapper);

        //找到蒸糠生产最新的一条数据
        LambdaQueryWrapper<TMpdBranProductionOrder> wrap = new LambdaQueryWrapper<TMpdBranProductionOrder>()
                .eq(TMpdBranProductionOrder::getDeleted, false)
                .like(TMpdBranProductionOrder::getOrderDate, DateUtil.format(branReceiveDetailDTOS.get(0).getEndTime(), "yyyy-MM-dd"))
                .orderByDesc(TMpdBranProductionOrder::getOrderDate)
                .last("LIMIT 1");
        TMpdBranProductionOrder newList = branProductionOrderMapper.selectOne(wrap);

        if (latest != null) {
            branReceiveDetailDTOS.forEach(item -> {
                //组装熟糠传输数据
                DetailInsertDTO insertDTO = new DetailInsertDTO();
                insertDTO.setOrderId(latest.getId());

                String soilCode = getSlowInventory(item.getRoute());
                //根据筒仓编码获取筒仓信息
                //根据筒仓编码获取筒仓信息
                RlManagementDetailDTO rlData = rlManagementMapper.getRlManagementDetailByCentralData(soilCode);
                if (rlData != null) {
                    insertDTO.setSendSiloId(rlData.getId());
                } else throw new BaseKnownException(1000, "线路编号有误！");

                //拼接发出筒仓S1001和中心709对应709D1
                String slowSiloCode = null;
                if (StringUtil.isNotEmpty(item.getAcceptSilo())) {
                    String lastDigit = item.getAcceptSilo().substring(item.getAcceptSilo().length() - 1);
                    slowSiloCode = item.getPlant() + "D" + lastDigit;
                }
                //根据筒仓编码获取筒仓信息
                RlManagementDetailDTO rlData1 = rlManagementMapper.getRlManagementDetailByCentralData(slowSiloCode);
                if (rlData1 != null) {
                    insertDTO.setAcceptSiloId(rlData1.getId());
                } else throw new BaseKnownException(1000, "车间缓存仓编号有误！");

                insertDTO.setMaterialId(99);
                insertDTO.setMaterialCode("11000560");
                insertDTO.setMaterialName("DK");
                insertDTO.setUnit("kg");
                insertDTO.setActualQuantity(new BigDecimal(item.getFeedWeight()));
                insertDTO.setActualBeginTime(item.getStartTime());
                insertDTO.setActualEndTime(item.getEndTime());
                detailInsertDTOS.add(insertDTO);
                //组装蒸糠生产数据
                DetailInsertDTO ZKDTO = new DetailInsertDTO();
                ZKDTO.setOrderId(newList.getId());
                ZKDTO.setAcceptSiloId(rlData.getId());
                ZKDTO.setMaterialId(99);
                ZKDTO.setMaterialCode("11000560");
                ZKDTO.setMaterialName("DK");
                ZKDTO.setUnit("kg");
                ZKDTO.setActualQuantity(new BigDecimal(item.getFeedWeight()));
                ZKDTO.setActualBeginTime(item.getStartTime());
                ZKDTO.setActualEndTime(item.getEndTime());
                ZKInsertDTOS.add(ZKDTO);
            });
        }

        try {
            branTransferOrderService.insertOrderDetail(detailInsertDTOS);
        } catch (Exception e) {
            log.error("BranTransferOrderService.insertOrderDetail  插入数据失败：" + e);
        }

        try {
            branProductionOrderService.insertOrderDetail(ZKInsertDTOS);
        } catch (Exception e) {
            log.error("BranProductionOrderService.insertOrderDetail  插入数据失败：" + e);
        }
        return 1;

    }

    public List<HuskReceiveDetailDTO> convertHuskPiData(PiBaseDataDto piBaseDataDto) {
        HuskReceiveDetailDTO huskReceiveDetailDTO = new HuskReceiveDetailDTO();
        List<PiBaseDataDto.Points> points = piBaseDataDto.getPoints();
        if ("MESDKHCC".equals(piBaseDataDto.getGroupName())) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            for (PiBaseDataDto.Points point : points) {
                if (point.getPointCode().endsWith("Feeding_PAR_Reprot_1_Weight")) {
                    huskReceiveDetailDTO.setFeedWeight(point.getValue());
                } else if (point.getPointCode().endsWith("Feeding_PAR_Reprot_1_ID")) {
                    huskReceiveDetailDTO.setSendNo(point.getValue());
                } else if (point.getPointCode().endsWith("Feeding_PAR_Reprot_1_LINE_Des")) {
                    huskReceiveDetailDTO.setSendSilo(point.getValue());
                } else if (point.getPointCode().endsWith("Feeding_PAR_Reprot_1_StartTime")) {

                    if (StringUtil.isEmpty(point.getValue())) {
                        return null;
                    }
                    Date startTime = null;
                    try {
                        startTime = dateFormat.parse(point.getValue());
                    } catch (ParseException e) {
                        log.error("开始时间格式有误！");
                    }
                    huskReceiveDetailDTO.setStartTime(startTime);
                } else if (point.getPointCode().endsWith("Feeding_PAR_Reprot_1_StopTime")) {

                    if (StringUtil.isEmpty(point.getValue())) {
                        return null;
                    }
                    Date stopTime = null;
                    try {
                        stopTime = dateFormat.parse(point.getValue());
                    } catch (ParseException e) {
                        log.error("停止时间格式有误！");
                    }
                    huskReceiveDetailDTO.setEndTime(stopTime);
                }
            }
        }
        return Collections.singletonList(huskReceiveDetailDTO);
    }

    public List<BranReceiveDetailDTO> convertPiData(PiBaseDataDto piBaseDataDto) {
        BranReceiveDetailDTO branReceiveDetailDTO = new BranReceiveDetailDTO();
        List<PiBaseDataDto.Points> points = piBaseDataDto.getPoints();
        Boolean aBoolean = handlePiData(points, branReceiveDetailDTO);
        if (!aBoolean) {
            return null;
        }
        switch (piBaseDataDto.getGroupName()) {
            case "MESDK709TEST": {
                branReceiveDetailDTO.setPlant("709");
                break;
            }
            case "MESDK710": {
                branReceiveDetailDTO.setPlant("710");
                break;
            }
            case "MESDK711": {
                branReceiveDetailDTO.setPlant("711");
                break;
            }
            case "MESDK712": {
                branReceiveDetailDTO.setPlant("712");
                break;
            }
            case "MESDK713": {
                branReceiveDetailDTO.setPlant("713");
                break;
            }
            case "MESDK714": {
                branReceiveDetailDTO.setPlant("714");
                break;
            }
            case "MESDK715": {
                branReceiveDetailDTO.setPlant("715");
                break;
            }
            case "MESDK716": {
                branReceiveDetailDTO.setPlant("716");
                break;
            }
            case "MESDK717": {
                branReceiveDetailDTO.setPlant("717");
                break;
            }
            case "MESDK718": {
                branReceiveDetailDTO.setPlant("718");
                break;
            }
            case "MESDK719": {
                branReceiveDetailDTO.setPlant("719");
                break;
            }

        }
        if (StringUtil.isEmpty(branReceiveDetailDTO.getPlant())) {
            return null;
        }
        return Collections.singletonList(branReceiveDetailDTO);
    }

    /**
     * 处理PI数据
     */
    public Boolean handlePiData(List<PiBaseDataDto.Points> points, BranReceiveDetailDTO branReceiveDetailDTO) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        for (PiBaseDataDto.Points point : points) {
            if (point.getPointCode().endsWith("Feed_Par_Reprot_2_Weight")) {
                branReceiveDetailDTO.setFeedWeight(point.getValue());
            } else if (point.getPointCode().endsWith("Feed_Par_Reprot_2_ROOT_Des")) {
                branReceiveDetailDTO.setAcceptSilo(point.getValue());
            } else if (point.getPointCode().endsWith("Feed_Par_Reprot_2_ID")) {
                branReceiveDetailDTO.setSendNo(point.getValue());
            } else if (point.getPointCode().endsWith("Feed_Par_Reprot_2_LINE_Des")) {
                branReceiveDetailDTO.setRoute(point.getValue());
            } else if (point.getPointCode().endsWith("Feed_Par_Reprot_2_StartTime")) {
                if (StringUtil.isEmpty(point.getValue())) {
                    return Boolean.FALSE;
                }
                Date startTime = null;
                try {
                    startTime = dateFormat.parse(point.getValue());
                } catch (ParseException e) {
                    log.error("开始时间格式有误！");
                }
                branReceiveDetailDTO.setStartTime(startTime);
            } else if (point.getPointCode().endsWith("Feed_Par_Reprot_2_StopTime")) {
                if (StringUtil.isEmpty(point.getValue())) {
                    return Boolean.FALSE;
                }
                Date stopTime = null;
                try {
                    stopTime = dateFormat.parse(point.getValue());
                } catch (ParseException e) {
                    log.error("停止时间格式有误！");
                }
                branReceiveDetailDTO.setEndTime(stopTime);
            }
        }

        return Boolean.TRUE;
    }


    /**
     * 定时任务每天8、16、24点零5分调用，抓取8小时内的熟糠发放记录
     */
    @Override
    @SchedulerLock(name = "branIssueTriggerLock")
    @Scheduled(cron = "0 5 0,8,16 * * ? ")
    public void branIssueTrigger() {
        log.info("抓取8小时内的熟糠发放记录,开始执行");
        getBranIssueInfo();
        log.info("抓取8小时内的熟糠发放记录,执行结束");
    }

    /**
     * 查询sqlserver数据库获取熟糠发放信息
     */
    public void getBranIssueInfo() {
        List<BranIssueDetailDTO> list = new ArrayList<>();
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            String url = "**************************************************";
            String user = "sa";
            String password = "Lzlj@1573";
            Connection conn = DriverManager.getConnection(url, user, password);
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT * FROM 日报表1 WHERE 时间 >= DATEADD(hour, -8, GETDATE())");
            while (rs.next()) {
                // 处理查询结果
                Date date = rs.getDate("时间");
                int location = rs.getInt("车间");
                float weight1 = rs.getFloat("重量1");
                float weight2 = rs.getFloat("重量2");
                float weight3 = rs.getFloat("重量3");
                float weight4 = rs.getFloat("重量4");
                float weight5 = rs.getFloat("重量5");
                String mesLocation = getMesLocation(location);
                BranIssueDetailDTO b1 = handleData(weight1, date, mesLocation, "D1");
                BranIssueDetailDTO b2 = handleData(weight2, date, mesLocation, "D2");
                BranIssueDetailDTO b3 = handleData(weight3, date, mesLocation, "D3");
                BranIssueDetailDTO b4 = handleData(weight4, date, mesLocation, "D4");
                BranIssueDetailDTO b5 = handleData(weight5, date, mesLocation, "D5");
                list.add(b1);
                list.add(b2);
                list.add(b3);
                list.add(b4);
                list.add(b5);
                //存入工单详情（数据库一条数据相当于5条工单详情记录）
            }

            // 关闭 ResultSet、Statement 和 Connection 对象
            rs.close();
            stmt.close();
            conn.close();
            addBranIssueOrderDetail(list);

        } catch (Exception e) {
            log.error("连接sqlserver数据库失败：" + e);
        }
    }

    /**
     * 存入工单详情
     */
    public void addBranIssueOrderDetail(List<BranIssueDetailDTO> branIssueDetailDTOList) {
        if (CollectionUtils.isEmpty(branIssueDetailDTOList)) {
            return;
        }
        log.info("开始接收熟糠发放,插入的记录条数：" + branIssueDetailDTOList.size());
        log.info("熟糠发放数据：" + JSON.toJSONString(branIssueDetailDTOList));

        Boolean isZero = false;
        // 判定是否是0点 如果是0点，则算到昨天的工单
        if (DateUtil.format(new Date(),"HH").equals("00")){
            isZero = true;
        }

        // 按车间进行分组
        Map<String, List<BranIssueDetailDTO>> locationMap = branIssueDetailDTOList.stream().collect(Collectors.groupingBy(BranIssueDetailDTO::getLocation));
        // 组装各个中心的详情数据
        for (String key : locationMap.keySet()) {
            List<BranIssueDetailDTO> branIssueDetailDTOList1 = locationMap.get(key);
            List<DetailInsertDTO> detailInsertDTOS = new ArrayList<>(branIssueDetailDTOList1.size());
            LambdaQueryWrapper<TMpdBranIssueOrder> wrapper;
            if (isZero){
                wrapper = new LambdaQueryWrapper<TMpdBranIssueOrder>()
                        .eq(TMpdBranIssueOrder::getDeleted, false)
                        .eq(TMpdBranIssueOrder::getCenter, branIssueDetailDTOList1.get(0).getLocation())
                        .like(TMpdBranIssueOrder::getOrderDate, DateUtil.format(DateUtil.addMinute(new Date(), -30), "yyyy-MM-dd"))
                        .orderByDesc(TMpdBranIssueOrder::getOrderDate)
                        .last("LIMIT 1");
            } else {
                wrapper = new LambdaQueryWrapper<TMpdBranIssueOrder>()
                        .eq(TMpdBranIssueOrder::getDeleted, false)
                        .eq(TMpdBranIssueOrder::getCenter, branIssueDetailDTOList1.get(0).getLocation())
                        .like(TMpdBranIssueOrder::getOrderDate, DateUtil.format(new Date(), "yyyy-MM-dd"))
                        .orderByDesc(TMpdBranIssueOrder::getOrderDate)
                        .last("LIMIT 1");
            }
            TMpdBranIssueOrder latest = branIssueOrderMapper.selectOne(wrapper);

            if (latest != null) {
                //组装数据
                branIssueDetailDTOList1.forEach(item -> {
                    DetailInsertDTO insertDTO = new DetailInsertDTO();
                    insertDTO.setOrderId(latest.getId());
                    insertDTO.setSendSiloId(item.getSendSiloId());
                    insertDTO.setPoint(item.getPoint());

                    //过账日期大于23号则设置为下个月1号
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(item.getPostingTime());
                    // 获取日期的天数
                    int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
                    if (dayOfMonth > 23) {
                        // 大于23号，设置为下个月的1号
                        calendar.add(Calendar.MONTH, 1);
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                    }
                    Date postingTime = calendar.getTime();
                    insertDTO.setPostingTime(postingTime);

                    insertDTO.setMaterialId(99);
                    insertDTO.setMaterialCode("11000560");
                    insertDTO.setMaterialName("DK");
                    insertDTO.setUnit("kg");
                    insertDTO.setActualQuantity(new BigDecimal(Float.valueOf(item.getFeedWeight())));
                    insertDTO.setActualBeginTime(new Date());
                    insertDTO.setActualEndTime(new Date());
                    detailInsertDTOS.add(insertDTO);
                });

                log.info("开始批量插入熟糠发放详情：{}", detailInsertDTOS);
                try {
                    //批量插入
                    branIssueOrderService.insertOrderDetail(detailInsertDTOS);
                } catch (Exception e) {
                    log.error("BranIssueOrderService.insertOrderDetail  插入数据失败原因：{}" + e);
                }
            }

        }

    }

    /**
     * 组装数据
     * param 重量，日期，中心，缓存仓
     */
    public BranIssueDetailDTO handleData(float weight, Date date, String mesLocation, String siloCode) {
        BranIssueDetailDTO bran = new BranIssueDetailDTO();
        bran.setFeedWeight(weight);
        bran.setPoint(1);
        bran.setPostingTime(date);
        bran.setSendSiloId(getSiloId(mesLocation, siloCode));
        bran.setLocation(mesLocation);
        return bran;
    }

    /**
     * 获取筒仓id
     */
    public Integer getSiloId(String mesLocation, String soilCode) {
        Integer siloId = null;
        String siloCode = mesLocation + soilCode;
        //根据筒仓编码获取筒仓信息
        RlManagementDetailDTO rlData = rlManagementMapper.getRlManagementDetailByCentralData(siloCode);
        if (rlData != null) {
            siloId = rlData.getId();
        } else throw new BaseKnownException(1000, "发放筒仓编号有误！");
        return siloId;
    }

    /**
     * 根据sqlserver数据库车间对应中心和车间
     */
    public static String getMesLocation(Integer location) {
        String mesLocaiton = null;
        switch (location) {
            case 1:
                mesLocaiton = "709";
                break;
            case 2:
                mesLocaiton = "710";
                break;
            case 3:
                mesLocaiton = "711";
                break;
            case 4:
                mesLocaiton = "712";
                break;
            case 5:
                mesLocaiton = "713";
                break;
            case 6:
                mesLocaiton = "714";
                break;
            case 7:
                mesLocaiton = "719";
                break;
            case 8:
                mesLocaiton = "715";
                break;
            case 9:
                mesLocaiton = "716";
                break;
            case 10:
                mesLocaiton = "717";
                break;
            case 11:
                mesLocaiton = "718";
                break;
        }
        return mesLocaiton;
    }

    /**
     * 线路对照缓存仓
     */
    public static String getSlowInventory(String route) {
        String inventory = null;
        switch (route) {
            case "A":
                inventory = "DKCK-A";
                break;
            case "B":
                inventory = "DKCK-B";
                break;
            case "C":
                inventory = "DKCK-C";
                break;
        }
        return inventory;
    }

    /**
     * 传入筒仓对应mes筒仓
     */
    public static String getStorageCode(String sendSilo) {
        String storageCode = null;
        switch (sendSilo) {
            case "S101":
                storageCode = "DK1-1";
                break;
            case "S102":
                storageCode = "DK1-2";
                break;
            case "S103":
                storageCode = "DK1-3";
                break;
            case "S104":
                storageCode = "DK1-4";
                break;
            case "S105":
                storageCode = "DK1-5";
                break;
            case "S106":
                storageCode = "DK1-6";
                break;
            case "S107":
                storageCode = "DK1-7";
                break;
            case "S108":
                storageCode = "DK1-8";
                break;
            case "S109":
                storageCode = "DK1-9";
                break;
        }
        return storageCode;
    }

}
