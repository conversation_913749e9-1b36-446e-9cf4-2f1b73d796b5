package com.hvisions.rawmaterial.service.ricehusk;

import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO;

import java.util.List;

/**
 * 稻壳入仓任务详情Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface RiceHuskInboundTaskDetailService {

    /**
     * 根据父任务ID查询子任务列表
     *
     * @param taskId 父任务ID
     * @return 子任务列表
     */
    List<RiceHuskInboundTaskDetailDTO> findByTaskId(String taskId);

    /**
     * 保存任务详情
     *
     * @param dto 任务详情DTO
     * @return 保存后的任务详情
     */
    RiceHuskInboundTaskDetailDTO save(RiceHuskInboundTaskDetailDTO dto);
}
