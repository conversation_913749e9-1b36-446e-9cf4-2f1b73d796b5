package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转粮工单
 * @date 2022/4/22 10:18
 */
public interface SorghumShipmentOrderService {

    Page<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(SorghumShipmentOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);


    Page<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(SorghumShipmentOrderPageQueryDTO queryDTO);

    /**
     * 创建高粱转粮任务信息（一期对接）
     * @param taskDTO
     * @return
     */
    String createSorghumGrainTransfer(UnifiedTaskDTO taskDTO);
}
