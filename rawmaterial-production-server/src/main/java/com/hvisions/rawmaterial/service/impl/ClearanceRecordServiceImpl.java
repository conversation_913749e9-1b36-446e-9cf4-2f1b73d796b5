package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.consts.ClearanceConstants;
import com.hvisions.rawmaterial.dao.ClearanceRecordRepository;
import com.hvisions.rawmaterial.dto.ClearanceRecordDTO;
import com.hvisions.rawmaterial.dto.ClearanceRecordQueryDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO;
import com.hvisions.rawmaterial.entity.clearance.TMpdClearanceRecord;
import com.hvisions.rawmaterial.mapper.ClearanceRecordMapper;
import com.hvisions.rawmaterial.service.ClearanceRecordService;
import com.hvisions.rawmaterial.service.materialsilo.MaterialSiloConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.covertStorage;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 清仓记录服务实现类
 * @Date: 2025/07/14
 */
@Slf4j
@Service
public class ClearanceRecordServiceImpl implements ClearanceRecordService {

    @Autowired
    private ClearanceRecordRepository clearanceRecordRepository;

    @Resource
    private ClearanceRecordMapper clearanceRecordMapper;

    @Resource
    private MaterialSiloConfigService materialSiloConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createClearanceRecord(ClearanceRecordDTO dto) {
        /**
         * 1.查询中控系统任务ID是否存在 - 存在则更新，不存在则添加
         * 2.处理批次切换逻辑，批号处理不设置成null
         */

        TMpdClearanceRecord record;
        boolean isUpdate = false;

        // 筒仓处理：根据筒仓编码自动填充筒仓名称
        if (dto.getSiloCode() != null && !dto.getSiloCode().trim().isEmpty()) {
            try {
                MaterialSiloConfigDTO materialSiloConfig = findSiloCodeByTubCode(covertStorage(dto.getSiloCode()));
                if (materialSiloConfig != null && materialSiloConfig.getName() != null) {
                    dto.setSiloName(materialSiloConfig.getName());
                    dto.setSiloCode(materialSiloConfig.getCode());
                    dto.setSiloId(materialSiloConfig.getId());
                    log.debug("根据筒仓编码{}自动填充筒仓名称：{}", dto.getSiloCode(), materialSiloConfig.getName());
                } else {
                    log.warn("未找到筒仓编码{}对应的筒仓配置信息", dto.getSiloCode());
                }
            } catch (Exception e) {
                log.error("查询筒仓配置信息失败，筒仓编码：{}，错误：{}", dto.getSiloCode(), e.getMessage(), e);
                // 不抛出异常，允许业务继续执行
            }
        }

        // 检查中控系统任务ID是否存在
        if (dto.getCentralControlTaskId() != null && !dto.getCentralControlTaskId().trim().isEmpty()) {
            Optional<TMpdClearanceRecord> existingRecordOpt = clearanceRecordRepository.findByCentralControlTaskId(dto.getCentralControlTaskId());
            if (existingRecordOpt.isPresent()) {
                // 存在则更新
                record = existingRecordOpt.get();
                isUpdate = true;
                log.info("找到已存在的清仓记录，将进行更新，中控任务ID：{}，原工单号：{}",
                        dto.getCentralControlTaskId(), record.getWorkOrderNumber());

                // 更新字段，但保留原有的批号（不设置为null）
                if (dto.getSiloName() != null) record.setSiloName(dto.getSiloName());
                if (dto.getSiloCode() != null) record.setSiloCode(dto.getSiloCode());
                if (dto.getSiloId() != null) record.setSiloId(dto.getSiloId());
                if (dto.getMaterialCode() != null) record.setMaterialCode(dto.getMaterialCode());
                if (dto.getMaterialName() != null) record.setMaterialName(dto.getMaterialName());
                if (dto.getUnit() != null) record.setUnit(dto.getUnit());
                if (dto.getQuantityBefore() != null) record.setQuantityBefore(dto.getQuantityBefore());
                if (dto.getClearanceTime() != null) record.setClearanceTime(dto.getClearanceTime());
                if (dto.getStatus() != null) record.setStatus(dto.getStatus());
                // 批号处理：只有当新的批号不为空时才更新，避免设置为null
                if (dto.getBatchNumber() != null && !dto.getBatchNumber().trim().isEmpty()) {
                    record.setBatchNumber(dto.getBatchNumber());
                }
                if (dto.getRemark() != null) record.setRemark(dto.getRemark());
                if (dto.getExecutorId() != null) record.setExecutorId(dto.getExecutorId());
                if (dto.getExecutorName() != null) record.setExecutorName(dto.getExecutorName());
                if (dto.getCompletionTime() != null) record.setCompletionTime(dto.getCompletionTime());
                if (dto.getRequestParams() != null) record.setRequestParams(dto.getRequestParams());
            } else {
                // 不存在则新建
                record = new TMpdClearanceRecord();
                BeanUtils.copyProperties(dto, record);
            }
        } else {
            // 没有中控任务ID，直接新建
            record = new TMpdClearanceRecord();
            BeanUtils.copyProperties(dto, record);
        }

        // 生成工单号（仅在新建时或工单号为空时）
        if (!isUpdate && (record.getWorkOrderNumber() == null || record.getWorkOrderNumber().trim().isEmpty())) {
            record.setWorkOrderNumber(generateWorkOrderNumber());
        }

        // 设置工单日期（仅在新建时或工单日期为空时）
        if (!isUpdate && record.getWorkOrderDate() == null) {
            record.setWorkOrderDate(new Date());
        }

        // 设置初始状态为执行中（仅在新建时或状态为空时）
        if (!isUpdate && record.getStatus() == null) {
            record.setStatus(ClearanceConstants.STATUS_EXECUTING);
        }

        record = clearanceRecordRepository.save(record);

        if (isUpdate) {
            log.info("更新清仓记录成功，工单号：{}，筒仓：{}，中控任务ID：{}",
                    record.getWorkOrderNumber(), record.getSiloName(), record.getCentralControlTaskId());
        } else {
            log.info("创建清仓记录成功，工单号：{}，筒仓：{}，中控任务ID：{}",
                    record.getWorkOrderNumber(), record.getSiloName(), record.getCentralControlTaskId());
        }

        // 处理批次切换逻辑（批号不为空时才处理）
        /*if (record.getBatchNumber() != null && !record.getBatchNumber().trim().isEmpty()) {
            processBatchSwitch(record.getSiloCode(), record.getBatchNumber(), null);
        }*/
        return "SUCCESS";
    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private MaterialSiloConfigDTO findSiloCodeByTubCode(String siloCode) {
        MaterialSiloConfigDTO silo = materialSiloConfigService.getMaterialSiloByCode(siloCode);
        if (silo == null){
            throw new RuntimeException("未找到筒仓信息");
        }
        return silo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeClearanceRecord(String centralControlTaskId, Date completionTime) {
        Optional<TMpdClearanceRecord> recordOpt = clearanceRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的清仓记录", centralControlTaskId);
            return false;
        }

        TMpdClearanceRecord record = recordOpt.get();
        record.setStatus(ClearanceConstants.STATUS_COMPLETED);
        record.setCompletionTime(completionTime);

        clearanceRecordRepository.save(record);
        log.info("清仓记录完成，工单号：{}，筒仓：{}，中控任务ID：{}", 
                record.getWorkOrderNumber(), record.getSiloName(), centralControlTaskId);

        // 处理库存校准
        processInventoryCalibration(record.getSiloCode());

        return true;
    }

    @Override
    public Page<ClearanceRecordDTO> queryClearanceRecords(ClearanceRecordQueryDTO queryDTO) {
        return PageHelperUtil.getPage(clearanceRecordMapper::queryClearanceRecords, queryDTO, ClearanceRecordDTO.class);
    }

    @Override
    public ClearanceRecordDTO getClearanceRecordById(Integer id) {
        Optional<TMpdClearanceRecord> recordOpt = clearanceRecordRepository.findById(id);
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("清仓记录不存在");
        }
        return convertToDTO(recordOpt.get());
    }

    @Override
    public ClearanceRecordDTO getClearanceRecordByWorkOrderNumber(String workOrderNumber) {
        ClearanceRecordDTO dto = clearanceRecordMapper.getClearanceRecordByWorkOrderNumber(workOrderNumber);
        if (dto == null) {
            throw new IllegalArgumentException("清仓记录不存在");
        }
        return dto;
    }

    @Override
    public ClearanceRecordDTO getClearanceRecordByTaskId(String centralControlTaskId) {
        ClearanceRecordDTO dto = clearanceRecordMapper.getClearanceRecordByTaskId(centralControlTaskId);
        if (dto == null) {
            throw new IllegalArgumentException("清仓记录不存在");
        }
        return dto;
    }

    @Override
    public List<ClearanceRecordDTO> getExecutingRecords() {
        return clearanceRecordMapper.getExecutingRecords();
    }

    @Override
    public ClearanceRecordDTO getLatestClearanceRecordBySilo(String siloCode) {
        return clearanceRecordMapper.getLatestClearanceRecordBySilo(siloCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteClearanceRecord(Integer id) {
        if (!clearanceRecordRepository.existsById(id)) {
            throw new IllegalArgumentException("清仓记录不存在");
        }
        clearanceRecordRepository.deleteById(id);
        log.info("删除清仓记录成功，ID：{}", id);
        return true;
    }

    @Override
    public String generateWorkOrderNumber() {
        // 生成工单号：QC+YYMMDD+三位流水
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String dateStr = dateFormat.format(new Date());
        
        // 查询当天的工单数量
        SimpleDateFormat workOrderDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String workOrderDateStr = workOrderDateFormat.format(new Date());
        Long count = clearanceRecordMapper.countByWorkOrderDate(workOrderDateStr);
        
        // 生成三位流水号
        String serialNumber = String.format("%03d", count + 1);
        
        return ClearanceConstants.WORK_ORDER_PREFIX + dateStr + serialNumber;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processBatchSwitch(String siloCode, String oldBatchNumber, String newBatchNumber) {
        log.info("处理批次切换，筒仓：{}，旧批次：{}，新批次：{}", siloCode, oldBatchNumber, newBatchNumber);

        try {
            // 查询该筒仓的相关清仓记录
            List<ClearanceRecordDTO> records = clearanceRecordMapper.getClearanceRecordsBySiloAndBatch(siloCode, oldBatchNumber);

            log.info("批次切换处理完成，筒仓：{}，影响记录数：{}", siloCode, records.size());
            return true;
        } catch (Exception e) {
            log.error("批次切换处理失败，筒仓：{}，错误：{}", siloCode, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processInventoryCalibration(String siloCode) {
        log.info("处理库存校准，筒仓：{}", siloCode);

        try {

            // 获取最新的清仓记录
            ClearanceRecordDTO latestRecord = clearanceRecordMapper.getLatestClearanceRecordBySilo(siloCode);

            if (latestRecord != null) {
                log.info("库存校准处理完成，筒仓：{}，最新清仓记录：{}", siloCode, latestRecord.getWorkOrderNumber());
            }

            return true;
        } catch (Exception e) {
            log.error("库存校准处理失败，筒仓：{}，错误：{}", siloCode, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成序号（流水号）
     * @return 序号
     */
    private String generateSerialNumber() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return dateFormat.format(new Date());
    }

    /**
     * 转换实体为DTO
     * @param record 清仓记录实体
     * @return 清仓记录DTO
     */
    private ClearanceRecordDTO convertToDTO(TMpdClearanceRecord record) {
        ClearanceRecordDTO dto = new ClearanceRecordDTO();
        BeanUtils.copyProperties(record, dto);

        // 设置状态描述
        if (record.getStatus() != null) {
            if (ClearanceConstants.STATUS_EXECUTING.equals(record.getStatus())) {
                dto.setStatusDesc(ClearanceConstants.STATUS_EXECUTING_DESC);
            } else if (ClearanceConstants.STATUS_COMPLETED.equals(record.getStatus())) {
                dto.setStatusDesc(ClearanceConstants.STATUS_COMPLETED_DESC);
            }
        }

        return dto;
    }

    /**
     * 获取筒仓最新的清仓记录（Repository辅助方法）
     * @param siloCode 筒仓编号
     * @return 最新的清仓记录，如果没有则返回null
     */
    private TMpdClearanceRecord getLatestClearanceRecordEntityBySilo(String siloCode) {
        List<TMpdClearanceRecord> records = clearanceRecordRepository.findLatestBySiloCodeWithLimit(siloCode, PageRequest.of(0, 1));
        return records.isEmpty() ? null : records.get(0);
    }
}