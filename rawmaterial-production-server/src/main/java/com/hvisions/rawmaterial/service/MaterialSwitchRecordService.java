package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO;
import com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordQueryDTO;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;

/**
 * 筒仓物料切换记录服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓物料切换记录服务接口
 * @Date: 2025-01-17
 */
public interface MaterialSwitchRecordService {

    /**
     * 创建物料切换记录
     * @param dto 物料切换记录DTO
     * @return 创建后的记录ID
     */
    String createMaterialSwitchRecord(MaterialSwitchRecordDTO dto);

    /**
     * 完成物料切换记录
     * @param taskNo 任务号
     * @param completionTime 完成时间
     * @return 是否成功
     */
    Boolean completeMaterialSwitchRecord(String taskNo, Date completionTime);

    /**
     * 完成物料切换记录（通过中控任务ID）
     * @param centralControlTaskId 中控系统任务ID
     * @param completionTime 完成时间
     * @return 是否成功
     */
    Boolean completeMaterialSwitchRecordByTaskId(String centralControlTaskId, Date completionTime);

    /**
     * 标记物料切换记录为失败
     * @param taskNo 任务号
     * @param failureReason 失败原因
     * @return 是否成功
     */
    Boolean failMaterialSwitchRecord(String taskNo, String failureReason);

    /**
     * 分页查询物料切换记录
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<MaterialSwitchRecordDTO> queryMaterialSwitchRecords(MaterialSwitchRecordQueryDTO queryDTO);

    /**
     * 根据ID查询物料切换记录
     * @param id 记录ID
     * @return 物料切换记录详情
     */
    MaterialSwitchRecordDTO getMaterialSwitchRecordById(Integer id);

    /**
     * 根据任务号查询物料切换记录
     * @param taskNo 任务号
     * @return 物料切换记录详情
     */
    MaterialSwitchRecordDTO getMaterialSwitchRecordByTaskNo(String taskNo);

    /**
     * 根据中控系统任务ID查询物料切换记录
     * @param centralControlTaskId 中控系统任务ID
     * @return 物料切换记录详情
     */
    MaterialSwitchRecordDTO getMaterialSwitchRecordByTaskId(String centralControlTaskId);

    /**
     * 根据唯一标识查询物料切换记录
     * @param uniqueId 唯一标识
     * @return 物料切换记录详情
     */
    MaterialSwitchRecordDTO getMaterialSwitchRecordByUniqueId(String uniqueId);

    /**
     * 查询进行中的物料切换记录
     * @return 进行中的物料切换记录列表
     */
    List<MaterialSwitchRecordDTO> getInProgressRecords();

    /**
     * 根据筒仓号查询最新的物料切换记录
     * @param siloNo 筒仓号
     * @return 最新的物料切换记录
     */
    MaterialSwitchRecordDTO getLatestMaterialSwitchRecordBySilo(String siloNo);

    /**
     * 根据筒仓号和状态查询物料切换记录
     * @param siloNo 筒仓号
     * @param status 状态
     * @return 物料切换记录列表
     */
    List<MaterialSwitchRecordDTO> getMaterialSwitchRecordsBySiloAndStatus(String siloNo, Integer status);

    /**
     * 删除物料切换记录
     * @param id 记录ID
     * @return 是否成功
     */
    Boolean deleteMaterialSwitchRecord(Integer id);

    /**
     * 更新物料切换记录
     * @param dto 物料切换记录DTO
     * @return 是否成功
     */
    Boolean updateMaterialSwitchRecord(MaterialSwitchRecordDTO dto);

    /**
     * 处理筒仓物料切换业务逻辑
     * @param siloNo 筒仓号
     * @param beforeMaterialType 更换前物料类型
     * @param afterMaterialType 更换后物料类型
     * @return 是否成功
     */
    Boolean processMaterialSwitch(String siloNo, String beforeMaterialType, String afterMaterialType);

    /**
     * 处理库存更新
     * @param siloNo 筒仓号
     * @param beforeMaterialCode 更换前物料编码
     * @param afterMaterialCode 更换后物料编码
     * @return 是否成功
     */
    Boolean processInventoryUpdate(String siloNo, String beforeMaterialCode, String afterMaterialCode);

    /**
     * 统计指定状态的记录数量
     * @param status 状态
     * @return 记录数量
     */
    Long countByStatus(Integer status);

    /**
     * 统计指定筒仓的记录数量
     * @param siloNo 筒仓号
     * @return 记录数量
     */
    Long countBySiloNo(String siloNo);

    /**
     * 检查任务号是否存在
     * @param taskNo 任务号
     * @return 是否存在
     */
    Boolean existsByTaskNo(String taskNo);

    /**
     * 检查唯一标识是否存在
     * @param uniqueId 唯一标识
     * @return 是否存在
     */
    Boolean existsByUniqueId(String uniqueId);
}
