package com.hvisions.rawmaterial.service.impl;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.brewage.feign.purchase.TimeClient;
import com.hvisions.rawmaterial.dto.production.bran.inspection.parameter.BranInspectionParameterDTO;
import com.hvisions.rawmaterial.dao.BranInspectionParameterMapper;
import com.hvisions.rawmaterial.entity.TMpdBranInspectionParameter;
import com.hvisions.rawmaterial.service.BranInspectionParameterService;
import com.hvisions.rawmaterial.utils.CronUtil;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.timer.client.TimerClient;
import com.hvisions.timer.dto.TimerDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;

/**
 * <p>
 * 蒸糠质量巡检参数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Service
public class BranInspectionParameterServiceImpl extends ServiceImpl<BranInspectionParameterMapper, TMpdBranInspectionParameter> implements BranInspectionParameterService {

    @Resource
    private TimerClient timerClient;

    @Resource
    private TimeClient timeClient;

    /*
     * @Description: 获取最新一条蒸糠质量巡检参数
     *
     * <AUTHOR>
     * @param :
     * @return com.hvisions.purchase.dto.production.bran.inspection.parameter.BranInspectionParameterDTO
     */
    @Override
    public BranInspectionParameterDTO getBranInspectionParameter() {
        return baseMapper.getBranInspectionParameter();
    }

    /*
     * @Description: 新增蒸糠质量巡检参数
     *
     * <AUTHOR>
     * @param branInspectionParameterDTO:
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Boolean addBranInspectionParameter(BranInspectionParameterDTO branInspectionParameterDTO) {
        try {
            Integer timerId = branInspectionParameterDTO.getTimerId();
            if (StringUtil.isEmpty(timerId) || timerId == 0) { // 新增定时任务配置
                TimerDTO timerDTO = createOrUpdateTimer(branInspectionParameterDTO);
                // 创建周期任务
                ResultVO<Integer> timerAndStart = timerClient.createTimer(timerDTO);
                if (StringUtil.isEmpty(timerAndStart.getData())) {
                    throw new BaseKnownException(10000, "定时任务创建失败!");
                }
                if (branInspectionParameterDTO.getGeneratedTask()) {
                    ResultVO resultVO = timeClient.startTimer(timerAndStart.getData());
                    if (resultVO.getCode() != 200) {
                        throw new BaseKnownException(10000, "定时任务开启失败!");
                    }
                }
                branInspectionParameterDTO.setTimerId(timerAndStart.getData());
                branInspectionParameterDTO.setCreateTime(new Date());
                branInspectionParameterDTO.setUpdateTime(new Date());
                // 创建巡检配置
                return this.save(DtoMapper.convert(branInspectionParameterDTO, TMpdBranInspectionParameter.class));
            } else { // 修改定时任务配置
                // 关闭定时任务
                ResultVO resultVO1 = timeClient.stopTimer(timerId);
                if (resultVO1.getCode() != 200) {
                    throw new BaseKnownException(10000, "定时任务关闭失败：" + resultVO1.getMessage());
                }
                TimerDTO timerDTO = createOrUpdateTimer(branInspectionParameterDTO);
                timerDTO.setId(timerId);
                // 修改周期任务
                ResultVO<Integer> resultVO = timerClient.updateTimer(timerDTO);
                if (resultVO.getCode() != 200) {
                    throw new BaseKnownException(10000, "定时任务修改失败:" + resultVO.getMessage());
                }
                if (branInspectionParameterDTO.getGeneratedTask()) {
                    // 启动周期任务
                    ResultVO resultVO2 = timeClient.startTimer(timerId);
                    if (resultVO2.getCode() != 200) {
                        throw new BaseKnownException(10000, "定时任务启动失败:" + resultVO2.getMessage());
                    }

                }
                branInspectionParameterDTO.setUpdateTime(new Date());
                return this.updateById(DtoMapper.convert(branInspectionParameterDTO, TMpdBranInspectionParameter.class));
            }
        } catch (Exception e) {
            throw new BaseKnownException(10000, "蒸糠质量巡检周期新增失败:" + e.getMessage());
        }
    }

    /*
     * @description:创建修改TimerDto
     * <AUTHOR>
     * @date 2022/5/13 14:13
     * @param branInspectionParameterDTO
     * @return com.hvisions.timer.dto.TimerDTO
     */
    private TimerDTO createOrUpdateTimer(BranInspectionParameterDTO branInspectionParameterDTO) throws ParseException {

        BigDecimal[] bigDecimals = new BigDecimal(branInspectionParameterDTO.getInspectionCycle()).divideAndRemainder(new BigDecimal(60));
        String cron = CronUtil.getCron(branInspectionParameterDTO.getBeginTime(), branInspectionParameterDTO.getEndTime(), bigDecimals[0].intValue());

        TimerDTO timerDTO = new TimerDTO();
        timerDTO.setActive(branInspectionParameterDTO.getGeneratedTask());
        timerDTO.setBeginTime(branInspectionParameterDTO.getBeginTime());
        timerDTO.setDayInterval(1);
        timerDTO.setDayRepeatBeginTime(branInspectionParameterDTO.getBeginTime());
        timerDTO.setDayRepeatEndTime(branInspectionParameterDTO.getEndTime());
        timerDTO.setDayRepeatIntervalCount(branInspectionParameterDTO.getInspectionCycle());
        timerDTO.setDayRepeatIntervalType(2);
        timerDTO.setDayRepeatType(2);
        timerDTO.setDescription("每一天在" + DateUtil.dateFormat(branInspectionParameterDTO.getBeginTime(), DatePattern.NORM_TIME_PATTERN) + "和" + DateUtil.dateFormat(branInspectionParameterDTO.getEndTime(), DatePattern.NORM_TIME_PATTERN) + "之间、每" + branInspectionParameterDTO.getInspectionCycle() + "分钟执行,将从" + DateUtil.dateFormat(branInspectionParameterDTO.getBeginTime(), DatePattern.NORM_DATE_PATTERN) + "开始使用计划。");
        timerDTO.setHasEndDate(false);
        timerDTO.setRepeatType(1);
        timerDTO.setTimerCode("ZKZLXJ");
        timerDTO.setTimerGroup("3");
        timerDTO.setTimerName("蒸糠质量巡检周期");
        timerDTO.setTimerType(4);
        timerDTO.setCron(cron);
        return timerDTO;
    }

}
