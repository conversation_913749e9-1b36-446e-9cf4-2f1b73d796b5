package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.rice.transfer.detail.RiceTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳转运工单
 * @date 2022/4/22 10:18
 */
public interface RiceTransferOrderService {

    Page<RiceTransferOrderPageDTO> getRiceTransferOrderPageList(RiceTransferOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);

    Integer detailUpdate(DetailUpdateDTO updateDTO);

    Page<RiceTransferDetailListDTO> getRiceTransferOrderDetailPageList(RiceTransferOrderPageQueryDTO queryDTO);

    /**
     * 从中控同步稻壳转运工单
     * @param taskDTO
     * @return
     */
    String syncRiceHuskTransControl(UnifiedTaskDTO taskDTO);
}
