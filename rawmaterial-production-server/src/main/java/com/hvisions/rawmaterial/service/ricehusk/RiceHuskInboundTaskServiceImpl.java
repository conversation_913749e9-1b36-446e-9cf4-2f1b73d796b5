package com.hvisions.rawmaterial.service.ricehusk;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskDetailMapper;
import com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskMapper;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTask;
import com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTaskDetail;
import com.hvisions.rawmaterial.enums.LogCaptureEnum;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.covertStorage;
import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.parseStatus;

/**
 * 稻壳入仓任务Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@Service
public class RiceHuskInboundTaskServiceImpl implements RiceHuskInboundTaskService {

    @Resource
    private RiceHuskInboundTaskMapper riceHuskInboundTaskMapper;

    @Resource
    private RiceHuskInboundTaskDetailMapper riceHuskInboundTaskDetailMapper;

    @Resource
    private LogCaptureClient logCaptureClient;

    @Resource
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Override
    public RiceHuskInboundTaskDTO findById(String id) {
        TMpdRiceHuskInboundTask entity = riceHuskInboundTaskMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RiceHuskInboundTaskDTO save(RiceHuskInboundTaskDTO dto) {
        TMpdRiceHuskInboundTask entity = new TMpdRiceHuskInboundTask();
        BeanUtils.copyProperties(dto, entity);

        // 如果没有工单号，则生成工单号
        if (StringUtils.isEmpty(entity.getOrderNo())) {
            entity.setOrderNo(generateOrderNo(entity.getOrderDate()));
        }

        entity.setDeleted(false);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        riceHuskInboundTaskMapper.insert(entity);
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RiceHuskInboundTaskDTO update(RiceHuskInboundTaskDTO dto) {
        TMpdRiceHuskInboundTask entity = riceHuskInboundTaskMapper.selectById(dto.getId());
        if (entity == null) {
            throw new RuntimeException("稻壳入仓任务不存在");
        }

        BeanUtils.copyProperties(dto, entity);
        entity.setUpdateTime(new Date());

        riceHuskInboundTaskMapper.updateById(entity);
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        TMpdRiceHuskInboundTask entity = riceHuskInboundTaskMapper.selectById(id);
        if (entity == null) {
            throw new RuntimeException("稻壳入仓任务不存在");
        }

        entity.setDeleted(true);
        entity.setUpdateTime(new Date());
        riceHuskInboundTaskMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFromCentralControl(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步稻壳入仓记录");

        // 记录日志到日志服务
        addLogCapture(taskDTO);

        try {
            // 1. 检查是否已存在相同的记录（根据物料编码、实际数量、开始时间判断）
            if (!isRecordExists(taskDTO)) {
                processInboundRecord(taskDTO);
                // 5. 记录日志详情
                log.info("稻壳入库任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }
            // 6. 返回结果
            return String.format("稻壳入库任务同步成功，taskNo:%s", taskDTO.getTaskNo());
            
        } catch (Exception e) {
            log.error("同步入仓记录失败", e);
            throw new RuntimeException("同步入仓记录失败：" + e.getMessage());
        }
    }

    @Override
    public Page<RiceHuskInboundTaskDTO> getRiceHuskInboundTaskPageList(RiceHuskInboundTaskQueryDTO queryDTO) {
        return PageHelperUtil.getPage(riceHuskInboundTaskMapper::getRiceHuskInboundTaskPageList, queryDTO, RiceHuskInboundTaskDTO.class);
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）- UnifiedTaskDTO版本
     */
    private boolean isRecordExists(UnifiedTaskDTO taskDTO) {
        TMpdRiceHuskInboundTaskDetail existing = riceHuskInboundTaskDetailMapper
            .selectByMaterialCodeAndTaskNoAndTime(
                taskDTO.getMaterialCode(),
                 taskDTO.getTaskNo(),
                taskDTO.getStartTime()
            );
        return existing != null;
    }

    /**
     * 处理单条入仓记录 - UnifiedTaskDTO版本
     */
    private void processInboundRecord(UnifiedTaskDTO taskDTO) {
        Date recordDate = taskDTO.getStartTime()!=null?taskDTO.getStartTime():new Date(); // 使用记录的开始时间作为任务日期

        // 2. 根据时间（YYYYMMdd格式）、物料编码判断主表是否存在记录
        TMpdRiceHuskInboundTask existingTask = riceHuskInboundTaskMapper.selectByDateAndMaterialCode(recordDate, taskDTO.getMaterialCode());

        if (existingTask == null) {
            // 2. 主表不存在则新增
            existingTask = createNewTask(recordDate, taskDTO);
            // 创建任务详情
            TMpdRiceHuskInboundTaskDetail detail = createTaskDetail(existingTask.getId(), taskDTO);
            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingTask.getOrderNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录（根据唯一标识判断）
            TMpdRiceHuskInboundTaskDetail existingDetail = riceHuskInboundTaskDetailMapper.selectByControlTaskNo(taskDTO.getTaskNo());;

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdRiceHuskInboundTaskDetail detail = createTaskDetail(existingTask.getId(), taskDTO);
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingTask.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateTaskDetail(existingDetail, taskDTO);
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表的汇总信息
            updateTaskSummary(existingTask, taskDTO);
            log.info("更新主表汇总信息，任务ID:{}, 累计数量:{}", existingTask.getId(), existingTask.getQuantity());
        }
    }

    /**
     * 创建新任务 - UnifiedTaskDTO版本
     */
    private TMpdRiceHuskInboundTask createNewTask(Date date, UnifiedTaskDTO taskDTO) {
        TMpdRiceHuskInboundTask task = new TMpdRiceHuskInboundTask();
        task.setOrderNo(generateOrderNo(date));
        task.setOrderDate(date);
        task.setMaterialName(taskDTO.getMaterialName());
        task.setMaterialCode(taskDTO.getMaterialCode());
        task.setQuantity(taskDTO.getActualWeight());
        task.setUnit(taskDTO.getUnit());
        task.setRemark("中控同步");
        task.setDeleted(false);
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());

        riceHuskInboundTaskMapper.insert(task);
        return task;
    }

    /**
     * 更新任务汇总信息 - UnifiedTaskDTO版本
     */
    private void updateTaskSummary(TMpdRiceHuskInboundTask task, UnifiedTaskDTO taskDTO) {
        BigDecimal summaryDTO = riceHuskInboundTaskDetailMapper.selectSummaryByOrderId(task.getId());
        TMpdRiceHuskInboundTask updateOrder = new TMpdRiceHuskInboundTask();
        updateOrder.setId(task.getId());
        updateOrder.setQuantity(summaryDTO != null ?
                summaryDTO : BigDecimal.ZERO);
        updateOrder.setUpdateTime(new Date());
        riceHuskInboundTaskMapper.updateById(task);
        log.info("根据详情汇总更新主表成功，工单ID：{}，数量：{}",
                task.getId(), summaryDTO);
    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private TMpdMateriaSiloDO findSiloCodeByTubCode(String siloCode,Integer materialType) {
        TMpdMateriaSiloDO silo = materialSiloConfigMapper.findSiloBySiloCode(siloCode,materialType);
        if (silo == null){
            throw new RuntimeException("未找到筒仓信息");
        }
        return silo;
    }

    /**
     * 创建任务详情 - UnifiedTaskDTO版本
     */
    private TMpdRiceHuskInboundTaskDetail createTaskDetail(Integer taskId, UnifiedTaskDTO taskDTO) {
        TMpdRiceHuskInboundTaskDetail detail = new TMpdRiceHuskInboundTaskDetail();
        detail.setTaskId(taskId);

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()),taskDTO.getExtField6());
        detail.setSiloName(silo.getName());
        detail.setSiloId(silo.getId());
        detail.setSiloCode(taskDTO.getSiloNo());
        detail.setMaterialName(taskDTO.getMaterialName());
        detail.setMaterialCode(taskDTO.getMaterialCode());
        detail.setUnit(taskDTO.getUnit());
        detail.setQuantity(taskDTO.getActualWeight());
        detail.setActualStartTime(taskDTO.getStartTime());
        detail.setActualCompletionTime(taskDTO.getEndTime());
        detail.setStatus(parseStatus(taskDTO.getStatus())); // 已完成
        detail.setCreatorName("中控");
        detail.setUniqueId(taskDTO.getUniqueId());

        riceHuskInboundTaskDetailMapper.insert(detail);
        return detail;
    }

    /**
     * 更新任务详情
     */
    private void updateTaskDetail(TMpdRiceHuskInboundTaskDetail existingDetail, UnifiedTaskDTO taskDTO) {
        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(taskDTO.getSiloNo(),taskDTO.getExtField6());
        existingDetail.setSiloName(silo.getName());
        existingDetail.setSiloId(silo.getId());
        existingDetail.setSiloCode(taskDTO.getSiloNo());
        existingDetail.setMaterialName(taskDTO.getMaterialName());
        existingDetail.setMaterialCode(taskDTO.getMaterialCode());
        existingDetail.setUnit(taskDTO.getUnit());
        existingDetail.setQuantity(taskDTO.getActualWeight());
        existingDetail.setActualStartTime(taskDTO.getStartTime());
        existingDetail.setActualCompletionTime(taskDTO.getEndTime());
        existingDetail.setStatus(1); // 已完成
        existingDetail.setUpdateTime(new Date());

        riceHuskInboundTaskDetailMapper.updateById(existingDetail);
    }

    /**
     * 生成工单号
     * 规则：RC + 年月日 + 三位流水号
     */
    private String generateOrderNo(Date orderDate) {
        if (orderDate == null) {
            orderDate = new Date();
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(orderDate);
        
        // 查询当天最大流水号
        String maxSerialNo = riceHuskInboundTaskMapper.selectMaxSerialNoByDate(orderDate);
        int serialNo = 1;
        if (maxSerialNo != null && !maxSerialNo.isEmpty()) {
            serialNo = Integer.parseInt(maxSerialNo) + 1;
        }
        
        return String.format("RC%s%03d", dateStr, serialNo);
    }

    /**
     * 将实体转换为DTO
     */
    private RiceHuskInboundTaskDTO convertToDTO(TMpdRiceHuskInboundTask entity) {
        if (entity == null) {
            return null;
        }
        
        RiceHuskInboundTaskDTO dto = new RiceHuskInboundTaskDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 新增日志
     *
     * @param syncDTO 同步数据
     */
    private void addLogCapture(UnifiedTaskDTO syncDTO) {
        LogDto logDto = new LogDto();
        logDto.setLogType(1);
        logDto.setLogParameter(com.alibaba.fastjson.JSONObject.toJSONString(syncDTO));
        LogCaptureEnum logCaptureEnum = LogCaptureEnum.RICE_HUSK_INBOUND_SYNC_CENTRAL_CONTROL;

        logDto.setLogModular(logCaptureEnum.getLogModular());
        logDto.setLogExceptionMessage(null);
        logDto.setLogInvocation(logCaptureEnum.getLogInvocation());
        logDto.setLocation(logCaptureEnum.getLocation());
        logDto.setControllerName(logCaptureEnum.getControllerName());
        logDto.setMethodName(logCaptureEnum.getMethodName());
        logCaptureClient.logRecord(logDto);
    }
}
