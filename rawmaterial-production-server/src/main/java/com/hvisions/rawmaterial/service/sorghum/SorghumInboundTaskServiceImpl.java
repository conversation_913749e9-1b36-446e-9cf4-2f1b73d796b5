package com.hvisions.rawmaterial.service.sorghum;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dao.sorghum.SorghumInboundTaskDetailMapper;
import com.hvisions.rawmaterial.dao.sorghum.SorghumInboundTaskMapper;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskDTO;
import com.hvisions.rawmaterial.dto.sorghum.SorghumInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTask;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTaskDetail;
import com.hvisions.rawmaterial.enums.LogCaptureEnum;
import com.hvisions.rawmaterial.service.log.SyncOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.covertStorage;
import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.parseStatus;

/**
 * 高粱入仓任务Service实现类
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Slf4j
@Service
public class SorghumInboundTaskServiceImpl implements SorghumInboundTaskService {

    @Autowired
    private SorghumInboundTaskMapper sorghumInboundTaskMapper;

    @Autowired
    private SorghumInboundTaskDetailMapper sorghumInboundTaskDetailMapper;

    @Autowired
    private SyncOperationLogService syncOperationLogService;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Resource
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Override
    public Page<SorghumInboundTaskDTO> findByPage(SorghumInboundTaskQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumInboundTaskMapper::selectByCondition, queryDTO, SorghumInboundTaskDTO.class);
    }

    @Override
    public SorghumInboundTaskDTO findById(String id) {
        TMpdSorghumInboundTask entity = sorghumInboundTaskMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SorghumInboundTaskDTO save(SorghumInboundTaskDTO dto) {
        TMpdSorghumInboundTask entity = new TMpdSorghumInboundTask();
        BeanUtils.copyProperties(dto, entity);

        // 生成ID
        //entity.setId(UUID.randomUUID().toString().replace("-", ""));
        
        // 如果没有工单号，则生成工单号
        if (StringUtils.isEmpty(entity.getOrderNo())) {
            entity.setOrderNo(generateOrderNo(entity.getOrderDate()));
        }
        
        // 设置默认值
        /*if (entity.getStatus() == null) {
            entity.setStatus(0); // 默认未完成
        }*/
        entity.setDeleted(false);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        sorghumInboundTaskMapper.insert(entity);
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SorghumInboundTaskDTO update(SorghumInboundTaskDTO dto) {
        TMpdSorghumInboundTask entity = sorghumInboundTaskMapper.selectById(dto.getId());
        if (entity == null) {
            throw new RuntimeException("高粱入仓任务不存在");
        }

        BeanUtils.copyProperties(dto, entity);
        entity.setUpdateTime(new Date());

        sorghumInboundTaskMapper.updateById(entity);
        return convertToDTO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        TMpdSorghumInboundTask entity = sorghumInboundTaskMapper.selectById(id);
        if (entity == null) {
            throw new RuntimeException("高粱入仓任务不存在");
        }

        entity.setDeleted(true);
        entity.setUpdateTime(new Date());
        sorghumInboundTaskMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFromCentralControl(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步高粱入仓记录");

        // 记录日志到日志服务
        addLogCapture(taskDTO);

        try {
            // 1. 检查是否已存在相同的记录（根据物料编码、实际数量、开始时间判断）
            if (!isRecordExists(taskDTO)) {
                processInboundRecord(taskDTO);
                // 5. 记录日志详情
                log.info("高粱入库任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logSkipDuplicate(taskDTO);
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }
            // 6. 返回结果
            return String.format("高粱入库任务同步成功，taskNo:%s", taskDTO.getTaskNo());

        } catch (Exception e) {
            // 5. 记录错误日志到数据库
            syncOperationLogService.logError(taskDTO, e.getMessage());
            log.error("同步入仓记录失败", e);
            throw new RuntimeException("同步入仓记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdSorghumInboundTaskDetail existing = sorghumInboundTaskDetailMapper
            .selectByMaterialCodeAndTaskNoAndTime(
                record.getMaterialCode(),
                record.getTaskNo(),
                record.getStartTime()
            );
        return existing != null;
    }

    /**
     * 处理单条入仓记录
     */
    private void processInboundRecord(UnifiedTaskDTO taskDTO) {
        Date recordDate = taskDTO.getStartTime() != null ? taskDTO.getStartTime() : new Date();

        // 2. 根据时间（YYYYMMdd格式）、物料编码判断主表是否存在记录
        TMpdSorghumInboundTask existingTask = sorghumInboundTaskMapper.selectByDateAndMaterialCode(recordDate, taskDTO.getMaterialCode());

        if (existingTask == null) {
            // 2. 主表不存在则新增
            existingTask = createNewTask(recordDate, taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logCreateMainTask(taskDTO, existingTask.getOrderNo(), existingTask.getId());

            // 创建任务详情
            TMpdSorghumInboundTaskDetail detail = createTaskDetail(existingTask.getId(), taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logCreateDetail(taskDTO, existingTask.getId(), detail.getId());

            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingTask.getOrderNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录
            TMpdSorghumInboundTaskDetail existingDetail = sorghumInboundTaskDetailMapper.selectByControlTaskNo(taskDTO.getTaskNo());

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdSorghumInboundTaskDetail detail = createTaskDetail(existingTask.getId(), taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logCreateDetail(taskDTO, existingTask.getId(), detail.getId());
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingTask.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateTaskDetail(existingDetail, taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logUpdateDetail(taskDTO, existingDetail.getId());
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表的汇总信息
            updateTaskSummary(existingTask);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logUpdateMainSummary(taskDTO, existingTask.getId(), existingTask.getQuantity());
            log.info("更新主表汇总信息，任务ID:{}, 累计数量:{}", existingTask.getId(), existingTask.getQuantity());
        }
    }

    /**
     * 创建新任务
     */
    private TMpdSorghumInboundTask createNewTask(Date date, UnifiedTaskDTO record) {
        TMpdSorghumInboundTask task = new TMpdSorghumInboundTask();
        task.setOrderNo(generateOrderNo(date));
        task.setOrderDate(date);
        task.setMaterialName(record.getMaterialName());
        task.setMaterialCode(record.getMaterialCode());
        task.setQuantity(record.getActualWeight());
        task.setUnit(record.getUnit());
        task.setRemark("中控同步");
        sorghumInboundTaskMapper.insert(task);
        return task;
    }

    /**
     * 更新任务汇总信息
     */
    private void updateTaskSummary(TMpdSorghumInboundTask task) {
        // 查询该主表下所有详情记录的汇总数据
        BigDecimal summaryDTO = sorghumInboundTaskDetailMapper.selectSummaryByOrderId(task.getId());

        TMpdSorghumInboundTask updateOrder = new TMpdSorghumInboundTask();
        updateOrder.setId(task.getId());

        updateOrder.setQuantity(summaryDTO != null ?
                summaryDTO : BigDecimal.ZERO);
        updateOrder.setUpdateTime(new Date());

        sorghumInboundTaskMapper.updateById(task);
        log.info("根据详情汇总更新主表成功，工单ID：{}，数量：{}",
                task.getId(), summaryDTO);

    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private TMpdMateriaSiloDO findSiloCodeByTubCode(String siloCode,Integer materialType) {
        TMpdMateriaSiloDO silo = materialSiloConfigMapper.findSiloBySiloCode(siloCode,materialType);
        if (silo == null){
            throw new RuntimeException("未找到筒仓信息");
        }
        return silo;
    }

    /**
     * 创建任务详情 - UnifiedTaskDTO版本
     */
    private TMpdSorghumInboundTaskDetail createTaskDetail(Integer taskId, UnifiedTaskDTO taskDTO) {
        TMpdSorghumInboundTaskDetail detail = new TMpdSorghumInboundTaskDetail();
        detail.setTaskId(taskId);

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()),taskDTO.getExtField6());

        detail.setSiloCode(silo.getCode());
        detail.setSiloName(silo.getName());
        detail.setSiloId(silo.getId());

        detail.setMaterialName(taskDTO.getMaterialName());
        detail.setMaterialCode(taskDTO.getMaterialCode());
        detail.setUnit(taskDTO.getUnit());
        detail.setQuantity(taskDTO.getActualWeight());
        detail.setActualStartTime(taskDTO.getStartTime());
        detail.setActualCompletionTime(taskDTO.getEndTime());
        detail.setControlTaskNo(taskDTO.getTaskNo());
        detail.setStatus(parseStatus(taskDTO.getStatus())); // 已完成
        detail.setCreatorName("中控");
        detail.setUniqueId(taskDTO.getUniqueId());

        sorghumInboundTaskDetailMapper.insert(detail);
        return detail;
    }

    /**
     * 更新任务详情
     */
    private void updateTaskDetail(TMpdSorghumInboundTaskDetail existingDetail, UnifiedTaskDTO taskDTO) {
        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()),taskDTO.getExtField6());

        existingDetail.setSiloCode(silo.getCode());
        existingDetail.setSiloName(silo.getName());
        existingDetail.setSiloId(silo.getId());
        existingDetail.setMaterialName(taskDTO.getMaterialName());
        existingDetail.setMaterialCode(taskDTO.getMaterialCode());
        existingDetail.setUnit(taskDTO.getUnit());
        existingDetail.setQuantity(taskDTO.getTargetWeight());
        existingDetail.setActualStartTime(taskDTO.getStartTime());
        existingDetail.setActualCompletionTime(taskDTO.getEndTime());
        existingDetail.setStatus(parseStatus(taskDTO.getStatus()));
        existingDetail.setUpdateTime(new Date());

        sorghumInboundTaskDetailMapper.updateById(existingDetail);
    }

    /**
     * 生成工单号
     * 规则：RC + 年月日 + 三位流水号
     *
     * @param orderDate 工单日期
     * @return 工单号
     */
    private String generateOrderNo(Date orderDate) {
        if (orderDate == null) {
            orderDate = new Date();
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(orderDate);
        
        // 查询当天最大流水号
        String maxSerialNo = sorghumInboundTaskMapper.selectMaxSerialNoByDate(orderDate);
        int serialNo = 1;
        if (maxSerialNo != null && !maxSerialNo.isEmpty()) {
            serialNo = Integer.parseInt(maxSerialNo) + 1;
        }
        
        return String.format("RC%s%03d", dateStr, serialNo);
    }

    /**
     * 将实体转换为DTO
     *
     * @param entity 实体
     * @return DTO
     */
    private SorghumInboundTaskDTO convertToDTO(TMpdSorghumInboundTask entity) {
        if (entity == null) {
            return null;
        }
        SorghumInboundTaskDTO dto = new SorghumInboundTaskDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 新增日志
     *
     * @param syncDTO 同步数据
     */
    private void addLogCapture(UnifiedTaskDTO syncDTO) {

        LogDto logDto = new LogDto();
        logDto.setLogType(1);
        logDto.setLogParameter(JSONObject.toJSONString(syncDTO));
        LogCaptureEnum logCaptureEnum = LogCaptureEnum.SORGHUM_INBOUND_SYNC_CENTRAL_CONTROL;

        logDto.setLogModular(logCaptureEnum.getLogModular());
        logDto.setLogExceptionMessage(null);
        logDto.setLogInvocation(logCaptureEnum.getLogInvocation());
        logDto.setLocation(logCaptureEnum.getLocation());
        logDto.setControllerName(logCaptureEnum.getControllerName());
        logDto.setMethodName(logCaptureEnum.getMethodName());
        logCaptureClient.logRecord(logDto);
    }
}