package com.hvisions.rawmaterial.service.ricehusk;

import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

/**
 * 稻壳入仓任务Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface RiceHuskInboundTaskService {

    /**
     * 根据ID查询稻壳入仓任务
     *
     * @param id 任务ID
     * @return 稻壳入仓任务
     */
    RiceHuskInboundTaskDTO findById(String id);

    /**
     * 保存稻壳入仓任务
     *
     * @param dto 稻壳入仓任务DTO
     * @return 保存后的稻壳入仓任务
     */
    RiceHuskInboundTaskDTO save(RiceHuskInboundTaskDTO dto);

    /**
     * 更新稻壳入仓任务
     *
     * @param dto 稻壳入仓任务DTO
     * @return 更新后的稻壳入仓任务
     */
    RiceHuskInboundTaskDTO update(RiceHuskInboundTaskDTO dto);

    /**
     * 删除稻壳入仓任务
     *
     * @param id 任务ID
     */
    void delete(String id);

    /**
     * 从中控同步入仓记录
     *
     * @param taskDTO 任务数据
     * @return 同步结果
     */
    String syncFromCentralControl(UnifiedTaskDTO taskDTO);

    /**
     * 分页查询稻壳入仓
     * @param queryDTO
     * @return
     */
    Page<RiceHuskInboundTaskDTO> getRiceHuskInboundTaskPageList(RiceHuskInboundTaskQueryDTO queryDTO);
}
