package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.consts.MaterialSwitchConstants;
import com.hvisions.rawmaterial.dao.MaterialSwitchRecordRepository;
import com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO;
import com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordQueryDTO;
import com.hvisions.rawmaterial.entity.materialswitch.TMpdMaterialSwitchRecord;
import com.hvisions.rawmaterial.mapper.MaterialSwitchRecordMapper;
import com.hvisions.rawmaterial.service.MaterialSwitchRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 筒仓物料切换记录服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 * @Description: 筒仓物料切换记录服务实现类
 * @Date: 2025-01-17
 */
@Slf4j
@Service
public class MaterialSwitchRecordServiceImpl implements MaterialSwitchRecordService {

    @Autowired
    private MaterialSwitchRecordRepository materialSwitchRecordRepository;

    @Resource
    private MaterialSwitchRecordMapper materialSwitchRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMaterialSwitchRecord(MaterialSwitchRecordDTO dto) {
        /**
         * 1.查询任务号或中控系统任务ID是否存在 - 存在则更新，不存在则添加
         * 2.处理物料切换逻辑
         */

        TMpdMaterialSwitchRecord record;
        boolean isUpdate = false;

        // 检查任务号是否存在
        if (dto.getTaskNo() != null && !dto.getTaskNo().trim().isEmpty()) {
            Optional<TMpdMaterialSwitchRecord> existingRecordOpt = materialSwitchRecordRepository.findByTaskNo(dto.getTaskNo());
            if (existingRecordOpt.isPresent()) {
                // 存在则更新
                record = existingRecordOpt.get();
                isUpdate = true;
                log.info("找到已存在的物料切换记录，将进行更新，任务号：{}，原记录ID：{}", 
                        dto.getTaskNo(), record.getId());
                
                // 更新字段
                updateRecordFields(record, dto);
            } else {
                // 检查中控系统任务ID是否存在
                if (dto.getCentralControlTaskId() != null && !dto.getCentralControlTaskId().trim().isEmpty()) {
                    Optional<TMpdMaterialSwitchRecord> taskIdRecordOpt = materialSwitchRecordRepository.findByCentralControlTaskId(dto.getCentralControlTaskId());
                    if (taskIdRecordOpt.isPresent()) {
                        record = taskIdRecordOpt.get();
                        isUpdate = true;
                        log.info("找到已存在的物料切换记录（通过中控任务ID），将进行更新，中控任务ID：{}，原记录ID：{}", 
                                dto.getCentralControlTaskId(), record.getId());
                        updateRecordFields(record, dto);
                    } else {
                        // 不存在则新建
                        record = new TMpdMaterialSwitchRecord();
                        BeanUtils.copyProperties(dto, record);
                    }
                } else {
                    // 不存在则新建
                    record = new TMpdMaterialSwitchRecord();
                    BeanUtils.copyProperties(dto, record);
                }
            }
        } else {
            // 没有任务号，直接新建
            record = new TMpdMaterialSwitchRecord();
            BeanUtils.copyProperties(dto, record);
        }

        // 设置切换时间（仅在新建时或切换时间为空时）
        if (!isUpdate && record.getSwitchTime() == null) {
            record.setSwitchTime(new Date());
        }

        // 设置初始状态为进行中（仅在新建时或状态为空时）
        if (!isUpdate && record.getStatus() == null) {
            record.setStatus(MaterialSwitchConstants.STATUS_IN_PROGRESS);
        }

        record = materialSwitchRecordRepository.save(record);
        
        if (isUpdate) {
            log.info("更新物料切换记录成功，任务号：{}，筒仓：{}，切换：{} -> {}", 
                    record.getTaskNo(), record.getSiloNo(), record.getBeforeMaterialType(), record.getAfterMaterialType());
        } else {
            log.info("创建物料切换记录成功，任务号：{}，筒仓：{}，切换：{} -> {}", 
                    record.getTaskNo(), record.getSiloNo(), record.getBeforeMaterialType(), record.getAfterMaterialType());
        }

        // 处理物料切换业务逻辑
        if (record.getSiloNo() != null && record.getBeforeMaterialType() != null && record.getAfterMaterialType() != null) {
            processMaterialSwitch(record.getSiloNo(), record.getBeforeMaterialType(), record.getAfterMaterialType());
        }

        return "SUCCESS";
    }

    /**
     * 更新记录字段
     */
    private void updateRecordFields(TMpdMaterialSwitchRecord record, MaterialSwitchRecordDTO dto) {
        if (dto.getBusinessSystem() != null) record.setBusinessSystem(dto.getBusinessSystem());
        if (dto.getMaterialType() != null) record.setMaterialType(dto.getMaterialType());
        if (dto.getTaskType() != null) record.setTaskType(dto.getTaskType());
        if (dto.getDataType() != null) record.setDataType(dto.getDataType());
        if (dto.getSiloNo() != null) record.setSiloNo(dto.getSiloNo());
        if (dto.getSiloName() != null) record.setSiloName(dto.getSiloName());
        if (dto.getBeforeMaterialType() != null) record.setBeforeMaterialType(dto.getBeforeMaterialType());
        if (dto.getBeforeMaterialCode() != null) record.setBeforeMaterialCode(dto.getBeforeMaterialCode());
        if (dto.getBeforeMaterialName() != null) record.setBeforeMaterialName(dto.getBeforeMaterialName());
        if (dto.getAfterMaterialType() != null) record.setAfterMaterialType(dto.getAfterMaterialType());
        if (dto.getAfterMaterialCode() != null) record.setAfterMaterialCode(dto.getAfterMaterialCode());
        if (dto.getAfterMaterialName() != null) record.setAfterMaterialName(dto.getAfterMaterialName());
        if (dto.getSwitchTime() != null) record.setSwitchTime(dto.getSwitchTime());
        if (dto.getStatus() != null) record.setStatus(dto.getStatus());
        if (dto.getExecutorId() != null) record.setExecutorId(dto.getExecutorId());
        if (dto.getExecutorName() != null) record.setExecutorName(dto.getExecutorName());
        if (dto.getCompletionTime() != null) record.setCompletionTime(dto.getCompletionTime());
        if (dto.getCentralControlTaskId() != null) record.setCentralControlTaskId(dto.getCentralControlTaskId());
        if (dto.getWorkshopCode() != null) record.setWorkshopCode(dto.getWorkshopCode());
        if (dto.getWorkshopName() != null) record.setWorkshopName(dto.getWorkshopName());
        if (dto.getCenterCode() != null) record.setCenterCode(dto.getCenterCode());
        if (dto.getCenterName() != null) record.setCenterName(dto.getCenterName());
        if (dto.getRemark() != null) record.setRemark(dto.getRemark());
        if (dto.getDataSource() != null) record.setDataSource(dto.getDataSource());
        if (dto.getUniqueId() != null) record.setUniqueId(dto.getUniqueId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeMaterialSwitchRecord(String taskNo, Date completionTime) {
        Optional<TMpdMaterialSwitchRecord> recordOpt = materialSwitchRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的物料切换记录", taskNo);
            return false;
        }

        TMpdMaterialSwitchRecord record = recordOpt.get();
        record.setStatus(MaterialSwitchConstants.STATUS_COMPLETED);
        record.setCompletionTime(completionTime);

        materialSwitchRecordRepository.save(record);
        log.info("物料切换记录完成，任务号：{}，筒仓：{}，切换：{} -> {}", 
                record.getTaskNo(), record.getSiloNo(), record.getBeforeMaterialType(), record.getAfterMaterialType());

        // 处理库存更新
        processInventoryUpdate(record.getSiloNo(), record.getBeforeMaterialCode(), record.getAfterMaterialCode());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeMaterialSwitchRecordByTaskId(String centralControlTaskId, Date completionTime) {
        Optional<TMpdMaterialSwitchRecord> recordOpt = materialSwitchRecordRepository.findByCentralControlTaskId(centralControlTaskId);
        if (!recordOpt.isPresent()) {
            log.warn("未找到中控任务ID为{}的物料切换记录", centralControlTaskId);
            return false;
        }

        TMpdMaterialSwitchRecord record = recordOpt.get();
        record.setStatus(MaterialSwitchConstants.STATUS_COMPLETED);
        record.setCompletionTime(completionTime);

        materialSwitchRecordRepository.save(record);
        log.info("物料切换记录完成，中控任务ID：{}，筒仓：{}，切换：{} -> {}", 
                centralControlTaskId, record.getSiloNo(), record.getBeforeMaterialType(), record.getAfterMaterialType());

        // 处理库存更新
        processInventoryUpdate(record.getSiloNo(), record.getBeforeMaterialCode(), record.getAfterMaterialCode());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean failMaterialSwitchRecord(String taskNo, String failureReason) {
        Optional<TMpdMaterialSwitchRecord> recordOpt = materialSwitchRecordRepository.findByTaskNo(taskNo);
        if (!recordOpt.isPresent()) {
            log.warn("未找到任务号为{}的物料切换记录", taskNo);
            return false;
        }

        TMpdMaterialSwitchRecord record = recordOpt.get();
        record.setStatus(MaterialSwitchConstants.STATUS_FAILED);
        record.setRemark(record.getRemark() == null ? failureReason : record.getRemark() + "; " + failureReason);

        materialSwitchRecordRepository.save(record);
        log.info("物料切换记录标记为失败，任务号：{}，失败原因：{}", taskNo, failureReason);

        return true;
    }

    @Override
    public Page<MaterialSwitchRecordDTO> queryMaterialSwitchRecords(MaterialSwitchRecordQueryDTO queryDTO) {
        return PageHelperUtil.getPage(materialSwitchRecordMapper::queryMaterialSwitchRecords, queryDTO, MaterialSwitchRecordDTO.class);
    }

    @Override
    public MaterialSwitchRecordDTO getMaterialSwitchRecordById(Integer id) {
        Optional<TMpdMaterialSwitchRecord> recordOpt = materialSwitchRecordRepository.findById(id);
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }
        return convertToDTO(recordOpt.get());
    }

    @Override
    public MaterialSwitchRecordDTO getMaterialSwitchRecordByTaskNo(String taskNo) {
        MaterialSwitchRecordDTO dto = materialSwitchRecordMapper.getMaterialSwitchRecordByTaskNo(taskNo);
        if (dto == null) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }
        return dto;
    }

    @Override
    public MaterialSwitchRecordDTO getMaterialSwitchRecordByTaskId(String centralControlTaskId) {
        MaterialSwitchRecordDTO dto = materialSwitchRecordMapper.getMaterialSwitchRecordByTaskId(centralControlTaskId);
        if (dto == null) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }
        return dto;
    }

    @Override
    public MaterialSwitchRecordDTO getMaterialSwitchRecordByUniqueId(String uniqueId) {
        MaterialSwitchRecordDTO dto = materialSwitchRecordMapper.getMaterialSwitchRecordByUniqueId(uniqueId);
        if (dto == null) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }
        return dto;
    }

    @Override
    public List<MaterialSwitchRecordDTO> getInProgressRecords() {
        return materialSwitchRecordMapper.getInProgressRecords();
    }

    @Override
    public MaterialSwitchRecordDTO getLatestMaterialSwitchRecordBySilo(String siloNo) {
        return materialSwitchRecordMapper.getLatestMaterialSwitchRecordBySilo(siloNo);
    }

    @Override
    public List<MaterialSwitchRecordDTO> getMaterialSwitchRecordsBySiloAndStatus(String siloNo, Integer status) {
        return materialSwitchRecordMapper.getMaterialSwitchRecordsBySiloAndStatus(siloNo, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteMaterialSwitchRecord(Integer id) {
        if (!materialSwitchRecordRepository.existsById(id)) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }
        materialSwitchRecordRepository.deleteById(id);
        log.info("删除物料切换记录成功，ID：{}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMaterialSwitchRecord(MaterialSwitchRecordDTO dto) {
        if (dto.getId() == null) {
            throw new IllegalArgumentException("记录ID不能为空");
        }
        
        Optional<TMpdMaterialSwitchRecord> recordOpt = materialSwitchRecordRepository.findById(Integer.valueOf(dto.getId()));
        if (!recordOpt.isPresent()) {
            throw new IllegalArgumentException("物料切换记录不存在");
        }

        TMpdMaterialSwitchRecord record = recordOpt.get();
        updateRecordFields(record, dto);
        
        materialSwitchRecordRepository.save(record);
        log.info("更新物料切换记录成功，ID：{}", dto.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processMaterialSwitch(String siloNo, String beforeMaterialType, String afterMaterialType) {
        log.info("处理物料切换，筒仓：{}，切换：{} -> {}", siloNo, beforeMaterialType, afterMaterialType);

        try {
            // 这里可以调用其他服务处理物料切换逻辑
            // 例如：更新筒仓配置、通知相关系统等

            // 查询该筒仓的相关物料切换记录
            List<MaterialSwitchRecordDTO> records = materialSwitchRecordMapper.getMaterialSwitchRecordsBySiloAndStatus(siloNo, MaterialSwitchConstants.STATUS_IN_PROGRESS);

            log.info("物料切换处理完成，筒仓：{}，影响记录数：{}", siloNo, records.size());
            return true;
        } catch (Exception e) {
            log.error("物料切换处理失败，筒仓：{}，错误：{}", siloNo, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processInventoryUpdate(String siloNo, String beforeMaterialCode, String afterMaterialCode) {
        log.info("处理库存更新，筒仓：{}，物料切换：{} -> {}", siloNo, beforeMaterialCode, afterMaterialCode);

        try {
            // 这里可以调用库存服务进行更新
            // 例如：更新库存表、生成库存变更记录等

            // 获取最新的物料切换记录
            MaterialSwitchRecordDTO latestRecord = materialSwitchRecordMapper.getLatestMaterialSwitchRecordBySilo(siloNo);

            if (latestRecord != null) {
                log.info("库存更新处理完成，筒仓：{}，最新切换记录：{}", siloNo, latestRecord.getTaskNo());
            }

            return true;
        } catch (Exception e) {
            log.error("库存更新处理失败，筒仓：{}，错误：{}", siloNo, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long countByStatus(Integer status) {
        return materialSwitchRecordMapper.countByStatus(status);
    }

    @Override
    public Long countBySiloNo(String siloNo) {
        return materialSwitchRecordMapper.countBySiloNo(siloNo);
    }

    @Override
    public Boolean existsByTaskNo(String taskNo) {
        return materialSwitchRecordMapper.existsByTaskNo(taskNo) > 0;
    }

    @Override
    public Boolean existsByUniqueId(String uniqueId) {
        return materialSwitchRecordMapper.existsByUniqueId(uniqueId) > 0;
    }

    /**
     * 转换实体为DTO
     * @param record 物料切换记录实体
     * @return 物料切换记录DTO
     */
    private MaterialSwitchRecordDTO convertToDTO(TMpdMaterialSwitchRecord record) {
        MaterialSwitchRecordDTO dto = new MaterialSwitchRecordDTO();
        BeanUtils.copyProperties(record, dto);

        // 设置状态描述
        if (record.getStatus() != null) {
            dto.setStatusDesc(MaterialSwitchConstants.getStatusDesc(record.getStatus()));
        }

        return dto;
    }
}
