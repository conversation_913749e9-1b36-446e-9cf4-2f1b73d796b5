package com.hvisions.rawmaterial.service.materialsilo;

import com.hvisions.rawmaterial.dto.silo.*;
import com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO;
import com.hvisions.rawmaterial.entity.materialsilo.RlPermission;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @Description: 物料筒仓配置服务接口
 * @author: z19235
 * @time: 2025/01/01
 */
public interface MaterialSiloConfigService {

    /**
     * 获取物料筒仓树形结构
     * @param materialType 物料类型：0-高粱，1-稻壳，2-小麦
     * @return 树形结构列表
     */
    List<MaterialSiloTreeDTO> getMaterialSiloTree(Integer materialType);

    /**
     * 分页查询物料筒仓列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<MaterialSiloTreeDTO> getMaterialSiloPageList(MaterialSiloQueryPageDTO queryDTO);

    /**
     * 添加物料筒仓
     * @param configDTO 筒仓配置信息
     * @return 影响行数
     */
    Integer addMaterialSilo(MaterialSiloConfigDTO configDTO);

    /**
     * 修改物料筒仓
     * @param configDTO 筒仓配置信息
     * @return 影响行数
     */
    Integer updateMaterialSilo(MaterialSiloConfigDTO configDTO);

    /**
     * 删除物料筒仓
     * @param id 筒仓ID
     * @return 影响行数
     */
    Integer deleteMaterialSilo(Integer id);

    /**
     * 根据ID获取筒仓详情
     * @param id 筒仓ID
     * @return 筒仓详情
     */
    MaterialSiloConfigDTO getMaterialSiloById(Integer id);

    /**
     * 根据code获取筒仓详情
     * @param code 筒仓code
     * @return 筒仓详情
     */
    MaterialSiloConfigDTO getMaterialSiloByCode(String code);

    /**
     * 根据code和类型获取筒仓详情
     * @param code 筒仓code
     * @param materialType 筒仓类型
     * @return 筒仓详情
     */
    MaterialSiloConfigDTO getMaterialSiloByCodeAndType(String code, Integer materialType);

    /**
     * 保存筒仓数据权限配置
     * @param permissionDTO 权限配置信息
     * @return 影响行数
     */
    Integer saveMaterialSiloPermissions(MaterialSiloPermissionDTO permissionDTO);

    /**
     * 根据筒仓ID获取权限配置
     * @param rlId 筒仓ID
     * @return 权限配置列表
     */
    List<RlPermission> getPermissionsByRlId(Integer rlId);

    /**
     * 获取部门树形结构
     * @return 部门树形结构
     */
    List<DepartmentTreeDTO> getDepartmentTree();

    /**
     * 根据筒仓ID获取已授权的部门列表
     * @param rlId 筒仓ID
     * @return 部门ID列表
     */
    List<Integer> getAuthorizedDeptIds(Integer rlId);

    /**
     * 查询所有物料类型第三级最后一层的数据
     * @return 所有物料类型第三级最后一层的筒仓列表
     */
    List<MaterialSiloTreeDTO> getAllMaterialTypeLeafNodes();

    /**
     * 查询所有物料类型第三级最后一层数据
     * @return 所有物料类型第三级最后一层的筒仓列表
     */
    List<MaterialSiloTreeDTO> getAllMaterialByCode(String  code);

    /**
     * 查询所有物料类型一层数据
     * @return
     */
    List<MaterialSiloConfigDTO> getSiloConfigFistList();

    /**
     * 查询所有物料类型第三层数据
     * @param queryDTO
     * @return
     */
    List<MaterialSiloConfigDTO> getLocationList(MaterialSiloQueryDTO queryDTO);
}
