package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.detail.SorghumProductionDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱生产工单
 * @date 2022/4/22 10:18
 */
public interface SorghumProductionOrderService {

    Page<SorghumProductionOrderPageDTO> getSorghumProductionOrderPageList(SorghumProductionOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);


    Page<SorghumProductionDetailListDTO> getSorghumProductionOrderDetailPageList(SorghumProductionOrderPageQueryDTO queryDTO);

    /**
     * 创建高粱粉碎任务信息（一期对接）
     * @param taskDTO
     * @return
     */
    String createSorghumCrushingTask(UnifiedTaskDTO taskDTO);
}
