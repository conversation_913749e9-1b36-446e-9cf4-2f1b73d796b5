package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.SorghumAndBranBatchPostDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.MaterialDispenseDetailQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.detail.SorghumDispenseDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.dispense.order.SorghumDispenseOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱分发工单
 * @date 2022/4/22 10:18
 */
public interface SorghumDispenseOrderService {

    Page<SorghumDispenseOrderPageDTO> getSorghumDispenseOrderPageList(SorghumDispenseOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);

    Integer writeOff(List<String> matDocs);

    Integer posting(SorghumAndBranBatchPostDTO postDTO);

    /***
     * @Description 高粱稻壳发放详情（批次追溯）
     *
     * <AUTHOR>
     * @Date 2022-11-7 15:55
     * @param queryDTO
     * @return com.hvisions.purchase.dto.production.sorghum.dispense.detail.MaterialDispenseDetailDTO
     **/
    List<MaterialDispenseDetailDTO> getMaterialDispenseDetail(MaterialDispenseDetailQueryDTO queryDTO);


    Integer updateSorghumDispense(UpdateFlourTransferDTO updateFlourTransferDTO);

    Integer revoke(List<Integer> ids);

    Page<SorghumDispenseDetailListDTO> getSorghumDispenseOrderDetailPageList(SorghumDispenseOrderPageQueryDTO queryDTO);

    /**
     * 创建高粱粉发放任务
     * @param taskDTO
     * @return
     */
    String createSorghumPowderDistribution(UnifiedTaskDTO taskDTO);
}
