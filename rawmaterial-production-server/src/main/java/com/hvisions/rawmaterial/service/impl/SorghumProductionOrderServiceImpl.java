package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.SorghumProductionOrderMapper;
import com.hvisions.rawmaterial.dao.SpOrderDetailMapper;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.detail.SorghumProductionDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.production.order.SorghumProductionOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumProductionOrder;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSpOrderDetail;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.service.SorghumProductionOrderService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.covertStorage;
import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.parseStatusStr;

/**
 * <AUTHOR>
 * @description:高粱生产工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumProductionOrderServiceImpl implements SorghumProductionOrderService {

    @Resource
    private SorghumProductionOrderMapper sorghumProductionOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private SpOrderDetailMapper orderDetailMapper;

    @Resource
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Override
    public Page<SorghumProductionOrderPageDTO> getSorghumProductionOrderPageList(SorghumProductionOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumProductionOrderMapper::getSorghumProductionOrderPageList, queryDTO, SorghumProductionOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumProductionOrder order = sorghumProductionOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdSpOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdSpOrderDetail.class);
            for (TMpdSpOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");

                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += orderDetailMapper.insert(orderDetail);
                count = count.add(orderDetail.getActualQuantity());


            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumProductionOrderMapper.updateById(order);
        }
        return res;
    }

    @Override
    public Page<SorghumProductionDetailListDTO> getSorghumProductionOrderDetailPageList(SorghumProductionOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumProductionOrderMapper::getSorghumProductionOrderDetailPageList, queryDTO, SorghumProductionDetailListDTO.class);
    }

    @Override
    @Transactional
    public String createSorghumCrushingTask(UnifiedTaskDTO taskDTO) {
        log.info("高粱粉碎任务信息（一期对接）:{}", taskDTO);
        try {
            if (!isRecordExists(taskDTO)) {
                processSorghumCrushingRecord(taskDTO);
                // 5. 记录日志详情
                log.info("高粱转粮任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情 - 记录到数据库
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }
            return String.format("高粱粉碎任务信息(一期对接)同步成功，taskNo:%s", taskDTO.getTaskNo());
        }catch (Exception e){
            log.error("高粱粉碎任务信息(一期对接)异常:{}", e);
            throw new RuntimeException("高粱粉碎任务信息(一期对接)异常："+ e);
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdSpOrderDetail existing = orderDetailMapper
                .selectByMaterialCodeAndTaskNoAndTime(
                        record.getMaterialCode(),
                        record.getTaskNo(),
                        record.getStartTime()
                );
        return existing != null;
    }

    /**
     * 处理转粮记录
     */
    private void processSorghumCrushingRecord(UnifiedTaskDTO taskDTO) {
        // 1. 根据工单日期查询或创建主表记录
        Date recordDate = taskDTO.getStartTime() != null ? taskDTO.getStartTime() : new Date();

        TMpdSorghumProductionOrder existingOrder = sorghumProductionOrderMapper.selectByDateAndMaterialCode(recordDate, taskDTO.getMaterialCode());

        if (existingOrder == null) {
            // 2. 主表不存在则新增
            existingOrder = createNewSorghumCrushingOrder(recordDate, taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            //syncOperationLogService.logCreateMainTask(taskDTO, existingOrder.getOrderNo(), Integer.valueOf(existingOrder.getId()));

            // 创建任务详情
            TMpdSpOrderDetail detail = createSorghumCrushingDetail(existingOrder.getId(), taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            //syncOperationLogService.logCreateDetail(taskDTO, Integer.valueOf(existingOrder.getId()), Integer.valueOf(detail.getId()));

            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingOrder.getOrderNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录
            TMpdSpOrderDetail existingDetail = orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                    taskDTO.getMaterialCode(), taskDTO.getTaskNo(), taskDTO.getStartTime());

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdSpOrderDetail detail = createSorghumCrushingDetail(existingOrder.getId(), taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                //syncOperationLogService.logCreateDetail(taskDTO, Integer.valueOf(existingOrder.getId()), Integer.valueOf(detail.getId()));
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingOrder.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateSorghumCrushingDetail(existingDetail, taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                //syncOperationLogService.logUpdateDetail(taskDTO, Integer.valueOf(existingDetail.getId()));
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表汇总信息
            updateSorghumCrushingSummary(existingOrder);
            // 5. 记录日志详情 - 记录到数据库
            //syncOperationLogService.logUpdateMainSummary(taskDTO, Integer.valueOf(existingOrder.getId()), existingOrder.getActualQuantity());
        }
    }

    private TMpdSorghumProductionOrder createNewSorghumCrushingOrder(Date recordDate, UnifiedTaskDTO taskDTO) {
        TMpdSorghumProductionOrder order = new TMpdSorghumProductionOrder();
        order.setOrderDate(recordDate);
        order.setMaterialCode(taskDTO.getMaterialCode());
        order.setMaterialName(taskDTO.getMaterialName());
        order.setUnit(taskDTO.getUnit());
        order.setActualQuantity(taskDTO.getActualWeight());
        order.setInventoryQuantity(taskDTO.getActualWeight());

        sorghumProductionOrderMapper.insert( order);
        return order;
    }

    private TMpdSpOrderDetail createSorghumCrushingDetail(Integer orderId, UnifiedTaskDTO taskDTO) {
        TMpdSpOrderDetail detail = new TMpdSpOrderDetail();
        detail.setOrderId(orderId);

        TMpdMateriaSiloDO sendSilo = findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()));
        detail.setSendSiloId(sendSilo.getId());

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()));
        detail.setAcceptSiloId(silo.getId());

        detail.setMaterialCode(taskDTO.getMaterialCode());
        detail.setMaterialName(taskDTO.getMaterialName());
        detail.setUnit(taskDTO.getUnit());
        detail.setActualQuantity(taskDTO.getActualWeight());
        detail.setState(parseStatusStr(taskDTO.getStatus()));
        detail.setActualBeginTime(taskDTO.getStartTime());
        detail.setActualEndTime(taskDTO.getEndTime());
        detail.setControlTaskNo(taskDTO.getTaskNo());
        detail.setLogId(taskDTO.getExtField1());

        orderDetailMapper.insert(detail);
        return detail;
    }

    /**
     * 更新转粮工单详情记录
     */
    private void updateSorghumCrushingDetail(TMpdSpOrderDetail existingDetail, UnifiedTaskDTO taskDTO) {
        TMpdMateriaSiloDO sendSilo = findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()));
        existingDetail.setSendSiloId(sendSilo.getId());

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()));
        existingDetail.setAcceptSiloId(silo.getId());

        existingDetail.setMaterialCode(taskDTO.getMaterialCode());
        existingDetail.setMaterialName(taskDTO.getMaterialName());
        existingDetail.setUnit(taskDTO.getUnit());
        existingDetail.setActualQuantity(taskDTO.getActualWeight());
        existingDetail.setActualBeginTime(taskDTO.getStartTime());
        existingDetail.setActualEndTime(taskDTO.getEndTime());
        existingDetail.setState(parseStatusStr(taskDTO.getStatus()));
        existingDetail.setUpdateTime(new Date());
        existingDetail.setControlTaskNo(taskDTO.getTaskNo());
        existingDetail.setLogId(taskDTO.getExtField1());

        orderDetailMapper.updateById(existingDetail);
    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private TMpdMateriaSiloDO findSiloCodeByTubCode(String siloCode) {
        TMpdMateriaSiloDO silo = materialSiloConfigMapper.findSiloBySiloCode(siloCode);
        if (silo == null){
            throw new RuntimeException("未找到筒仓信息");
        }
        return silo;
    }

    /**
     * 更新转粮工单汇总信息
     */
    private void updateSorghumCrushingSummary(TMpdSorghumProductionOrder order) {
        // 查询该主表下所有详情记录的汇总数据
        BigDecimal summaryQuantity = orderDetailMapper.selectSummaryByOrderId(order.getId());

        order.setActualQuantity(summaryQuantity != null ? summaryQuantity : BigDecimal.ZERO);
        order.setUpdateTime(new Date());

        sorghumProductionOrderMapper.updateById(order);
        log.info("根据详情汇总更新主表成功，工单ID：{}，数量：{}", order.getId(), summaryQuantity);
    }
}
