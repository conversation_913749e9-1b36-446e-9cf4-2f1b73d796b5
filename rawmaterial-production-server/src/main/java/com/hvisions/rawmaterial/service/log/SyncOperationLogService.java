package com.hvisions.rawmaterial.service.log;

import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.log.TMpdSyncOperationLog;

import java.util.Date;
import java.util.List;

/**
 * 同步操作日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface SyncOperationLogService {

    /**
     * 记录新增主表日志
     *
     * @param taskDTO 任务DTO
     * @param orderNo 工单号
     * @param mainTaskId 主表ID
     */
    void logCreateMainTask(UnifiedTaskDTO taskDTO, String orderNo, Integer mainTaskId);

    /**
     * 记录新增详情日志
     *
     * @param taskDTO 任务DTO
     * @param mainTaskId 主表ID
     * @param detailId 详情ID
     */
    void logCreateDetail(UnifiedTaskDTO taskDTO, Integer mainTaskId, Integer detailId);

    /**
     * 记录更新详情日志
     *
     * @param taskDTO 任务DTO
     * @param detailId 详情ID
     */
    void logUpdateDetail(UnifiedTaskDTO taskDTO, Integer detailId);

    /**
     * 记录更新主表汇总日志
     *
     * @param taskDTO 任务DTO
     * @param mainTaskId 主表ID
     * @param totalQuantity 累计数量
     */
    void logUpdateMainSummary(UnifiedTaskDTO taskDTO, Integer mainTaskId, java.math.BigDecimal totalQuantity);

    /**
     * 记录跳过重复记录日志
     *
     * @param taskDTO 任务DTO
     */
    void logSkipDuplicate(UnifiedTaskDTO taskDTO);

    /**
     * 记录错误日志
     *
     * @param taskDTO 任务DTO
     * @param errorMessage 错误信息
     */
    void logError(UnifiedTaskDTO taskDTO, String errorMessage);

    /**
     * 根据任务号查询日志
     *
     * @param taskNo 任务号
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> findByTaskNo(String taskNo);

    /**
     * 根据工单号查询日志
     *
     * @param orderNo 工单号
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> findByOrderNo(String orderNo);

    /**
     * 根据条件查询日志
     *
     * @param moduleName 模块名称
     * @param businessType 业务类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志列表
     */
    List<TMpdSyncOperationLog> findByCondition(String moduleName, Integer businessType, Date startTime, Date endTime);
}
