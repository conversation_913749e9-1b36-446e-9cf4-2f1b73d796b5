package com.hvisions.rawmaterial.service.unified.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import com.hvisions.rawmaterial.dao.unified.UnifiedInterfaceMapper;
import com.hvisions.rawmaterial.dto.ClearanceRecordDTO;
import com.hvisions.rawmaterial.dto.VentilationRecordDTO;
import com.hvisions.rawmaterial.dto.materialswitch.MaterialSwitchRecordDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO;
import com.hvisions.rawmaterial.dto.silotransfer.SiloTransferRecordDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedMaterialDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.enums.LogCaptureEnum;
import com.hvisions.rawmaterial.enums.MaterialTypeEnum;
import com.hvisions.rawmaterial.enums.unified.BusinessSystemEnum;
import com.hvisions.rawmaterial.enums.unified.TaskTypeEnum;
import com.hvisions.rawmaterial.service.*;
import com.hvisions.rawmaterial.service.materialsilo.MaterialSiloConfigService;
import com.hvisions.rawmaterial.service.ricehusk.RiceHuskInboundTaskService;
import com.hvisions.rawmaterial.service.silo.SiloRealtimeStatusService;
import com.hvisions.rawmaterial.service.sorghum.SorghumInboundTaskService;
import com.hvisions.rawmaterial.service.sorghum.SorghumTransferOrderService;
import com.hvisions.rawmaterial.service.unified.UnifiedInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 统一接口服务实现类
 * 根据TaskTypeEnum的不同类型实现相应的业务逻辑
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@Service
public class UnifiedInterfaceServiceImpl implements UnifiedInterfaceService {


    @Resource
    private UnifiedInterfaceMapper unifiedInterfaceMapper;

    @Resource
    private LogCaptureClient logCaptureClient;

    @Resource
    private SorghumInboundTaskService sorghumInboundTaskService;

    @Resource
    private RiceHuskInboundTaskService riceHuskInboundTaskService;

    @Resource
    private SiloRealtimeStatusService siloRealtimeStatusService;

    @Resource
    private ClearanceRecordService clearanceRecordService;

    @Resource
    private MaterialSwitchRecordService materialSwitchRecordService;

    @Resource
    private SiloTransferRecordService siloTransferRecordService;

    @Resource
    private VentilationRecordService ventilationRecordService;

    @Resource
    private SorghumTransferOrderService sorghumTransferOrderService;

    @Resource
    private MaterialSiloConfigService materialSiloConfigService;

    @Resource
    private SorghumShipmentOrderService sorghumShipmentOrderService;

    // ==================== 任务相关方法实现 ====================

    /**
     * 创建任务
     * 根据任务类型枚举执行不同的业务逻辑
     *
     * @param taskDTO 任务DTO
     * @param request HTTP请求
     * @return 创建后的任务DTO
     */
    @Override
    public String createTask(UnifiedTaskDTO taskDTO, HttpServletRequest request) {
        log.info("开始创建任务：业务系统:{}, 任务类型:{}, 任务参数:{}",
                taskDTO.getBusinessSystem(), taskDTO.getTaskType(), JSONObject.toJSONString(taskDTO));

        // 验证业务系统枚举
        BusinessSystemEnum businessSystemEnum = BusinessSystemEnum.getByCode(taskDTO.getBusinessSystem());
        if (businessSystemEnum == null) {
            throw new IllegalArgumentException("不支持的业务系统：" + taskDTO.getBusinessSystem());
        }

        // 获取任务类型枚举
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getByCode(taskDTO.getTaskType());
        if (taskTypeEnum == null) {
            throw new IllegalArgumentException("不支持的任务类型：" + taskDTO.getTaskType());
        }

        //任务号不能为 空
        if (taskDTO.getTaskNo() == null || taskDTO.getTaskNo().isEmpty()) {
            throw new IllegalArgumentException("任务号不能为空");
        }

        // 设置创建时间
        taskDTO.setCreateTime(new Date());

        //物料处理 eigenvalue不确定先传1如果没查到再传null
        UnifiedMaterialDTO material = unifiedInterfaceMapper.queryMaterialInfo(taskDTO.getMaterialCode(),"1");
        if (material == null){
            material = unifiedInterfaceMapper.queryMaterialInfo(taskDTO.getMaterialCode(),null);
        }

        if (material == null){
            throw new IllegalArgumentException("物料编码不存在：" + taskDTO.getMaterialCode() +",需联系MES添加对应的物料信息");
        }

        taskDTO.setMaterialName(material.getMaterialName());
        taskDTO.setUnit(material.getUomCode());

        // 根据任务类型执行相应的业务逻辑
        switch (taskTypeEnum) {
            case INBOUND://入仓==中控系统二期筒仓收货记录推送给MES
                return handleInbound(taskDTO);
            case SILO_REALTIME_STATUS://筒仓实时状态信息(含一期、3*17、1*7、225仓)
                return handleSiloRealtimeStatus(taskDTO);
            case SILO_CLEARANCE://筒仓清仓记录
                return handleSiloClearance(taskDTO);
            case SILO_MATERIAL_SWITCH://筒仓物料切换记录
                return handleMaterialSwitch(taskDTO);
            case SILO_TRANSFER://倒仓任务信息
                return handleSiloTransfer(taskDTO);
            case VENTILATION://通风记录
                return handleVentilation(taskDTO);
            case SORGHUM_TRANSFER://高粱二期转一期的转运任务信息
                return handleSorghumTransfer(taskDTO);
            case SORGHUM_DIRECT_INBOUND://高粱入仓任务信息（未经二期筒仓转运）
                return handleSorghumInbound(taskDTO);
            case SORGHUM_GRAIN_TRANSFER://高粱转粮任务信息（一期对接）
                return handleSorghumGrainTransfer(taskDTO);
            case SORGHUM_CRUSHING_TASK://高粱粉碎任务信息（一期对接）
                return handleSorghumCrushingTask(taskDTO);
            case SORGHUM_POWDER_TRANSFER://高粱粉转运任务信息（一期对接）
                return handleSorghumPowderTransfer(taskDTO);
            case SORGHUM_POWDER_DISTRIBUTION://高粱粉分发任务信息（一期对接）
                return handleSorghumPowderDistribution(taskDTO);
            case SORGHUM_QUALITY_CHECK://高粱二期筒仓进出快检数据对接（淀粉、水分、蛋白质）
                return handleSorghumQualityCheck(taskDTO);
            default:
                throw new UnsupportedOperationException("暂不支持的任务类型：" + taskTypeEnum.getDescription());
        }
    }

    private String handleSorghumQualityCheck(UnifiedTaskDTO taskDTO) {
        return "";
    }

    private String handleSorghumPowderDistribution(UnifiedTaskDTO taskDTO) {
        return "";
    }

    private String handleSorghumPowderTransfer(UnifiedTaskDTO taskDTO) {
        return "";
    }

    private String handleSorghumCrushingTask(UnifiedTaskDTO taskDTO) {
        return "";
    }

    /**
     * 处理高粱转粮任务信息（一期对接）
     *
     * @param taskDTO 任务DTO
     * @return 处理结果
     */
    private String handleSorghumGrainTransfer(UnifiedTaskDTO taskDTO) {
        log.info("开始处理高粱转粮任务信息：业务系统:{}, 任务参数:{}",
                taskDTO.getBusinessSystem(), JSONObject.toJSONString(taskDTO));
        Integer logId =writerLog(taskDTO, LogCaptureEnum.SORGHUM_TRANSFER);
        taskDTO.setExtField1(String.valueOf(logId));
        return sorghumShipmentOrderService.createSorghumGrainTransfer(taskDTO);
    }

    private String handleSorghumInbound(UnifiedTaskDTO taskDTO) {
        return "";
    }

    private Integer writerLog(UnifiedTaskDTO syncDTO, LogCaptureEnum logCaptureEnum) {
        LogDto logDto = new LogDto();
        logDto.setLogType(1);
        logDto.setLogParameter(JSONObject.toJSONString(syncDTO));

        logDto.setControllerName(logCaptureEnum.getControllerName());
        logDto.setLogCaptureTime(new Date());
        logDto.setLogModular(logCaptureEnum.getLogModular());
        logDto.setLogInvocation(syncDTO.getBusinessSystem());
        logDto.setLocation(logCaptureEnum.getLocation());
        logDto.setMethodName(logCaptureEnum.getMethodName());
        ResultVO<Integer> resultVO = logCaptureClient.logRecord(logDto);
        if (!resultVO.isSuccess()) {
            log.error("日志记录失败：{}", resultVO.getMessage());
            return null;
        }
        log.info("日志记录成功：{}", resultVO.getData());
        return resultVO.getData();
    }

    /**
     * 处理高粱转一期任务
     */
    private String handleSorghumTransfer(UnifiedTaskDTO taskDTO) {
        log.info("处理高粱转一期任务接口：任务号={}", taskDTO.getTaskNo());
        Integer logId =writerLog(taskDTO, LogCaptureEnum.SORGHUM_TRANSFER);
        taskDTO.setExtField1(String.valueOf(logId));
        return sorghumTransferOrderService.createSorghumTransfer(taskDTO);
    }

    /**
     * 处理通风任务
     */
    private String handleVentilation(UnifiedTaskDTO taskDTO) {
        log.info("处理通风任务接口：任务号={}", taskDTO.getTaskNo());
        Integer logId =writerLog(taskDTO, LogCaptureEnum.VENTILATION_RECORD_SYNC_CENTRAL_CONTROL);

        VentilationRecordDTO recordDTO = new VentilationRecordDTO() ;
        recordDTO.setMaterialName(taskDTO.getMaterialName());
        recordDTO.setMaterialCode(taskDTO.getMaterialCode());

        MaterialSiloConfigDTO materialSiloConfigDTO = materialSiloConfigService.getMaterialSiloByCode(covertStorage(taskDTO.getSiloNo()));
        if (materialSiloConfigDTO == null){
            throw new IllegalArgumentException("未找到对应的筒仓配置信息");
        }
        recordDTO.setSiloId(materialSiloConfigDTO.getId());
        recordDTO.setSiloName(materialSiloConfigDTO.getName());
        recordDTO.setSiloCode(materialSiloConfigDTO.getCode());
        recordDTO.setVentilationDuration(taskDTO.getVentilationDuration());
        recordDTO.setStartTime(taskDTO.getStartTime());
        recordDTO.setEndTime(taskDTO.getEndTime());
        recordDTO.setStatus(parseStatus(taskDTO.getStatus()));
        recordDTO.setRemark(taskDTO.getRemark());
        //recordDTO.setRequestParams(JSON.toJSONString(taskDTO));
        recordDTO.setCentralControlTaskId(taskDTO.getTaskNo());
        recordDTO.setLogId(String.valueOf(logId));
        return ventilationRecordService.createVentilationRecord(recordDTO);
    }

    /**
     * 处理倒仓任务
     */
    private String handleSiloTransfer(UnifiedTaskDTO taskDTO) {
        log.info("处理倒仓任务接口：任务号={}", taskDTO.getTaskNo());

        // 验证必要字段
        validateSiloTransferFields(taskDTO);

        // 创建倒仓任务记录DTO
        SiloTransferRecordDTO siloTransferRecordDTO = new SiloTransferRecordDTO();
        siloTransferRecordDTO.setBusinessSystem(taskDTO.getBusinessSystem());
        siloTransferRecordDTO.setMaterialType(taskDTO.getMaterialType());
        siloTransferRecordDTO.setTaskType(taskDTO.getTaskType());
        siloTransferRecordDTO.setDataType(taskDTO.getDataType());
        siloTransferRecordDTO.setTaskNo(taskDTO.getTaskNo());
        siloTransferRecordDTO.setSourceSiloNo(taskDTO.getSourceSiloNo());
        siloTransferRecordDTO.setTargetSiloNo(taskDTO.getTargetSiloNo());
        siloTransferRecordDTO.setMaterialCode(taskDTO.getMaterialCode());
        siloTransferRecordDTO.setActualWeight(taskDTO.getActualWeight());
        siloTransferRecordDTO.setStartTime(taskDTO.getStartTime());
        siloTransferRecordDTO.setEndTime(taskDTO.getEndTime());
        siloTransferRecordDTO.setStatus(parseStatus(taskDTO.getStatus()));
        siloTransferRecordDTO.setCentralControlTaskId(taskDTO.getTaskNo()); // 使用任务号作为中控任务ID
        siloTransferRecordDTO.setDataSource("CENTRAL_CONTROL");
        siloTransferRecordDTO.setUniqueId(taskDTO.getUniqueId());
        siloTransferRecordDTO.setRemark(JSON.toJSONString(taskDTO));

        // 调用倒仓任务记录服务
        return siloTransferRecordService.createSiloTransferRecord(siloTransferRecordDTO);
    }

    /**
     * 处理筒仓清仓任务
     */
    private String handleSiloClearance(UnifiedTaskDTO taskDTO) {
        log.info("处理筒仓清仓接口：任务号={}", taskDTO.getTaskNo());
        ClearanceRecordDTO clearanceRecordDTO = new ClearanceRecordDTO() ;
        clearanceRecordDTO.setCentralControlTaskId(taskDTO.getTaskNo());
        clearanceRecordDTO.setQuantityBefore(taskDTO.getBeforeClearanceWeight());
        clearanceRecordDTO.setMaterialCode(taskDTO.getMaterialCode());
        clearanceRecordDTO.setSiloCode(taskDTO.getSiloNo());
        clearanceRecordDTO.setExecutorName(JSON.toJSONString(taskDTO));
        return clearanceRecordService.createClearanceRecord(clearanceRecordDTO);
    }

    /**
     * 处理筒仓物料切换任务
     */
    private String handleMaterialSwitch(UnifiedTaskDTO taskDTO) {
        log.info("处理筒仓物料切换接口：任务号={}", taskDTO.getTaskNo());

        // 验证必要字段
        validateMaterialSwitchFields(taskDTO);

        // 创建物料切换记录DTO
        MaterialSwitchRecordDTO materialSwitchRecordDTO = new MaterialSwitchRecordDTO();
        materialSwitchRecordDTO.setBusinessSystem(taskDTO.getBusinessSystem());
        materialSwitchRecordDTO.setMaterialType(taskDTO.getMaterialType());
        materialSwitchRecordDTO.setTaskType(taskDTO.getTaskType());
        materialSwitchRecordDTO.setDataType(taskDTO.getDataType());
        materialSwitchRecordDTO.setTaskNo(taskDTO.getTaskNo());
        materialSwitchRecordDTO.setSiloNo(taskDTO.getSiloNo());
        materialSwitchRecordDTO.setBeforeMaterialType(taskDTO.getBeforeMaterialType());
        materialSwitchRecordDTO.setAfterMaterialType(taskDTO.getAfterMaterialType());
        materialSwitchRecordDTO.setCentralControlTaskId(taskDTO.getTaskNo()); // 使用任务号作为中控任务ID
        materialSwitchRecordDTO.setSwitchTime(new Date());
        materialSwitchRecordDTO.setDataSource("CENTRAL_CONTROL");
        materialSwitchRecordDTO.setUniqueId(taskDTO.getUniqueId());
        materialSwitchRecordDTO.setRemark(JSON.toJSONString(taskDTO));

        // 调用物料切换记录服务
        return materialSwitchRecordService.createMaterialSwitchRecord(materialSwitchRecordDTO);
    }

    /**
     * 验证物料切换字段
     */
    private void validateMaterialSwitchFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getTaskNo() == null || taskDTO.getTaskNo().trim().isEmpty()) {
            throw new IllegalArgumentException("任务号不能为空");
        }
        if (taskDTO.getBusinessSystem() == null || taskDTO.getBusinessSystem().trim().isEmpty()) {
            throw new IllegalArgumentException("业务系统不能为空");
        }
        if (taskDTO.getTaskType() == null || taskDTO.getTaskType().trim().isEmpty()) {
            throw new IllegalArgumentException("任务类型不能为空");
        }
        // 筒仓号可以为空，因为有些切换可能是全局的
        // 物料类型可以为空，因为可能是通用切换
        // 更换前后物料类型可以为空，因为可能只是记录切换事件
    }

    /**
     * 验证倒仓任务字段
     */
    private void validateSiloTransferFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getTaskNo() == null || taskDTO.getTaskNo().trim().isEmpty()) {
            throw new IllegalArgumentException("任务号不能为空");
        }
        if (taskDTO.getBusinessSystem() == null || taskDTO.getBusinessSystem().trim().isEmpty()) {
            throw new IllegalArgumentException("业务系统不能为空");
        }
        if (taskDTO.getTaskType() == null || taskDTO.getTaskType().trim().isEmpty()) {
            throw new IllegalArgumentException("任务类型不能为空");
        }
        // 源筒仓和目标筒仓可以为空，因为有些倒仓可能是批量操作
        // 物料编码可以为空，因为可能是通用倒仓
        // 重量可以为空，因为可能是计划阶段
    }

    /**
     * 解析状态字符串为状态码
     */
    public static Integer parseStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return 1; // 默认为待执行
        }
        switch (status.toUpperCase()) {
            case "PENDING":
            case "待执行":
                return 1;
            case "PROCESSING":
            case "执行中":
                return 2;
            case "COMPLETED":
            case "已完成":
                return 3;
            case "FAILED":
            case "失败":
                return 4;
            case "CANCELLED":
            case "已取消":
                return 5;
            default:
                return 1; // 默认为待执行
        }
    }

    // ==================== 高粱业务任务处理方法 ====================

    /**
     * 处理入仓任务
     */
    private String handleInbound(UnifiedTaskDTO taskDTO) {
        log.info("处理高粱二期筒仓收货接口（入仓）：任务号={}", taskDTO.getTaskNo());
        // 验证必要字段
        validateSorghumInboundFields(taskDTO);

        // 业务逻辑：记录收货信息
        log.info("收货仓号：{}, 收货重量：{}", taskDTO.getTargetSiloNo(), taskDTO.getActualWeight());

        // 调用具体的业务服务
        // 1. 更新筒仓库存
        // 2. 记录入仓日志
        if (taskDTO.getMaterialType().equals(MaterialTypeEnum.SORGHUM)){
            Integer logId =writerLog(taskDTO, LogCaptureEnum.SORGHUM_INBOUND_SYNC_CENTRAL_CONTROL);
            taskDTO.setExtField1(String.valueOf(logId));
            return  sorghumInboundTaskService.syncFromCentralControl(taskDTO);
        }else if (taskDTO.getMaterialType().equals(MaterialTypeEnum.RICE_HUSK)){
            Integer logId =writerLog(taskDTO, LogCaptureEnum.RICE_HUSK_INBOUND_SYNC_CENTRAL_CONTROL);
            taskDTO.setExtField1(String.valueOf(logId));
            return  riceHuskInboundTaskService.syncFromCentralControl(taskDTO);
        }else{
            throw new UnsupportedOperationException("暂不支持的物料类型：" + taskDTO.getMaterialType());
        }
    }


    /**
     * 处理筒仓实时状态信息
     */
    private String handleSiloRealtimeStatus(UnifiedTaskDTO taskDTO) {
        log.info("处理高粱筒仓实时状态接口：任务号={}", taskDTO.getTaskNo());
        // 验证必要字段
        validateSiloRealtimeStatusFields(taskDTO);

        // 业务逻辑：更新筒仓库存
        siloRealtimeStatusService.syncRealtimeDataFromCentralControl(taskDTO);
        return "处理成功";
    }

    private void validateSiloRealtimeStatusFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getSiloNo() == null) {
            throw new IllegalArgumentException("筒仓号不能为空");
        }

        /*if (taskDTO.getInventoryWeight() == null) {
            throw new IllegalArgumentException("库存重量不能为空");
        }*/
    }


    /**
     * 验证高粱入仓字段
     */
    private void validateSorghumInboundFields(UnifiedTaskDTO taskDTO) {
        if (taskDTO.getTargetSiloNo() == null) {
            throw new IllegalArgumentException("收货仓号不能为空");
        }
        if (taskDTO.getTargetWeight() == null) {
            throw new IllegalArgumentException("收货重量不能为空");
        }
    }


    /**
     * 中粮筒仓对应MES筒仓
     *
     * @param blStorage 中粮筒仓
     * return String
     *
     */
    public static String covertStorage(String blStorage) {
        String storageCode = null;

        if (blStorage.contains("A")) { // 一期筒仓
            switch (blStorage) {
                case "A201":
                    storageCode = "GL2-1";
                    break;
                case "A202":
                    storageCode = "GL2-2";
                    break;
                case "A203":
                    storageCode = "GL2-3";
                    break;
                case "A204":
                    storageCode = "GL2-4";
                    break;
                case "A205":
                    storageCode = "GL2-5";
                    break;
                case "A206":
                    storageCode = "GL2-6";
                    break;
            }
        } else if (blStorage.contains("S")) { // 709筒仓
            switch (blStorage) {
                case "S201":
                    storageCode = "DK2-1";
                    break;
                case "S202":
                    storageCode = "DK2-2";
                    break;
                case "S203":
                    storageCode = "DK2-3";
                    break;
                case "S204":
                    storageCode = "DK2-4";
                    break;
                case "S205":
                    storageCode = "DK2-5";
                    break;
                case "S206":
                    storageCode = "DK2-6";
                    break;
                case "S207":
                    storageCode = "DK2-7";
                    break;
                case "S208":
                    storageCode = "DK2-8";
                    break;
                case "S209":
                    storageCode = "DK2-9";
                    break;
                case "S210":
                    storageCode = "DK2-10";
                    break;
                case "S211":
                    storageCode = "DK2-11";
                    break;
                case "S212":
                    storageCode = "DK2-12";
                    break;
            }
        }
        return storageCode;
    }
}
