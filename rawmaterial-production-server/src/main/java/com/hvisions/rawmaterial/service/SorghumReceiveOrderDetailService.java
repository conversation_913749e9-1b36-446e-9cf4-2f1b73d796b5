package com.hvisions.rawmaterial.service;

import com.hvisions.rawmaterial.dto.production.sorghum.receive.SorghumReceiveDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;

import java.util.List;

/**
 * 高粱工单详情接收自控信息接口
 *
 * <AUTHOR>
 * @since 2023-6-5
 */
public interface SorghumReceiveOrderDetailService {

    /**
     * 接收高粱转运工单详情，二期--------一期
     */
    Integer receiveSorghumTransfer(List<SorghumReceiveDTO> sorghumReceiveDTOS);

    /**
     * 接收高粱转粮工单详情，一期----------高粱暂存仓
     */
    Integer receiveSorghumShipment(List<SorghumReceiveDTO> sorghumReceiveDTOS);

    /**
     * 接收高粱粉碎生产工单详情，高粱暂存仓----------高粱粉暂存仓
     */
    Integer receiveSorghumProduction(List<SorghumReceiveDTO> sorghumReceiveDTOS);

    /**
     * 接收高粱粉转运工单详情，高粱粉暂存仓----高粱粉发放仓
     */
    Integer receiveFlourTransfer(List<SorghumReceiveDTO> sorghumReceiveDTOS);

    /**
     * 接收高粱粉发放工单详情，高粱粉发放仓----各个中心车间
     */
    Integer receiveSorghumDispense(List<SorghumReceiveDTO> sorghumReceiveDTOS);


    Integer updateFlourTransfer(UpdateFlourTransferDTO updateFlourTransferDTO);

    Integer updateSorghumDispense(UpdateFlourTransferDTO updateFlourTransferDTO);
}
