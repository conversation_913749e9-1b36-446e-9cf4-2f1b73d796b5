package com.hvisions.rawmaterial.service.silo.impl;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusDTO;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusQueryDTO;
import com.hvisions.rawmaterial.dto.silo.MaterialSiloConfigDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.silo.TMpdSiloRealtimeDO;
import com.hvisions.rawmaterial.dao.realtime.TMpdSiloRealtimeStatusMapper;
import com.hvisions.rawmaterial.service.materialsilo.MaterialSiloConfigService;
import com.hvisions.rawmaterial.service.silo.SiloRealtimeStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 筒仓实时状态信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class SiloRealtimeStatusServiceImpl implements SiloRealtimeStatusService {

    @Autowired
    private TMpdSiloRealtimeStatusMapper siloRealtimeStatusMapper;

    @Resource
    private MaterialSiloConfigService materialSiloConfigService;

    @Override
    public Page<SiloRealtimeStatusDTO> getSiloRealtimeStatusPageList(SiloRealtimeStatusQueryDTO queryDTO) {
        log.info("分页查询筒仓实时状态信息列表，查询条件：{}", queryDTO);
        // 返回分页结果
        return PageHelperUtil.getPage(siloRealtimeStatusMapper::selectSiloRealtimeStatusList, queryDTO, SiloRealtimeStatusDTO.class);
    }

    @Override
    public SiloRealtimeStatusDTO getSiloRealtimeStatusById(Integer id) {
        log.info("根据ID查询筒仓实时状态信息详情，ID：{}", id);
        
        TMpdSiloRealtimeDO entity = siloRealtimeStatusMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        
        SiloRealtimeStatusDTO dto = new SiloRealtimeStatusDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addSiloRealtimeStatus(SiloRealtimeStatusDTO statusDTO) {
        log.info("新增筒仓实时状态信息：{}", statusDTO);
        
        TMpdSiloRealtimeDO entity = new TMpdSiloRealtimeDO();
        BeanUtils.copyProperties(statusDTO, entity);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        return siloRealtimeStatusMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateSiloRealtimeStatus(SiloRealtimeStatusDTO statusDTO) {
        log.info("修改筒仓实时状态信息：{}", statusDTO);
        
        TMpdSiloRealtimeDO entity = new TMpdSiloRealtimeDO();
        BeanUtils.copyProperties(statusDTO, entity);
        entity.setUpdateTime(new Date());
        
        return siloRealtimeStatusMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteSiloRealtimeStatus(Integer id) {
        log.info("删除筒仓实时状态信息，ID：{}", id);
        
        return siloRealtimeStatusMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeleteSiloRealtimeStatus(List<Integer> ids) {
        log.info("批量删除筒仓实时状态信息，IDs：{}", ids);
        
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        
        return siloRealtimeStatusMapper.deleteBatchIds(ids);
    }

    @Override
    public List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByBusinessSystemAndTaskNo(String businessSystem, String taskNo) {
        log.info("根据业务系统和任务号查询筒仓实时状态信息，业务系统：{}，任务号：{}", businessSystem, taskNo);
        
        return siloRealtimeStatusMapper.selectByBusinessSystemAndTaskNo(businessSystem, taskNo);
    }

    @Override
    public SiloRealtimeStatusDTO getLatestSiloRealtimeStatusBySiloNo(String siloNo) {
        log.info("根据筒仓号查询最新的实时状态信息，筒仓号：{}", siloNo);
        
        return siloRealtimeStatusMapper.selectLatestBySiloNo(siloNo);
    }

    @Override
    public List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByMaterialType(String materialType) {
        log.info("根据物料类型查询筒仓实时状态信息，物料类型：{}", materialType);
        
        return siloRealtimeStatusMapper.selectByMaterialType(materialType);
    }

    @Override
    public List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByBusinessSystemAndMaterialType(String businessSystem, String materialType) {
        log.info("根据业务系统和物料类型查询筒仓实时状态信息，业务系统：{}，物料类型：{}", businessSystem, materialType);
        
        return siloRealtimeStatusMapper.selectByBusinessSystemAndMaterialType(businessSystem, materialType);
    }

    @Override
    public List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByDataType(String dataType) {
        log.info("根据数据类型查询筒仓实时状态信息，数据类型：{}", dataType);
        
        return siloRealtimeStatusMapper.selectByDataType(dataType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchSaveSiloRealtimeStatus(List<SiloRealtimeStatusDTO> statusList) {
        log.info("批量保存筒仓实时状态信息，数量：{}", statusList.size());
        
        if (CollectionUtils.isEmpty(statusList)) {
            return 0;
        }
        
        List<TMpdSiloRealtimeDO> entityList = new ArrayList<>();
        Date now = new Date();
        
        for (SiloRealtimeStatusDTO dto : statusList) {
            TMpdSiloRealtimeDO entity = new TMpdSiloRealtimeDO();
            BeanUtils.copyProperties(dto, entity);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entityList.add(entity);
        }
        
        return siloRealtimeStatusMapper.batchInsert(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteSiloRealtimeStatusByTaskNo(String taskNo) {
        log.info("根据任务号删除筒仓实时状态信息，任务号：{}", taskNo);
        
        return siloRealtimeStatusMapper.deleteByTaskNo(taskNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanHistoryData(String siloNo, String beforeTime) {
        log.info("清理历史数据，筒仓号：{}，时间点：{}", siloNo, beforeTime);
        
        return siloRealtimeStatusMapper.deleteHistoryData(siloNo, beforeTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncRealtimeDataFromCentralControl(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步筒仓实时数据");
        
        try {
            if (!isRecordExists(taskDTO)) {
                processRealtimeData(taskDTO);
            } else {
                // 5. 记录日志详情
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }
            return String.format("同步成功，taskNo:%s", taskDTO.getTaskNo());
        } catch (Exception e) {
            log.error("同步中控系统实时数据失败", e);
            throw new RuntimeException("同步中控系统实时数据失败：" + e.getMessage());
        }
    }

    @Override
    public List<SiloRealtimeStatusDTO> getListLatestSiloRealtimeStatusBySiloNo(List<String> siloNoList) {
        return siloRealtimeStatusMapper.getListLatestSiloRealtimeStatusBySiloNo(siloNoList);
    }

    /**
     * 处理中控系统实时数据
     *
     * @param taskDTO
     */
    private void processRealtimeData(UnifiedTaskDTO taskDTO) {

        TMpdSiloRealtimeDO existingTask = findByTaskNoAndMaterialCode(taskDTO.getSiloNo(), taskDTO.getTaskNo(), taskDTO.getMaterialCode());

        if (existingTask == null) {
            // 2. 不存在则新增
            existingTask = createNewSiloRealtimeStatus(taskDTO);
            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingTask.getTaskNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 存在，则更新
            existingTask.setInventoryWeight(taskDTO.getInventoryWeight());
            existingTask.setStatus(taskDTO.getStatus());
            existingTask.setUpdateTime(new Date());
            siloRealtimeStatusMapper.updateById(existingTask);
            log.info("更新主表汇总信息，任务ID:{}, 数量:{}", existingTask.getId(), existingTask.getInventoryWeight());
        }
    }

    /**
     * 创建新任务
     */
    private TMpdSiloRealtimeDO createNewSiloRealtimeStatus(UnifiedTaskDTO record) {
        TMpdSiloRealtimeDO realtimeDO = new TMpdSiloRealtimeDO();
        realtimeDO.setTaskNo(record.getTaskNo());
        realtimeDO.setBusinessSystem(record.getBusinessSystem());
        realtimeDO.setMaterialName(record.getMaterialName());
        realtimeDO.setMaterialCode(record.getMaterialCode());
        realtimeDO.setInventoryWeight(record.getInventoryWeight());
        realtimeDO.setUnit(record.getUnit());

        //筒仓处理
        realtimeDO.setSiloNo(record.getSiloNo());
        //筒仓名称
        MaterialSiloConfigDTO materialSiloConfig = getSiloConfig(record.getSiloNo());
        realtimeDO.setSiloName(materialSiloConfig != null ? materialSiloConfig.getName() : null);

        siloRealtimeStatusMapper.insert(realtimeDO);
        return realtimeDO;
    }

    private MaterialSiloConfigDTO getSiloConfig(String siloNo){
        MaterialSiloConfigDTO siloConfig = materialSiloConfigService.getMaterialSiloByCode(siloNo);
        /*if (siloConfig == null){
            throw new RuntimeException("未找到对应的筒仓配置信息");
        }*/
        return siloConfig;
    }

    private TMpdSiloRealtimeDO findByTaskNoAndMaterialCode(String siloNo, @NotNull(message = "任务号不能为空") String taskNo, String materialCode) {
         return siloRealtimeStatusMapper.selectByMaterialCodeAndTaskNoAndSiloNo(materialCode, taskNo, siloNo);
    }

    /**
     * 检查记录是否已存在（根据物料编码、数量、筒仓号、任务号判断）- UnifiedTaskDTO版本
     */
    private boolean isRecordExists(UnifiedTaskDTO taskDTO) {
        TMpdSiloRealtimeDO existing = siloRealtimeStatusMapper
                .selectByMaterialCodeAndQuantityAndSiloNoAndTaskNo(
                        taskDTO.getMaterialCode(),
                        taskDTO.getInventoryWeight(),
                        taskDTO.getSiloNo(),
                        taskDTO.getTaskNo()
                );
        return existing != null;
    }
}
