package com.hvisions.rawmaterial.service;


import com.hvisions.rawmaterial.dto.production.flour.transfer.detail.FlourTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱粉转运工单
 * @date 2022/4/22 10:18
 */
public interface FlourTransferOrderService {

    Page<FlourTransferOrderPageDTO> getFlourTransferOrderPageList(FlourTransferOrderPageQueryDTO queryDTO);

    Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS);


    Integer updateFlourTransfer(UpdateFlourTransferDTO updateFlourTransferDTO);

    Page<FlourTransferDetailListDTO> getFlourTransferOrderDetailPageList(FlourTransferOrderPageQueryDTO queryDTO);

    /**
     * 创建高粱粉转运工单
     * @param taskDTO
     * @return
     */
    String createSorghumPowderTransfer(UnifiedTaskDTO taskDTO);
}
