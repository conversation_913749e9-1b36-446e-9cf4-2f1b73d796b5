package com.hvisions.rawmaterial.service.materialsilo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dto.silo.*;
import com.hvisions.rawmaterial.dto.storage.Rl.DepartmentTreeDTO;
import com.hvisions.rawmaterial.entity.materialsilo.RlPermission;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 物料筒仓配置服务实现类
 * @author: z19235
 * @time: 2025/08/01
 */
@Slf4j
@Service
public class MaterialSiloConfigServiceImpl implements MaterialSiloConfigService {

    @Resource
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Resource
    private RlPermissionService rlPermissionService;

    @Override
    public List<MaterialSiloTreeDTO> getMaterialSiloTree(Integer materialType) {
        return materialSiloConfigMapper.getMaterialSiloTree(materialType);
    }

    @Override
    public Page<MaterialSiloTreeDTO> getMaterialSiloPageList(MaterialSiloQueryPageDTO queryDTO) {
        return PageHelperUtil.getPage(materialSiloConfigMapper::getMaterialSiloPageList, queryDTO, MaterialSiloTreeDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addMaterialSilo(MaterialSiloConfigDTO configDTO) {
        // 检查编码唯一性
        LambdaQueryWrapper<TMpdMateriaSiloDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TMpdMateriaSiloDO::getCode, configDTO.getCode())
               .eq(TMpdMateriaSiloDO::getMaterialType, configDTO.getMaterialType());

        TMpdMateriaSiloDO existing = materialSiloConfigMapper.selectOne(wrapper);
        if (existing != null) {
            throw new BaseKnownException(10000, "筒仓编码已存在！");
        }

        TMpdMateriaSiloDO rlManagement = DtoMapper.convert(configDTO, TMpdMateriaSiloDO.class);
        rlManagement.setCreateTime(new Date());
        rlManagement.setUpdateTime(new Date());
        
        return materialSiloConfigMapper.insert(rlManagement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateMaterialSilo(MaterialSiloConfigDTO configDTO) {
        TMpdMateriaSiloDO rlManagement = DtoMapper.convert(configDTO, TMpdMateriaSiloDO.class);
        rlManagement.setUpdateTime(new Date());
        rlManagement.setCreatorId(null); // 不更新创建者信息
        
        return materialSiloConfigMapper.updateById(rlManagement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteMaterialSilo(Integer id) {
        // 检查是否有子节点
        LambdaQueryWrapper<TMpdMateriaSiloDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TMpdMateriaSiloDO::getParentId, id);
        
        Integer childCount = materialSiloConfigMapper.selectCount(wrapper);
        if (childCount > 0) {
            throw new BaseKnownException(10000, "存在子节点，无法删除！");
        }

        // 删除权限配置
        rlPermissionService.deleteByRlId(id);
        
        // 逻辑删除筒仓
        return materialSiloConfigMapper.deleteById(id);
    }

    @Override
    public MaterialSiloConfigDTO getMaterialSiloById(Integer id) {
        TMpdMateriaSiloDO rlManagement = materialSiloConfigMapper.selectById(id);
        if (rlManagement == null) {
            return null;
        }
        
        MaterialSiloConfigDTO configDTO = DtoMapper.convert(rlManagement, MaterialSiloConfigDTO.class);
        
        return configDTO;
    }

    @Override
    public MaterialSiloConfigDTO getMaterialSiloByCode(String code) {
        return materialSiloConfigMapper.getMaterialSiloByCode(code);
    }

    @Override
    public MaterialSiloConfigDTO getMaterialSiloByCodeAndType(String code, Integer materialType) {
        return materialSiloConfigMapper.getMaterialSiloByCodeAndType(code, materialType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveMaterialSiloPermissions(MaterialSiloPermissionDTO permissionDTO) {
        List<RlPermission> permissionList = permissionDTO.getDeptIds().stream()
                .map(deptId -> RlPermission.builder()
                        .rlId(permissionDTO.getRlId())
                        .deptId(deptId)
                        .permissionType(permissionDTO.getPermissionType())
                        .build())
                .collect(Collectors.toList());
        
        return rlPermissionService.saveRlPermissions(permissionList);
    }

    @Override
    public List<RlPermission> getPermissionsByRlId(Integer rlId) {
        return rlPermissionService.getByRlId(rlId);
    }

    @Override
    public List<DepartmentTreeDTO> getDepartmentTree() {
        return materialSiloConfigMapper.getDepartmentTree();
    }

    @Override
    public List<Integer> getAuthorizedDeptIds(Integer rlId) {
        List<RlPermission> permissions = rlPermissionService.getByRlId(rlId);
        return permissions.stream()
                .map(RlPermission::getDeptId)
                .collect(Collectors.toList());
    }

    @Override
    public List<MaterialSiloTreeDTO> getAllMaterialTypeLeafNodes() {
        return materialSiloConfigMapper.getAllMaterialTypeLeafNodes();
    }

    @Override
    public List<MaterialSiloTreeDTO> getAllMaterialByCode(String code) {
        return materialSiloConfigMapper.getAllMaterialByCode(code);
    }

    @Override
    public List<MaterialSiloConfigDTO> getSiloConfigFistList() {
        return materialSiloConfigMapper.getSiloConfigFistList();
    }

    @Override
    public List<MaterialSiloConfigDTO> getLocationList(MaterialSiloQueryDTO queryDTO) {
        return materialSiloConfigMapper.getLocationList(queryDTO);
    }
}
