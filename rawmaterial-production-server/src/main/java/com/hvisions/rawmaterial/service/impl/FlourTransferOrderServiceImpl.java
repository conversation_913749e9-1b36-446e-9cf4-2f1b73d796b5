package com.hvisions.rawmaterial.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.rawmaterial.dto.production.flour.transfer.detail.FlourTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.flour.transfer.order.FlourTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.dao.FlourTransferOrderMapper;
import com.hvisions.rawmaterial.dao.FtOrderDetailMapper;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdFlourTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdFtOrderDetail;
import com.hvisions.rawmaterial.service.FlourTransferOrderService;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.utils.DateUtil;
import com.hvisions.rawmaterial.utils.StringUtil;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:高粱粉转运工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class FlourTransferOrderServiceImpl implements FlourTransferOrderService {

    @Resource
    private FlourTransferOrderMapper flourTransferOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private FtOrderDetailMapper orderDetailMapper;

    @Override
    public Page<FlourTransferOrderPageDTO> getFlourTransferOrderPageList(FlourTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(flourTransferOrderMapper::getFlourTransferOrderPageList, queryDTO, FlourTransferOrderPageDTO.class);
    }


    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdFlourTransferOrder order = flourTransferOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdFtOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdFtOrderDetail.class);
            for (TMpdFtOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += orderDetailMapper.insert(orderDetail);

                count = count.add(orderDetail.getActualQuantity());

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += flourTransferOrderMapper.updateById(order);
        }
        return res;
    }

    /**
     * 更新高粱粉转运工单详情
     * @param updateFlourTransferDTO
     * @return
     */
    @Override
    public Integer updateFlourTransfer(UpdateFlourTransferDTO updateFlourTransferDTO) {
        TMpdFtOrderDetail tMpdFtOrderDetail = orderDetailMapper.selectOne(new LambdaQueryWrapper<TMpdFtOrderDetail>().eq(TMpdFtOrderDetail::getJobIdent, updateFlourTransferDTO.getJobIdent()).eq(TMpdFtOrderDetail::getDeleted, false));
        if (tMpdFtOrderDetail == null) {
            throw new BaseKnownException(10000, "未获取到详细数据");
        }
        TMpdFlourTransferOrder oldOrder = flourTransferOrderMapper.selectById(tMpdFtOrderDetail.getOrderId());
        TMpdFlourTransferOrder newOrder = flourTransferOrderMapper.selectOne(new LambdaQueryWrapper<TMpdFlourTransferOrder>()
                .eq(TMpdFlourTransferOrder::getDeleted, false)
                .eq(TMpdFlourTransferOrder::getCenter, oldOrder.getCenter())
                .like(TMpdFlourTransferOrder::getOrderDate, DateUtil.format(updateFlourTransferDTO.getEndDateActual(),"yyyy-MM-dd"))
                .orderByDesc(TMpdFlourTransferOrder::getOrderDate)
                .last("limit 1"));
        if (newOrder != null && !Objects.equals(newOrder.getId(), oldOrder.getId())) {
            tMpdFtOrderDetail.setOrderId(newOrder.getId());
            tMpdFtOrderDetail.setActualEndTime(updateFlourTransferDTO.getEndDateActual());
            //更改工单数据，对汇总数据进行加减
            oldOrder.setActualQuantity(oldOrder.getActualQuantity().subtract(tMpdFtOrderDetail.getActualQuantity()));
            flourTransferOrderMapper.updateById(oldOrder);
            newOrder.setActualQuantity(newOrder.getActualQuantity().add(tMpdFtOrderDetail.getActualQuantity()));
            flourTransferOrderMapper.updateById(newOrder);
            log.info("更新高粱粉转运工单详情, 原工单号{}, 现工单号{}", oldOrder.getOrderNo(), newOrder.getOrderNo());
            return orderDetailMapper.updateById(tMpdFtOrderDetail);
        }
        log.info("更新高粱粉转运工单详情,工单号数据一致不用更新:" + JSONObject.toJSONString(updateFlourTransferDTO));
        return 0;
    }

    @Override
    public Page<FlourTransferDetailListDTO> getFlourTransferOrderDetailPageList(FlourTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(flourTransferOrderMapper::getFlourTransferOrderDetailPageList, queryDTO, FlourTransferDetailListDTO.class);
    }

    @Override
    public String createSorghumPowderTransfer(UnifiedTaskDTO taskDTO) {
        return "";
    }


}
