package com.hvisions.rawmaterial.service.ricehusk;

import com.hvisions.rawmaterial.dao.ricehusk.RiceHuskInboundTaskDetailMapper;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDetailDTO;
import com.hvisions.rawmaterial.entity.ricehusk.TMpdRiceHuskInboundTaskDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 稻壳入仓任务详情Service实现类
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class RiceHuskInboundTaskDetailServiceImpl implements RiceHuskInboundTaskDetailService {

    @Autowired
    private RiceHuskInboundTaskDetailMapper riceHuskInboundTaskDetailMapper;

    @Override
    public List<RiceHuskInboundTaskDetailDTO> findByTaskId(String taskId) {
        return riceHuskInboundTaskDetailMapper.findByTaskId(taskId);
    }

    @Override
    public RiceHuskInboundTaskDetailDTO save(RiceHuskInboundTaskDetailDTO dto) {
        TMpdRiceHuskInboundTaskDetail entity = new TMpdRiceHuskInboundTaskDetail();
        BeanUtils.copyProperties(dto, entity);
        
        entity.setDeleted(false);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        riceHuskInboundTaskDetailMapper.insert(entity);
        
        RiceHuskInboundTaskDetailDTO result = new RiceHuskInboundTaskDetailDTO();
        BeanUtils.copyProperties(entity, result);
        return result;
    }
}
