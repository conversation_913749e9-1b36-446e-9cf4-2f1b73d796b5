package com.hvisions.rawmaterial.service.silo;

import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusDTO;
import com.hvisions.rawmaterial.dto.silo.SiloRealtimeStatusQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 筒仓实时状态信息服务接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface SiloRealtimeStatusService {

    /**
     * 分页查询筒仓实时状态信息列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<SiloRealtimeStatusDTO> getSiloRealtimeStatusPageList(SiloRealtimeStatusQueryDTO queryDTO);

    /**
     * 根据ID查询筒仓实时状态信息详情
     *
     * @param id 主键ID
     * @return 筒仓实时状态信息详情
     */
    SiloRealtimeStatusDTO getSiloRealtimeStatusById(Integer id);

    /**
     * 新增筒仓实时状态信息
     *
     * @param statusDTO 筒仓实时状态信息
     * @return 影响行数
     */
    Integer addSiloRealtimeStatus(SiloRealtimeStatusDTO statusDTO);

    /**
     * 修改筒仓实时状态信息
     *
     * @param statusDTO 筒仓实时状态信息
     * @return 影响行数
     */
    Integer updateSiloRealtimeStatus(SiloRealtimeStatusDTO statusDTO);

    /**
     * 删除筒仓实时状态信息
     *
     * @param id 主键ID
     * @return 影响行数
     */
    Integer deleteSiloRealtimeStatus(Integer id);

    /**
     * 批量删除筒仓实时状态信息
     *
     * @param ids 主键ID列表
     * @return 影响行数
     */
    Integer batchDeleteSiloRealtimeStatus(List<Integer> ids);

    /**
     * 根据业务系统和任务号查询筒仓实时状态信息
     *
     * @param businessSystem 业务系统
     * @param taskNo 任务号
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByBusinessSystemAndTaskNo(String businessSystem, String taskNo);

    /**
     * 根据筒仓号查询最新的实时状态信息
     *
     * @param siloNo 筒仓号
     * @return 筒仓实时状态信息
     */
    SiloRealtimeStatusDTO getLatestSiloRealtimeStatusBySiloNo(String siloNo);

    /**
     * 根据物料类型查询筒仓实时状态信息
     *
     * @param materialType 物料类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByMaterialType(String materialType);

    /**
     * 根据业务系统和物料类型查询筒仓实时状态信息
     *
     * @param businessSystem 业务系统
     * @param materialType 物料类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByBusinessSystemAndMaterialType(String businessSystem, String materialType);

    /**
     * 根据数据类型查询筒仓实时状态信息
     *
     * @param dataType 数据类型
     * @return 筒仓实时状态信息列表
     */
    List<SiloRealtimeStatusDTO> getSiloRealtimeStatusByDataType(String dataType);

    /**
     * 批量保存筒仓实时状态信息
     *
     * @param statusList 筒仓实时状态信息列表
     * @return 影响行数
     */
    Integer batchSaveSiloRealtimeStatus(List<SiloRealtimeStatusDTO> statusList);

    /**
     * 根据任务号删除筒仓实时状态信息
     *
     * @param taskNo 任务号
     * @return 影响行数
     */
    Integer deleteSiloRealtimeStatusByTaskNo(String taskNo);

    /**
     * 清理历史数据
     *
     * @param siloNo 筒仓号（可选）
     * @param beforeTime 时间点（删除此时间之前的数据）
     * @return 影响行数
     */
    Integer cleanHistoryData(String siloNo, String beforeTime);

    /**
     * 同步中控系统实时数据
     * @param taskDTO 任务信息
     * @return 同步结果
     */
    String syncRealtimeDataFromCentralControl(UnifiedTaskDTO taskDTO);

    /**
     * 批量查询最新数据
     * @param siloNoList
     * @return
     */
    List<SiloRealtimeStatusDTO> getListLatestSiloRealtimeStatusBySiloNo(List<String> siloNoList);
}
