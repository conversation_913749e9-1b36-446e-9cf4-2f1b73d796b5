package com.hvisions.rawmaterial.service.quality;

import com.hvisions.rawmaterial.dto.quality.SorghumQualityCheckDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;

import java.util.List;

/**
 * 高粱筒仓快检数据服务接口
 *
 * <AUTHOR>
 * @date 2025-09-23
 */
public interface SorghumQualityCheckService {

    /**
     * 创建高粱筒仓快检记录
     *
     * @param qualityCheckDTO 快检数据DTO
     * @return 创建结果消息
     */
    String createQualityCheckRecord(SorghumQualityCheckDTO qualityCheckDTO);

    /**
     * 从中控系统同步高粱筒仓快检数据
     *
     * @param taskDTO 统一任务DTO
     * @return 同步结果消息
     */
    String syncQualityCheckFromCentralControl(UnifiedTaskDTO taskDTO);

    /**
     * 根据任务号查询快检记录
     *
     * @param taskNo 任务号
     * @return 快检记录
     */
    SorghumQualityCheckDTO getQualityCheckByTaskNo(String taskNo);

    /**
     * 根据筒仓号和时间范围查询快检记录
     *
     * @param siloNo 筒仓号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 快检记录列表
     */
    List<SorghumQualityCheckDTO> getQualityCheckBySiloNoAndTimeRange(String siloNo, String startTime, String endTime);

    /**
     * 根据物料编码和时间范围查询快检记录
     *
     * @param materialCode 物料编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 快检记录列表
     */
    List<SorghumQualityCheckDTO> getQualityCheckByMaterialCodeAndTimeRange(String materialCode, String startTime, String endTime);

    /**
     * 更新检测状态
     *
     * @param taskNo 任务号
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateQualityCheckStatus(String taskNo, Integer status);

    /**
     * 批量创建快检记录
     *
     * @param qualityCheckDTOs 快检数据DTO列表
     * @return 创建结果消息
     */
    String batchCreateQualityCheckRecords(List<SorghumQualityCheckDTO> qualityCheckDTOs);

    /**
     * 根据检测状态查询快检记录
     *
     * @param status 检测状态
     * @return 快检记录列表
     */
    List<SorghumQualityCheckDTO> getQualityCheckByStatus(Integer status);

    /**
     * 验证快检数据的完整性和有效性
     *
     * @param qualityCheckDTO 快检数据DTO
     * @return 验证结果
     */
    boolean validateQualityCheckData(SorghumQualityCheckDTO qualityCheckDTO);
}
