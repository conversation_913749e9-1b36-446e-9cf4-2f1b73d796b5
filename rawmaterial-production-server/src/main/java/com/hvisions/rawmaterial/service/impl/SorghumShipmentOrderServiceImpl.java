package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.rawmaterial.dao.SorghumShipmentOrderMapper;
import com.hvisions.rawmaterial.dao.SsOrderDetailMapper;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSsOrderDetail;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.service.SorghumShipmentOrderService;
import com.hvisions.rawmaterial.service.log.SyncOperationLogService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.covertStorage;
import static com.hvisions.rawmaterial.service.unified.impl.UnifiedInterfaceServiceImpl.parseStatusStr;

/**
 * <AUTHOR>
 * @description:高粱转粮工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumShipmentOrderServiceImpl implements SorghumShipmentOrderService {

    @Resource
    private SorghumShipmentOrderMapper sorghumShipmentOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private SsOrderDetailMapper orderDetailMapper;

    @Autowired
    private SyncOperationLogService syncOperationLogService;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Resource
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Override
    public Page<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderPageList, queryDTO, SorghumShipmentOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumShipmentOrder order = sorghumShipmentOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdSsOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdSsOrderDetail.class);
            for (TMpdSsOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                count = count.add(orderDetail.getActualQuantity());

                int i = orderDetailMapper.insert(orderDetail);

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumShipmentOrderMapper.updateById(order);
        }
        return res;
    }

    @Override
    public Page<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderDetailPageList, queryDTO, SorghumShipmentDetailListDTO.class);
    }

    /**
     * 高粱转粮工单同步
     * @param taskDTO
     * @return
     */
    @Override
    @Transactional
    public String createSorghumGrainTransfer(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步高粱转粮记录");

        try {
            // 1. 检查是否已存在相同的记录（根据物料编码、实际数量、开始时间判断）
            if (!isRecordExists(taskDTO)) {
                processShipmentRecord(taskDTO);
                // 5. 记录日志详情
                log.info("高粱转粮任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logSkipDuplicate(taskDTO);
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }

            return String.format("高粱转粮任务信息（一期对接）同步成功，taskNo:%s", taskDTO.getTaskNo());
        }catch (Exception e){
            // 5. 记录错误日志到数据库
            syncOperationLogService.logError(taskDTO, e.getMessage());
            log.error("高粱转粮工单同步异常", e);
            throw new RuntimeException("同步高粱转粮工单异常：" + e.getMessage());
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdSsOrderDetail existing = orderDetailMapper
                .selectByMaterialCodeAndTaskNoAndTime(
                        record.getMaterialCode(),
                        record.getTaskNo(),
                        record.getStartTime()
                );
        return existing != null;
    }

    /**
     * 处理转粮记录
     */
    private void processShipmentRecord(UnifiedTaskDTO taskDTO) {
        // 1. 根据工单日期查询或创建主表记录
        Date recordDate = taskDTO.getStartTime() != null ? taskDTO.getStartTime() : new Date();

        TMpdSorghumShipmentOrder existingOrder = sorghumShipmentOrderMapper.selectByDateAndMaterialCode(recordDate, taskDTO.getMaterialCode());

        if (existingOrder == null) {
            // 2. 主表不存在则新增
            existingOrder = createNewShipmentOrder(recordDate, taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            //syncOperationLogService.logCreateMainTask(taskDTO, existingOrder.getOrderNo(), Integer.valueOf(existingOrder.getId()));

            // 创建任务详情
            TMpdSsOrderDetail detail = createShipmentDetail(existingOrder.getId(), taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            //syncOperationLogService.logCreateDetail(taskDTO, Integer.valueOf(existingOrder.getId()), Integer.valueOf(detail.getId()));

            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingOrder.getOrderNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录
            TMpdSsOrderDetail existingDetail = orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                    taskDTO.getMaterialCode(), taskDTO.getTaskNo(), taskDTO.getStartTime());

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdSsOrderDetail detail = createShipmentDetail(existingOrder.getId(), taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logCreateDetail(taskDTO, Integer.valueOf(existingOrder.getId()), Integer.valueOf(detail.getId()));
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingOrder.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateShipmentDetail(existingDetail, taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logUpdateDetail(taskDTO, Integer.valueOf(existingDetail.getId()));
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表汇总信息
            updateShipmentSummary(existingOrder);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logUpdateMainSummary(taskDTO, Integer.valueOf(existingOrder.getId()), existingOrder.getActualQuantity());
        }
    }

    /**
     * 创建新的转粮工单主表记录
     */
    private TMpdSorghumShipmentOrder createNewShipmentOrder(Date orderDate, UnifiedTaskDTO taskDTO) {
        TMpdSorghumShipmentOrder order = new TMpdSorghumShipmentOrder();

        // 生成工单号
        order.setOrderNo(generateOrderNo(orderDate));
        order.setOrderDate(orderDate);
        order.setMaterialCode(taskDTO.getMaterialCode());
        order.setMaterialName(taskDTO.getMaterialName());
        order.setUnit(taskDTO.getUnit());
        order.setActualQuantity(taskDTO.getActualWeight());

        sorghumShipmentOrderMapper.insert(order);
        return order;
    }

    /**
     * 创建转粮工单详情记录
     */
    private TMpdSsOrderDetail createShipmentDetail(Integer orderId, UnifiedTaskDTO taskDTO) {
        TMpdSsOrderDetail detail = new TMpdSsOrderDetail();

        detail.setOrderId(orderId);

        TMpdMateriaSiloDO sendSilo = findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()));
        detail.setSendSiloId(sendSilo.getId());

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()));
        detail.setAcceptSiloId(silo.getId());

        detail.setMaterialCode(taskDTO.getMaterialCode());
        detail.setMaterialName(taskDTO.getMaterialName());
        detail.setUnit(taskDTO.getUnit());
        detail.setActualQuantity(taskDTO.getActualWeight());
        detail.setState(parseStatusStr(taskDTO.getStatus()));
        detail.setActualBeginTime(taskDTO.getStartTime());
        detail.setActualEndTime(taskDTO.getEndTime());
        detail.setControlTaskNo(taskDTO.getTaskNo());
        detail.setLogId(taskDTO.getExtField1());

        orderDetailMapper.insert(detail);
        return detail;
    }

    /**
     * 更新转粮工单详情记录
     */
    private void updateShipmentDetail(TMpdSsOrderDetail existingDetail, UnifiedTaskDTO taskDTO) {
        TMpdMateriaSiloDO sendSilo = findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()));
        existingDetail.setSendSiloId(sendSilo.getId());

        TMpdMateriaSiloDO silo = findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()));
        existingDetail.setAcceptSiloId(silo.getId());

        existingDetail.setMaterialCode(taskDTO.getMaterialCode());
        existingDetail.setMaterialName(taskDTO.getMaterialName());
        existingDetail.setUnit(taskDTO.getUnit());
        existingDetail.setActualQuantity(taskDTO.getActualWeight());
        existingDetail.setActualBeginTime(taskDTO.getStartTime());
        existingDetail.setActualEndTime(taskDTO.getEndTime());
        existingDetail.setState(parseStatusStr(taskDTO.getStatus()));
        existingDetail.setUpdateTime(new Date());

        orderDetailMapper.updateById(existingDetail);
    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private TMpdMateriaSiloDO findSiloCodeByTubCode(String siloCode) {
        TMpdMateriaSiloDO silo = materialSiloConfigMapper.findSiloBySiloCode(siloCode);
        if (silo == null){
            throw new RuntimeException("未找到筒仓信息");
        }
        return silo;
    }

    /**
     * 更新转粮工单汇总信息
     */
    private void updateShipmentSummary(TMpdSorghumShipmentOrder order) {
        // 查询该主表下所有详情记录的汇总数据
        BigDecimal summaryQuantity = orderDetailMapper.selectSummaryByOrderId(order.getId());

        order.setActualQuantity(summaryQuantity != null ? summaryQuantity : BigDecimal.ZERO);
        order.setUpdateTime(new Date());

        sorghumShipmentOrderMapper.updateById(order);
        log.info("根据详情汇总更新主表成功，工单ID：{}，数量：{}", order.getId(), summaryQuantity);
    }

    /**
     * 生成工单号
     * 规则：GD + 年月日 + 三位流水号
     *
     * @param orderDate 工单日期
     * @return 工单号
     */
    private String generateOrderNo(Date orderDate) {
        if (orderDate == null) {
            orderDate = new Date();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(orderDate);

        // 查询当天最大流水号
        String maxSerialNo = sorghumShipmentOrderMapper.selectMaxSerialNoByDate(orderDate);
        int serialNo = 1;
        if (maxSerialNo != null && !maxSerialNo.isEmpty()) {
            serialNo = Integer.parseInt(maxSerialNo) + 1;
        }

        return String.format("GD%s%03d", dateStr, serialNo);
    }

}
