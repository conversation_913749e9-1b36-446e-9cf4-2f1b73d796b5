package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.SorghumShipmentOrderMapper;
import com.hvisions.rawmaterial.dao.SsOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSsOrderDetail;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.service.SorghumShipmentOrderService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转粮工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumShipmentOrderServiceImpl implements SorghumShipmentOrderService {

    @Resource
    private SorghumShipmentOrderMapper sorghumShipmentOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private SsOrderDetailMapper orderDetailMapper;

    @Autowired
    private SyncOperationLogService syncOperationLogService;

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Override
    public Page<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderPageList, queryDTO, SorghumShipmentOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumShipmentOrder order = sorghumShipmentOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdSsOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdSsOrderDetail.class);
            for (TMpdSsOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                count = count.add(orderDetail.getActualQuantity());

                int i = orderDetailMapper.insert(orderDetail);

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumShipmentOrderMapper.updateById(order);
        }
        return res;
    }

    @Override
    public Page<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderDetailPageList, queryDTO, SorghumShipmentDetailListDTO.class);
    }

    /**
     * 高粱转粮工单同步
     * @param taskDTO
     * @return
     */
    @Override
    @Transactional
    public String createSorghumGrainTransfer(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步高粱转粮记录");

        // 记录日志到日志服务
        addLogCapture(taskDTO);

        try {
            // 1. 检查是否已存在相同的记录（根据物料编码、实际数量、开始时间判断）
            if (!isRecordExists(taskDTO)) {
                processShipmentRecord(taskDTO);
                // 5. 记录日志详情
                log.info("高粱转粮任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logSkipDuplicate(taskDTO);
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }

            return String.format("高粱转粮任务信息（一期对接）同步成功，taskNo:%s", taskDTO.getTaskNo());
        }catch (Exception e){
            // 5. 记录错误日志到数据库
            syncOperationLogService.logError(taskDTO, e.getMessage());
            log.error("高粱转粮工单同步异常", e);
            throw new RuntimeException("同步高粱转粮工单异常：" + e.getMessage());
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdSsOrderDetail existing = orderDetailMapper
                .selectByMaterialCodeAndTaskNoAndTime(
                        record.getMaterialCode(),
                        record.getTaskNo(),
                        record.getStartTime()
                );
        return existing != null;
    }

    /**
     * 处理转粮记录
     */
    private void processShipmentRecord(UnifiedTaskDTO taskDTO) {
        // 1. 根据工单日期查询或创建主表记录
        Date recordDate = taskDTO.getStartTime() != null ? taskDTO.getStartTime() : new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdf.format(recordDate);

        TMpdSorghumShipmentOrder existingOrder = sorghumShipmentOrderMapper.selectByOrderDate(recordDate);

        if (existingOrder == null) {
            // 2. 主表不存在则新增
            existingOrder = createNewShipmentOrder(recordDate, taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logCreateMainTask(taskDTO, existingOrder.getOrderNo(), existingOrder.getId());

            // 创建任务详情
            TMpdSsOrderDetail detail = createShipmentDetail(existingOrder.getId(), taskDTO);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logCreateDetail(taskDTO, existingOrder.getId(), detail.getId());

            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingOrder.getOrderNo(), taskDTO.getMaterialCode());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录
            TMpdSsOrderDetail existingDetail = orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                    taskDTO.getMaterialCode(), taskDTO.getTaskNo(), taskDTO.getStartTime());

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdSsOrderDetail detail = createShipmentDetail(existingOrder.getId(), taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logCreateDetail(taskDTO, existingOrder.getId(), detail.getId());
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingOrder.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateShipmentDetail(existingDetail, taskDTO);
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logUpdateDetail(taskDTO, existingDetail.getId());
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表汇总信息
            updateShipmentSummary(existingOrder);
            // 5. 记录日志详情 - 记录到数据库
            syncOperationLogService.logUpdateMainSummary(taskDTO, existingOrder.getId(), existingOrder.getActualQuantity());
        }
    }

}
