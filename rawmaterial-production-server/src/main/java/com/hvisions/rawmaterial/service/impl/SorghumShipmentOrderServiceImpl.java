package com.hvisions.rawmaterial.service.impl;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.SorghumShipmentOrderMapper;
import com.hvisions.rawmaterial.dao.SsOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.detail.SorghumShipmentDetailListDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.shipment.order.SorghumShipmentOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSsOrderDetail;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumInboundTaskDetail;
import com.hvisions.rawmaterial.entity.sorghum.TMpdSorghumShipmentOrder;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.service.SorghumShipmentOrderService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱转粮工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class SorghumShipmentOrderServiceImpl implements SorghumShipmentOrderService {

    @Resource
    private SorghumShipmentOrderMapper sorghumShipmentOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private SsOrderDetailMapper orderDetailMapper;

    @Override
    public Page<SorghumShipmentOrderPageDTO> getSorghumShipmentOrderPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderPageList, queryDTO, SorghumShipmentOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdSorghumShipmentOrder order = sorghumShipmentOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdSsOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdSsOrderDetail.class);
            for (TMpdSsOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                count = count.add(orderDetail.getActualQuantity());

                int i = orderDetailMapper.insert(orderDetail);

            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += sorghumShipmentOrderMapper.updateById(order);
        }
        return res;
    }

    @Override
    public Page<SorghumShipmentDetailListDTO> getSorghumShipmentOrderDetailPageList(SorghumShipmentOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(sorghumShipmentOrderMapper::getSorghumShipmentOrderDetailPageList, queryDTO, SorghumShipmentDetailListDTO.class);
    }

    /**
     * 高粱转粮工单同步
     * @param taskDTO
     * @return
     */
    @Override
    @Transactional
    public String createSorghumGrainTransfer(UnifiedTaskDTO taskDTO) {
        try {
            // 1. 检查是否已存在相同的记录（根据物料编码、实际数量、开始时间判断）
            if (!isRecordExists(taskDTO)) {
                processInboundRecord(taskDTO);
                // 5. 记录日志详情
                log.info("高粱入库任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 5. 记录日志详情 - 记录到数据库
                syncOperationLogService.logSkipDuplicate(taskDTO);
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }

            return String.format("高粱转粮任务信息（一期对接）同步成功，taskNo:%s", taskDTO.getTaskNo());
        }catch (Exception e){
            log.error("高粱转粮工单同步异常", e);
            throw new RuntimeException("同步高粱转粮工单异常：" + e.getMessage());
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、实际数量、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdSsOrderDetail existing = orderDetailMapper
                .selectByMaterialCodeAndTaskNoAndTime(
                        record.getMaterialCode(),
                        record.getTaskNo(),
                        record.getStartTime()
                );
        return existing != null;
    }

}
