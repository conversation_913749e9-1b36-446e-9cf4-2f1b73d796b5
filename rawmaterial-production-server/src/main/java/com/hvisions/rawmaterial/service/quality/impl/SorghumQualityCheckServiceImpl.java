package com.hvisions.rawmaterial.service.quality.impl;

import com.hvisions.rawmaterial.dao.SorghumQualityCheckMapper;
import com.hvisions.rawmaterial.dto.quality.SorghumQualityCheckDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdSorghumQualityCheck;
import com.hvisions.rawmaterial.service.log.SyncOperationLogService;
import com.hvisions.rawmaterial.service.quality.SorghumQualityCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 高粱筒仓快检数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-23
 */
@Slf4j
@Service
public class SorghumQualityCheckServiceImpl implements SorghumQualityCheckService {

    @Resource
    private SorghumQualityCheckMapper sorghumQualityCheckMapper;

    @Resource
    private SyncOperationLogService syncOperationLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createQualityCheckRecord(SorghumQualityCheckDTO qualityCheckDTO) {
        log.info("开始创建高粱筒仓快检记录：任务号={}", qualityCheckDTO.getTaskNo());

        try {
            // 验证数据完整性
            if (!validateQualityCheckData(qualityCheckDTO)) {
                throw new IllegalArgumentException("快检数据验证失败");
            }

            // 检查是否已存在相同记录
            TMpdSorghumQualityCheck existingRecord = sorghumQualityCheckMapper.selectByTaskNo(qualityCheckDTO.getTaskNo());
            if (existingRecord != null) {
                log.warn("任务号 {} 的快检记录已存在，跳过创建", qualityCheckDTO.getTaskNo());
                return "记录已存在，跳过创建";
            }

            // 转换DTO为实体
            TMpdSorghumQualityCheck entity = convertDtoToEntity(qualityCheckDTO);
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            entity.setDataSource("CENTRAL_CONTROL");
            entity.setIsDeleted(0);

            // 插入数据库
            int result = sorghumQualityCheckMapper.insert(entity);
            if (result > 0) {
                log.info("高粱筒仓快检记录创建成功：任务号={}, ID={}", qualityCheckDTO.getTaskNo(), entity.getId());
                return "快检记录创建成功";
            } else {
                throw new RuntimeException("快检记录创建失败");
            }

        } catch (Exception e) {
            log.error("创建高粱筒仓快检记录失败：任务号={}, 错误信息={}", qualityCheckDTO.getTaskNo(), e.getMessage(), e);
            throw new RuntimeException("创建快检记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncQualityCheckFromCentralControl(UnifiedTaskDTO taskDTO) {
        log.info("开始从中控系统同步高粱筒仓快检数据：任务号={}", taskDTO.getTaskNo());

        try {
            // 记录同步开始日志
            String logMessage = String.format("开始同步高粱筒仓快检数据，任务号：%s", taskDTO.getTaskNo());
            /*syncOperationLogService.recordSyncLog(taskDTO.getTaskNo(), "SORGHUM_QUALITY_CHECK",
                    "SYNC_START", logMessage, JSON.toJSONString(taskDTO), taskDTO.getLogId());*/

            // 转换UnifiedTaskDTO为SorghumQualityCheckDTO
            SorghumQualityCheckDTO qualityCheckDTO = convertUnifiedTaskToQualityCheck(taskDTO);

            // 创建快检记录
            String result = createQualityCheckRecord(qualityCheckDTO);

            // 记录同步成功日志
            logMessage = String.format("高粱筒仓快检数据同步成功，任务号：%s，结果：%s", taskDTO.getTaskNo(), result);
            /*syncOperationLogService.recordSyncLog(taskDTO.getTaskNo(), "SORGHUM_QUALITY_CHECK",
                    "SYNC_SUCCESS", logMessage, JSON.toJSONString(qualityCheckDTO), taskDTO.getLogId());*/

            return result;

        } catch (Exception e) {
            // 记录同步失败日志
            String logMessage = String.format("高粱筒仓快检数据同步失败，任务号：%s，错误：%s", taskDTO.getTaskNo(), e.getMessage());
            /*syncOperationLogService.recordSyncLog(taskDTO.getTaskNo(), "SORGHUM_QUALITY_CHECK",
                    "SYNC_FAILED", logMessage, JSON.toJSONString(taskDTO), taskDTO.getLogId());*/

            log.error("从中控系统同步高粱筒仓快检数据失败：任务号={}, 错误信息={}", taskDTO.getTaskNo(), e.getMessage(), e);
            throw new RuntimeException("同步快检数据失败：" + e);
        }
    }

    @Override
    public SorghumQualityCheckDTO getQualityCheckByTaskNo(String taskNo) {
        log.info("根据任务号查询快检记录：任务号={}", taskNo);

        TMpdSorghumQualityCheck entity = sorghumQualityCheckMapper.selectByTaskNo(taskNo);
        if (entity != null) {
            return convertEntityToDto(entity);
        }
        return null;
    }

    @Override
    public List<SorghumQualityCheckDTO> getQualityCheckBySiloNoAndTimeRange(String siloNo, String startTime, String endTime) {
        log.info("根据筒仓号和时间范围查询快检记录：筒仓号={}, 开始时间={}, 结束时间={}", siloNo, startTime, endTime);

        List<TMpdSorghumQualityCheck> entities = sorghumQualityCheckMapper.selectBySiloNoAndTimeRange(siloNo, startTime, endTime);
        return convertEntitiesToDtos(entities);
    }

    @Override
    public List<SorghumQualityCheckDTO> getQualityCheckByMaterialCodeAndTimeRange(String materialCode, String startTime, String endTime) {
        log.info("根据物料编码和时间范围查询快检记录：物料编码={}, 开始时间={}, 结束时间={}", materialCode, startTime, endTime);

        List<TMpdSorghumQualityCheck> entities = sorghumQualityCheckMapper.selectByMaterialCodeAndTimeRange(materialCode, startTime, endTime);
        return convertEntitiesToDtos(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQualityCheckStatus(String taskNo, Integer status) {
        log.info("更新快检记录状态：任务号={}, 新状态={}", taskNo, status);

        TMpdSorghumQualityCheck entity = sorghumQualityCheckMapper.selectByTaskNo(taskNo);
        if (entity != null) {
            int result = sorghumQualityCheckMapper.updateStatus(entity.getId(), status);
            return result > 0;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCreateQualityCheckRecords(List<SorghumQualityCheckDTO> qualityCheckDTOs) {
        log.info("批量创建快检记录：记录数量={}", qualityCheckDTOs.size());

        try {
            List<TMpdSorghumQualityCheck> entities = new ArrayList<>();
            Date now = new Date();

            for (SorghumQualityCheckDTO dto : qualityCheckDTOs) {
                if (!validateQualityCheckData(dto)) {
                    log.warn("快检数据验证失败，跳过记录：任务号={}", dto.getTaskNo());
                    continue;
                }

                TMpdSorghumQualityCheck entity = convertDtoToEntity(dto);
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entity.setDataSource("CENTRAL_CONTROL");
                entity.setIsDeleted(0);
                entities.add(entity);
            }

            if (!entities.isEmpty()) {
                int result = sorghumQualityCheckMapper.batchInsert(entities);
                log.info("批量创建快检记录完成：成功创建{}条记录", result);
                return String.format("批量创建成功：%d条记录", result);
            } else {
                return "没有有效的记录需要创建";
            }

        } catch (Exception e) {
            log.error("批量创建快检记录失败：错误信息={}", e.getMessage(), e);
            throw new RuntimeException("批量创建快检记录失败：" + e.getMessage());
        }
    }

    @Override
    public List<SorghumQualityCheckDTO> getQualityCheckByStatus(Integer status) {
        log.info("根据检测状态查询快检记录：状态={}", status);

        List<TMpdSorghumQualityCheck> entities = sorghumQualityCheckMapper.selectByStatus(status);
        return convertEntitiesToDtos(entities);
    }

    @Override
    public boolean validateQualityCheckData(SorghumQualityCheckDTO qualityCheckDTO) {
        if (qualityCheckDTO == null) {
            log.warn("快检数据为空");
            return false;
        }

        if (qualityCheckDTO.getTaskNo() == null || qualityCheckDTO.getTaskNo().trim().isEmpty()) {
            log.warn("任务号不能为空");
            return false;
        }

        if (qualityCheckDTO.getBusinessSystem() == null || qualityCheckDTO.getBusinessSystem().trim().isEmpty()) {
            log.warn("业务系统不能为空");
            return false;
        }

        // 验证快检数据的合理性
        if (qualityCheckDTO.getStarchContent() != null && 
            (qualityCheckDTO.getStarchContent().compareTo(BigDecimal.ZERO) < 0 || 
             qualityCheckDTO.getStarchContent().compareTo(new BigDecimal("100")) > 0)) {
            log.warn("淀粉含量数据异常：{}", qualityCheckDTO.getStarchContent());
            return false;
        }

        if (qualityCheckDTO.getMoistureContent() != null && 
            (qualityCheckDTO.getMoistureContent().compareTo(BigDecimal.ZERO) < 0 || 
             qualityCheckDTO.getMoistureContent().compareTo(new BigDecimal("100")) > 0)) {
            log.warn("水分含量数据异常：{}", qualityCheckDTO.getMoistureContent());
            return false;
        }

        if (qualityCheckDTO.getProteinContent() != null && 
            (qualityCheckDTO.getProteinContent().compareTo(BigDecimal.ZERO) < 0 || 
             qualityCheckDTO.getProteinContent().compareTo(new BigDecimal("100")) > 0)) {
            log.warn("蛋白质含量数据异常：{}", qualityCheckDTO.getProteinContent());
            return false;
        }

        return true;
    }

    /**
     * 转换UnifiedTaskDTO为SorghumQualityCheckDTO
     */
    private SorghumQualityCheckDTO convertUnifiedTaskToQualityCheck(UnifiedTaskDTO taskDTO) {
        SorghumQualityCheckDTO qualityCheckDTO = new SorghumQualityCheckDTO();
        
        qualityCheckDTO.setBusinessSystem(taskDTO.getBusinessSystem());
        qualityCheckDTO.setMaterialType(taskDTO.getMaterialType());
        qualityCheckDTO.setTaskType(taskDTO.getTaskType());
        qualityCheckDTO.setDataType(taskDTO.getDataType());
        qualityCheckDTO.setTaskNo(taskDTO.getTaskNo());
        qualityCheckDTO.setSiloNo(taskDTO.getSiloNo());
        qualityCheckDTO.setQualityCheckLocation(taskDTO.getQualityCheckLocation());
        qualityCheckDTO.setStartTime(taskDTO.getStartTime());
        qualityCheckDTO.setEndTime(taskDTO.getEndTime());
        qualityCheckDTO.setStarchContent(taskDTO.getStarchContent());
        qualityCheckDTO.setMoistureContent(taskDTO.getMoistureContent());
        qualityCheckDTO.setProteinContent(taskDTO.getProteinContent());
        qualityCheckDTO.setMaterialCode(taskDTO.getMaterialCode());
        qualityCheckDTO.setMaterialName(taskDTO.getMaterialName());
        qualityCheckDTO.setUnit(taskDTO.getUnit());
        qualityCheckDTO.setCentralControlTaskId(taskDTO.getTaskNo());
        qualityCheckDTO.setUniqueId(taskDTO.getUniqueId());
        qualityCheckDTO.setLogId(taskDTO.getExtField1());
        qualityCheckDTO.setStatus(2); // 默认设置为检测完成状态
        
        return qualityCheckDTO;
    }

    /**
     * 转换DTO为实体
     */
    private TMpdSorghumQualityCheck convertDtoToEntity(SorghumQualityCheckDTO dto) {
        TMpdSorghumQualityCheck entity = new TMpdSorghumQualityCheck();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 转换实体为DTO
     */
    private SorghumQualityCheckDTO convertEntityToDto(TMpdSorghumQualityCheck entity) {
        SorghumQualityCheckDTO dto = new SorghumQualityCheckDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 批量转换实体列表为DTO列表
     */
    private List<SorghumQualityCheckDTO> convertEntitiesToDtos(List<TMpdSorghumQualityCheck> entities) {
        List<SorghumQualityCheckDTO> dtos = new ArrayList<>();
        for (TMpdSorghumQualityCheck entity : entities) {
            dtos.add(convertEntityToDto(entity));
        }
        return dtos;
    }
}
