package com.hvisions.rawmaterial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.RiceTransferOrderMapper;
import com.hvisions.rawmaterial.dao.RtOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.rice.transfer.detail.RiceTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdRiceTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdRtOrderDetail;
import com.hvisions.rawmaterial.service.RiceTransferOrderService;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳转运工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class RiceTransferOrderServiceImpl implements RiceTransferOrderService {

    @Resource
    private RiceTransferOrderMapper riceTransferOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private RtOrderDetailMapper orderDetailMapper;


    @Override
    public Page<RiceTransferOrderPageDTO> getRiceTransferOrderPageList(RiceTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(riceTransferOrderMapper::getRiceTransferOrderPageList, queryDTO, RiceTransferOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdRiceTransferOrder order = riceTransferOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdRtOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdRtOrderDetail.class);
            for (TMpdRtOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += orderDetailMapper.insert(orderDetail);
                count = count.add(orderDetail.getActualQuantity());
            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += riceTransferOrderMapper.updateById(order);
        }
        return res;
    }

    /**
     * 修改稻壳转运工单详情
     * @param updateDTO
     * @return
     */
    @Override
    @Transactional
    public Integer detailUpdate(DetailUpdateDTO updateDTO) {
        TMpdRtOrderDetail tMpdStOrderDetail = orderDetailMapper.selectById(updateDTO.getId());
        if (tMpdStOrderDetail == null) {
            throw new BaseKnownException(10000, "获取数据有误，id：" + updateDTO.getId());
        }
        //修正的差值
        BigDecimal subtract = updateDTO.getActualQuantity().subtract(tMpdStOrderDetail.getActualQuantity());
        TMpdRtOrderDetail update = new TMpdRtOrderDetail();
        update.setId(updateDTO.getId());
        update.setActualQuantity(updateDTO.getActualQuantity());
        //更新订单的重量
        TMpdRiceTransferOrder order = riceTransferOrderMapper.selectById(tMpdStOrderDetail.getOrderId());
        TMpdRiceTransferOrder updateOrder = new TMpdRiceTransferOrder();
        updateOrder.setId(order.getId());
        updateOrder.setActualQuantity(order.getActualQuantity().add(subtract));
        riceTransferOrderMapper.updateById(updateOrder);
        return orderDetailMapper.updateById(update);
    }

    @Override
    public Page<RiceTransferDetailListDTO> getRiceTransferOrderDetailPageList(RiceTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(riceTransferOrderMapper::getRiceTransferOrderDetailPageList, queryDTO, RiceTransferDetailListDTO.class);
    }

    @Override
    public String syncRiceHuskTransControl(UnifiedTaskDTO taskDTO) {
        log.info("开始处理稻壳二期转一期的转运任务信息：业务系统:{}, 转运任务信息:{}",
                taskDTO.getBusinessSystem(), JSONObject.toJSONString(taskDTO));
        return "";
    }


}
