package com.hvisions.rawmaterial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.rawmaterial.dao.RiceTransferOrderMapper;
import com.hvisions.rawmaterial.dao.RtOrderDetailMapper;
import com.hvisions.rawmaterial.dto.production.rice.transfer.detail.RiceTransferDetailListDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageDTO;
import com.hvisions.rawmaterial.dto.production.rice.transfer.order.RiceTransferOrderPageQueryDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailInsertDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.DetailUpdateDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdRiceTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdRtOrderDetail;
import com.hvisions.rawmaterial.service.RiceTransferOrderService;
import com.hvisions.rawmaterial.service.RlManagementService;
import com.hvisions.rawmaterial.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:稻壳转运工单
 * @date 2022/4/26 10:18
 */
@Slf4j
@Service
public class RiceTransferOrderServiceImpl implements RiceTransferOrderService {

    @Resource
    private RiceTransferOrderMapper riceTransferOrderMapper;

    @Resource
    private RlManagementService rlManagementService;

    @Resource
    private RtOrderDetailMapper orderDetailMapper;

    @Resource
    private com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper materialSiloConfigMapper;

    @Resource
    private com.hvisions.rawmaterial.service.log.SyncOperationLogService syncOperationLogService;


    @Override
    public Page<RiceTransferOrderPageDTO> getRiceTransferOrderPageList(RiceTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(riceTransferOrderMapper::getRiceTransferOrderPageList, queryDTO, RiceTransferOrderPageDTO.class);
    }

    @Override
    @Transactional
    public Integer insertOrderDetail(List<DetailInsertDTO> detailInsertDTOS) {
        int res = 0;
        if (detailInsertDTOS != null && detailInsertDTOS.size() > 0) {
            TMpdRiceTransferOrder order = riceTransferOrderMapper.selectById(detailInsertDTOS.get(0).getOrderId());
            BigDecimal count = new BigDecimal(0);
            List<TMpdRtOrderDetail> orderDetails = DtoMapper.convertList(detailInsertDTOS, TMpdRtOrderDetail.class);
            for (TMpdRtOrderDetail orderDetail : orderDetails) {
                orderDetail.setCreateTime(new Date());
                orderDetail.setState("1");
                orderDetail.setIsManual("1");
                // 执行出入仓操作
                String batch = null;//rlManagementService.stockOutAndIn(orderDetail.getSendSiloId(), orderDetail.getAcceptSiloId(), orderDetail.getActualQuantity());
                orderDetail.setBatch(batch);
                res += orderDetailMapper.insert(orderDetail);
                count = count.add(orderDetail.getActualQuantity());
            }
            // 更新工单实际数量
            order.setActualQuantity(StringUtil.isEmpty(order.getActualQuantity()) ? count : order.getActualQuantity().add(count));
            res += riceTransferOrderMapper.updateById(order);
        }
        return res;
    }

    /**
     * 修改稻壳转运工单详情
     * @param updateDTO
     * @return
     */
    @Override
    @Transactional
    public Integer detailUpdate(DetailUpdateDTO updateDTO) {
        TMpdRtOrderDetail tMpdStOrderDetail = orderDetailMapper.selectById(updateDTO.getId());
        if (tMpdStOrderDetail == null) {
            throw new BaseKnownException(10000, "获取数据有误，id：" + updateDTO.getId());
        }
        //修正的差值
        BigDecimal subtract = updateDTO.getActualQuantity().subtract(tMpdStOrderDetail.getActualQuantity());
        TMpdRtOrderDetail update = new TMpdRtOrderDetail();
        update.setId(updateDTO.getId());
        update.setActualQuantity(updateDTO.getActualQuantity());
        //更新订单的重量
        TMpdRiceTransferOrder order = riceTransferOrderMapper.selectById(tMpdStOrderDetail.getOrderId());
        TMpdRiceTransferOrder updateOrder = new TMpdRiceTransferOrder();
        updateOrder.setId(order.getId());
        updateOrder.setActualQuantity(order.getActualQuantity().add(subtract));
        riceTransferOrderMapper.updateById(updateOrder);
        return orderDetailMapper.updateById(update);
    }

    @Override
    public Page<RiceTransferDetailListDTO> getRiceTransferOrderDetailPageList(RiceTransferOrderPageQueryDTO queryDTO) {
        return PageHelperUtil.getPage(riceTransferOrderMapper::getRiceTransferOrderDetailPageList, queryDTO, RiceTransferDetailListDTO.class);
    }

    @Override
    @Transactional
    public String syncRiceHuskTransControl(UnifiedTaskDTO taskDTO) {
        log.info("开始处理稻壳二期转一期的转运任务信息：业务系统:{}, 转运任务信息:{}",
                taskDTO.getBusinessSystem(), JSONObject.toJSONString(taskDTO));
        try {
            if (!isRecordExists(taskDTO)) {
                processRiceHuskTransferRecord(taskDTO);
                // 记录日志详情
                log.info("稻壳转运任务同步成功，taskNo:{}, 物料编码:{}, 数量:{}, 开始时间:{}",
                        taskDTO.getTaskNo(), taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            } else {
                // 记录日志详情 - 记录到数据库
                log.debug("记录已存在，跳过：物料编码={}, 数量={}, 开始时间={}",
                        taskDTO.getMaterialCode(), taskDTO.getActualWeight(), taskDTO.getStartTime());
            }
            return String.format("稻壳转运任务信息同步成功，taskNo:%s", taskDTO.getTaskNo());
        } catch (Exception e) {
            log.error("稻壳转运任务信息同步异常:{}", e.getMessage(), e);
            throw new RuntimeException("稻壳转运任务信息同步异常：" + e.getMessage());
        }
    }

    /**
     * 检查记录是否已存在（根据物料编码、任务号、开始时间判断）
     */
    private boolean isRecordExists(UnifiedTaskDTO record) {
        TMpdRtOrderDetail existing = orderDetailMapper
                .selectByMaterialCodeAndTaskNoAndTime(
                        record.getMaterialCode(),
                        record.getTaskNo(),
                        record.getStartTime()
                );
        return existing != null;
    }

    /**
     * 处理稻壳转运记录
     */
    private void processRiceHuskTransferRecord(UnifiedTaskDTO taskDTO) {
        // 1. 根据工单日期查询或创建主表记录
        Date recordDate = taskDTO.getStartTime() != null ? taskDTO.getStartTime() : new Date();

        TMpdRiceTransferOrder existingOrder = riceTransferOrderMapper.selectByDateAndMaterialCode(recordDate, taskDTO.getMaterialCode());

        if (existingOrder == null) {
            // 2. 主表不存在则新增
            existingOrder = createNewRiceTransferOrder(recordDate, taskDTO);
            log.info("新增主表任务，工单号:{}, 物料编码:{}", existingOrder.getOrderNo(), taskDTO.getMaterialCode());

            // 创建任务详情
            TMpdRtOrderDetail detail = createRiceTransferDetail(existingOrder.getId(), taskDTO);
            log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingOrder.getId(), taskDTO.getUniqueId());
        } else {
            // 2. 主表存在，判断详情表是否有相同记录
            TMpdRtOrderDetail existingDetail = orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                    taskDTO.getMaterialCode(), taskDTO.getTaskNo(), taskDTO.getStartTime());

            if (existingDetail == null) {
                // 3. 详情表不存在，则新增
                TMpdRtOrderDetail detail = createRiceTransferDetail(existingOrder.getId(), taskDTO);
                log.info("新增详情记录，任务ID:{}, uniqueId:{}", existingOrder.getId(), taskDTO.getUniqueId());
            } else {
                // 3. 详情表存在，则更新
                updateRiceTransferDetail(existingDetail, taskDTO);
                log.info("更新详情记录，详情ID:{}, uniqueId:{}", existingDetail.getId(), taskDTO.getUniqueId());
            }

            // 4. 更新主表汇总信息
            updateRiceTransferSummary(existingOrder);
        }
    }

    /**
     * 创建新的稻壳转运工单主表记录
     */
    private TMpdRiceTransferOrder createNewRiceTransferOrder(Date recordDate, UnifiedTaskDTO taskDTO) {
        TMpdRiceTransferOrder order = new TMpdRiceTransferOrder();
        order.setOrderDate(recordDate);
        order.setMaterialCode(taskDTO.getMaterialCode());
        order.setMaterialName(taskDTO.getMaterialName());
        order.setUnit(taskDTO.getUnit());
        order.setActualQuantity(taskDTO.getActualWeight());
        order.setInventoryQuantity(taskDTO.getActualWeight());

        // 生成工单号 - 参考高粱的格式：ZL + 年月日 + 三位流水号，稻壳用DK
        order.setOrderNo(generateOrderNo(recordDate));

        riceTransferOrderMapper.insert(order);
        return order;
    }

    /**
     * 创建稻壳转运工单详情记录
     */
    private TMpdRtOrderDetail createRiceTransferDetail(Integer orderId, UnifiedTaskDTO taskDTO) {
        TMpdRtOrderDetail detail = new TMpdRtOrderDetail();
        detail.setOrderId(orderId);

        // 查找发送筒仓
        com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO sendSilo =
            findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()), taskDTO.getExtField6());
        detail.setSendSiloId(sendSilo.getId());

        // 查找接收筒仓
        com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO acceptSilo =
            findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()), taskDTO.getExtField6());
        detail.setAcceptSiloId(acceptSilo.getId());

        detail.setMaterialCode(taskDTO.getMaterialCode());
        detail.setMaterialName(taskDTO.getMaterialName());
        detail.setUnit(taskDTO.getUnit());
        detail.setActualQuantity(taskDTO.getActualWeight());
        detail.setState(parseStatusStr(taskDTO.getStatus()));
        detail.setActualBeginTime(taskDTO.getStartTime());
        detail.setActualEndTime(taskDTO.getEndTime());
        detail.setControlTaskNo(taskDTO.getTaskNo());
        detail.setLogId(taskDTO.getExtField1());

        orderDetailMapper.insert(detail);
        return detail;
    }

    /**
     * 更新稻壳转运工单详情记录
     */
    private void updateRiceTransferDetail(TMpdRtOrderDetail existingDetail, UnifiedTaskDTO taskDTO) {
        // 查找发送筒仓
        com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO sendSilo =
            findSiloCodeByTubCode(covertStorage(taskDTO.getSourceSiloNo()), taskDTO.getExtField6());
        existingDetail.setSendSiloId(sendSilo.getId());

        // 查找接收筒仓
        com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO acceptSilo =
            findSiloCodeByTubCode(covertStorage(taskDTO.getTargetSiloNo()), taskDTO.getExtField6());
        existingDetail.setAcceptSiloId(acceptSilo.getId());

        existingDetail.setMaterialCode(taskDTO.getMaterialCode());
        existingDetail.setMaterialName(taskDTO.getMaterialName());
        existingDetail.setUnit(taskDTO.getUnit());
        existingDetail.setActualQuantity(taskDTO.getActualWeight());
        existingDetail.setActualBeginTime(taskDTO.getStartTime());
        existingDetail.setActualEndTime(taskDTO.getEndTime());
        existingDetail.setState(parseStatusStr(taskDTO.getStatus()));
        existingDetail.setUpdateTime(new Date());
        existingDetail.setControlTaskNo(taskDTO.getTaskNo());
        existingDetail.setLogId(taskDTO.getExtField1());

        orderDetailMapper.updateById(existingDetail);
    }

    /**
     * 通过筒码查原辅料仓储位
     */
    private com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO findSiloCodeByTubCode(String siloCode, Integer materialType) {
        com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO silo =
            materialSiloConfigMapper.findSiloBySiloCode(siloCode, materialType);
        if (silo == null) {
            throw new RuntimeException("未找到筒仓信息：" + siloCode);
        }
        return silo;
    }

    /**
     * 更新稻壳转运工单汇总信息
     */
    private void updateRiceTransferSummary(TMpdRiceTransferOrder order) {
        // 查询该主表下所有详情记录的汇总数据
        BigDecimal summaryQuantity = orderDetailMapper.selectSummaryByOrderId(order.getId());

        order.setActualQuantity(summaryQuantity != null ? summaryQuantity : BigDecimal.ZERO);
        order.setUpdateTime(new Date());

        riceTransferOrderMapper.updateById(order);
        log.info("根据详情汇总更新主表成功，工单ID：{}，数量：{}", order.getId(), summaryQuantity);
    }

    /**
     * 生成工单号
     * 规则：DK + 年月日 + 三位流水号
     */
    private String generateOrderNo(Date orderDate) {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(orderDate);

        // 查询当天最大流水号
        // 这里简化处理，实际应该查询数据库获取当天最大流水号
        String serialNo = String.format("%03d", (int)(Math.random() * 999) + 1);

        return "DK" + dateStr + serialNo;
    }

    /**
     * 中粮筒仓对应MES筒仓
     */
    public static String covertStorage(String blStorage) {
        if (blStorage == null || blStorage.trim().isEmpty()) {
            return blStorage;
        }

        String storageCode = null;
        if (blStorage.contains("A")) { // 一期筒仓
            switch (blStorage) {
                case "A201":
                    storageCode = "GL2-1";
                    break;
                case "A202":
                    storageCode = "GL2-2";
                    break;
                case "A203":
                    storageCode = "GL2-3";
                    break;
                case "A204":
                    storageCode = "GL2-4";
                    break;
                default:
                    storageCode = blStorage;
                    break;
            }
        } else {
            storageCode = blStorage;
        }
        return storageCode;
    }

    /**
     * 解析状态字符串为状态码
     */
    public static String parseStatusStr(String status) {
        if (status == null || status.trim().isEmpty()) {
            return "0"; // 默认为待执行
        }
        switch (status.toUpperCase()) {
            case "PROCESSING":
            case "执行中":
                return "0";
            case "COMPLETED":
            case "已完成":
                return "1";
            default:
                return "0"; // 默认为待执行
        }
    }

}
