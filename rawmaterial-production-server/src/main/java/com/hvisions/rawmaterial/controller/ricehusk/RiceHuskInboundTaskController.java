package com.hvisions.rawmaterial.controller.ricehusk;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskDTO;
import com.hvisions.rawmaterial.dto.ricehusk.RiceHuskInboundTaskQueryDTO;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.service.ricehusk.RiceHuskInboundTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * 稻壳入仓任务控制器
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/ricehusk/inbound-task")
@Api(value = "稻壳入仓任务管理", tags = "稻壳入仓任务管理")
public class RiceHuskInboundTaskController {

    @Autowired
    private RiceHuskInboundTaskService riceHuskInboundTaskService;


    @ApiOperation(value = "分页查询高粱转运工单")
    @RequestMapping(value = "/list/page/get", method = RequestMethod.POST)
    public Page<RiceHuskInboundTaskDTO> getRiceHuskInboundTaskPageList(@RequestBody RiceHuskInboundTaskQueryDTO queryDTO) {
        return riceHuskInboundTaskService.getRiceHuskInboundTaskPageList(queryDTO);
    }

    @ApiOperation("保存稻壳入仓任务")
    @PostMapping
    public ResultVO<RiceHuskInboundTaskDTO> save(@RequestBody RiceHuskInboundTaskDTO dto) {
        try {
            RiceHuskInboundTaskDTO result = riceHuskInboundTaskService.save(dto);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "保存稻壳入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("更新稻壳入仓任务")
    @PutMapping
    public ResultVO<RiceHuskInboundTaskDTO> update(@RequestBody RiceHuskInboundTaskDTO dto) {
        try {
            RiceHuskInboundTaskDTO result = riceHuskInboundTaskService.update(dto);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "更新稻壳入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除稻壳入仓任务")
    @DeleteMapping("/{id}")
    public ResultVO<Void> delete(@PathVariable("id") String id) {
        try {
            riceHuskInboundTaskService.delete(id);
            return ResultVO.success(null);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "删除稻壳入仓任务失败：" + e.getMessage());
        }
    }

    @ApiOperation("从中控同步入仓记录")
    @PostMapping("/sync")
    public ResultVO<String> syncFromCentralControl(@RequestBody UnifiedTaskDTO taskDTO) {
        try {
            String result = riceHuskInboundTaskService.syncFromCentralControl(taskDTO);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "同步入仓记录失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据ID查询稻壳入仓任务")
    @GetMapping("/{id}")
    public ResultVO<RiceHuskInboundTaskDTO> findById(@PathVariable("id") String id) {
        try {
            RiceHuskInboundTaskDTO result = riceHuskInboundTaskService.findById(id);
            return ResultVO.success(result);
        } catch (Exception e) {
            throw new BaseKnownException(10000, "查询稻壳入仓任务失败：" + e.getMessage());
        }
    }
}
