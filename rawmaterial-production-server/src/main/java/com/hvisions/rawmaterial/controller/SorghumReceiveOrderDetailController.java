package com.hvisions.rawmaterial.controller;


import com.hvisions.rawmaterial.dto.production.sorghum.receive.SorghumReceiveDTO;
import com.hvisions.rawmaterial.dto.production.sorghum.receive.UpdateFlourTransferDTO;
import com.hvisions.rawmaterial.service.SorghumReceiveOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description:高粱工单详情接收自控信息接口
 * @date 2023/6/2 10:30
 */
@RestController
@RequestMapping(value = "/sorghumReceiveOrderDetail")
@Api(tags = "高粱工单详情接收自控信息")
public class SorghumReceiveOrderDetailController {

    @Resource
    private SorghumReceiveOrderDetailService sorghumReceiveOrderDetailService;

    // 转运：2期到1期；转粮：一期到高粱暂存仓，粉碎生产：高粱暂存仓到粉暂存仓，高粱粉转运：粉暂存仓到粉发放仓，分发：粉发放仓到各个中心

    @ApiOperation(value = "接收高粱转运工单详情")
    @RequestMapping(value = "/receiveSorghumTransfer", method = RequestMethod.POST)
    public Integer receiveSorghumTransfer(@RequestBody List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        return sorghumReceiveOrderDetailService.receiveSorghumTransfer(sorghumReceiveDTOS);
    }

    @ApiOperation(value = "接收高粱转粮工单详情")
    @RequestMapping(value = "/receiveSorghumShipment", method = RequestMethod.POST)
    public Integer receiveSorghumShipment(@RequestBody List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        return sorghumReceiveOrderDetailService.receiveSorghumShipment(sorghumReceiveDTOS);
    }

    @ApiOperation(value = "接收高粱粉生产工单详情")
    @RequestMapping(value = "/receiveSorghumProduction", method = RequestMethod.POST)
    public Integer receiveSorghumProduction(@RequestBody List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        return sorghumReceiveOrderDetailService.receiveSorghumProduction(sorghumReceiveDTOS);
    }

    @ApiOperation(value = "接收高粱粉转运工单详情")
    @RequestMapping(value = "/receiveFlourTransfer", method = RequestMethod.POST)
    public Integer receiveFlourTransfer(@RequestBody List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        return sorghumReceiveOrderDetailService.receiveFlourTransfer(sorghumReceiveDTOS);
    }

    @ApiOperation(value = "更新高粱粉转运工单详情")
    @RequestMapping(value = "/updateFlourTransfer", method = RequestMethod.POST)
    public Integer updateFlourTransfer(@RequestBody UpdateFlourTransferDTO updateFlourTransferDTO) {
        return sorghumReceiveOrderDetailService.updateFlourTransfer(updateFlourTransferDTO);
    }

    @ApiOperation(value = "接收高粱粉发放工单详情")
    @RequestMapping(value = "/receiveSorghumDispense", method = RequestMethod.POST)
    public Integer receiveSorghumDispense(@RequestBody List<SorghumReceiveDTO> sorghumReceiveDTOS) {
        return sorghumReceiveOrderDetailService.receiveSorghumDispense(sorghumReceiveDTOS);
    }

    @ApiOperation(value = "更新高粱粉发放工单详情")
    @RequestMapping(value = "/updateSorghumDispense", method = RequestMethod.POST)
    public Integer updateSorghumDispense(@RequestBody UpdateFlourTransferDTO updateFlourTransferDTO) {
        return sorghumReceiveOrderDetailService.updateSorghumDispense(updateFlourTransferDTO);
    }

}
