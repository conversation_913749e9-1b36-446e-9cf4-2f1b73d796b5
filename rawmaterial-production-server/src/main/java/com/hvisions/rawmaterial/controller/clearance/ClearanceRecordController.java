package com.hvisions.rawmaterial.controller.clearance;

import com.hvisions.rawmaterial.dto.ClearanceRecordDTO;
import com.hvisions.rawmaterial.dto.ClearanceRecordQueryDTO;
import com.hvisions.rawmaterial.service.ClearanceRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 清仓记录控制器
 * @Date: 2025/07/14
 */
@Slf4j
@RestController
@RequestMapping("/clearance-record")
@Api(tags = "清仓记录管理")
public class ClearanceRecordController {

    @Autowired
    private ClearanceRecordService clearanceRecordService;

    /**
     * 创建清仓记录（中控系统同步接口）
     */
    @PostMapping("/create")
    @ApiOperation("创建清仓记录")
    public String createClearanceRecord(@Valid @RequestBody ClearanceRecordDTO dto) {
        log.info("创建清仓记录，筒仓：{}，物料：{}", dto.getSiloName(), dto.getMaterialName());
        return clearanceRecordService.createClearanceRecord(dto);
    }

    /**
     * 完成清仓记录（中控系统同步接口）
     */
    @PostMapping("/complete")
    @ApiOperation("完成清仓记录")
    public Boolean completeClearanceRecord(
            @ApiParam(value = "中控系统任务ID", required = true) @RequestParam String centralControlTaskId,
            @ApiParam(value = "完成时间", required = true) @RequestParam Date completionTime) {
        log.info("完成清仓记录，中控任务ID：{}", centralControlTaskId);
        return clearanceRecordService.completeClearanceRecord(centralControlTaskId, completionTime);
    }

    /**
     * 分页查询清仓记录
     */
    @PostMapping("/query")
    @ApiOperation("分页查询清仓记录")
    public Page<ClearanceRecordDTO> queryClearanceRecords(@RequestBody ClearanceRecordQueryDTO queryDTO) {
        return clearanceRecordService.queryClearanceRecords(queryDTO);
    }

    /**
     * 根据ID查询清仓记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询清仓记录详情")
    public ClearanceRecordDTO getClearanceRecordById(@ApiParam(value = "清仓记录ID", required = true) @PathVariable Integer id) {
        return clearanceRecordService.getClearanceRecordById(id);
    }

    /**
     * 根据工单号查询清仓记录
     */
    @GetMapping("/work-order/{workOrderNumber}")
    @ApiOperation("根据工单号查询清仓记录")
    public ClearanceRecordDTO getClearanceRecordByWorkOrderNumber(
            @ApiParam(value = "工单号", required = true) @PathVariable String workOrderNumber) {
        return clearanceRecordService.getClearanceRecordByWorkOrderNumber(workOrderNumber);
    }

    /**
     * 查询执行中的清仓记录
     */
    @GetMapping("/executing")
    @ApiOperation("查询执行中的清仓记录")
    public List<ClearanceRecordDTO> getExecutingRecords() {
        return clearanceRecordService.getExecutingRecords();
    }

    /**
     * 根据筒仓查询最新的清仓记录
     */
    @GetMapping("/latest/{siloCode}")
    @ApiOperation("根据筒仓查询最新的清仓记录")
    public ClearanceRecordDTO getLatestClearanceRecordBySilo(
            @ApiParam(value = "筒仓编号", required = true) @PathVariable String siloCode) {
        return clearanceRecordService.getLatestClearanceRecordBySilo(siloCode);
    }

    /**
     * 删除清仓记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除清仓记录")
    public Boolean deleteClearanceRecord(@ApiParam(value = "清仓记录ID", required = true) @PathVariable Integer id) {
        log.info("删除清仓记录，ID：{}", id);
        return clearanceRecordService.deleteClearanceRecord(id);
    }

    /**
     * 生成工单号
     */
    @GetMapping("/generate-work-order-number")
    @ApiOperation("生成工单号")
    public String generateWorkOrderNumber() {
        return clearanceRecordService.generateWorkOrderNumber();
    }

    /**
     * 批次切换处理
     */
    @PostMapping("/batch-switch")
    @ApiOperation("批次切换处理")
    public Boolean processBatchSwitch(
            @ApiParam(value = "筒仓编号", required = true) @RequestParam String siloCode,
            @ApiParam(value = "旧批次号", required = true) @RequestParam String oldBatchNumber,
            @ApiParam(value = "新批次号") @RequestParam(required = false) String newBatchNumber) {
        log.info("处理批次切换，筒仓：{}，旧批次：{}，新批次：{}", siloCode, oldBatchNumber, newBatchNumber);
        return clearanceRecordService.processBatchSwitch(siloCode, oldBatchNumber, newBatchNumber);
    }

    /**
     * 库存校准处理
     */
    @PostMapping("/inventory-calibration")
    @ApiOperation("库存校准处理")
    public Boolean processInventoryCalibration(
            @ApiParam(value = "筒仓编号", required = true) @RequestParam String siloCode) {
        log.info("处理库存校准，筒仓：{}", siloCode);
        return clearanceRecordService.processInventoryCalibration(siloCode);
    }
}
