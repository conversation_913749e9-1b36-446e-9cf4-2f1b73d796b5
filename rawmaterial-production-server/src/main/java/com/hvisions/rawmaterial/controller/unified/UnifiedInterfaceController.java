package com.hvisions.rawmaterial.controller.unified;

import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.service.unified.UnifiedInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 统一接口控制器 - 合并所有业务接口
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/unified")
@Api(tags = "统一业务接口")
public class UnifiedInterfaceController {

    @Resource
    private UnifiedInterfaceService unifiedInterfaceService;

    // ==================== 任务相关接口 ====================

    @ApiOperation(value = "统一任务")
    @PostMapping("/v1/task")
    public String createTask(@Valid @RequestBody UnifiedTaskDTO taskDTO, HttpServletRequest request) {
        return unifiedInterfaceService.createTask(taskDTO, request);
    }
}
