package com.hvisions.rawmaterial.service.impl;

import com.hvisions.rawmaterial.dao.RiceTransferOrderMapper;
import com.hvisions.rawmaterial.dao.RtOrderDetailMapper;
import com.hvisions.rawmaterial.dao.materialsilo.MaterialSiloConfigMapper;
import com.hvisions.rawmaterial.dto.unified.UnifiedTaskDTO;
import com.hvisions.rawmaterial.entity.TMpdRiceTransferOrder;
import com.hvisions.rawmaterial.entity.TMpdRtOrderDetail;
import com.hvisions.rawmaterial.entity.materialsilo.TMpdMateriaSiloDO;
import com.hvisions.rawmaterial.service.log.SyncOperationLogService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 稻壳转运工单服务测试类
 *
 * <AUTHOR>
 * @date 2025-09-23
 */
@ExtendWith(MockitoExtension.class)
class RiceTransferOrderServiceImplTest {

    @Mock
    private RiceTransferOrderMapper riceTransferOrderMapper;

    @Mock
    private RtOrderDetailMapper orderDetailMapper;

    @Mock
    private MaterialSiloConfigMapper materialSiloConfigMapper;

    @Mock
    private SyncOperationLogService syncOperationLogService;

    @InjectMocks
    private RiceTransferOrderServiceImpl riceTransferOrderService;

    private UnifiedTaskDTO taskDTO;
    private TMpdRiceTransferOrder transferOrder;
    private TMpdRtOrderDetail orderDetail;
    private TMpdMateriaSiloDO silo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskDTO = new UnifiedTaskDTO();
        taskDTO.setBusinessSystem("ZHONGLIANG");
        taskDTO.setMaterialType("RICE_HUSK");
        taskDTO.setTaskType("RICE_HUSK_TRANSFER");
        taskDTO.setTaskNo("RH20250923001");
        taskDTO.setSourceSiloNo("A201");
        taskDTO.setTargetSiloNo("A202");
        taskDTO.setMaterialCode("RH001");
        taskDTO.setMaterialName("稻壳");
        taskDTO.setUnit("KG");
        taskDTO.setActualWeight(new BigDecimal("1000.00"));
        taskDTO.setStartTime(new Date());
        taskDTO.setEndTime(new Date());
        taskDTO.setStatus("COMPLETED");
        taskDTO.setExtField1("LOG001");
        taskDTO.setExtField6(1); // 稻壳物料类型

        // 初始化转运工单
        transferOrder = new TMpdRiceTransferOrder();
        transferOrder.setId(1);
        transferOrder.setOrderNo("DK20250923001");
        transferOrder.setMaterialCode("RH001");
        transferOrder.setMaterialName("稻壳");
        transferOrder.setActualQuantity(new BigDecimal("1000.00"));

        // 初始化工单详情
        orderDetail = new TMpdRtOrderDetail();
        orderDetail.setId(1);
        orderDetail.setOrderId(1);
        orderDetail.setMaterialCode("RH001");
        orderDetail.setActualQuantity(new BigDecimal("1000.00"));

        // 初始化筒仓信息
        silo = new TMpdMateriaSiloDO();
        silo.setId(1);
        silo.setCode("GL2-1");
        silo.setName("高粱筒仓2-1");
    }

    @Test
    void testSyncRiceHuskTransControl_NewRecord_Success() {
        // 模拟不存在重复记录
        when(orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                eq("RH001"), eq("RH20250923001"), any(Date.class))).thenReturn(null);
        
        // 模拟不存在主表记录
        when(riceTransferOrderMapper.selectByDateAndMaterialCode(any(Date.class), eq("RH001")))
                .thenReturn(null);
        
        // 模拟筒仓查询
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-1"), eq(1)))
                .thenReturn(silo);
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-2"), eq(1)))
                .thenReturn(silo);
        
        // 模拟插入操作
        when(riceTransferOrderMapper.insert(any(TMpdRiceTransferOrder.class))).thenReturn(1);
        when(orderDetailMapper.insert(any(TMpdRtOrderDetail.class))).thenReturn(1);

        // 执行测试
        String result = riceTransferOrderService.syncRiceHuskTransControl(taskDTO);

        // 验证结果
        assertTrue(result.contains("稻壳转运任务信息同步成功"));
        assertTrue(result.contains("RH20250923001"));
        
        // 验证方法调用
        verify(riceTransferOrderMapper, times(1)).insert(any(TMpdRiceTransferOrder.class));
        verify(orderDetailMapper, times(1)).insert(any(TMpdRtOrderDetail.class));
    }

    @Test
    void testSyncRiceHuskTransControl_ExistingMainOrder_NewDetail() {
        // 模拟不存在重复详情记录
        when(orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                eq("RH001"), eq("RH20250923001"), any(Date.class))).thenReturn(null);
        
        // 模拟存在主表记录
        when(riceTransferOrderMapper.selectByDateAndMaterialCode(any(Date.class), eq("RH001")))
                .thenReturn(transferOrder);
        
        // 模拟筒仓查询
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-1"), eq(1)))
                .thenReturn(silo);
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-2"), eq(1)))
                .thenReturn(silo);
        
        // 模拟插入详情和汇总查询
        when(orderDetailMapper.insert(any(TMpdRtOrderDetail.class))).thenReturn(1);
        when(orderDetailMapper.selectSummaryByOrderId(1)).thenReturn(new BigDecimal("2000.00"));
        when(riceTransferOrderMapper.updateById(any(TMpdRiceTransferOrder.class))).thenReturn(1);

        // 执行测试
        String result = riceTransferOrderService.syncRiceHuskTransControl(taskDTO);

        // 验证结果
        assertTrue(result.contains("稻壳转运任务信息同步成功"));
        
        // 验证方法调用
        verify(riceTransferOrderMapper, never()).insert(any(TMpdRiceTransferOrder.class));
        verify(orderDetailMapper, times(1)).insert(any(TMpdRtOrderDetail.class));
        verify(riceTransferOrderMapper, times(1)).updateById(any(TMpdRiceTransferOrder.class));
    }

    @Test
    void testSyncRiceHuskTransControl_ExistingDetail_Update() {
        // 模拟存在重复详情记录
        when(orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                eq("RH001"), eq("RH20250923001"), any(Date.class))).thenReturn(orderDetail);
        
        // 模拟存在主表记录
        when(riceTransferOrderMapper.selectByDateAndMaterialCode(any(Date.class), eq("RH001")))
                .thenReturn(transferOrder);
        
        // 模拟筒仓查询
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-1"), eq(1)))
                .thenReturn(silo);
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-2"), eq(1)))
                .thenReturn(silo);
        
        // 模拟更新操作和汇总查询
        when(orderDetailMapper.updateById(any(TMpdRtOrderDetail.class))).thenReturn(1);
        when(orderDetailMapper.selectSummaryByOrderId(1)).thenReturn(new BigDecimal("1500.00"));
        when(riceTransferOrderMapper.updateById(any(TMpdRiceTransferOrder.class))).thenReturn(1);

        // 执行测试
        String result = riceTransferOrderService.syncRiceHuskTransControl(taskDTO);

        // 验证结果
        assertTrue(result.contains("稻壳转运任务信息同步成功"));
        
        // 验证方法调用
        verify(orderDetailMapper, times(1)).updateById(any(TMpdRtOrderDetail.class));
        verify(riceTransferOrderMapper, times(1)).updateById(any(TMpdRiceTransferOrder.class));
    }

    @Test
    void testSyncRiceHuskTransControl_DuplicateRecord_Skip() {
        // 模拟存在重复记录（第一次检查）
        when(orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                eq("RH001"), eq("RH20250923001"), any(Date.class))).thenReturn(orderDetail);

        // 执行测试
        String result = riceTransferOrderService.syncRiceHuskTransControl(taskDTO);

        // 验证结果
        assertTrue(result.contains("稻壳转运任务信息同步成功"));
        
        // 验证没有进行插入或更新操作
        verify(riceTransferOrderMapper, never()).insert(any(TMpdRiceTransferOrder.class));
        verify(orderDetailMapper, never()).insert(any(TMpdRtOrderDetail.class));
        verify(riceTransferOrderMapper, never()).selectByDateAndMaterialCode(any(Date.class), any(String.class));
    }

    @Test
    void testSyncRiceHuskTransControl_SiloNotFound_ThrowException() {
        // 模拟不存在重复记录
        when(orderDetailMapper.selectByMaterialCodeAndTaskNoAndTime(
                eq("RH001"), eq("RH20250923001"), any(Date.class))).thenReturn(null);
        
        // 模拟不存在主表记录
        when(riceTransferOrderMapper.selectByDateAndMaterialCode(any(Date.class), eq("RH001")))
                .thenReturn(null);
        
        // 模拟筒仓查询失败
        when(materialSiloConfigMapper.findSiloBySiloCode(eq("GL2-1"), eq(1)))
                .thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            riceTransferOrderService.syncRiceHuskTransControl(taskDTO);
        });

        assertTrue(exception.getMessage().contains("稻壳转运任务信息同步异常"));
    }

    @Test
    void testCovertStorage() {
        // 测试筒仓编码转换
        assertEquals("GL2-1", RiceTransferOrderServiceImpl.covertStorage("A201"));
        assertEquals("GL2-2", RiceTransferOrderServiceImpl.covertStorage("A202"));
        assertEquals("GL2-3", RiceTransferOrderServiceImpl.covertStorage("A203"));
        assertEquals("GL2-4", RiceTransferOrderServiceImpl.covertStorage("A204"));
        assertEquals("B101", RiceTransferOrderServiceImpl.covertStorage("B101"));
        assertNull(RiceTransferOrderServiceImpl.covertStorage(null));
        assertEquals("", RiceTransferOrderServiceImpl.covertStorage(""));
    }

    @Test
    void testParseStatusStr() {
        // 测试状态解析
        assertEquals("0", RiceTransferOrderServiceImpl.parseStatusStr("PROCESSING"));
        assertEquals("0", RiceTransferOrderServiceImpl.parseStatusStr("执行中"));
        assertEquals("1", RiceTransferOrderServiceImpl.parseStatusStr("COMPLETED"));
        assertEquals("1", RiceTransferOrderServiceImpl.parseStatusStr("已完成"));
        assertEquals("0", RiceTransferOrderServiceImpl.parseStatusStr("UNKNOWN"));
        assertEquals("0", RiceTransferOrderServiceImpl.parseStatusStr(null));
        assertEquals("0", RiceTransferOrderServiceImpl.parseStatusStr(""));
    }
}
